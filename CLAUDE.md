# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

QiyuAI-Web is a full-stack cryptocurrency analysis platform consisting of a Django backend (`finance_backend/`) and React frontend (`qiyu-ai-frontend/`). The system provides AI-powered cryptocurrency analysis, real-time K-line chart visualization, and user management with credit-based usage tracking. Originally designed for WeChat Mini Programs, it's actively migrating to a web platform with SMS-based authentication.

## Development Commands

### Backend (Django)

**Environment Setup:**
```bash
cd finance_backend

# Install dependencies if needed
pip install -r requirements.txt
```

**Core Development Commands:**
```bash
# Development server
python manage.py runserver 0.0.0.0:8000

# Development server (background with nohup)
nohup python3 manage.py runserver 0.0.0.0:8000 > server.log 2>&1 &

# Check if server is running
lsof -i:8000

# View server logs
tail -f server.log

# Database operations
python manage.py migrate
python manage.py makemigrations

# Testing
python manage.py test
python manage.py test user_api     # Specific app
python manage.py test ai_analysis

# Production
gunicorn finance_project.wsgi:application --bind 0.0.0.0:8000 --workers 3
```

**Real-time Data Collection:**
```bash
# Start real-time data collector (automated script)
./start_realtime_collector.sh

# Manual data collection commands
python manage.py collect_realtime_data --symbol=BTCUSDT --daemon
python manage.py collect_realtime_data --symbol=BTCUSDT --debug
python manage.py collect_multi_data  # Multiple cryptocurrencies
```

### Frontend (React)

```bash
cd qiyu-ai-frontend

# Install dependencies (if needed)
npm install

# Development server (http://localhost:3000)
npm start

# Development server (background with nohup)
nohup npm start > frontend.log 2>&1 &

# Check if frontend is running
lsof -i:3000

# View frontend logs
tail -f frontend.log

# Production build
npm run build

# Testing
npm test

# Linting (if ESLint configured)
npm run lint
```

## Core Architecture

### Backend Structure (Django)

- **`finance_project/`** - Main Django configuration and settings
- **`user_api/`** - WeChat OAuth authentication and credit management (legacy)
- **`web_api/`** - SMS-based authentication for web platform (active)
- **`crypto_api/`** - Basic cryptocurrency data endpoints
- **`ai_analysis/`** - AI analysis services with streaming responses
- **`historical_data/`** - Historical cryptocurrency data management

### Frontend Structure (React)

- **`src/components/Chart/`** - K-line chart components using KLineCharts library
  - `EnhancedKLineChart.js` - Advanced chart with real-time data and technical indicators
  - `GridKLineChart.js` - Multi-chart grid layout for different timeframes
  - `MultiYearKLineChart.js` - Multi-year data visualization wrapper
- **`src/services/`** - API clients and data services
  - `BinanceWebSocketService.js` - Real-time WebSocket connections to Binance
  - `StreamDataService.js` - COS data fetching with chunked transfer handling
  - `AIAnalysisService.js` - AI analysis API integration
- **`src/pages/`** - Main application pages
  - `Dashboard.advanced.js` - Main dashboard with chart visualization
  - `AIAnalysisPage.js` - AI analysis interface with multiple cryptocurrency support

### Data Flow Architecture

1. **Historical Data**: COS (Tencent Cloud) → StreamDataService → Chart Components
2. **Real-time Data**: Binance WebSocket → BinanceWebSocketService → Chart Updates
3. **AI Analysis**: User Input → Django Backend → DeepSeek API → Streaming Response
4. **Authentication**: SMS Verification → JWT Tokens → API Access

## Key Technologies

### Backend Stack

- **Django 4.2.7** with Django REST Framework
- **DeepSeek AI** integration for cryptocurrency analysis
- **Tencent Cloud** services (COS storage, SMS verification)
- **Binance API** proxy for real-time market data
- **JWT authentication** with djangorestframework-simplejwt

### Frontend Stack

- **React 19.1.0** with functional components and hooks
- **Ant Design 5.26.4** for UI components with dark theme
- **KLineCharts 9.8.12** for advanced cryptocurrency chart visualization
- **Supabase 2.17.0** for SMS authentication services
- **Real-time WebSocket** connections for live market data

## WebSocket Architecture

The system uses a sophisticated WebSocket management system for real-time data:

### Supported Intervals and Data Sources

- **WebSocket Intervals**: `['1m', '15m', '1h', '4h', '1d']`
- **Historical Data Support**: `['1m', '15m', '1h', '4h', '1d', '1mo']`
- **Binance Stream**: `wss://stream.binance.com:9443/ws`

### Throttling Strategy

```javascript
// Update frequencies by timeframe
'1m': 10000,    // 10 seconds
'15m': 60000,   // 1 minute
'1h': 300000,   // 5 minutes
'4h': 600000,   // 10 minutes
'1d': 1200000   // 20 minutes
```

### Connection Management

- **Multi-connection support**: Up to 200 streams per WebSocket connection
- **Auto-reconnection**: Exponential backoff with 5 retry attempts
- **Health monitoring**: 30-second ping intervals with automatic cleanup

## Data Storage Architecture

### COS (Tencent Cloud Object Storage) Structure

```
# Historical data (2017-2024): 3-layer nested path
/finance-**********/finance-**********/finance-**********/crypto-kline-data-v2/20250724/

# 2025 data: Direct path
/crypto-kline-data-v2/20250726/

# File naming convention
{symbol}/{timeframe}/{symbol}_{timeframe}_{year}_compressed.json
```

### Data Processing

- **AWS Chunked Transfer**: Handled by `StreamDataService.cleanChunkedData()`
- **JSON Compression**: Reduces transfer size for large datasets
- **Multi-year Support**: Separate storage paths prevent data conflicts
- **Real-time Updates**: WebSocket integration with historical data overlay

## Authentication Migration

### Current Status (Dual System)

- **Legacy**: WeChat OAuth for Mini Program users
- **Active**: SMS-based authentication for web users

### Web Authentication Flow

1. SMS verification via Tencent Cloud SMS service
2. JWT token generation with refresh capability
3. Credit system integration for usage tracking
4. Referral system with commission tracking

## Important File Dependencies

### Chart System Dependencies

- `EnhancedKLineChart.js` is used by `GridKLineChart.js` and `MultiYearKLineChart.js`
- Both `BinanceWebSocket.js` and `BinanceWebSocketService.js` exist - prefer `BinanceWebSocketService.js` (newer, more features)
- `StreamDataService.js` contains the working COS data parsing logic

### Configuration Files

- `yearConfig.js` - Controls current year data inclusion (CURRENT_YEAR_CUTOFF_MONTH: 1)
- `Dashboard.advanced.js` - Defines supported timeframes: `['1m', '15m', '1h', '4h', '1d', '1mo']`

## Migration Notes

### WeChat to Web Platform Migration

- **Authentication**: WeChat OAuth → SMS verification + JWT
- **Storage**: `wx.setStorageSync()` → `localStorage`
- **Requests**: `wx.request()` → `fetch()` with CORS handling
- **UI**: WeChat Mini Program components → React + Ant Design

### Active Development Areas

- SMS authentication system refinement
- Real-time chart performance optimization
- COS data storage path organization
- AI analysis streaming response handling

## Environment Configuration

### Backend Environment Variables

Required environment variables (create `.env` file in `finance_backend/`):
- `SECRET_KEY` - Django security key
- `DEBUG` - Development mode (True/False)
- `ALLOWED_HOSTS` - Comma-separated list of allowed hosts
- `DEEPSEEK_API_KEY` - AI analysis service
- `COS_*` - Tencent Cloud storage credentials for COS access
- `SMS_*` - Tencent Cloud SMS service credentials
- Database settings (currently using SQLite in development)

### Frontend Configuration

Environment variables in `qiyu-ai-frontend/.env`:
- `REACT_APP_SUPABASE_URL` - Supabase project URL for SMS authentication
- `REACT_APP_SUPABASE_ANON_KEY` - Supabase anonymous access key
- `REACT_APP_API_BASE_URL` - Backend API base URL (http://localhost:8000/api/web)

**Key Integration Points:**
- Supabase integration for SMS authentication
- CORS configuration for development (localhost:3000 ↔ localhost:8000)
- WebSocket connection to Binance API for real-time data

## Development Workflow

1. **Environment Setup**: Always activate conda environment (`conda activate qiyu-web`) before backend work
2. **Backend Changes**: Test with `python manage.py test`, run migrations if needed
3. **Frontend Changes**: Use React hot reload, test chart components with real data  
4. **WebSocket Testing**: Monitor browser console for connection status and data flow
5. **AI Analysis**: Verify streaming responses work correctly with timeout handling
6. **Authentication**: Test both SMS verification and JWT token flow
7. **Real-time Data**: Use `./start_realtime_collector.sh` to ensure continuous data collection

## Running the Application

### Quick Start (Background Services)

Both backend and frontend are already running with nohup:

**Backend (Django)** - Running on port 8000:
```bash
# Check backend status
lsof -i:8000
# View backend logs
tail -f finance_backend/server.log
```

**Frontend (React)** - Should be running on port 3000:
```bash
# Check frontend status  
lsof -i:3000
# View frontend logs
tail -f qiyu-ai-frontend/frontend.log
```

### Service Management

**Stop Services:**
```bash
# Kill backend process
kill $(lsof -t -i:8000)
# Kill frontend process  
kill $(lsof -t -i:3000)
```

**Restart Services:**
```bash
# Restart backend
cd finance_backend && nohup python3 manage.py runserver 0.0.0.0:8000 > server.log 2>&1 &
# Restart frontend
cd qiyu-ai-frontend && nohup npm start > frontend.log 2>&1 &
```

## Critical Data Handling Issues

### 2025年1分钟数据特殊处理

**⚠️ 重要**: 2025年1分钟数据有特殊的存储结构和时间戳格式，需要特别注意：

#### 存储路径结构
```
# 常规年份数据格式
/crypto-kline-data-v2/20250724/{symbol}/{timeframe}/{symbol}_{timeframe}_{year}_compressed.json

# 2025年1分钟数据特殊格式
/crypto-kline-data-v2/20250726/{symbol}/1m/{symbol}_1m_01_2025_compressed.json  # 月度文件(1-6月)
/crypto-kline-data-v2/20250726/{symbol}/1m/daily/{symbol}_1m_07-01_2025_compressed.json  # 每日文件(7月1-27日)
```

#### 时间戳格式问题
- **2025年数据**: 使用**微秒时间戳** (16位数字，如 `1735689600000000`)
- **其他年份**: 使用**毫秒时间戳** (13位数字，如 `1735689600000`)
- **修复方案**: `StreamDataService.js` 中自动检测并转换
  ```javascript
  const timestamp = rawTimestamp > 1e15 ? Math.floor(rawTimestamp / 1000) : rawTimestamp
  ```

#### 年份验证逻辑
- **CryptoMetadataService**: 需要特殊检查2025年1分钟数据可用性
- **检查文件**: `{symbol}_1m_01_2025_compressed.json` 而不是 `{symbol}_1m_2025_compressed.json`
- **数据加载**: 使用 `load2025MinuteData()` 方法合并月度和每日数据

#### 常见问题
1. **十字指标显示错误日期** (如57541年) → 时间戳格式问题，已修复
2. **1分钟模式缺少2025年图表** → 年份验证失败，已修复
3. **数据加载失败** → 路径结构不匹配，使用专用加载方法

## Performance Considerations

- **Chart Rendering**: Use throttling to prevent excessive redraws
- **WebSocket Management**: Implement connection pooling and cleanup
- **Data Caching**: Cache historical data to reduce COS requests
- **AI Analysis**: Handle 10-minute timeout limits for complex analysis
- **2025年数据**: 清除缓存后重新加载以应用时间戳修复

## Additional Documentation

For deeper understanding of specific subsystems, refer to:
- `REALTIME_DATA_ARCHITECTURE.md` - Real-time data collection and COS integration architecture
- `finance_backend/CLAUDE.md` - Backend-specific development guidance  
- `finance_backend/WEB_MIGRATION_API_DOCS.md` - API documentation for web platform
- `finance_backend/MINIPROGRAM_TO_WEB_MIGRATION_GUIDE.md` - WeChat Mini Program migration details

## Debugging and Browser Console Commands

The frontend includes debug utilities accessible via browser console:

```javascript
// Check real-time data service status
debugCrypto.getServiceStatus()

// Force check for data updates
debugCrypto.forceCheckUpdates()

// Manual chart refresh trigger
debugCrypto.triggerChartRefresh()

// Clear cache and refresh charts
debugCrypto.refreshCharts()

// Repair data gaps (calls backend API)
debugCrypto.repairGap('BTCUSDT')
```
