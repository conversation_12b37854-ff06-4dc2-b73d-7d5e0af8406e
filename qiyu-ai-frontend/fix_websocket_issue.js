#!/usr/bin/env node
/**
 * 修复前端WebSocket连接问题
 * 禁用WebSocket实时连接，改为定时刷新模式
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 修复前端WebSocket连接问题...');

// 1. 修改EnhancedKLineChart.js - 禁用实时WebSocket
const chartFilePath = path.join(__dirname, 'src/components/Chart/EnhancedKLineChart.js');

try {
  let chartContent = fs.readFileSync(chartFilePath, 'utf8');
  
  // 注释掉WebSocket相关的导入和功能
  chartContent = chartContent.replace(
    'import binanceWebSocketService from \'../../services/BinanceWebSocketService\'',
    '// import binanceWebSocketService from \'../../services/BinanceWebSocketService\' // 已禁用WebSocket'
  );
  
  chartContent = chartContent.replace(
    'import realTimeDataManager from \'../../services/RealTimeDataManager\'',
    '// import realTimeDataManager from \'../../services/RealTimeDataManager\' // 已禁用WebSocket'
  );
  
  // 强制禁用实时连接
  chartContent = chartContent.replace(
    'enableRealTime = false,',
    'enableRealTime = false, // 强制禁用WebSocket实时连接'
  );
  
  fs.writeFileSync(chartFilePath, chartContent);
  console.log('✅ 已修改 EnhancedKLineChart.js');
} catch (error) {
  console.log('❌ EnhancedKLineChart.js 修改失败:', error.message);
}

// 2. 创建简化的BinanceWebSocketService（返回空实现）
const wsServicePath = path.join(__dirname, 'src/services/BinanceWebSocketService.js');

try {
  const emptyWebSocketService = `/**
 * 简化的WebSocket服务 - 禁用实时连接
 * 改为使用定时刷新模式
 */

class BinanceWebSocketService {
  constructor() {
    console.log('WebSocket服务已禁用，使用定时刷新模式');
  }
  
  subscribeKline() {
    console.log('WebSocket订阅已禁用');
    return 'disabled';
  }
  
  unsubscribe() {
    console.log('WebSocket取消订阅已禁用');
  }
  
  closeAllConnections() {
    console.log('WebSocket连接清理已禁用');
  }
}

const binanceWebSocketService = new BinanceWebSocketService();
export default binanceWebSocketService;
export { BinanceWebSocketService };
`;

  // 备份原文件
  if (fs.existsSync(wsServicePath)) {
    fs.copyFileSync(wsServicePath, wsServicePath + '.backup');
    console.log('✅ 已备份原始 BinanceWebSocketService.js');
  }
  
  fs.writeFileSync(wsServicePath, emptyWebSocketService);
  console.log('✅ 已替换 BinanceWebSocketService.js 为空实现');
} catch (error) {
  console.log('❌ BinanceWebSocketService.js 修改失败:', error.message);
}

// 3. 创建定时刷新配置
const configPath = path.join(__dirname, 'src/config/chartConfig.js');

try {
  const chartConfig = `/**
 * 图表配置 - 定时刷新模式
 */

export const CHART_CONFIG = {
  // 禁用WebSocket实时连接
  ENABLE_WEBSOCKET: false,
  
  // 定时刷新间隔（毫秒）
  REFRESH_INTERVALS: {
    '1m': 30000,   // 1分钟数据每30秒刷新
    '15m': 60000,  // 15分钟数据每1分钟刷新
    '1h': 300000,  // 1小时数据每5分钟刷新
    '4h': 600000,  // 4小时数据每10分钟刷新
    '1d': 1200000  // 1天数据每20分钟刷新
  },
  
  // 数据源配置
  DATA_SOURCE: {
    BASE_URL: 'http://localhost:8000/api',
    ENDPOINTS: {
      HISTORICAL: '/historical',
      SAME_PERIOD: '/historical/same-period',
      INDICATORS: '/historical/period-indicators',
      COMPARATIVE: '/historical/comparative-analysis'
    }
  }
};

export default CHART_CONFIG;
`;

  // 确保config目录存在
  const configDir = path.dirname(configPath);
  if (!fs.existsSync(configDir)) {
    fs.mkdirSync(configDir, { recursive: true });
  }
  
  fs.writeFileSync(configPath, chartConfig);
  console.log('✅ 已创建图表配置文件');
} catch (error) {
  console.log('❌ 图表配置文件创建失败:', error.message);
}

console.log('');
console.log('🎉 修复完成！现在前端将使用以下模式：');
console.log('   ✓ 禁用WebSocket实时连接');
console.log('   ✓ 使用定时刷新获取数据');
console.log('   ✓ 从后端API获取历史数据');
console.log('   ✓ 支持新的历史同期分析功能');
console.log('');
console.log('🔄 请重启前端服务来应用更改：');
console.log('   cd qiyu-ai-frontend');
console.log('   npm start');
console.log('');
console.log('🧪 测试新的历史分析API：');
console.log('   http://localhost:3000 (前端)');
console.log('   http://localhost:8000/api/historical/analysis-capabilities/ (后端API)');