# Generated by Django 4.2.7 on 2025-07-30 15:49

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AutoPredictionTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('symbol', models.CharField(max_length=20, unique=True, verbose_name='交易对符号')),
                ('interval', models.Char<PERSON>ield(default='1m', max_length=10, verbose_name='时间间隔')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('priority', models.IntegerField(default=1, help_text='数字越小优先级越高', verbose_name='优先级(1-10)')),
                ('last_run_at', models.DateTimeField(blank=True, null=True, verbose_name='上次运行时间')),
                ('next_run_at', models.DateTimeField(blank=True, null=True, verbose_name='下次运行时间')),
                ('total_runs', models.IntegerField(default=0, verbose_name='总运行次数')),
                ('success_runs', models.IntegerField(default=0, verbose_name='成功次数')),
                ('failed_runs', models.IntegerField(default=0, verbose_name='失败次数')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '自动预测任务',
                'verbose_name_plural': '自动预测任务',
                'db_table': 'ai_auto_prediction_tasks',
                'ordering': ['priority', 'symbol'],
            },
        ),
        migrations.CreateModel(
            name='PredictionRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('symbol', models.CharField(db_index=True, max_length=20, verbose_name='交易对符号')),
                ('interval', models.CharField(default='1m', max_length=10, verbose_name='时间间隔')),
                ('prediction_timestamp', models.BigIntegerField(db_index=True, verbose_name='预测目标时间戳')),
                ('prediction_datetime', models.DateTimeField(db_index=True, verbose_name='预测目标时间')),
                ('created_at', models.DateTimeField(db_index=True, default=django.utils.timezone.now, verbose_name='创建时间')),
                ('current_price', models.DecimalField(decimal_places=8, max_digits=20, verbose_name='当前价格')),
                ('support_level', models.DecimalField(decimal_places=8, max_digits=20, null=True, verbose_name='支撑位')),
                ('resistance_level', models.DecimalField(decimal_places=8, max_digits=20, null=True, verbose_name='阻力位')),
                ('technical_indicators', models.JSONField(default=dict, verbose_name='技术指标数据')),
                ('btc_reference', models.JSONField(default=dict, verbose_name='BTC参考数据')),
                ('analysis_summary', models.TextField(verbose_name='AI分析总结')),
                ('prediction_distribution', models.JSONField(default=dict, verbose_name='多空预测分布')),
                ('market_sentiment', models.JSONField(default=dict, verbose_name='市场情绪数据')),
                ('confidence_level', models.CharField(default='中', max_length=10, verbose_name='置信度')),
                ('prediction_status', models.CharField(choices=[('pending', '待处理'), ('processing', '处理中'), ('completed', '已完成'), ('failed', '失败')], db_index=True, default='pending', max_length=20, verbose_name='预测状态')),
                ('data_points_count', models.IntegerField(default=0, verbose_name='数据点数量')),
                ('historical_years_count', models.IntegerField(default=0, verbose_name='历史年份数量')),
                ('processing_time_seconds', models.FloatField(null=True, verbose_name='处理耗时(秒)')),
                ('error_message', models.TextField(blank=True, verbose_name='错误信息')),
                ('raw_ai_response', models.TextField(blank=True, verbose_name='AI原始响应')),
            ],
            options={
                'verbose_name': 'AI预测记录',
                'verbose_name_plural': 'AI预测记录',
                'db_table': 'ai_prediction_records',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['symbol', '-created_at'], name='ai_predicti_symbol_a7ae94_idx'), models.Index(fields=['prediction_datetime', 'symbol'], name='ai_predicti_predict_07f18f_idx'), models.Index(fields=['prediction_status', 'created_at'], name='ai_predicti_predict_9feb59_idx')],
            },
        ),
    ]
