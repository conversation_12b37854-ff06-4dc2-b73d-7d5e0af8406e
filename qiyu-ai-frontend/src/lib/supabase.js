// 导入SMS认证服务替代Supabase
import { smsAuthService, userService as smsUserService, onAuthStateChange as smsOnAuthStateChange } from './smsAuth';

console.log('SMS Auth Service initialized')
console.log('Backend API URL:', process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000/api/web')

// 导出SMS认证服务（替代supabase）
export { smsAuthService as supabase }

// 数据库表结构定义
export const DB_TABLES = {
  PROFILES: 'profiles',
  BALANCE_TRANSACTIONS: 'balance_transactions'
}

// 导出SMS用户服务（替代原有的userService）
export const userService = smsUserService

// 监听认证状态变化
export const onAuthStateChange = smsOnAuthStateChange
