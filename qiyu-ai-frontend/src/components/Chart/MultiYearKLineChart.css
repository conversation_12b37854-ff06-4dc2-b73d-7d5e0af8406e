.multi-year-kline-chart {
  background-color: #0d1117;
  min-height: 100vh;
  padding: 16px;
  transition: all 0.3s ease;
}

.multi-year-kline-chart.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  padding: 8px;
}

/* 全局工具栏 */
.global-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #21262d;
  margin-bottom: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-center {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-title {
  color: #f0f6fc;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.realtime-indicator {
  color: #26a641;
  font-size: 12px;
  padding: 4px 8px;
  background-color: rgba(38, 166, 65, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(38, 166, 65, 0.3);
}

.toolbar-right {
  display: flex;
  align-items: center;
}

/* 图表容器 */
.charts-container {
  background-color: #161b22;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}

/* 网格布局容器 */
.charts-grid-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  padding: 16px;
  background-color: #161b22;
  overflow-y: auto;
  max-height: calc(100vh - 200px);
}

/* 响应式网格布局 */
@media (max-width: 1200px) {
  .charts-grid-layout {
    grid-template-columns: 1fr; /* 小屏幕改为单列 */
    max-height: calc(100vh - 180px);
  }
}

/* 网格中的图表卡片 */
.grid-chart-card {
  background-color: #1f1f1f !important;
  border: 1px solid #30363d !important;
  border-radius: 8px !important;
  height: 450px;
}

.grid-chart-card .ant-card-head {
  background-color: #262626 !important;
  border-bottom: 1px solid #30363d !important;
  min-height: 50px;
}

.grid-chart-card .ant-card-head-title {
  color: #f0f6fc !important;
  font-weight: 600;
  font-size: 14px;
}

.grid-chart-card .ant-card-body {
  background-color: #1f1f1f !important;
  height: calc(100% - 50px);
  padding: 0 !important;
}

/* 网格布局滚动条样式 */
.charts-grid-layout::-webkit-scrollbar {
  width: 8px;
}

.charts-grid-layout::-webkit-scrollbar-track {
  background-color: #161b22;
  border-radius: 4px;
}

.charts-grid-layout::-webkit-scrollbar-thumb {
  background-color: #30363d;
  border-radius: 4px;
}

.charts-grid-layout::-webkit-scrollbar-thumb:hover {
  background-color: #484f58;
}

/* 年份标签页样式 */
.year-tabs {
  background-color: #21262d;
}

.year-tabs .ant-tabs-nav {
  background-color: #21262d;
  margin: 0;
  padding: 0 16px;
}

.year-tabs .ant-tabs-tab {
  background-color: transparent;
  border: 1px solid #30363d;
  color: #8b949e;
  margin-right: 8px;
  border-radius: 6px 6px 0 0;
}

.year-tabs .ant-tabs-tab:hover {
  color: #f0f6fc;
  border-color: #58a6ff;
}

.year-tabs .ant-tabs-tab-active {
  background-color: #161b22;
  color: #f0f6fc;
  border-color: #30363d #30363d #161b22;
}

.year-tabs .ant-tabs-ink-bar {
  display: none;
}

.year-tabs .ant-tabs-content-holder {
  background-color: #161b22;
}

.year-tabs .ant-tabs-tabpane {
  padding: 0;
}

/* 图表卡片 */
.chart-card {
  background-color: #161b22;
  border: none;
  border-radius: 0;
}

.chart-card .ant-card-body {
  padding: 0;
}

/* 说明信息 */
.chart-info {
  margin-top: 16px;
  padding: 16px;
  background-color: #161b22;
  border-radius: 8px;
  border: 1px solid #21262d;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #8b949e;
  font-size: 12px;
}

.info-item strong {
  color: #f0f6fc;
  min-width: 80px;
}

.info-item .connected {
  color: #26a641;
}

.info-item .connecting {
  color: #f85149;
}

/* 自定义按钮样式 */
.global-toolbar .ant-btn {
  background-color: #21262d;
  border-color: #30363d;
  color: #f0f6fc;
}

.global-toolbar .ant-btn:hover,
.global-toolbar .ant-btn:focus {
  background-color: #30363d;
  border-color: #58a6ff;
  color: #58a6ff;
}

/* 时间周期选择器样式 */
.toolbar-center .ant-radio-group {
  background-color: #21262d;
  border-radius: 6px;
  padding: 2px;
}

.toolbar-center .ant-radio-button-wrapper {
  background-color: transparent;
  border-color: #30363d;
  color: #8b949e;
  font-size: 11px;
  padding: 2px 8px;
  height: 28px;
  line-height: 24px;
}

.toolbar-center .ant-radio-button-wrapper:hover {
  color: #f0f6fc;
  border-color: #58a6ff;
}

.toolbar-center .ant-radio-button-wrapper-checked {
  background-color: #58a6ff;
  border-color: #58a6ff;
  color: #ffffff;
}

.toolbar-center .ant-radio-button-wrapper-checked:hover {
  background-color: #3d8bfd;
  border-color: #3d8bfd;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .multi-year-kline-chart {
    padding: 8px;
  }
  
  .chart-title {
    font-size: 18px;
  }
}

@media (max-width: 768px) {
  .global-toolbar {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
  
  .toolbar-left {
    justify-content: center;
  }
  
  .year-tabs .ant-tabs-nav {
    padding: 0 8px;
  }
  
  .year-tabs .ant-tabs-tab {
    margin-right: 4px;
    padding: 4px 8px;
  }
  
  .chart-info {
    margin-top: 8px;
    padding: 12px;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .info-item strong {
    min-width: auto;
  }
}

/* 平滑过渡动画 */
.charts-container,
.chart-card,
.year-tabs {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 滚动条样式 */
.multi-year-kline-chart::-webkit-scrollbar {
  width: 6px;
}

.multi-year-kline-chart::-webkit-scrollbar-track {
  background-color: #161b22;
}

.multi-year-kline-chart::-webkit-scrollbar-thumb {
  background-color: #30363d;
  border-radius: 3px;
}

.multi-year-kline-chart::-webkit-scrollbar-thumb:hover {
  background-color: #484f58;
}