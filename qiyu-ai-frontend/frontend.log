
> qiyu-ai-frontend@0.1.0 start
> react-scripts start

(node:2091356) [DEP_WEBPACK_DEV_SERVER_ON_AFTER_SETUP_MIDDLEWARE] DeprecationWarning: 'onAfterSetupMiddleware' option is deprecated. Please use the 'setupMiddlewares' option.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:2091356) [DEP_WEBPACK_DEV_SERVER_ON_BEFORE_SETUP_MIDDLEWARE] DeprecationWarning: 'onBeforeSetupMiddleware' option is deprecated. Please use the 'setupMiddlewares' option.
Starting the development server...

Compiled successfully!

You can now view qiyu-ai-frontend in the browser.

  Local:            http://localhost:3000
  On Your Network:  http://********:3000

Note that the development build is not optimized.
To create a production build, use npm run build.

webpack compiled successfully
Compiling...
Compiled successfully!
webpack compiled successfully
Compiling...
Compiled successfully!
webpack compiled successfully
Compiling...
Compiled successfully!
webpack compiled successfully
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Failed to compile.

[eslint] 
src/components/Auth/AuthContainer.js
Syntax error: Unexpected token (30:3) (30:3)
WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

ERROR in ./src/components/Auth/AuthContainer.js
Module build failed (from ./node_modules/babel-loader/lib/index.js):
SyntaxError: /home/<USER>/qiyuai-web/qiyu-ai-frontend/src/components/Auth/AuthContainer.js: Unexpected token (30:3)

  28 |       console.log('📧 检测到邀请码:', ref);
  29 |
> 30 |   }, []);
     |    ^
  31 |
  32 |   // 如果用户已登录，直接跳转
  33 |   useEffect(() => {
    at constructor (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:367:19)
    at FlowParserMixin.raise (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:6627:19)
    at FlowParserMixin.unexpected (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:6647:16)
    at FlowParserMixin.parseExprAtom (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:11442:16)
    at FlowParserMixin.parseExprAtom (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:4794:20)
    at FlowParserMixin.parseExprSubscripts (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:11076:23)
    at FlowParserMixin.parseUpdate (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:11061:21)
    at FlowParserMixin.parseMaybeUnary (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:11041:23)
    at FlowParserMixin.parseMaybeUnaryOrPrivate (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:10894:61)
    at FlowParserMixin.parseExprOps (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:10899:23)
    at FlowParserMixin.parseMaybeConditional (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:10876:23)
    at FlowParserMixin.parseMaybeAssign (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:10826:21)
    at FlowParserMixin.parseMaybeAssign (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:3579:18)
    at FlowParserMixin.parseExpressionBase (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:10779:23)
    at /home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:10775:39
    at FlowParserMixin.allowInAnd (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:12422:16)
    at FlowParserMixin.parseExpression (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:10775:17)
    at FlowParserMixin.parseStatementContent (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:12895:23)
    at FlowParserMixin.parseStatementLike (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:12767:17)
    at FlowParserMixin.parseStatementLike (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:2946:24)
    at FlowParserMixin.parseStatementListItem (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:12747:17)
    at FlowParserMixin.parseBlockOrModuleBlockBody (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:13316:61)
    at FlowParserMixin.parseBlockBody (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:13309:10)
    at FlowParserMixin.parseBlock (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:13297:10)
    at FlowParserMixin.parseFunctionBody (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:12101:24)
    at /home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:2920:63
    at FlowParserMixin.forwardNoArrowParamsConversionAt (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:3096:16)
    at FlowParserMixin.parseFunctionBody (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:2920:12)
    at FlowParserMixin.parseArrowExpression (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:12076:10)
    at FlowParserMixin.parseParenAndDistinguishExpression (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:11686:12)
    at FlowParserMixin.parseParenAndDistinguishExpression (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:3621:18)
    at FlowParserMixin.parseExprAtom (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:11326:23)
    at FlowParserMixin.parseExprAtom (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:4794:20)
    at FlowParserMixin.parseExprSubscripts (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:11076:23)
    at FlowParserMixin.parseUpdate (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:11061:21)
    at FlowParserMixin.parseMaybeUnary (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:11041:23)
    at FlowParserMixin.parseMaybeUnaryOrPrivate (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:10894:61)
    at FlowParserMixin.parseExprOps (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:10899:23)
    at FlowParserMixin.parseMaybeConditional (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:10876:23)
    at FlowParserMixin.parseMaybeAssign (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:10826:21)
    at FlowParserMixin.parseMaybeAssign (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:3579:18)
    at /home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:10795:39
    at FlowParserMixin.allowInAnd (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:12427:12)
    at FlowParserMixin.parseMaybeAssignAllowIn (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:10795:17)
    at FlowParserMixin.parseMaybeAssignAllowInOrVoidPattern (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:12494:17)
    at FlowParserMixin.parseExprListItem (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:12176:18)
    at FlowParserMixin.parseCallExpressionArguments (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:11247:22)
    at FlowParserMixin.parseCoverCallAndAsyncArrowHead (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:11181:29)
    at FlowParserMixin.parseSubscript (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:11115:19)
    at FlowParserMixin.parseSubscript (/home/<USER>/qiyuai-web/qiyu-ai-frontend/node_modules/@babel/parser/lib/index.js:3680:18)

ERROR in [eslint] 
src/components/Auth/AuthContainer.js
  Line 30:3:  Parsing error: Unexpected token (30:3)

webpack compiled with 2 errors and 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/config/yearConfig.js
  Line 118:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/config/yearConfig.js
  Line 118:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/config/yearConfig.js
  Line 117:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/config/yearConfig.js
  Line 117:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

webpack compiled with 1 warning
