"""
基于腾讯云SMS的Web API视图
替代Supabase认证系统
"""

import re
import logging
from datetime import datetime, timedelta
from decimal import Decimal
from django.utils import timezone
from django.db import transaction
from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.contrib.auth.hashers import make_password, check_password

from .models import WebUser, SMSRecord, CreditTransaction, PackageConfig, CommissionConfig
from .sms_service import sms_service
from .agent_service import AgentService
from .authentication import CustomJWTAuthentication

logger = logging.getLogger(__name__)


def validate_phone_number(phone):
    """验证手机号格式"""
    if not phone:
        return False
    
    # 去除可能的前缀
    if phone.startswith('+86'):
        phone = phone[3:]
    elif phone.startswith('86'):
        phone = phone[2:]
    
    # 验证11位数字且以1开头
    return re.match(r'^1[3-9]\d{9}$', phone) is not None


def generate_tokens_for_user(user):
    """为用户生成JWT tokens"""
    refresh = RefreshToken()
    refresh['user_id'] = str(user.id)  # 确保UUID转为字符串
    refresh['phone'] = user.phone
    return {
        'refresh': str(refresh),
        'access': str(refresh.access_token),
    }


@api_view(['POST'])
@permission_classes([AllowAny])
def send_sms_code(request):
    """
    发送短信验证码
    
    POST data:
    {
        "phone": "13800138000",
        "purpose": "register"  // register, login, reset_password
    }
    """
    try:
        phone = request.data.get('phone', '').strip()
        purpose = request.data.get('purpose', 'register')
        
        # 验证参数
        if not phone:
            return Response({
                'error': '手机号不能为空'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if not validate_phone_number(phone):
            return Response({
                'error': '手机号格式不正确'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if purpose not in ['register', 'login', 'reset_password']:
            return Response({
                'error': '用途参数无效'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 去除+86前缀统一格式
        if phone.startswith('+86'):
            phone = phone[3:]
        elif phone.startswith('86'):
            phone = phone[2:]
        
        # 检查用户是否存在
        user_exists = WebUser.objects.filter(phone=phone).exists()
        
        if purpose == 'register' and user_exists:
            return Response({
                'error': '该手机号已注册，请直接登录'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if purpose in ['login', 'reset_password'] and not user_exists:
            return Response({
                'error': '该手机号未注册，请先注册'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 发送短信验证码
        success, message, code = sms_service.send_verification_code(phone)
        
        if success:
            # 记录短信发送
            expires_at = timezone.now() + timedelta(minutes=5)
            SMSRecord.objects.create(
                phone=phone,
                code=code,
                purpose=purpose,
                expires_at=expires_at
            )
            
            logger.info(f"短信验证码发送成功: {phone}, 用途: {purpose}, 验证码: {code}")
            
            # 开发模式下返回验证码
            if 'development mode' in message.lower() or '开发模式' in message:
                return Response({
                    'message': message,
                    'code': code  # 仅开发模式返回验证码
                })
            else:
                return Response({
                    'message': '验证码发送成功，5分钟内有效'
                })
        else:
            logger.error(f"短信验证码发送失败: {phone}, 错误: {message}")
            return Response({
                'error': message
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        logger.error(f"发送短信验证码异常: {e}")
        return Response({
            'error': '服务器内部错误'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([AllowAny])
def sms_register(request):
    """
    短信验证码注册
    
    POST data:
    {
        "phone": "13800138000",
        "sms_code": "123456",
        "password": "password123",
        "nickname": "用户昵称",
        "invite_code": "ABC123"  // 可选，邀请码
    }
    """
    try:
        phone = request.data.get('phone', '').strip()
        sms_code = request.data.get('sms_code', '').strip()
        password = request.data.get('password', '')
        nickname = request.data.get('nickname', '').strip()
        invite_code = request.data.get('invite_code', '').strip()
        
        # 参数验证
        if not all([phone, sms_code, password]):
            return Response({
                'error': '手机号、验证码和密码不能为空'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if not validate_phone_number(phone):
            return Response({
                'error': '手机号格式不正确'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if len(password) < 6:
            return Response({
                'error': '密码至少6位'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 去除+86前缀
        if phone.startswith('+86'):
            phone = phone[3:]
        elif phone.startswith('86'):
            phone = phone[2:]
        
        # 检查用户是否已存在
        if WebUser.objects.filter(phone=phone).exists():
            return Response({
                'error': '该手机号已注册'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 验证短信验证码
        if not sms_service.verify_code(phone, sms_code):
            return Response({
                'error': '验证码错误或已过期'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 使用事务确保数据一致性
        with transaction.atomic():
            # 创建用户
            user = WebUser.objects.create(
                phone=phone,
                nickname=nickname or f'用户{phone[-4:]}',
                phone_verified=True,
                created_at=timezone.now()
            )
            user.set_password(password)
            user.save()
            
            # 绑定代理关系
            if invite_code:
                success, message = AgentService.bind_agent_relation(user, invite_code)
                if not success:
                    logger.warning(f"绑定代理关系失败: {phone} - {message}")
                    # 注意：即使代理关系绑定失败，注册仍然继续
            
            # 记录初始积分
            CreditTransaction.objects.create(
                user=user,
                type='initial',
                amount=3,
                balance_before=0,
                balance_after=3,
                description='注册赠送积分'
            )
        
        # 标记验证码为已使用
        SMSRecord.objects.filter(
            phone=phone,
            code=sms_code,
            purpose='register',
            is_used=False
        ).update(is_used=True, used_at=timezone.now())
        
        # 生成JWT token
        tokens = generate_tokens_for_user(user)
        
        logger.info(f"用户注册成功: {phone}")
        return Response({
            'message': '注册成功',
            'user': {
                'id': str(user.id),
                'phone': user.phone,
                'nickname': user.nickname,
                'credits': user.credits,
                'invite_code': user.invite_code
            },
            'tokens': tokens
        })
        
    except Exception as e:
        logger.error(f"用户注册异常: {e}")
        return Response({
            'error': '服务器内部错误'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([AllowAny])
def sms_login(request):
    """
    短信验证码登录
    
    POST data:
    {
        "phone": "13800138000",
        "sms_code": "123456"
    }
    """
    try:
        phone = request.data.get('phone', '').strip()
        sms_code = request.data.get('sms_code', '').strip()
        
        if not phone or not sms_code:
            return Response({
                'error': '手机号和验证码不能为空'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if not validate_phone_number(phone):
            return Response({
                'error': '手机号格式不正确'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 去除+86前缀
        if phone.startswith('+86'):
            phone = phone[3:]
        elif phone.startswith('86'):
            phone = phone[2:]
        
        # 检查用户是否存在
        try:
            user = WebUser.objects.get(phone=phone)
        except WebUser.DoesNotExist:
            return Response({
                'error': '该手机号未注册'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 验证短信验证码
        if not sms_service.verify_code(phone, sms_code):
            return Response({
                'error': '验证码错误或已过期'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 更新最后登录时间
        user.update_last_login()
        
        # 标记验证码为已使用
        SMSRecord.objects.filter(
            phone=phone,
            code=sms_code,
            purpose='login',
            is_used=False
        ).update(is_used=True, used_at=timezone.now())
        
        # 生成JWT token
        tokens = generate_tokens_for_user(user)
        
        logger.info(f"用户登录成功: {phone}")
        return Response({
            'message': '登录成功',
            'user': {
                'id': str(user.id),
                'phone': user.phone,
                'nickname': user.nickname,
                'credits': user.credits,
                'invite_code': user.invite_code,
                'email': user.email
            },
            'tokens': tokens
        })
        
    except Exception as e:
        logger.error(f"用户登录异常: {e}")
        return Response({
            'error': '服务器内部错误'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([AllowAny])
def password_login(request):
    """
    密码登录
    
    POST data:
    {
        "phone": "13800138000",
        "password": "password123"
    }
    """
    try:
        phone = request.data.get('phone', '').strip()
        password = request.data.get('password', '')
        
        if not phone or not password:
            return Response({
                'error': '手机号和密码不能为空'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if not validate_phone_number(phone):
            return Response({
                'error': '手机号格式不正确'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 去除+86前缀
        if phone.startswith('+86'):
            phone = phone[3:]
        elif phone.startswith('86'):
            phone = phone[2:]
        
        # 检查用户是否存在
        try:
            user = WebUser.objects.get(phone=phone)
        except WebUser.DoesNotExist:
            return Response({
                'error': '手机号或密码错误'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        # 验证密码
        if not user.check_password(password):
            return Response({
                'error': '手机号或密码错误'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        # 更新最后登录时间
        user.update_last_login()
        
        # 生成JWT token
        tokens = generate_tokens_for_user(user)
        
        logger.info(f"用户密码登录成功: {phone}")
        return Response({
            'message': '登录成功',
            'user': {
                'id': str(user.id),
                'phone': user.phone,
                'nickname': user.nickname,
                'credits': user.credits,
                'invite_code': user.invite_code,
                'email': user.email
            },
            'tokens': tokens
        })
        
    except Exception as e:
        logger.error(f"密码登录异常: {e}")
        return Response({
            'error': '服务器内部错误'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsAuthenticated])
def user_profile(request):
    """获取用户信息"""
    try:
        user = request.user
        
        # 获取代理统计信息
        agent_stats = AgentService.get_agent_stats(user)
        
        return Response({
            'user': {
                'id': str(user.id),
                'phone': user.phone,
                'email': user.email,
                'nickname': user.nickname,
                'credits': user.credits,
                'invite_code': user.invite_code,
                'phone_verified': user.phone_verified,
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'created_at': user.created_at.isoformat(),
                'agent_stats': agent_stats
            }
        })
        
    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")
        return Response({
            'error': '服务器内部错误'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsAuthenticated])
def purchase_credits(request):
    """
    购买积分并触发代理返利
    
    POST data:
    {
        "package": "small",  // small, medium, large
        "amount": 9.99
    }
    """
    try:
        user = request.user
        package = request.data.get('package', '')
        amount = request.data.get('amount', 0)
        
        # 套餐配置
        packages = {
            'small': {'credits': 10, 'amount': 3, 'name': '3 USD/10次'},
            'medium': {'credits': 20, 'amount': 5, 'name': '5 USD/20次'},
            'large': {'credits': 50, 'amount': 9, 'name': '9 USD/50次'}
        }
        
        if package not in packages:
            return Response({
                'error': '无效的套餐类型'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        package_info = packages[package]
        
        # 模拟支付成功，直接发放积分
        with transaction.atomic():
            # 记录充值交易
            balance_before = user.credits
            user.credits += package_info['credits']
            user.save()
            
            # 记录积分交易
            credit_transaction = CreditTransaction.objects.create(
                user=user,
                type='purchase',
                amount=package_info['credits'],
                balance_before=balance_before,
                balance_after=user.credits,
                description=f'购买套餐: {package_info["name"]}'
            )
            
            # 触发代理返利
            success, message, commission_records = AgentService.calculate_and_distribute_commission(
                order_user=user,
                amount=Decimal(str(package_info['amount'])),
                order_id=f'purchase_{credit_transaction.id}'
            )
            
            if success and commission_records:
                logger.info(f"返利分发成功: {user.phone} - {len(commission_records)} 条返利记录")
            
        return Response({
            'message': '购买成功',
            'user': {
                'credits': user.credits,
                'package': package_info['name']
            },
            'commission_distributed': len(commission_records) if commission_records else 0
        })
        
    except Exception as e:
        logger.error(f"购买积分失败: {e}")
        return Response({
            'error': '服务器内部错误'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsAuthenticated])
def agent_dashboard(request):
    """获取代理仪表板数据"""
    try:
        user = request.user
        
        # 获取详细的代理统计
        agent_stats = AgentService.get_agent_stats(user)
        
        # 获取直接邀请的用户列表
        direct_invitees = []
        if hasattr(user, 'direct_invitees'):
            for invitee_relation in user.direct_invitees.select_related('user')[:10]:
                invitee_user = invitee_relation.user
                direct_invitees.append({
                    'phone': invitee_user.phone,
                    'nickname': invitee_user.nickname,
                    'joined_at': invitee_relation.created_at.strftime('%Y-%m-%d'),
                    'credits': invitee_user.credits
                })
        
        return Response({
            'invite_code': user.invite_code,
            'stats': agent_stats,
            'direct_invitees': direct_invitees,
            'invite_url': f'https://mastergo.com/register?invite={user.invite_code}'
        })
        
    except Exception as e:
        logger.error(f"获取代理仪表板失败: {e}")
        return Response({
            'error': '服务器内部错误'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsAuthenticated])
def credit_transactions(request):
    """获取用户积分交易记录"""
    try:
        user = request.user
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 10))
        
        # 获取用户的积分交易记录
        transactions = CreditTransaction.objects.filter(
            user=user
        ).order_by('-created_at')
        
        # 分页
        total = transactions.count()
        start = (page - 1) * page_size
        end = start + page_size
        page_transactions = transactions[start:end]
        
        # 格式化交易记录
        formatted_transactions = []
        for transaction in page_transactions:
            formatted_transactions.append({
                'id': transaction.id,
                'time': transaction.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'type': transaction.type,
                'type_display': transaction.get_type_display(),
                'amount': transaction.amount,
                'balance_before': transaction.balance_before,
                'balance_after': transaction.balance_after,
                'description': transaction.description or '',
            })
        
        return Response({
            'transactions': formatted_transactions,
            'pagination': {
                'current': page,
                'total': total,
                'page_size': page_size,
                'total_pages': (total + page_size - 1) // page_size
            }
        })
        
    except Exception as e:
        logger.error(f"获取积分交易记录失败: {e}")
        return Response({
            'error': '服务器内部错误'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def get_package_configs(request):
    """获取套餐配置"""
    try:
        packages = PackageConfig.objects.filter(is_active=True).order_by('sort_order', 'price')

        package_list = []
        for package in packages:
            package_list.append({
                'id': package.id,
                'name': package.name,
                'package_key': package.package_key,
                'price': float(package.price),
                'credits': package.credits,
                'description': package.description or f"{package.price_per_credit} USD/次",
                'icon': package.icon,
                'price_per_credit': package.price_per_credit
            })

        return Response({
            'packages': package_list
        })

    except Exception as e:
        logger.error(f"获取套餐配置失败: {e}")
        # 返回默认配置
        return Response({
            'packages': [
                {
                    'id': 1,
                    'name': '基础套餐',
                    'package_key': 'small',
                    'price': 3.0,
                    'credits': 10,
                    'description': '0.3 USD/次',
                    'icon': 'star',
                    'price_per_credit': 0.3
                },
                {
                    'id': 2,
                    'name': '标准套餐',
                    'package_key': 'medium',
                    'price': 5.0,
                    'credits': 20,
                    'description': '0.25 USD/次',
                    'icon': 'crown',
                    'price_per_credit': 0.25
                },
                {
                    'id': 3,
                    'name': '高级套餐',
                    'package_key': 'large',
                    'price': 9.0,
                    'credits': 50,
                    'description': '0.18 USD/次',
                    'icon': 'rocket',
                    'price_per_credit': 0.18
                }
            ]
        })


@api_view(['GET'])
@permission_classes([AllowAny])
def get_commission_configs(request):
    """获取返利配置"""
    try:
        configs = CommissionConfig.objects.filter(is_active=True)

        config_dict = {}
        for config in configs:
            config_dict[config.commission_type] = {
                'rate': float(config.commission_rate),
                'description': config.description
            }

        return Response({
            'commission_configs': config_dict
        })

    except Exception as e:
        logger.error(f"获取返利配置失败: {e}")
        # 返回默认配置
        return Response({
            'commission_configs': {
                'level1': {
                    'rate': 0.3333,
                    'description': '一级返利：直接邀请人消费时获得1/3返利'
                },
                'level2': {
                    'rate': 0.2000,
                    'description': '二级返利：间接邀请人消费时获得1/5返利'
                }
            }
        })