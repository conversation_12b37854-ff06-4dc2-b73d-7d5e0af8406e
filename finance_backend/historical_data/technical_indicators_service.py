"""
🧮 技术指标计算服务 - AI分析的数据引擎

🎯 核心功能：
- 计算MACD指标（趋势分析）
- 计算KDJ指标（超买超卖分析）  
- 计算RSI指标（相对强弱分析）
- 计算布林带等其他技术指标

🔄 在数据流程链路中的位置：
TimestampAnalysisService → [TechnicalIndicatorService] → 技术指标数据

📊 计算引擎：
- 优先使用pandas-ta库（高精度、高性能）
- 备用方案：自定义算法实现
- 支持多种时间周期的指标计算

🎯 输出数据：格式化的技术指标字典，直接用于AI分析
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from decimal import Decimal
import logging

# 尝试导入pandas-ta，如果没有安装则使用备用实现
try:
    import pandas_ta as ta
    PANDAS_TA_AVAILABLE = True
except ImportError:
    PANDAS_TA_AVAILABLE = False
    import warnings
    warnings.warn("pandas-ta未安装，将使用基础实现。建议运行: pip install pandas-ta")

logger = logging.getLogger(__name__)


class TechnicalIndicatorService:
    """
    🧮 技术指标计算服务 - 链路中的计算引擎
    
    🔧 主要方法：
    - prepare_dataframe(): 准备K线数据格式
    - calculate_all_indicators(): 计算所有技术指标
    - calculate_macd(): MACD指标计算
    - calculate_kdj(): KDJ指标计算  
    - calculate_rsi(): RSI指标计算
    
    📊 计算流程：
    K线数据 → 数据标准化 → 指标计算 → 格式化输出 → AI分析
    """
    
    def __init__(self):
        self.pandas_ta_available = PANDAS_TA_AVAILABLE
        if not self.pandas_ta_available:
            logger.warning("⚠️ pandas-ta未安装，将使用基础实现")
    
    def prepare_dataframe(self, kline_data: List[Dict]) -> pd.DataFrame:
        """
        准备DataFrame用于技术指标计算
        
        参数:
        - kline_data: K线数据列表
        
        返回:
        - 标准化的DataFrame
        """
        if not kline_data:
            return pd.DataFrame()
        
        # 转换为DataFrame
        df = pd.DataFrame(kline_data)
        
        # 确保列名标准化
        column_mapping = {
            'open_time': 'timestamp',
            'open_price': 'open',
            'high_price': 'high', 
            'low_price': 'low',
            'close_price': 'close',
            'volume': 'volume'
        }
        
        # 重命名列
        for old_col, new_col in column_mapping.items():
            if old_col in df.columns:
                df = df.rename(columns={old_col: new_col})
        
        # 确保必需的列存在
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        for col in required_cols:
            if col not in df.columns:
                logger.error(f"缺少必需列: {col}")
                return pd.DataFrame()
        
        # 转换数据类型
        for col in required_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 处理时间戳
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df = df.set_index('timestamp')
        
        # 去除无效数据
        df = df.dropna()
        
        # 按时间排序
        if not df.index.is_monotonic_increasing:
            df = df.sort_index()
        
        return df
    
    def calculate_all_indicators(self, df: pd.DataFrame) -> Dict:
        """
        计算所有技术指标
        
        参数:
        - df: 包含OHLCV数据的DataFrame
        
        返回:
        - 指标字典
        """
        if df.empty or len(df) < 26:
            logger.warning(f"数据不足（{len(df)}条），无法计算技术指标，至少需要26条数据用于MACD计算")
            return {}
        
        try:
            if self.pandas_ta_available:
                return self._calculate_with_pandas_ta(df)
            else:
                return self._calculate_with_basic_methods(df)
        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
            return {}
    
    def _calculate_with_pandas_ta(self, df: pd.DataFrame) -> Dict:
        """使用pandas-ta计算指标"""
        
        # 计算MACD
        macd_data = ta.macd(df['close'])
        
        # 计算RSI  
        rsi = ta.rsi(df['close'])
        
        # 计算随机指标(KDJ类似)
        stoch = ta.stoch(df['high'], df['low'], df['close'])
        
        # 计算移动平均线
        sma_5 = ta.sma(df['close'], length=5)
        sma_10 = ta.sma(df['close'], length=10)
        sma_20 = ta.sma(df['close'], length=20)
        sma_50 = ta.sma(df['close'], length=50)
        
        # 计算EMA
        ema_12 = ta.ema(df['close'], length=12)
        ema_26 = ta.ema(df['close'], length=26)
        
        # 计算布林带
        bbands = ta.bbands(df['close'])
        
        # 计算威廉指标
        willr = ta.willr(df['high'], df['low'], df['close'])
        
        # 计算成交量指标
        volume_sma = ta.sma(df['volume'], length=20)
        
        # 计算ATR (平均真实波幅)
        atr = ta.atr(df['high'], df['low'], df['close'])
        
        # 计算CCI (商品通道指数)
        cci = ta.cci(df['high'], df['low'], df['close'])
        
        # 提取最新值
        latest_idx = -1
        
        indicators = {
            # MACD指标
            'macd_line': float(macd_data.iloc[latest_idx, 0]) if not macd_data.empty else 0.0,
            'macd_histogram': float(macd_data.iloc[latest_idx, 1]) if not macd_data.empty else 0.0,
            'macd_signal': float(macd_data.iloc[latest_idx, 2]) if not macd_data.empty else 0.0,
            
            # RSI指标
            'rsi': float(rsi.iloc[latest_idx]) if not rsi.isna().iloc[latest_idx] else 50.0,
            
            # 随机指标(KDJ)
            'stoch_k': float(stoch.iloc[latest_idx, 0]) if not stoch.empty else 50.0,
            'stoch_d': float(stoch.iloc[latest_idx, 1]) if not stoch.empty else 50.0,
            'stoch_j': float(3 * stoch.iloc[latest_idx, 0] - 2 * stoch.iloc[latest_idx, 1]) if not stoch.empty else 50.0,
            
            # 移动平均线
            'sma_5': float(sma_5.iloc[latest_idx]) if not sma_5.isna().iloc[latest_idx] else float(df['close'].iloc[latest_idx]),
            'sma_10': float(sma_10.iloc[latest_idx]) if not sma_10.isna().iloc[latest_idx] else float(df['close'].iloc[latest_idx]),
            'sma_20': float(sma_20.iloc[latest_idx]) if not sma_20.isna().iloc[latest_idx] else float(df['close'].iloc[latest_idx]),
            'sma_50': float(sma_50.iloc[latest_idx]) if not sma_50.isna().iloc[latest_idx] else float(df['close'].iloc[latest_idx]),
            
            # EMA
            'ema_12': float(ema_12.iloc[latest_idx]) if not ema_12.isna().iloc[latest_idx] else float(df['close'].iloc[latest_idx]),
            'ema_26': float(ema_26.iloc[latest_idx]) if not ema_26.isna().iloc[latest_idx] else float(df['close'].iloc[latest_idx]),
            
            # 布林带
            'bb_lower': float(bbands.iloc[latest_idx, 0]) if not bbands.empty else float(df['close'].iloc[latest_idx]),
            'bb_middle': float(bbands.iloc[latest_idx, 1]) if not bbands.empty else float(df['close'].iloc[latest_idx]),
            'bb_upper': float(bbands.iloc[latest_idx, 2]) if not bbands.empty else float(df['close'].iloc[latest_idx]),
            'bb_width': float(bbands.iloc[latest_idx, 3]) if not bbands.empty else 0.0,
            'bb_percent': float(bbands.iloc[latest_idx, 4]) if not bbands.empty else 0.5,
            
            # 威廉指标
            'williams_r': float(willr.iloc[latest_idx]) if not willr.isna().iloc[latest_idx] else -50.0,
            
            # 成交量指标
            'volume_sma': float(volume_sma.iloc[latest_idx]) if not volume_sma.isna().iloc[latest_idx] else float(df['volume'].iloc[latest_idx]),
            'volume_ratio': float(df['volume'].iloc[latest_idx] / volume_sma.iloc[latest_idx]) if not volume_sma.isna().iloc[latest_idx] and volume_sma.iloc[latest_idx] != 0 else 1.0,
            
            # 其他指标
            'atr': float(atr.iloc[latest_idx]) if not atr.isna().iloc[latest_idx] else 0.0,
            'cci': float(cci.iloc[latest_idx]) if not cci.isna().iloc[latest_idx] else 0.0,
            
            # 价格信息
            'current_price': float(df['close'].iloc[latest_idx]),
            'current_volume': float(df['volume'].iloc[latest_idx]),
            'price_change_24h': float(df['close'].iloc[latest_idx] - df['close'].iloc[-96]) if len(df) >= 96 else 0.0,
            'price_change_pct': float((df['close'].iloc[latest_idx] - df['close'].iloc[-96]) / df['close'].iloc[-96] * 100) if len(df) >= 96 and df['close'].iloc[-96] != 0 else 0.0,
        }
        
        # 计算布林带位置
        if indicators['bb_upper'] != indicators['bb_lower']:
            indicators['bb_position'] = (indicators['current_price'] - indicators['bb_lower']) / (indicators['bb_upper'] - indicators['bb_lower'])
        else:
            indicators['bb_position'] = 0.5
        
        return indicators
    
    def _calculate_with_basic_methods(self, df: pd.DataFrame) -> Dict:
        """备用：基础方法计算指标"""
        logger.info("使用基础方法计算技术指标")
        
        # 基础实现（简化版）
        closes = df['close'].values
        highs = df['high'].values
        lows = df['low'].values
        volumes = df['volume'].values
        
        # 简单移动平均
        sma_5 = np.mean(closes[-5:]) if len(closes) >= 5 else closes[-1]
        sma_10 = np.mean(closes[-10:]) if len(closes) >= 10 else closes[-1]
        sma_20 = np.mean(closes[-20:]) if len(closes) >= 20 else closes[-1]
        
        # 简单RSI计算
        if len(closes) >= 15:
            deltas = np.diff(closes[-15:])
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)
            avg_gain = np.mean(gains)
            avg_loss = np.mean(losses)
            
            if avg_loss == 0:
                rsi = 100.0
            else:
                rs = avg_gain / avg_loss
                rsi = 100.0 - (100.0 / (1.0 + rs))
        else:
            rsi = 50.0
        
        return {
            'sma_5': float(sma_5),
            'sma_10': float(sma_10),
            'sma_20': float(sma_20),
            'rsi': float(rsi),
            'current_price': float(closes[-1]),
            'current_volume': float(volumes[-1]),
            'macd_line': 0.0,
            'macd_signal': 0.0,
            'macd_histogram': 0.0,
            'stoch_k': 50.0,
            'stoch_d': 50.0,
            'stoch_j': 50.0,
            'bb_upper': float(closes[-1] * 1.02),
            'bb_middle': float(closes[-1]),
            'bb_lower': float(closes[-1] * 0.98),
            'bb_position': 0.5,
            'williams_r': -50.0,
            'volume_sma': float(np.mean(volumes)),
            'volume_ratio': 1.0,
        }
    
    def get_chart_indicators(self, df: pd.DataFrame, indicators: List[str] = None) -> Dict:
        """
        获取用于图表显示的指标时间序列
        
        参数:
        - df: K线数据
        - indicators: 需要的指标列表
        
        返回:
        - 包含时间序列的指标数据
        """
        if not self.pandas_ta_available:
            logger.warning("pandas-ta未安装，无法提供图表指标时间序列")
            return {}
        
        if df.empty:
            return {}
        
        default_indicators = ['macd', 'rsi', 'bbands', 'sma_5', 'sma_10', 'sma_20']
        if indicators is None:
            indicators = default_indicators
        
        chart_data = {}
        
        try:
            # 计算MACD时间序列
            if 'macd' in indicators:
                macd_data = ta.macd(df['close'])
                if not macd_data.empty:
                    chart_data['macd'] = {
                        'timestamps': macd_data.index.strftime('%Y-%m-%d %H:%M:%S').tolist(),
                        'macd_line': macd_data.iloc[:, 0].fillna(0).tolist(),
                        'macd_histogram': macd_data.iloc[:, 1].fillna(0).tolist(),
                        'macd_signal': macd_data.iloc[:, 2].fillna(0).tolist(),
                    }
            
            # 计算RSI时间序列
            if 'rsi' in indicators:
                rsi_data = ta.rsi(df['close'])
                chart_data['rsi'] = {
                    'timestamps': rsi_data.index.strftime('%Y-%m-%d %H:%M:%S').tolist(),
                    'values': rsi_data.fillna(50).tolist()
                }
            
            # 计算布林带时间序列
            if 'bbands' in indicators:
                bbands_data = ta.bbands(df['close'])
                if not bbands_data.empty:
                    chart_data['bbands'] = {
                        'timestamps': bbands_data.index.strftime('%Y-%m-%d %H:%M:%S').tolist(),
                        'lower': bbands_data.iloc[:, 0].fillna(method='ffill').tolist(),
                        'middle': bbands_data.iloc[:, 1].fillna(method='ffill').tolist(),
                        'upper': bbands_data.iloc[:, 2].fillna(method='ffill').tolist(),
                    }
            
            # 计算移动平均线
            for period in [5, 10, 20, 50]:
                indicator_name = f'sma_{period}'
                if indicator_name in indicators:
                    sma_data = ta.sma(df['close'], length=period)
                    chart_data[indicator_name] = {
                        'timestamps': sma_data.index.strftime('%Y-%m-%d %H:%M:%S').tolist(),
                        'values': sma_data.fillna(method='ffill').tolist()
                    }
            
            return chart_data
            
        except Exception as e:
            logger.error(f"计算图表指标失败: {e}")
            return {}


def install_pandas_ta():
    """安装pandas-ta的辅助函数"""
    try:
        import subprocess
        import sys
        
        print("📦 正在安装 pandas-ta...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pandas-ta'])
        print("✅ pandas-ta 安装完成")
        
        # 重新导入
        global ta, PANDAS_TA_AVAILABLE
        import pandas_ta as ta
        PANDAS_TA_AVAILABLE = True
        
        return True
    except Exception as e:
        print(f"❌ 安装 pandas-ta 失败: {e}")
        return False


# 使用示例和测试
if __name__ == "__main__":
    # 测试技术指标服务
    service = TechnicalIndicatorService()
    
    if not service.pandas_ta_available:
        print("📦 检测到pandas-ta未安装，是否现在安装？(y/n)")
        choice = input().lower()
        if choice == 'y':
            install_pandas_ta()
    
    print("✅ 技术指标服务已准备就绪")