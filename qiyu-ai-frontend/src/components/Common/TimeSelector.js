import React, { useState } from 'react';
import Button from './Button';
import './TimeSelector.css';

const TimeSelector = ({
  onTimeChange,
  defaultValue = '15分钟',
  className = '',
  options = null // 允许自定义选项
}) => {
  const [activeTime, setActiveTime] = useState(defaultValue);

  // 默认时间周期选项（更具体的时间周期）
  const defaultTimeOptions = [
    { label: '1分钟', value: '1m' },
    { label: '15分钟', value: '15m' },
    { label: '1小时', value: '1h' },
    { label: '4小时', value: '4h' },
    { label: '1天', value: '1d' },
    { label: '1月', value: '1mo' }
  ];

  // 使用传入的选项或默认选项
  const timeOptions = options || defaultTimeOptions;

  const handleTimeClick = (option) => {
    setActiveTime(option.label);
    onTimeChange && onTimeChange(option);
  };

  return (
    <div className={`time-selector ${className}`}>
      {timeOptions.map((option) => (
        <Button
          key={option.value}
          variant={activeTime === option.label ? 'primary' : 'ghost'}
          size="small"
          onClick={() => handleTimeClick(option)}
          className="time-btn"
        >
          {option.label}
        </Button>
      ))}
    </div>
  );
};

export default TimeSelector;
