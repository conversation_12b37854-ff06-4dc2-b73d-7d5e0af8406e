import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import Login from './Login';
import Register from './Register';

const AuthContainer = ({ onAuthSuccess }) => {
  const [isLogin, setIsLogin] = useState(true);
  const [inviteCode, setInviteCode] = useState('');
  const { user, loading } = useAuth();

  // 检查 URL 参数来决定显示登录还是注册，并获取邀请码
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const mode = urlParams.get('mode');
    let ref = urlParams.get('ref');
    
    if (mode === 'register') {
      setIsLogin(false);
    }
    
    // 优先从URL获取ref，如果不存在，则尝试从localStorage获取
    if (!ref) {
      ref = localStorage.getItem('referral_code');
    }

    if (ref) {
      setInviteCode(ref);
      console.log('📧 检测到邀请码:', ref);
    }
  }, []);

  // 如果用户已登录，直接跳转
  useEffect(() => {
    if (user && !loading) {
      onAuthSuccess && onAuthSuccess(user);
    }
  }, [user, loading, onAuthSuccess]);

  const handleSwitchToRegister = () => {
    setIsLogin(false);
    // 更新 URL 参数
    const url = new URL(window.location);
    url.searchParams.set('mode', 'register');
    window.history.replaceState({}, '', url);
  };

  const handleSwitchToLogin = () => {
    setIsLogin(true);
    // 移除 URL 参数
    const url = new URL(window.location);
    url.searchParams.delete('mode');
    window.history.replaceState({}, '', url);
  };

  const handleAuthSuccess = (user) => {
    // 认证成功后的处理
    onAuthSuccess && onAuthSuccess(user);
  };

  if (loading) {
    return (
      <div className="auth-container">
        <div className="auth-form-container">
          <div className="auth-form">
            <div style={{ textAlign: 'center', color: 'white', padding: '40px' }}>
              加载中...
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {isLogin ? (
        <Login
          onSwitchToRegister={handleSwitchToRegister}
          onLoginSuccess={handleAuthSuccess}
        />
      ) : (
        <Register
          onSwitchToLogin={handleSwitchToLogin}
          onRegisterSuccess={handleAuthSuccess}
          inviteCode={inviteCode}
        />
      )}
    </>
  );
};

export default AuthContainer;
