import React, { useState, useEffect, useCallback } from 'react';
import { Card, Row, Col, Badge, Progress, Button, Tooltip, Alert, Typography, Divider, Tag } from 'antd';
import { 
  RobotOutlined, 
  RiseOutlined, 
  FallOutlined, 
  PauseCircleOutlined,
  PlayCircleOutlined,
  ReloadOutlined,
  ClockCircleOutlined,
  WarningOutlined
} from '@ant-design/icons';
import autoAIAnalysisService from '../../services/AutoAIAnalysisService';



const { Text, Title } = Typography;

const AIPredictionModule = ({ symbols = ['BTC', 'ETH', 'SOL', 'XRP', 'DOGE'] }) => {
  const [analysisResults, setAnalysisResults] = useState({});
  const [analysisStatus, setAnalysisStatus] = useState({});
  const [isServiceRunning, setIsServiceRunning] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(null);
  const [errors, setErrors] = useState([]);

  // 处理服务事件
  const handleServiceEvent = useCallback((event) => {
    switch (event.type) {
      case 'service_started':
        setIsServiceRunning(true);
        setErrors([]);
        break;
        
      case 'service_stopped':
        setIsServiceRunning(false);
        break;
        
      case 'analysis_started':
        setAnalysisStatus(prev => ({ ...prev, isAnalyzing: true }));
        break;
        
      case 'analysis_progress':
        setAnalysisStatus(prev => ({ 
          ...prev, 
          currentSymbol: event.data.symbol,
          progress: event.data.progress 
        }));
        break;
        
      case 'analysis_completed':
        setAnalysisResults(event.data.results);
        setAnalysisStatus(prev => ({ ...prev, isAnalyzing: false, progress: 100 }));
        setLastUpdate(new Date());
        setErrors(event.data.errors || []);
        break;
        
      case 'results_update':
        setAnalysisResults(event.data);
        setAnalysisStatus(event.status);
        break;
        
      default:
        break;
    }
  }, []);

  // 订阅服务事件
  useEffect(() => {
    const unsubscribe = autoAIAnalysisService.subscribe(handleServiceEvent);

    // 获取当前状态
    setAnalysisResults(autoAIAnalysisService.getLatestResults());
    setAnalysisStatus(autoAIAnalysisService.getStatus());
    setIsServiceRunning(autoAIAnalysisService.isRunning);

    return unsubscribe;
  }, [handleServiceEvent]);

  // 启动服务
  const startService = () => {
    autoAIAnalysisService.start(symbols);
  };

  // 停止服务
  const stopService = () => {
    autoAIAnalysisService.stop();
  };

  // 手动触发分析
  const triggerAnalysis = () => {
    autoAIAnalysisService.triggerAnalysis(symbols);
  };

  // 获取情绪图标和颜色
  const getSentimentDisplay = (sentiment) => {
    switch (sentiment) {
      case 'bullish':
        return { icon: <RiseOutlined />, color: '#52c41a', text: '看多' };
      case 'bearish':
        return { icon: <FallOutlined />, color: '#ff4d4f', text: '看空' };
      default:
        return { icon: <PauseCircleOutlined />, color: '#faad14', text: '中性' };
    }
  };

  // 获取建议标签
  const getRecommendationTag = (recommendation) => {
    const configs = {
      buy: { color: 'green', text: '买入' },
      sell: { color: 'red', text: '卖出' },
      hold: { color: 'blue', text: '持有' }
    };
    return configs[recommendation] || configs.hold;
  };

  // 渲染单个币种的分析结果
  const renderSymbolAnalysis = (symbol, result) => {
    if (!result) {
      return (
        <Card size="small" style={{ marginBottom: '8px', opacity: 0.6 }}>
          <div style={{ textAlign: 'center', color: '#8c8c8c' }}>
            <Text>暂无数据</Text>
          </div>
        </Card>
      );
    }

    if (result.status === 'error') {
      return (
        <Card size="small" style={{ marginBottom: '8px', borderColor: '#ff4d4f' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <WarningOutlined style={{ color: '#ff4d4f' }} />
            <div>
              <Text strong>{symbol}</Text>
              <div style={{ fontSize: '12px', color: '#ff4d4f' }}>
                分析失败: {result.error}
              </div>
            </div>
          </div>
        </Card>
      );
    }

    const sentimentDisplay = getSentimentDisplay(result.sentiment);
    const recommendationTag = getRecommendationTag(result.recommendation);
    
    return (
      <Card 
        size="small" 
        style={{ 
          marginBottom: '8px',
          borderColor: sentimentDisplay.color,
          borderWidth: '2px'
        }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <div style={{ flex: 1 }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '4px' }}>
              <Text strong style={{ fontSize: '16px' }}>{symbol}</Text>
              <div style={{ display: 'flex', alignItems: 'center', gap: '4px', color: sentimentDisplay.color }}>
                {sentimentDisplay.icon}
                <Text style={{ color: sentimentDisplay.color, fontWeight: 'bold' }}>
                  {sentimentDisplay.text}
                </Text>
              </div>
            </div>
            
            <div style={{ display: 'flex', gap: '8px', marginBottom: '8px' }}>
              <Tag color={recommendationTag.color} size="small">
                {recommendationTag.text}
              </Tag>
              <Tag color="blue" size="small">
                置信度: {Math.round(result.confidence * 100)}%
              </Tag>
            </div>
            
            {result.targetPrice && (
              <div style={{ fontSize: '12px', color: '#666' }}>
                目标价格: ${result.targetPrice.toLocaleString()}
              </div>
            )}
            
            {result.keyPoints && result.keyPoints.length > 0 && (
              <Tooltip title={result.keyPoints.join(' | ')}>
                <div style={{ 
                  fontSize: '12px', 
                  color: '#666', 
                  marginTop: '4px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}>
                  {result.keyPoints[0]}
                </div>
              </Tooltip>
            )}
          </div>
          
          <div style={{ textAlign: 'right', fontSize: '10px', color: '#999' }}>
            {new Date(result.timestamp).toLocaleTimeString()}
          </div>
        </div>
      </Card>
    );
  };

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <RobotOutlined style={{ color: '#1890ff' }} />
            <span>AI智能预测</span>
            <Badge 
              status={isServiceRunning ? 'processing' : 'default'} 
              text={isServiceRunning ? '运行中' : '已停止'} 
            />
          </div>
          
          <div style={{ display: 'flex', gap: '8px' }}>
            <Tooltip title="手动刷新">
              <Button 
                size="small" 
                icon={<ReloadOutlined />} 
                onClick={triggerAnalysis}
                loading={analysisStatus.isAnalyzing}
              />
            </Tooltip>
            
            <Button
              size="small"
              type={isServiceRunning ? 'default' : 'primary'}
              icon={isServiceRunning ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={isServiceRunning ? stopService : startService}
            >
              {isServiceRunning ? '停止' : '启动'}
            </Button>
          </div>
        </div>
      }
      size="small"
      style={{ height: '100%' }}
    >
      {/* 分析进度 */}
      {analysisStatus.isAnalyzing && (
        <div style={{ marginBottom: '16px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
            <ClockCircleOutlined style={{ color: '#1890ff' }} />
            <Text>正在分析 {analysisStatus.currentSymbol}...</Text>
          </div>
          <Progress 
            percent={analysisStatus.progress} 
            size="small" 
            status="active"
            format={percent => `${percent}%`}
          />
        </div>
      )}

      {/* 错误提示 */}
      {errors.length > 0 && (
        <Alert
          message={`${errors.length} 个币种分析失败`}
          type="warning"
          size="small"
          style={{ marginBottom: '16px' }}
          showIcon
        />
      )}

      {/* 最后更新时间 */}
      {lastUpdate && (
        <div style={{ textAlign: 'center', marginBottom: '16px' }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            最后更新: {lastUpdate.toLocaleString()}
          </Text>
        </div>
      )}

      {/* 分析结果列表 */}
      <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
        {symbols.map(symbol => (
          <div key={symbol}>
            {renderSymbolAnalysis(symbol, analysisResults[symbol])}
          </div>
        ))}
      </div>

      {/* 服务状态说明 */}
      <Divider style={{ margin: '16px 0 8px 0' }} />
      <div style={{ textAlign: 'center' }}>
        <Text type="secondary" style={{ fontSize: '11px' }}>
          {isServiceRunning ? (
            <>
              <ClockCircleOutlined style={{ marginRight: '4px' }} />
              每15分钟自动分析 | 下次更新: {
                lastUpdate ? 
                new Date(lastUpdate.getTime() + 15 * 60 * 1000).toLocaleTimeString() : 
                '计算中...'
              }
            </>
          ) : (
            '点击启动按钮开始自动分析'
          )}
        </Text>
      </div>
    </Card>
  );
};

export default AIPredictionModule;
