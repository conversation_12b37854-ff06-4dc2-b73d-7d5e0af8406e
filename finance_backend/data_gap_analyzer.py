#!/usr/bin/env python3
"""
数据断层分析和修复工具
"""

import os
import sys
import logging
from datetime import datetime, timedelta
from pathlib import Path

# Django环境设置
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'finance_project.settings')
import django
django.setup()

from ai_analysis.models import PredictionRecord, AutoPredictionTask
from django.utils import timezone
from django.db.models import Count, Min, Max
import json

class DataGapAnalyzer:
    """数据断层分析器"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        
    def _setup_logger(self):
        logger = logging.getLogger('GapAnalyzer')
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
        return logger
    
    def analyze_prediction_gaps(self, hours_back: int = 24) -> dict:
        """分析AI预测数据断层"""
        cutoff_time = timezone.now() - timedelta(hours=hours_back)
        gaps = {}
        
        print(f"🔍 分析最近 {hours_back} 小时的AI预测数据断层...")
        print("=" * 60)
        
        # 获取所有活跃任务
        active_tasks = AutoPredictionTask.objects.filter(is_active=True)
        
        for task in active_tasks:
            symbol = task.symbol
            print(f"\n📊 分析 {symbol}:")
            
            # 获取该币种的预测记录
            records = PredictionRecord.objects.filter(
                symbol=symbol,
                created_at__gte=cutoff_time,
                prediction_status__in=['completed', 'failed']
            ).order_by('created_at')
            
            if not records.exists():
                gaps[symbol] = {
                    'type': 'no_data',
                    'message': f'最近{hours_back}小时无预测记录',
                    'severity': 'high',
                    'last_prediction': None,
                    'expected_count': hours_back * 4,  # 每15分钟一次
                    'actual_count': 0
                }
                print(f"  ❌ 无预测记录 (最近{hours_back}小时)")
                continue
            
            # 分析时间间隔
            record_times = []
            actual_count = 0
            failed_count = 0
            
            for record in records:
                record_times.append(record.created_at)
                actual_count += 1
                if record.prediction_status == 'failed':
                    failed_count += 1
            
            # 计算期望的预测次数 (每15分钟一次)
            expected_count = hours_back * 4
            missing_count = max(0, expected_count - actual_count)
            
            # 检查时间间隔
            large_gaps = []
            if len(record_times) > 1:
                for i in range(1, len(record_times)):
                    gap_minutes = (record_times[i] - record_times[i-1]).total_seconds() / 60
                    if gap_minutes > 20:  # 超过20分钟认为是断层
                        large_gaps.append({
                            'start': record_times[i-1],
                            'end': record_times[i],
                            'gap_minutes': gap_minutes
                        })
            
            # 检查最近的预测时间
            latest_record = records.last()
            latest_gap_minutes = (timezone.now() - latest_record.created_at).total_seconds() / 60
            
            # 生成报告
            status = "✅ 正常"
            severity = "low"
            
            if missing_count > expected_count * 0.2:  # 缺失超过20%
                status = "⚠️ 有缺失"
                severity = "medium"
            if missing_count > expected_count * 0.5:  # 缺失超过50%
                status = "❌ 严重缺失"
                severity = "high"
            if latest_gap_minutes > 30:  # 最近30分钟无预测
                status = "🔴 停止预测"
                severity = "critical"
            
            print(f"  状态: {status}")
            print(f"  期望次数: {expected_count}, 实际次数: {actual_count}, 缺失: {missing_count}")
            print(f"  失败次数: {failed_count}")
            print(f"  最近预测: {latest_record.created_at} ({latest_gap_minutes:.1f}分钟前)")
            print(f"  大断层数: {len(large_gaps)}")
            
            if large_gaps:
                print("  断层详情:")
                for gap in large_gaps[:3]:  # 只显示前3个
                    print(f"    {gap['start']} → {gap['end']} ({gap['gap_minutes']:.1f}分钟)")
            
            gaps[symbol] = {
                'type': 'analysis',
                'status': status,
                'severity': severity,
                'expected_count': expected_count,
                'actual_count': actual_count,
                'missing_count': missing_count,
                'failed_count': failed_count,
                'latest_prediction': latest_record.created_at,
                'latest_gap_minutes': latest_gap_minutes,
                'large_gaps': large_gaps,
                'success_rate': round((actual_count - failed_count) / actual_count * 100, 1) if actual_count > 0 else 0
            }
        
        return gaps
    
    def analyze_stuck_tasks(self) -> dict:
        """分析卡住的任务"""
        print(f"\n🔒 分析卡住的任务...")
        
        # 查找处理中时间过长的任务
        stuck_cutoff = timezone.now() - timedelta(hours=2)
        stuck_tasks = PredictionRecord.objects.filter(
            prediction_status='processing',
            created_at__lt=stuck_cutoff
        )
        
        if stuck_tasks.exists():
            print(f"  ❌ 发现 {stuck_tasks.count()} 个卡住的任务:")
            for task in stuck_tasks:
                age_hours = (timezone.now() - task.created_at).total_seconds() / 3600
                print(f"    {task.symbol}: 卡住 {age_hours:.1f} 小时 (PID: {task.id})")
            
            return {
                'has_stuck_tasks': True,
                'stuck_count': stuck_tasks.count(),
                'tasks': [
                    {
                        'symbol': task.symbol,
                        'created_at': task.created_at,
                        'age_hours': (timezone.now() - task.created_at).total_seconds() / 3600
                    }
                    for task in stuck_tasks
                ]
            }
        else:
            print("  ✅ 无卡住的任务")
            return {'has_stuck_tasks': False}
    
    def repair_data_gaps(self, gaps: dict, auto_fix: bool = True) -> dict:
        """修复数据断层"""
        print(f"\n🔧 数据断层修复...")
        repairs = {}
        
        if not auto_fix:
            print("  ⚠️ 自动修复已禁用，仅分析模式")
            return repairs
        
        # 1. 清理卡住的任务
        stuck_count = PredictionRecord.objects.filter(
            prediction_status='processing',
            created_at__lt=timezone.now() - timedelta(hours=1)
        ).update(prediction_status='failed')
        
        if stuck_count > 0:
            print(f"  ✅ 清理了 {stuck_count} 个卡住的任务")
            repairs['stuck_tasks'] = {
                'action': 'cleared',
                'count': stuck_count,
                'success': True
            }
        
        # 2. 重置异常的任务状态
        for symbol, gap_info in gaps.items():
            if gap_info.get('severity') == 'critical':
                # 对于严重问题，重置任务的next_run_at
                try:
                    task = AutoPredictionTask.objects.get(symbol=symbol)
                    task.next_run_at = timezone.now()
                    task.save()
                    
                    print(f"  ✅ 重置 {symbol} 任务调度")
                    repairs[symbol] = {
                        'action': 'reset_schedule',
                        'success': True
                    }
                except AutoPredictionTask.DoesNotExist:
                    print(f"  ❌ 找不到 {symbol} 的任务配置")
        
        # 3. 触发一次预测任务（如果有严重问题）
        critical_symbols = [
            symbol for symbol, info in gaps.items() 
            if info.get('severity') == 'critical'
        ]
        
        if critical_symbols:
            print(f"  🚀 建议手动触发预测: {', '.join(critical_symbols)}")
            repairs['manual_trigger'] = {
                'symbols': critical_symbols,
                'command': 'python3 manage.py cron_predictions --force'
            }
        
        return repairs
    
    def generate_report(self, gaps: dict, stuck_info: dict, repairs: dict) -> dict:
        """生成完整报告"""
        report = {
            'timestamp': timezone.now().isoformat(),
            'summary': {
                'total_symbols': len(gaps),
                'normal_symbols': len([s for s, info in gaps.items() if info.get('severity') == 'low']),
                'warning_symbols': len([s for s, info in gaps.items() if info.get('severity') == 'medium']),
                'critical_symbols': len([s for s, info in gaps.items() if info.get('severity') in ['high', 'critical']]),
                'has_stuck_tasks': stuck_info.get('has_stuck_tasks', False),
                'stuck_count': stuck_info.get('stuck_count', 0)
            },
            'gaps': gaps,
            'stuck_tasks': stuck_info,
            'repairs': repairs
        }
        
        # 保存报告
        report_file = Path('logs/data_gap_report.json')
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, default=str, ensure_ascii=False)
        
        print(f"\n📋 报告已保存到: {report_file}")
        return report


def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='数据断层分析和修复工具')
    parser.add_argument('--hours', type=int, default=24, help='分析最近N小时的数据')
    parser.add_argument('--no-repair', action='store_true', help='仅分析，不执行修复')
    parser.add_argument('--report-only', action='store_true', help='仅生成报告')
    
    args = parser.parse_args()
    
    analyzer = DataGapAnalyzer()
    
    print("🩺 数据断层诊断系统")
    print("=" * 60)
    
    # 1. 分析预测断层
    gaps = analyzer.analyze_prediction_gaps(args.hours)
    
    # 2. 分析卡住的任务
    stuck_info = analyzer.analyze_stuck_tasks()
    
    # 3. 执行修复（如果启用）
    repairs = {}
    if not args.no_repair and not args.report_only:
        repairs = analyzer.repair_data_gaps(gaps, auto_fix=True)
    
    # 4. 生成报告
    report = analyzer.generate_report(gaps, stuck_info, repairs)
    
    # 5. 输出摘要
    summary = report['summary']
    print(f"\n📊 诊断摘要:")
    print(f"  总币种数: {summary['total_symbols']}")
    print(f"  正常: {summary['normal_symbols']} | 警告: {summary['warning_symbols']} | 严重: {summary['critical_symbols']}")
    print(f"  卡住任务: {summary['stuck_count']}")
    
    if repairs:
        print(f"  执行修复: {len(repairs)} 项")
    
    print("\n" + "=" * 60)
    print("✅ 诊断完成")


if __name__ == "__main__":
    main()