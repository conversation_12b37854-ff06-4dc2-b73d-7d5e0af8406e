import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import reportWebVitals from './reportWebVitals';

// 初始化全局调试助手（仅在开发环境）
if (process.env.NODE_ENV === 'development') {
  import('./utils/globalDebugHelper').then(() => {
    console.log('🔧 全局调试助手已加载，使用 debugAI.help() 查看可用命令');
  });
}

// 🎯 开发环境下加载调试助手
if (process.env.NODE_ENV === 'development') {
  import('./utils/debugHelper.js').then(() => {
    console.log('📊 加密货币调试工具已启用')
    console.log('🔧 在控制台输入 debugCrypto 查看可用命令')
  }).catch(err => {
    console.error('调试工具加载失败:', err)
  })
}

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <App />
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
