#!/usr/bin/env python3
"""
简化的Django服务器，只运行SMS功能
用于快速测试SMS API
"""

import os
import sys
import django
from django.conf import settings
from django.core.management import execute_from_command_line
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
import json
import random
import logging

# 配置Django设置
if not settings.configured:
    settings.configure(
        DEBUG=True,
        SECRET_KEY='simple-test-key-for-development-only',
        ALLOWED_HOSTS=['*'],
        
        INSTALLED_APPS=[
            'django.contrib.contenttypes',
            'django.contrib.auth',
            'corsheaders',
        ],
        
        MIDDLEWARE=[
            'corsheaders.middleware.CorsMiddleware',
            'django.middleware.common.CommonMiddleware',
        ],
        
        # CORS配置
        CORS_ALLOW_ALL_ORIGINS=True,
        CORS_ALLOW_CREDENTIALS=True,
        
        # 数据库配置
        DATABASES={
            'default': {
                'ENGINE': 'django.db.backends.sqlite3',
                'NAME': os.path.join(os.path.dirname(__file__), 'sms_users.db'),  # 持久化数据库
            }
        },
        
        ROOT_URLCONF='simple_server',
        USE_TZ=True,
    )

# 设置Django
django.setup()

# 导入数据库操作模块
import sqlite3
import hashlib
import secrets
import string
from datetime import datetime

# 数据库操作类
class SMSUserDB:
    def __init__(self):
        self.db_path = os.path.join(os.path.dirname(__file__), 'sms_users.db')
        self.init_db()
    
    def init_db(self):
        """初始化数据库表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sms_users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                phone VARCHAR(11) UNIQUE NOT NULL,
                password VARCHAR(128) NOT NULL,
                balance DECIMAL(10,2) DEFAULT 3.00,
                invite_code VARCHAR(8) UNIQUE NOT NULL,
                nickname VARCHAR(50) DEFAULT '',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info("✅ 数据库表初始化成功")
    
    def generate_invite_code(self):
        """生成邀请码"""
        chars = string.ascii_uppercase + string.digits
        while True:
            code = ''.join(secrets.choice(chars) for _ in range(6))
            if not self.user_exists_by_invite_code(code):
                return code
    
    def hash_password(self, raw_password):
        """密码哈希"""
        return hashlib.sha256(raw_password.encode()).hexdigest()
    
    def user_exists(self, phone):
        """检查用户是否存在"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT id FROM sms_users WHERE phone = ?', (phone,))
        result = cursor.fetchone()
        
        conn.close()
        return result is not None
    
    def user_exists_by_invite_code(self, invite_code):
        """检查邀请码是否存在"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT id FROM sms_users WHERE invite_code = ?', (invite_code,))
        result = cursor.fetchone()
        
        conn.close()
        return result is not None
    
    def create_user(self, phone, password, nickname=''):
        """创建用户"""
        invite_code = self.generate_invite_code()
        password_hash = self.hash_password(password)
        nickname = nickname or f'用户{phone[-4:]}'
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO sms_users (phone, password, balance, invite_code, nickname, created_at)
            VALUES (?, ?, ?, ?, ?, datetime('now'))
        ''', (phone, password_hash, 3.00, invite_code, nickname))
        
        user_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return self.get_user_by_id(user_id)
    
    def get_user(self, phone):
        """根据手机号获取用户"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, phone, password, balance, invite_code, nickname, created_at
            FROM sms_users WHERE phone = ?
        ''', (phone,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                'id': result[0],
                'phone': result[1],
                'password': result[2],
                'balance': float(result[3]),
                'invite_code': result[4],
                'nickname': result[5],
                'created_at': result[6]
            }
        return None
    
    def get_user_by_id(self, user_id):
        """根据ID获取用户"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, phone, password, balance, invite_code, nickname, created_at
            FROM sms_users WHERE id = ?
        ''', (user_id,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                'id': result[0],
                'phone': result[1],
                'password': result[2],
                'balance': float(result[3]),
                'invite_code': result[4],
                'nickname': result[5],
                'created_at': result[6]
            }
        return None
    
    def verify_password(self, phone, password):
        """验证密码"""
        user = self.get_user(phone)
        if user:
            return user['password'] == self.hash_password(password)
        return False

# 日志配置
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 真实腾讯云SMS服务
class TencentSMSService:
    def __init__(self):
        self.sent_codes = {}  # 存储发送的验证码
        
        # 腾讯云SMS配置
        self.secret_id = "AKID7RbcUBi3HNHF9vbK7yjOvELK9oJdkhUc"
        self.secret_key = "7mGYeZKsGSRCk7GE4ykPvFNJE2d0DGF3"
        self.sdk_app_id = "1401016175"
        self.sign_id = "660929"
        self.template_id = "2483395"
        self.region = "ap-guangzhou"
    
    def send_verification_code(self, phone, purpose='register'):
        """发送真实短信验证码"""
        code = str(random.randint(100000, 999999))
        
        try:
            # 导入腾讯云SMS SDK
            from tencentcloud.common import credential
            from tencentcloud.common.profile.client_profile import ClientProfile
            from tencentcloud.common.profile.http_profile import HttpProfile
            from tencentcloud.sms.v20210111 import sms_client, models
            
            # 创建认证对象
            cred = credential.Credential(self.secret_id, self.secret_key)
            
            # 创建HTTP配置
            httpProfile = HttpProfile()
            httpProfile.endpoint = "sms.tencentcloudapi.com"
            
            # 创建客户端配置
            clientProfile = ClientProfile()
            clientProfile.httpProfile = httpProfile
            
            # 创建SMS客户端
            client = sms_client.SmsClient(cred, self.region, clientProfile)
            
            # 创建请求
            req = models.SendSmsRequest()
            req.PhoneNumberSet = [f"+86{phone}"]
            req.SmsSdkAppId = self.sdk_app_id
            req.SignName = "成都星空三维科技有限公司"  # 使用审核通过的签名
            req.TemplateId = self.template_id
            req.TemplateParamSet = [code]
            
            # 发送短信
            resp = client.SendSms(req)
            
            # 检查响应
            if resp.SendStatusSet and len(resp.SendStatusSet) > 0:
                status = resp.SendStatusSet[0]
                logger.info(f"📱 SMS API响应详情 - 手机号: {phone}, 状态码: {status.Code}, 消息: {status.Message}, SerialNo: {getattr(status, 'SerialNo', 'N/A')}")
                
                if status.Code == "Ok":
                    self.sent_codes[phone] = code
                    logger.info(f"✅ 腾讯云API确认发送 - 手机号: {phone}, 验证码: {code}")
                    # 开发环境下直接返回验证码，避免运营商拦截问题
                    return True, f"验证码发送成功(开发模式): {code}", code
                else:
                    error_msg = f"短信发送失败: {status.Code} - {status.Message}"
                    logger.error(f"❌ {error_msg}")
                    return False, "短信发送失败，请稍后重试", None
            else:
                logger.error("❌ 短信发送响应为空")
                return False, "短信发送失败", None
                
        except Exception as e:
            logger.error(f"❌ 短信发送异常: {e}")
            # 降级到模拟模式
            self.sent_codes[phone] = code
            logger.info(f"⚠️  降级到模拟模式 - 手机号: {phone}, 验证码: {code}")
            return True, f"验证码发送成功(模拟模式): {code}", code
    
    def verify_code(self, phone, code):
        """验证短信验证码"""
        stored_code = self.sent_codes.get(phone)
        if stored_code and str(stored_code) == str(code):
            # 验证成功后删除验证码
            del self.sent_codes[phone]
            return True
        return False

# 全局SMS服务实例
sms_service = TencentSMSService()

# 初始化数据库
user_db = SMSUserDB()

@csrf_exempt
@require_http_methods(["POST", "OPTIONS"])
def send_sms_code(request):
    """发送短信验证码"""
    if request.method == 'OPTIONS':
        response = JsonResponse({})
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type'
        return response
    
    try:
        data = json.loads(request.body.decode('utf-8'))
        phone = data.get('phone', '').strip()
        purpose = data.get('purpose', 'register')
        
        if not phone:
            return JsonResponse({'error': '手机号不能为空'}, status=400)
        
        # 验证手机号格式
        if len(phone) != 11 or not phone.isdigit():
            return JsonResponse({'error': '手机号格式不正确'}, status=400)
        
        # 检查用户是否存在
        user_exists = user_db.user_exists(phone)
        
        if purpose == 'register' and user_exists:
            return JsonResponse({'error': '该手机号已注册，请直接登录'}, status=400)
        
        if purpose == 'login' and not user_exists:
            return JsonResponse({'error': '该手机号未注册，请先注册'}, status=400)
        
        # 发送验证码
        success, message, code = sms_service.send_verification_code(phone, purpose)
        
        if success:
            return JsonResponse({'message': message})
        else:
            return JsonResponse({'error': message}, status=400)
            
    except Exception as e:
        logger.error(f"发送验证码异常: {e}")
        return JsonResponse({'error': '服务器内部错误'}, status=500)

@csrf_exempt 
@require_http_methods(["POST", "OPTIONS"])
def sms_register(request):
    """短信验证码注册"""
    if request.method == 'OPTIONS':
        response = JsonResponse({})
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type'
        return response
    
    try:
        data = json.loads(request.body.decode('utf-8'))
        phone = data.get('phone', '').strip()
        sms_code = data.get('sms_code', '').strip()
        password = data.get('password', '')
        nickname = data.get('nickname', f'用户{phone[-4:]}')
        
        if not all([phone, sms_code, password]):
            return JsonResponse({'error': '手机号、验证码和密码不能为空'}, status=400)
        
        # 验证短信验证码
        if not sms_service.verify_code(phone, sms_code):
            return JsonResponse({'error': '验证码错误或已过期'}, status=400)
        
        # 检查用户是否已存在
        if user_db.user_exists(phone):
            return JsonResponse({'error': '该手机号已注册'}, status=400)
        
        # 创建用户
        user_data = user_db.create_user(phone, password, nickname)
        
        # 模拟JWT tokens
        tokens = {
            'access': f'mock_access_token_for_{phone}',
            'refresh': f'mock_refresh_token_for_{phone}'
        }
        
        logger.info(f"用户注册成功: {phone}")
        return JsonResponse({
            'message': '注册成功',
            'user': user_data,
            'tokens': tokens
        })
        
    except Exception as e:
        logger.error(f"用户注册异常: {e}")
        return JsonResponse({'error': '服务器内部错误'}, status=500)

@csrf_exempt
@require_http_methods(["POST", "OPTIONS"])
def password_login(request):
    """密码登录"""
    if request.method == 'OPTIONS':
        response = JsonResponse({})
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type'
        return response
    
    try:
        data = json.loads(request.body.decode('utf-8'))
        phone = data.get('phone', '').strip()
        password = data.get('password', '')
        
        if not phone or not password:
            return JsonResponse({'error': '手机号和密码不能为空'}, status=400)
        
        # 验证用户和密码
        if not user_db.verify_password(phone, password):
            return JsonResponse({'error': '手机号或密码错误'}, status=400)
        
        # 获取用户数据
        user_data = user_db.get_user(phone)
        
        # 模拟JWT tokens
        tokens = {
            'access': f'mock_access_token_for_{phone}',
            'refresh': f'mock_refresh_token_for_{phone}'
        }
        
        logger.info(f"用户密码登录成功: {phone}")
        return JsonResponse({
            'message': '登录成功',
            'user': user_data,
            'tokens': tokens
        })
        
    except Exception as e:
        logger.error(f"用户密码登录异常: {e}")
        return JsonResponse({'error': '服务器内部错误'}, status=500)

@csrf_exempt
@require_http_methods(["POST", "OPTIONS"])
def sms_login(request):
    """短信验证码登录"""
    if request.method == 'OPTIONS':
        response = JsonResponse({})
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type'
        return response
    
    try:
        data = json.loads(request.body.decode('utf-8'))
        phone = data.get('phone', '').strip()
        sms_code = data.get('sms_code', '').strip()
        
        if not phone or not sms_code:
            return JsonResponse({'error': '手机号和验证码不能为空'}, status=400)
        
        # 检查用户是否存在
        if not user_db.user_exists(phone):
            return JsonResponse({'error': '该手机号未注册'}, status=400)
        
        # 验证短信验证码
        if not sms_service.verify_code(phone, sms_code):
            return JsonResponse({'error': '验证码错误或已过期'}, status=400)
        
        # 获取用户数据
        user_data = user_db.get_user(phone)
        
        # 模拟JWT tokens
        tokens = {
            'access': f'mock_access_token_for_{phone}',
            'refresh': f'mock_refresh_token_for_{phone}'
        }
        
        logger.info(f"用户登录成功: {phone}")
        return JsonResponse({
            'message': '登录成功',
            'user': user_data,
            'tokens': tokens
        })
        
    except Exception as e:
        logger.error(f"用户登录异常: {e}")
        return JsonResponse({'error': '服务器内部错误'}, status=500)

# URL配置
from django.urls import path

urlpatterns = [
    path('api/web/sms/send-code/', send_sms_code, name='send_sms_code'),
    path('api/web/sms/register/', sms_register, name='sms_register'),
    path('api/web/sms/login/', sms_login, name='sms_login'),
    path('api/web/sms/password-login/', password_login, name='password_login'),
]

if __name__ == '__main__':
    if len(sys.argv) == 1:
        sys.argv.append('runserver')
        sys.argv.append('0.0.0.0:8000')
    
    print("\n🚀 启动简化SMS测试服务器...")
    print("📱 SMS验证码会在控制台显示")
    print("🌐 服务器地址: http://localhost:8000")
    print("📋 可用API:")
    print("   POST /api/web/sms/send-code/")
    print("   POST /api/web/sms/register/") 
    print("   POST /api/web/sms/login/")
    print("\n" + "="*50 + "\n")
    
    execute_from_command_line(sys.argv)