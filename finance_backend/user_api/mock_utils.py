"""
Mock utilities for testing WeChat API functionality without real WeChat integration
"""
import json
import random
import string


class MockWechatAPI:
    """Mock WeChat API for testing purposes"""
    
    @staticmethod
    def generate_mock_openid():
        """Generate a mock openid for testing"""
        return 'mock_openid_' + ''.join(random.choices(string.ascii_lowercase + string.digits, k=20))
    
    @staticmethod
    def code2session(code):
        """
        Mock implementation of WeChat code2session API
        Returns mock data for testing
        """
        if not code or code == 'invalid_code':
            return None
            
        # Generate consistent mock data based on code
        mock_openid = f"mock_openid_{hash(code) % 1000000}"
        mock_session_key = f"mock_session_key_{hash(code) % 1000000}"
        
        return {
            'openid': mock_openid,
            'session_key': mock_session_key,
            'unionid': f"mock_unionid_{hash(code) % 1000000}"
        }
    
    @staticmethod
    def decrypt_phone_number(session_key, encrypted_data, iv):
        """
        Mock implementation of phone number decryption
        Returns mock phone number for testing
        """
        if not session_key or not encrypted_data or not iv:
            return None
            
        # Generate mock phone number based on input
        phone_suffix = hash(session_key + encrypted_data + iv) % 100000000
        mock_phone = f"138{phone_suffix:08d}"
        
        return {
            'phone_number': mock_phone,
            'pure_phone_number': mock_phone,
            'country_code': '86'
        }


# For testing purposes, we can temporarily override the real WeChat API
def enable_mock_mode():
    """Enable mock mode for testing"""
    from . import utils
    utils.WechatAPI.code2session = MockWechatAPI.code2session
    utils.WechatAPI.decrypt_phone_number = MockWechatAPI.decrypt_phone_number