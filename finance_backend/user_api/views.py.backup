from django.shortcuts import render
import logging
from django.db import transaction
from rest_framework import status, viewsets, permissions
from rest_framework.decorators import action, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenRefreshView

from .models import <PERSON>chat<PERSON>ser, CreditRecord
from .serializers import (
    WechatUserSerializer, CreditRecordSerializer,
    WechatLoginSerializer, PhoneBindSerializer, WechatRegisterSerializer
)
from .utils import WechatAPI
from .storage import FileStorage
from .authentication import WechatTokenGenerator, WechatJWTAuthentication
from .mock_utils import enable_mock_mode

logger = logging.getLogger(__name__)

# Enable mock mode for testing (comment out in production)
# enable_mock_mode()

class WechatRegisterView(APIView):
    """微信用户注册视图"""
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        """
        微信小程序用户注册接口
        
        前端调用wx.login()获取code，然后调用此接口注册新用户
        """
        serializer = WechatRegisterSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({"code": 400, "message": "参数错误", "errors": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)
        
        code = serializer.validated_data.get('code')
        nickname = serializer.validated_data.get('nickname', '')
        avatar_url = serializer.validated_data.get('avatar_url', '')
        
        # 获取微信openid和session_key
        wx_result = WechatAPI.code2session(code)
        if not wx_result or 'openid' not in wx_result:
            return Response({"code": 500, "message": "微信登录失败"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        openid = wx_result.get('openid')
        unionid = wx_result.get('unionid', '')
        
        # 检查用户是否已存在
        if WechatUser.objects.filter(openid=openid).exists():
            return Response({"code": 409, "message": "用户已存在，请直接登录"}, status=status.HTTP_409_CONFLICT)
        
        # 创建新用户
        try:
            with transaction.atomic():
                user = WechatUser.objects.create(
                    openid=openid,
                    unionid=unionid,
                    nickname=nickname,
                    avatar_url=avatar_url
                )
                
                # 添加初始积分记录
                CreditRecord.objects.create(
                    user=user,
                    type=1,  # 初始赠送
                    amount=3,
                    description="新用户注册赠送3次分析机会"
                )
                
                # 生成JWT tokens
                tokens = WechatTokenGenerator.generate_tokens(user)
                
                # 序列化用户数据
                user_data = WechatUserSerializer(user).data
                
                return Response({
                    "code": 200,
                    "message": "注册成功",
                    "data": {
                        "user": user_data,
                        "tokens": tokens
                    }
                })
                
        except Exception as e:
            logger.error(f"用户注册异常: {e}")
            return Response({"code": 500, "message": f"服务器错误: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class WechatLoginView(APIView):
    """微信登录视图"""
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        """
        微信小程序登录接口
        
        前端调用wx.login()获取code，然后调用此接口登录获取token
        """
        serializer = WechatLoginSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({"code": 400, "message": "参数错误", "errors": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)
        
        code = serializer.validated_data.get('code')
        nickname = serializer.validated_data.get('nickname', '')
        avatar_url = serializer.validated_data.get('avatar_url', '')
        
        # 获取微信openid和session_key
        wx_result = WechatAPI.code2session(code)
        if not wx_result or 'openid' not in wx_result:
            return Response({"code": 500, "message": "微信登录失败"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        openid = wx_result.get('openid')
        unionid = wx_result.get('unionid', '')
        
        # 查找用户
        try:
            user = WechatUser.objects.get(openid=openid, is_active=True)
            
            # 更新用户信息（如果提供了新信息）
            updated = False
            if nickname and nickname != user.nickname:
                user.nickname = nickname
                updated = True
            if avatar_url and avatar_url != user.avatar_url:
                if avatar_url.startswith('http'):
                    user.avatar_url = avatar_url
                    updated = True
                elif avatar_url.startswith('data:image'):
                    cos_url = FileStorage.upload_base64_image(avatar_url, 'avatars')
                    if cos_url:
                        user.avatar_url = cos_url
                        updated = True
                        
            if updated:
                user.save()
            
            # 生成JWT tokens
            tokens = WechatTokenGenerator.generate_tokens(user)
            
            # 序列化用户数据
            user_data = WechatUserSerializer(user).data
            
            return Response({
                "code": 200,
                "message": "登录成功",
                "data": {
                    "user": user_data,
                    "tokens": tokens,
                    "has_phone": bool(user.phone)
                }
            })
            
        except WechatUser.DoesNotExist:
            return Response({"code": 404, "message": "用户不存在，请先注册"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"微信登录异常: {e}")
            return Response({"code": 500, "message": f"服务器错误: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PhoneBindView(APIView):
    """手机号绑定视图"""
    authentication_classes = [WechatJWTAuthentication]
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """
        绑定微信小程序获取的手机号
        
        需要用户先登录获取JWT token，然后前端调用getPhoneNumber获取加密数据，最后调用此接口绑定手机号
        """
        if not hasattr(request.user, 'openid'):
            return Response({"code": 401, "message": "用户未认证"}, status=status.HTTP_401_UNAUTHORIZED)
        
        serializer = PhoneBindSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({"code": 400, "message": "参数错误", "errors": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)
        
        code = serializer.validated_data.get('code')
        encrypted_data = serializer.validated_data.get('encrypted_data')
        iv = serializer.validated_data.get('iv')
        
        # 获取微信session_key
        wx_result = WechatAPI.code2session(code)
        if not wx_result or 'session_key' not in wx_result:
            return Response({"code": 500, "message": "获取session_key失败"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        session_key = wx_result.get('session_key')
        
        # 解密手机号
        phone_result = WechatAPI.decrypt_phone_number(session_key, encrypted_data, iv)
        if not phone_result or 'phone_number' not in phone_result:
            return Response({"code": 500, "message": "手机号解密失败"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        # 更新用户手机号
        try:
            with transaction.atomic():
                user = request.user
                user.phone = phone_result.get('phone_number')
                user.save()
                
                # 序列化用户数据
                user_data = WechatUserSerializer(user).data
                
                # 返回结果
                return Response({
                    "code": 200,
                    "message": "手机号绑定成功",
                    "data": {
                        "user": user_data
                    }
                })
        except Exception as e:
            logger.error(f"手机号绑定异常: {e}")
            return Response({"code": 500, "message": f"服务器错误: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TokenRefreshView(APIView):
    """Token刷新视图"""
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        """
        刷新JWT token
        """
        refresh_token = request.data.get('refresh_token')
        if not refresh_token:
            return Response({"code": 400, "message": "缺少refresh_token参数"}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            from rest_framework_simplejwt.tokens import RefreshToken
            
            refresh = RefreshToken(refresh_token)
            openid = refresh.get('openid')
            
            # 验证用户是否存在且有效
            user = WechatUser.objects.get(openid=openid, is_active=True)
            
            # 生成新的tokens
            tokens = WechatTokenGenerator.generate_tokens(user)
            
            return Response({
                "code": 200,
                "message": "Token刷新成功",
                "data": {
                    "tokens": tokens
                }
            })
            
        except Exception as e:
            logger.error(f"Token刷新异常: {e}")
            return Response({"code": 401, "message": "Token无效或已过期"}, status=status.HTTP_401_UNAUTHORIZED)


class UserViewSet(viewsets.ModelViewSet):
    """用户视图集"""
    queryset = WechatUser.objects.all()
    serializer_class = WechatUserSerializer
    authentication_classes = [WechatJWTAuthentication]
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]
    
    @action(detail=False, methods=['get'])
    def me(self, request):
        """获取当前用户信息"""
        # 从JWT token中获取用户信息
        if hasattr(request.user, 'openid'):
            user = request.user
            serializer = self.get_serializer(user)
            return Response({
                "code": 200,
                "message": "获取成功",
                "data": serializer.data
            })
        else:
            return Response({"code": 401, "message": "用户未认证"}, status=status.HTTP_401_UNAUTHORIZED)
    
    @action(detail=False, methods=['get'], permission_classes=[permissions.AllowAny])
    def info(self, request):
        """根据openid获取用户信息（公开接口）"""
        openid = request.query_params.get('openid')
        if not openid:
            return Response({"code": 400, "message": "缺少openid参数"}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            user = WechatUser.objects.get(openid=openid, is_active=True)
            serializer = self.get_serializer(user)
            return Response({
                "code": 200,
                "message": "获取成功",
                "data": serializer.data
            })
        except WechatUser.DoesNotExist:
            return Response({"code": 404, "message": "用户不存在"}, status=status.HTTP_404_NOT_FOUND)
    
    @action(detail=False, methods=['post'])
    def upload_avatar(self, request):
        """上传头像"""
        if not hasattr(request.user, 'openid'):
            return Response({"code": 401, "message": "用户未认证"}, status=status.HTTP_401_UNAUTHORIZED)
        
        avatar_file = request.FILES.get('avatar')
        if not avatar_file:
            return Response({"code": 400, "message": "缺少avatar文件"}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            user = request.user
            
            # 上传头像到COS
            avatar_url = FileStorage.upload_file(avatar_file, 'avatars')
            if not avatar_url:
                return Response({"code": 500, "message": "头像上传失败"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            # 更新用户头像
            user.avatar_url = avatar_url
            user.save()
            
            return Response({
                "code": 200,
                "message": "头像上传成功",
                "data": {
                    "avatar_url": avatar_url
                }
            })
        except Exception as e:
            logger.error(f"头像上传异常: {e}")
            return Response({"code": 500, "message": f"服务器错误: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['post'])
    def use_credit(self, request):
        """使用分析次数"""
        if not hasattr(request.user, 'openid'):
            return Response({"code": 401, "message": "用户未认证"}, status=status.HTTP_401_UNAUTHORIZED)
        
        try:
            with transaction.atomic():
                user = WechatUser.objects.select_for_update().get(id=request.user.id)
                
                # 检查用户是否有足够的积分
                if user.credits <= 0:
                    return Response({"code": 403, "message": "分析次数不足"}, status=status.HTTP_403_FORBIDDEN)
                
                # 扣除积分
                user.credits -= 1
                user.save()
                
                # 记录积分使用
                CreditRecord.objects.create(
                    user=user,
                    type=4,  # 消费使用
                    amount=-1,
                    description="使用1次分析机会"
                )
                
                return Response({
                    "code": 200,
                    "message": "使用成功",
                    "data": {
                        "credits": user.credits
                    }
                })
        except WechatUser.DoesNotExist:
            return Response({"code": 404, "message": "用户不存在"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"使用积分异常: {e}")
            return Response({"code": 500, "message": f"服务器错误: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CreditRecordViewSet(viewsets.ReadOnlyModelViewSet):
    """积分记录视图集"""
    queryset = CreditRecord.objects.all()
    serializer_class = CreditRecordSerializer
    authentication_classes = [WechatJWTAuthentication]
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """获取当前用户的积分记录"""
        if hasattr(self.request.user, 'openid'):
            return CreditRecord.objects.filter(user=self.request.user)
        return CreditRecord.objects.none()
    
    def list(self, request, *args, **kwargs):
        """重写列表方法，返回统一格式"""
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            "code": 200,
            "message": "获取成功",
            "data": serializer.data
        })


class TestMockView(APIView):
    """测试接口 - 仅用于开发测试"""
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        """启用或禁用Mock模式"""
        action = request.data.get('action', 'enable')
        
        if action == 'enable':
            enable_mock_mode()
            return Response({
                "code": 200,
                "message": "Mock模式已启用",
                "data": {"mock_mode": True}
            })
        else:
            return Response({
                "code": 200,
                "message": "Mock模式操作完成",
                "data": {"mock_mode": False}
            })
    
    def get(self, request):
        """获取Mock API状态"""
        return Response({
            "code": 200,
            "message": "Mock API状态",
            "data": {
                "mock_mode": True,
                "note": "当前使用Mock WeChat API进行测试"
            }
        })

class TokenVerifyView(APIView):
    """Token验证视图"""
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        """
        验证JWT token有效性
        """
        try:
            # 从请求头获取token
            auth_header = request.META.get('HTTP_AUTHORIZATION', '')
            if not auth_header.startswith('Bearer '):
                return Response({"code": 401, "message": "Token格式错误"}, status=status.HTTP_401_UNAUTHORIZED)
            
            token = auth_header.split(' ')[1]
            
            # 验证token
            from rest_framework_simplejwt.tokens import AccessToken
            access_token = AccessToken(token)
            openid = access_token.get('openid')
            
            # 验证用户是否存在且有效
            user = WechatUser.objects.get(openid=openid, is_active=True)
            
            # 返回用户信息
            serializer = WechatUserSerializer(user)
            return Response({
                "code": 200,
                "message": "Token有效",
                "isValid": True,
                "data": {
                    "userInfo": serializer.data
                }
            })
            
        except Exception as e:
            logger.error(f"Token验证异常: {e}")
            return Response({
                "code": 401, 
                "message": "Token无效或已过期",
                "isValid": False
            }, status=status.HTTP_401_UNAUTHORIZED)

class GetOpenidView(APIView):
    """获取OpenID视图"""
    permission_classes = [permissions.AllowAny]
    
    def get(self, request):
        """通过code获取OpenID"""
        code = request.query_params.get('code')
        if not code:
            return Response({"code": 400, "message": "缺少code参数"}, status=status.HTTP_400_BAD_REQUEST)
        
        # 获取微信openid
        wx_result = WechatAPI.code2session(code)
        if not wx_result or 'openid' not in wx_result:
            return Response({"code": 500, "message": "获取OpenID失败"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response({
            "code": 200,
            "message": "获取成功",
            "data": {
                "openid": wx_result.get('openid')
            }
        })
