"""
多时间周期数据修复命令
从1分钟数据生成15m, 1h, 4h, 1d数据并上传到COS
"""

import logging
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand, CommandError
from crypto_api.services.gap_recovery import GapRecoveryService
from crypto_api.services.cos_service import COSRealtimeService
import pandas as pd
import json

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '修复多时间周期数据缺失'

    def add_arguments(self, parser):
        parser.add_argument(
            '--symbol',
            type=str,
            default='BTCUSDT',
            help='交易对符号 (默认: BTCUSDT)'
        )
        parser.add_argument(
            '--timeframe',
            type=str,
            default='15m',
            help='目标时间周期 (15m, 1h, 4h, 1d)'
        )
        parser.add_argument(
            '--start',
            type=str,
            required=True,
            help='开始日期 (格式: 2025-07-16)'
        )
        parser.add_argument(
            '--end',
            type=str,
            required=True,
            help='结束日期 (格式: 2025-07-30)'
        )
        parser.add_argument(
            '--debug',
            action='store_true',
            help='启用调试模式'
        )

    def handle(self, *args, **options):
        symbol = options['symbol'].upper()
        timeframe = options['timeframe']
        start_date = options['start']
        end_date = options['end']
        debug = options['debug']

        # 验证时间周期
        valid_timeframes = ['15m', '1h', '4h', '1d']
        if timeframe not in valid_timeframes:
            raise CommandError(f"不支持的时间周期: {timeframe}，支持: {', '.join(valid_timeframes)}")

        # 解析日期
        try:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError as e:
            raise CommandError(f"日期格式错误: {e}")

        if start_dt >= end_dt:
            raise CommandError("开始日期必须早于结束日期")

        self.stdout.write(f"🔧 开始修复多时间周期数据...")
        self.stdout.write(f"📊 交易对: {symbol}")
        self.stdout.write(f"⏰ 时间周期: {timeframe}")
        self.stdout.write(f"📅 日期范围: {start_date} ~ {end_date}")

        try:
            processor = MultiTimeframeDataProcessor(symbol, timeframe, debug)
            success_count, total_count = processor.process_date_range(start_dt, end_dt)
            
            if success_count == total_count:
                self.stdout.write(
                    self.style.SUCCESS(f"✅ 多时间周期数据修复成功: {symbol} {timeframe} ({success_count}/{total_count}天)")
                )
            else:
                raise CommandError(f"❌ 部分修复失败: {success_count}/{total_count}天成功")
                
        except Exception as e:
            logger.error(f"多时间周期数据修复异常: {e}")
            raise CommandError(f"❌ 修复过程异常: {e}")


class MultiTimeframeDataProcessor:
    """多时间周期数据处理器"""
    
    def __init__(self, symbol: str, timeframe: str, debug: bool = False):
        self.symbol = symbol
        self.timeframe = timeframe
        self.debug = debug
        self.cos_service = COSRealtimeService(debug=debug)
        
        # 时间周期转换参数
        self.timeframe_params = {
            '15m': {'minutes': 15, 'rule': '15min'},
            '1h': {'minutes': 60, 'rule': '1H'},
            '4h': {'minutes': 240, 'rule': '4H'},
            '1d': {'minutes': 1440, 'rule': '1D'}
        }
    
    def process_date_range(self, start_dt: datetime, end_dt: datetime) -> tuple:
        """处理日期范围内的数据"""
        success_count = 0
        total_count = 0
        
        current_date = start_dt
        while current_date <= end_dt:
            date_str = current_date.strftime('%Y-%m-%d')
            
            if self.debug:
                print(f"📅 处理日期: {date_str}")
            
            try:
                if self.process_single_date(date_str):
                    success_count += 1
                    if self.debug:
                        print(f"✅ {date_str} 处理成功")
                else:
                    if self.debug:
                        print(f"❌ {date_str} 处理失败")
                        
            except Exception as e:
                logger.error(f"处理日期 {date_str} 失败: {e}")
                if self.debug:
                    print(f"❌ {date_str} 处理异常: {e}")
            
            total_count += 1
            current_date += timedelta(days=1)
        
        return success_count, total_count
    
    def process_single_date(self, date_str: str) -> bool:
        """处理单个日期的数据"""
        try:
            # 1. 获取1分钟数据
            minute_data = self.load_minute_data(date_str)
            if not minute_data:
                if self.debug:
                    print(f"  ⚠️ {date_str} 无1分钟数据，跳过")
                return False
            
            # 2. 转换为目标时间周期
            converted_data = self.convert_timeframe(minute_data, date_str)
            if not converted_data:
                if self.debug:
                    print(f"  ⚠️ {date_str} 时间周期转换失败")
                return False
            
            # 3. 上传到COS
            if self.upload_converted_data(converted_data, date_str):
                if self.debug:
                    print(f"  ✅ {date_str} {self.timeframe} 数据上传成功 ({len(converted_data)} 条)")
                return True
            else:
                if self.debug:
                    print(f"  ❌ {date_str} {self.timeframe} 数据上传失败")
                return False
                
        except Exception as e:
            logger.error(f"处理单日数据失败 {date_str}: {e}")
            return False
    
    def load_minute_data(self, date_str: str) -> list:
        """加载1分钟数据"""
        try:
            # 生成1分钟数据的COS路径
            cos_key = self.cos_service.generate_cos_path(self.symbol, date_str, '1m')
            
            # 检查文件是否存在
            if not self.cos_service.check_file_exists(cos_key):
                return []
            
            # 下载并解析文件
            file_obj = self.cos_service.cos_client.get_object(
                Bucket=self.cos_service.bucket_name,
                Key=cos_key
            )
            file_content = file_obj['Body'].read().decode('utf-8')
            cleaned_content = self.cos_service.clean_chunked_data(file_content)
            data = json.loads(cleaned_content)
            
            return data.get('data', [])
            
        except Exception as e:
            logger.error(f"加载1分钟数据失败 {date_str}: {e}")
            return []
    
    def convert_timeframe(self, minute_data: list, date_str: str) -> list:
        """将1分钟数据转换为目标时间周期"""
        try:
            if not minute_data:
                return []
            
            # 转换为DataFrame
            df = pd.DataFrame(minute_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # 处理时间戳（支持微秒和毫秒格式）
            df['timestamp'] = df['timestamp'].astype(float)
            df.loc[df['timestamp'] > 1e15, 'timestamp'] = df.loc[df['timestamp'] > 1e15, 'timestamp'] / 1000  # 微秒转毫秒
            
            # 转换为datetime
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('datetime', inplace=True)
            
            # 确保数值类型
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 按目标时间周期重采样
            rule = self.timeframe_params[self.timeframe]['rule']
            
            resampled = df.resample(rule).agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }).dropna()
            
            # 转换回列表格式
            converted_data = []
            for idx, row in resampled.iterrows():
                converted_data.append([
                    int(idx.timestamp() * 1000),  # 转换为毫秒时间戳
                    float(row['open']),
                    float(row['high']),
                    float(row['low']),
                    float(row['close']),
                    float(row['volume'])
                ])
            
            return converted_data
            
        except Exception as e:
            logger.error(f"时间周期转换失败 {date_str}: {e}")
            return []
    
    def upload_converted_data(self, converted_data: list, date_str: str) -> bool:
        """上传转换后的数据到COS"""
        try:
            if not converted_data:
                return False
            
            # 构建数据结构
            data_dict = []
            for item in converted_data:
                data_dict.append({
                    'timestamp': item[0],
                    'open': item[1],
                    'high': item[2],
                    'low': item[3],
                    'close': item[4],
                    'volume': item[5]
                })
            
            # 使用COS服务上传（会自动处理路径和合并）
            return self.cos_service.upload_realtime_data(
                symbol=self.symbol,
                data=data_dict,
                timeframe=self.timeframe
            )
            
        except Exception as e:
            logger.error(f"上传转换数据失败 {date_str}: {e}")
            return False