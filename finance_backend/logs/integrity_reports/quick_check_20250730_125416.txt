================================================================================
🔍 数据完整性检查报告
================================================================================
📅 检查时间: 2025-07-30 12:54:25
📊 检查期间: 2025-07-27 ~ 2025-07-30
⏱️  检查天数: 4 天

📈 总体统计
----------------------------------------
期望分钟数: 28,800
实际分钟数: 10,559
总体覆盖率: 36.66%
重要缺失: 21 个时段

📊 分交易对统计
----------------------------------------
BTCUSDT:
  覆盖率: 42.50%
  缺失时段: 5 个
  实际/期望: 2448/5760
ETHUSDT:
  覆盖率: 43.56%
  缺失时段: 4 个
  实际/期望: 2509/5760
SOLUSDT:
  覆盖率: 51.82%
  缺失时段: 6 个
  实际/期望: 2985/5760
DOGEUSDT:
  覆盖率: 43.56%
  缺失时段: 4 个
  实际/期望: 2509/5760
XRPUSDT:
  覆盖率: 1.88%
  缺失时段: 5 个
  实际/期望: 108/5760

🚨 重要缺失时段 (>60分钟)
----------------------------------------
XRPUSDT 2025-07-27
  时间: 2025-07-27 00:00:00 ~ 2025-07-27 23:59:00
  持续: 1440 分钟

XRPUSDT 2025-07-28
  时间: 2025-07-28 00:00:00 ~ 2025-07-28 23:59:00
  持续: 1440 分钟

BTCUSDT 2025-07-29
  时间: 2025-07-29 00:00:00 ~ 2025-07-29 23:34:59
  持续: 1415 分钟

ETHUSDT 2025-07-29
  时间: 2025-07-29 00:00:00 ~ 2025-07-29 23:34:59
  持续: 1415 分钟

SOLUSDT 2025-07-29
  时间: 2025-07-29 00:00:00 ~ 2025-07-29 23:34:59
  持续: 1415 分钟

DOGEUSDT 2025-07-29
  时间: 2025-07-29 00:00:00 ~ 2025-07-29 23:34:59
  持续: 1415 分钟

XRPUSDT 2025-07-29
  时间: 2025-07-29 00:00:00 ~ 2025-07-29 23:34:59
  持续: 1415 分钟

BTCUSDT 2025-07-30
  时间: 2025-07-30 00:00:00 ~ 2025-07-30 11:24:59
  持续: 685 分钟

ETHUSDT 2025-07-30
  时间: 2025-07-30 00:00:00 ~ 2025-07-30 11:24:59
  持续: 685 分钟

SOLUSDT 2025-07-30
  时间: 2025-07-30 00:00:00 ~ 2025-07-30 11:24:59
  持续: 685 分钟

🔧 修复建议
----------------------------------------
python3 manage.py collect_realtime_data --symbol=BTCUSDT --repair-gap --start='2025-07-28T00:00:00' --end='2025-07-28T07:58:59'
python3 manage.py collect_realtime_data --symbol=BTCUSDT --repair-gap --start='2025-07-29T00:00:00' --end='2025-07-29T23:34:59'
python3 manage.py collect_realtime_data --symbol=BTCUSDT --repair-gap --start='2025-07-30T00:00:00' --end='2025-07-30T11:24:59'
python3 manage.py collect_realtime_data --symbol=BTCUSDT --repair-gap --start='2025-07-30T11:31:00' --end='2025-07-30T12:31:59'
python3 manage.py collect_realtime_data --symbol=BTCUSDT --repair-gap --start='2025-07-30T12:48:00' --end='2025-07-30T23:59:59'
