#!/usr/bin/env python3
"""
测试专用视图 - 用于验证时间戳分析功能
"""
import json
import logging
from datetime import datetime
from django.http import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

from historical_data.timestamp_analysis_service import TimestampAnalysisService

logger = logging.getLogger(__name__)

@method_decorator(csrf_exempt, name='dispatch')
class TestTimestampAPIView(View):
    """测试时间戳分析API - 不调用DeepSeek"""
    
    def options(self, request, *args, **kwargs):
        """处理OPTIONS请求（CORS预检请求）"""
        response = JsonResponse({})
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"
        response["Access-Control-Max-Age"] = "86400"
        return response
    
    def post(self, request, *args, **kwargs):
        """处理POST请求 - 测试时间戳分析功能"""
        try:
            logger.info(f"收到测试请求: {request.method} {request.path}")
            
            # 解析请求数据
            data = json.loads(request.body)
            logger.info(f"请求数据: {data}")
            
            target_timestamp = data.get("target_timestamp")
            symbol = data.get("symbol", "BTCUSDT")
            interval = data.get("interval", "1m")
            
            if not target_timestamp:
                return JsonResponse({
                    "error": "缺少target_timestamp参数",
                    "required_params": ["target_timestamp"],
                    "optional_params": ["symbol", "interval"]
                }, status=400)
            
            logger.info(f"开始时间戳分析: {target_timestamp} {symbol} {interval}")
            
            # 使用时间戳分析服务获取数据
            timestamp_service = TimestampAnalysisService()
            analysis_result = timestamp_service.get_timestamp_analysis_data(
                timestamp=target_timestamp,
                symbol=symbol,
                interval=interval
            )
            
            if 'error' in analysis_result:
                return JsonResponse({
                    "error": f"时间戳分析失败: {analysis_result['error']}",
                    "analysis_result": analysis_result
                }, status=500)
            
            # 转换数据格式
            crypto_data = self._convert_timestamp_data_to_crypto_data(analysis_result)
            
            # 构建测试响应
            test_response = {
                "success": True,
                "timestamp_analysis": {
                    "target_timestamp": target_timestamp,
                    "target_datetime": analysis_result.get('target_datetime'),
                    "symbol": symbol,
                    "interval": interval,
                    "total_data_points": analysis_result.get('total_data_points', 0)
                },
                "daily_data_summary": {},
                "technical_indicators": analysis_result.get('technical_indicators', {}),
                "support_resistance": analysis_result.get('support_resistance', {}),  # 新增支撑阻力位
                "vertical_analysis": analysis_result.get('vertical_analysis', {}),
                "btc_reference": analysis_result.get('btc_reference', {}),  # 新增BTC参考数据
                "crypto_data_converted": bool(crypto_data),
                "crypto_data_sample": {
                    "ticker": crypto_data.get('ticker', {}) if crypto_data else {},
                    "shortTermKlines_count": len(crypto_data.get('shortTermKlines', [])) if crypto_data else 0,
                    "has_technical_indicators": 'technical_indicators' in (crypto_data or {}),
                    "has_vertical_analysis": 'vertical_analysis' in (crypto_data or {})
                }
            }
            
            # 添加当前数据摘要
            current_data = analysis_result.get('current_data', {})
            for day_name, day_info in current_data.items():
                if 'error' in day_info:
                    test_response["daily_data_summary"][day_name] = {
                        "status": "error",
                        "error": day_info['error']
                    }
                else:
                    test_response["daily_data_summary"][day_name] = {
                        "status": "success",
                        "date": day_info.get('date'),
                        "data_points": day_info.get('data_points', 0),
                        "price_range": day_info.get('price_range', {})
                    }
            
            # 添加历史数据摘要
            historical_data = analysis_result.get('historical_years_data', {})
            historical_summary = {}
            for year, year_data in historical_data.items():
                if year_data and 'summary' in year_data:
                    historical_summary[year] = {
                        "data_availability": year_data['summary']['data_availability'],
                        "total_days": year_data['summary']['total_days'],
                        "data_points": year_data.get('total_data_points', 0)
                    }
            test_response["historical_data_summary"] = historical_summary
            
            logger.info("✅ 时间戳分析测试完成")
            return JsonResponse(test_response)
            
        except Exception as e:
            logger.error(f"测试请求处理失败: {e}", exc_info=True)
            return JsonResponse({"error": str(e)}, status=500)
    
    def _convert_timestamp_data_to_crypto_data(self, timestamp_analysis_data):
        """将时间戳分析数据转换为crypto_data格式"""
        try:
            # 从current_data和historical_years_data中提取K线数据
            all_klines = []
            
            # 1. 添加当前数据
            current_data = timestamp_analysis_data.get('current_data', {})
            for day_name, day_info in current_data.items():
                if 'klines' in day_info:
                    for kline in day_info['klines']:
                        # 处理时间戳格式并添加K线数据
                        kline_array = self._convert_kline_to_array(kline)
                        if kline_array:
                            all_klines.append(kline_array)
            
            # 2. 添加历史数据（可选，用于更丰富的技术分析）
            historical_data = timestamp_analysis_data.get('historical_years_data', {})
            for year, year_data in historical_data.items():
                if year_data and 'klines' in year_data:
                    for kline in year_data['klines'][:100]:  # 限制历史数据量
                        kline_array = self._convert_kline_to_array(kline)
                        if kline_array:
                            all_klines.append(kline_array)
            
            # 按时间戳排序
            all_klines.sort(key=lambda x: x[0])
            
            # 构建crypto_data格式
            symbol = timestamp_analysis_data.get('symbol', 'BTCUSDT')
            last_price = all_klines[-1][4] if all_klines else 0
            
            crypto_data = {
                'ticker': {
                    'symbol': symbol,
                    'lastPrice': str(last_price),
                    'priceChangePercent': '0.0',
                    'volume': str(sum([k[5] for k in all_klines])),
                    'timestamp': timestamp_analysis_data.get('target_timestamp', int(datetime.now().timestamp() * 1000))
                },
                'shortTermKlines': all_klines,
                'dailyKlines': []
            }
            
            # 添加技术指标和纵向分析
            tech_indicators = timestamp_analysis_data.get('technical_indicators', {})
            if tech_indicators and 'error' not in tech_indicators:
                crypto_data['technical_indicators'] = tech_indicators
            
            vertical_analysis = timestamp_analysis_data.get('vertical_analysis', {})
            if vertical_analysis:
                crypto_data['vertical_analysis'] = vertical_analysis
            
            return crypto_data
            
        except Exception as e:
            logger.error(f"数据转换失败: {e}")
            return None
    
    def _convert_kline_to_array(self, kline):
        """将K线数据转换为数组格式"""
        try:
            # 处理不同的时间戳格式
            open_time = kline['open_time']
            if hasattr(open_time, 'timestamp'):
                # pandas Timestamp对象
                timestamp = int(open_time.timestamp() * 1000)
            elif isinstance(open_time, (int, float)):
                # 已经是数字格式的时间戳
                timestamp = int(open_time)
            else:
                # 其他格式，尝试转换
                timestamp = int(float(open_time))
            
            return [
                timestamp,
                float(kline['open']),
                float(kline['high']),
                float(kline['low']),
                float(kline['close']),
                float(kline['volume'])
            ]
        except Exception as e:
            logger.error(f"K线数据转换失败: {e}")
            return None