#!/usr/bin/env python3
"""
简单的时间戳API测试 - 用于快速验证功能
"""
import requests
import json

def test_simple_timestamp_api():
    """测试基于时间戳的API调用（简化版）"""
    print("🔗 测试时间戳API调用...")
    
    # 测试时间戳: 2025-07-29 00:00:00 UTC
    test_timestamp = 1753718400000
    
    print(f"📅 测试时间戳: {test_timestamp}")
    
    # 构建请求数据（简化版本，不包含完整的AI分析）
    request_data = {
        "messages": [
            {"role": "user", "content": "请简要分析一下市场情况"}
        ],
        "target_timestamp": test_timestamp,
        "symbol": "BTCUSDT",
        "interval": "1m"
    }
    
    try:
        print("📡 发送API请求...")
        response = requests.post(
            'http://localhost:8000/api/ai/chat/',
            headers={'Content-Type': 'application/json'},
            json=request_data,
            timeout=30  # 减少超时时间到30秒
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ API响应成功!")
                print(f"📝 响应数据类型: {type(result)}")
                if 'choices' in result:
                    print("📝 包含choices字段")
                elif 'error' in result:
                    print(f"❌ API返回错误: {result['error']}")
                else:
                    print(f"📝 响应格式: {list(result.keys()) if isinstance(result, dict) else 'Unknown'}")
                return True
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"   原始响应: {response.text[:200]}...")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"   错误内容: {response.text[:200]}...")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ API请求超时（30秒）")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    print("🚀 简单时间戳API测试")
    print("=" * 40)
    
    if test_simple_timestamp_api():
        print("\n🎉 时间戳API测试基本通过！")
    else:
        print("\n⚠️  时间戳API测试失败")