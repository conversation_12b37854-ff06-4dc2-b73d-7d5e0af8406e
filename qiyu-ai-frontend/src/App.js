import React from 'react';
import { ConfigProvider, App as AntdApp, theme } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AuthProvider } from './contexts/AuthContext';
import SimpleRouter from './components/Router/SimpleRouter';
import './styles/globals.css';

function App() {
  return (
    <ConfigProvider
      theme={{
        algorithm: theme.darkAlgorithm,
        token: {
          colorPrimary: '#26A69A',
          colorBgBase: '#0d1117',
          colorTextBase: '#f0f6fc',
          borderRadius: 8,
        }
      }}
      locale={zhCN}
    >
      <AntdApp>
        <AuthProvider>
          <div className="App" style={{
            margin: 0,
            padding: 0,
            minHeight: '100vh',
            backgroundColor: '#0d1117'
          }}>
            <SimpleRouter />
          </div>
        </AuthProvider>
      </AntdApp>
    </ConfigProvider>
  );
}

export default App;
