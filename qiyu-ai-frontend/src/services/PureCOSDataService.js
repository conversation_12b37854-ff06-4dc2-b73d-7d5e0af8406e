/**
 * 纯COS数据服务
 * 完全基于COS读取数据，移除所有WebSocket逻辑
 * 后端常驻进程负责数据收集，前端只负责读取和展示
 */

class PureCOSDataService {
  constructor() {
    this.cache = new Map()
    this.loggedInitializations = new Set()
    
    // 🚀 纯COS模式配置
    this.refreshInterval = 300000 // 5分钟刷新周期
    this.lastUpdateTime = new Map() // 记录每个数据源的最后更新时间
    
    // COS路径配置（与现有架构一致）
    this.cosPaths = {
      // 历史数据路径（2017-2024年）
      'legacy': 'https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/finance-1324685443/finance-1324685443/finance-1324685443/crypto-kline-data-v2/20250724',
      // 2025年数据路径
      '2025': 'https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/20250726'
    }
    
    // 启动定期刷新机制
    this.startPeriodicRefresh()
    
    console.log('🚀 纯COS数据服务启动 (移除WebSocket，5分钟刷新周期)')
  }
  
  /**
   * 🔄 启动定期刷新机制
   */
  startPeriodicRefresh() {
    // 立即执行一次检查
    setTimeout(() => {
      this.checkForUpdates()
    }, 1000)
    
    // 每5分钟检查一次更新
    setInterval(() => {
      this.checkForUpdates()
    }, this.refreshInterval)
    
    console.log('⏰ 定期刷新任务已启动 (5分钟周期)')
  }
  
  /**
   * 🔍 检查数据更新
   */
  async checkForUpdates() {
    try {
      console.log('🔍 检查COS数据更新...')
      
      // 检查当天实时数据是否有更新
      await this.checkTodayRealTimeUpdate()
      
      // 检查其他常用数据源
      await this.checkCommonDataSources()
      
    } catch (error) {
      console.log('⚠️ 数据更新检查失败:', error.message)
    }
  }
  
  /**
   * 📅 检查当天实时数据更新
   */
  async checkTodayRealTimeUpdate() {
    try {
      const today = new Date()
      const symbol = 'BTCUSDT' // 可扩展支持多个交易对
      
      // 生成今天的COS路径
      const todayPath = this.generateTodayRealtimePath(symbol, today)
      
      // 检查文件更新时间
      const response = await fetch(todayPath, { method: 'HEAD' })
      
      if (response.ok) {
        const lastModified = response.headers.get('Last-Modified')
        const fileUpdateTime = new Date(lastModified).getTime()
        
        const cacheKey = `${symbol}_1m_today`
        const lastCheck = this.lastUpdateTime.get(cacheKey) || 0
        
        if (fileUpdateTime > lastCheck) {
          console.log('📥 发现今日实时数据更新，触发图表刷新')
          
          // 清除相关缓存
          this.clearRelevantCache(symbol, '1m', '2025')
          
          // 触发图表更新事件
          this.triggerChartUpdate('realtime_update', {
            symbol,
            timeframe: '1m',
            updateTime: fileUpdateTime
          })
          
          this.lastUpdateTime.set(cacheKey, fileUpdateTime)
        }
      }
      
    } catch (error) {
      console.log('⚠️ 今日数据检查失败:', error.message)
    }
  }
  
  /**
   * 📊 检查常用数据源
   */
  async checkCommonDataSources() {
    const commonSources = [
      { symbol: 'BTCUSDT', timeframe: '15m', year: '2025' },
      { symbol: 'BTCUSDT', timeframe: '1h', year: '2025' },
      { symbol: 'BTCUSDT', timeframe: '4h', year: '2025' },
      { symbol: 'BTCUSDT', timeframe: '1d', year: '2025' }
    ]
    
    for (const source of commonSources) {
      try {
        await this.checkDataSourceUpdate(source)
      } catch (error) {
        console.log(`⚠️ ${source.symbol} ${source.timeframe} 检查失败:`, error.message)
      }
    }
  }
  
  /**
   * 🔍 检查单个数据源更新
   */
  async checkDataSourceUpdate({ symbol, timeframe, year }) {
    const cacheKey = `${symbol}_${timeframe}_${year}`
    
    // 如果缓存中有数据，先检查是否需要更新
    if (this.cache.has(cacheKey)) {
      const lastCheck = this.lastUpdateTime.get(cacheKey) || 0
      const now = Date.now()
      
      // 如果距离上次检查不到5分钟，跳过
      if (now - lastCheck < this.refreshInterval) {
        return
      }
    }
    
    // 生成数据路径
    const dataPath = this.generateDataPath(symbol, timeframe, year)
    
    try {
      // 检查文件头信息
      const response = await fetch(dataPath, { method: 'HEAD' })
      
      if (response.ok) {
        const lastModified = response.headers.get('Last-Modified')
        const fileUpdateTime = new Date(lastModified).getTime()
        const lastCheck = this.lastUpdateTime.get(cacheKey) || 0
        
        if (fileUpdateTime > lastCheck) {
          console.log(`📥 ${symbol} ${timeframe} ${year} 数据已更新`)
          
          // 清除缓存
          this.cache.delete(cacheKey)
          
          this.lastUpdateTime.set(cacheKey, fileUpdateTime)
        }
      }
      
    } catch (error) {
      // 文件不存在或其他错误，忽略
    }
  }
  
  /**
   * 📂 生成今日实时数据路径
   */
  generateTodayRealtimePath(symbol, date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    
    // 与后端生成的路径格式完全一致
    const basePath = this.cosPaths['2025']
    return `${basePath}/${symbol}/1m/daily/${symbol}_1m_${month}-${day}_${year}_compressed.json`
  }
  
  /**
   * 📂 生成数据路径
   */
  generateDataPath(symbol, timeframe, year) {
    const basePath = this.getCosPathForYear(year)
    
    if (year === '2025' && timeframe === '1m') {
      // 1分钟数据使用今日路径
      return this.generateTodayRealtimePath(symbol, new Date())
    } else {
      // 其他数据使用标准路径
      return `${basePath}/${symbol}/${timeframe}/${symbol}_${timeframe}_${year}_compressed.json`
    }
  }
  
  /**
   * 🗑️ 清除相关缓存
   */
  clearRelevantCache(symbol, timeframe, year) {
    const keysToRemove = []
    
    for (const key of this.cache.keys()) {
      if (key.includes(`${symbol}_${timeframe}_${year}`)) {
        keysToRemove.push(key)
      }
    }
    
    keysToRemove.forEach(key => {
      this.cache.delete(key)
      this.loggedInitializations.delete(key)
    })
    
    if (keysToRemove.length > 0) {
      console.log(`🗑️ 清除缓存: ${keysToRemove.length} 个条目`)
    }
  }
  
  /**
   * 🔔 触发图表更新事件
   */
  triggerChartUpdate(eventType, detail) {
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      const updateEvent = new CustomEvent('cosDataUpdate', {
        detail: {
          type: eventType,
          timestamp: Date.now(),
          ...detail
        }
      })
      window.dispatchEvent(updateEvent)
      
      console.log(`🔔 触发图表更新事件: ${eventType}`)
    }
  }
  
  // 继承原有的数据加载方法（保持兼容性）
  getCosPathForYear(year) {
    if (year === '2025') {
      return this.cosPaths['2025']
    } else {
      return this.cosPaths['legacy']
    }
  }
  
  // 使用Stream API处理分块传输（保持原有逻辑）
  async loadDataWithStream(url) {
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const reader = response.body.getReader()
    const decoder = new TextDecoder('utf-8')
    
    let result = ''
    let chunks = []
    
    try {
      while (true) {
        const { done, value } = await reader.read()
        
        if (done) {
          break
        }
        
        chunks.push(value)
        const chunk = decoder.decode(value, { stream: true })
        result += chunk
      }
      
      const finalChunk = decoder.decode()
      if (finalChunk) {
        result += finalChunk
      }
      
      const cleanedData = this.cleanChunkedData(result)
      const jsonData = JSON.parse(cleanedData)
      
      return jsonData
      
    } finally {
      reader.releaseLock()
    }
  }
  
  // 清理分块传输标记（保持原有逻辑）
  cleanChunkedData(rawData) {
    if (rawData.trim().startsWith('{')) {
      return rawData.trim()
    }
    
    let cleaned = rawData
      .replace(/^[0-9a-fA-F]+\\r?\\n/, '')
      .replace(/\\r?\\n[0-9a-fA-F]+\\r?\\n/g, '')
      .replace(/\\r?\\n0\\r?\\n\\r?\\n?$/, '')
      .replace(/^\\s*[0-9a-fA-F]+\\s*$/gm, '')
      .trim()
    
    const firstBrace = cleaned.indexOf('{')
    const lastBrace = cleaned.lastIndexOf('}')
    
    if (firstBrace >= 0 && lastBrace >= 0 && lastBrace > firstBrace) {
      cleaned = cleaned.substring(firstBrace, lastBrace + 1)
    }
    
    return cleaned
  }
  
  /**
   * 📊 带重试机制的数据加载（更新为纯COS模式）
   */
  async loadYearDataWithRetry(symbol = 'BTCUSDT', timeframe = '1m', year = '2024', maxRetries = 3) {
    const cacheKey = `${symbol}_${timeframe}_${year}`

    // 检查缓存
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    // 🎯 2025年1分钟数据：从实时COS文件加载
    if (year === '2025' && timeframe === '1m') {
      return this.load2025RealtimeMinuteData(symbol, maxRetries)
    }

    // 🎯 2025年其他周期：从标准COS文件加载
    if (year === '2025') {
      return this.load2025StandardData(symbol, timeframe, maxRetries)
    }

    // 其他年份数据：使用原有逻辑
    const cosBasePath = this.getCosPathForYear(year)
    const dataURL = `${cosBasePath}/${symbol}/${timeframe}/${symbol}_${timeframe}_${year}_compressed.json`

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const jsonData = await this.loadDataWithStream(dataURL)
        const klineData = jsonData.data || []

        if (klineData.length === 0) {
          throw new Error('数据为空')
        }

        // 转换数据格式
        const processedData = this.processKlineData(klineData)
        
        // 缓存数据
        this.cache.set(cacheKey, processedData)

        // 📊 记录加载日志
        if (!this.loggedInitializations.has(cacheKey)) {
          console.log(`📊 [COS数据] ${symbol} ${timeframe} ${year}年: ${processedData.length}条记录`)
          this.loggedInitializations.add(cacheKey)
        }

        return processedData
        
      } catch (error) {
        console.error(`❌ [COS数据] 尝试 ${attempt} 失败:`, error.message)
        
        if (attempt === maxRetries) {
          console.error(`💥 [COS数据] 所有重试失败`)
          return []
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
      }
    }
  }
  
  /**
   * 📊 加载2025年实时1分钟数据
   */
  async load2025RealtimeMinuteData(symbol, maxRetries = 3) {
    const cacheKey = `${symbol}_1m_2025_realtime`
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }
    
    try {
      const allData = []
      
      // 1. 加载历史月度数据 (1-6月)
      const historicalData = await this.loadHistorical2025MinuteData(symbol, maxRetries)
      if (historicalData.length > 0) {
        allData.push(...historicalData)
      }
      
      // 2. 加载当日实时数据
      const todayData = await this.loadTodayRealtimeData(symbol)
      if (todayData.length > 0) {
        allData.push(...todayData)
      }
      
      // 3. 去重并排序
      const uniqueData = this.deduplicateByTimestamp(allData)
      uniqueData.sort((a, b) => a.timestamp - b.timestamp)
      
      // 缓存数据
      this.cache.set(cacheKey, uniqueData)
      
      console.log(`📊 [2025实时] ${symbol} 1m: ${uniqueData.length}条记录`)
      
      return uniqueData
      
    } catch (error) {
      console.error(`❌ [2025实时] ${symbol} 1m 加载失败:`, error.message)
      return []
    }
  }
  
  /**
   * 📅 加载当日实时数据
   */
  async loadTodayRealtimeData(symbol) {
    try {
      const today = new Date()
      const todayPath = this.generateTodayRealtimePath(symbol, today)
      
      const jsonData = await this.loadDataWithStream(todayPath)
      const klineData = jsonData.data || []
      
      if (klineData.length > 0) {
        console.log(`📅 [当日实时] ${symbol}: ${klineData.length}条记录`)
        return this.processKlineData(klineData)
      }
      
      return []
      
    } catch (error) {
      console.log(`⚠️ [当日实时] ${symbol} 暂无数据: ${error.message}`)
      return []
    }
  }
  
  /**
   * 🔄 按时间戳去重
   */
  deduplicateByTimestamp(data) {
    const uniqueMap = new Map()
    
    data.forEach(item => {
      uniqueMap.set(item.timestamp, item)
    })
    
    return Array.from(uniqueMap.values())
  }
  
  /**
   * 🔄 处理K线数据格式
   */
  processKlineData(klineData) {
    return klineData.map(item => {
      if (Array.isArray(item)) {
        const rawTimestamp = item[0]
        const timestamp = rawTimestamp > 1e15 ? Math.floor(rawTimestamp / 1000) : rawTimestamp
        
        return {
          timestamp: timestamp,
          open: parseFloat(item[1]),
          high: parseFloat(item[2]),
          low: parseFloat(item[3]),
          close: parseFloat(item[4]),
          volume: parseFloat(item[5]),
          turnover: parseFloat(item[1]) * parseFloat(item[5])
        }
      } else {
        const rawTimestamp = item.timestamp
        const timestamp = rawTimestamp > 1e15 ? Math.floor(rawTimestamp / 1000) : rawTimestamp
        
        return {
          timestamp: timestamp,
          open: parseFloat(item.open),
          high: parseFloat(item.high),
          low: parseFloat(item.low),
          close: parseFloat(item.close),
          volume: parseFloat(item.volume),
          turnover: item.turnover || (parseFloat(item.open) * parseFloat(item.volume))
        }
      }
    })
  }
  
  // 继承其他必要方法（保持兼容性）...
  // loadHistorical2025MinuteData, load2025StandardData, 等等
  
  /**
   * 🗑️ 清除缓存
   */
  clearCache() {
    this.cache.clear()
    this.loggedInitializations.clear()
    this.lastUpdateTime.clear()
    console.log('🗑️ [纯COS] 缓存已清除')
  }
  
  /**
   * 📊 获取服务状态
   */
  getStatus() {
    return {
      mode: 'Pure COS',
      cacheSize: this.cache.size,
      refreshInterval: this.refreshInterval,
      lastUpdateTimes: Object.fromEntries(this.lastUpdateTime),
      monitoredSources: Array.from(this.loggedInitializations)
    }
  }
}

// 导出单例
const pureCOSDataService = new PureCOSDataService()
export default pureCOSDataService