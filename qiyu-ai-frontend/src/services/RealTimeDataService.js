// 实时数据服务 - 集成历史数据和WebSocket实时数据
class RealTimeDataService {
  constructor() {
    // 历史数据服务
    this.historicalService = null // 延迟初始化，避免循环依赖
    
    // 实时数据缓存
    this.realtimeCache = new Map()
    this.lastRealtimeUpdate = new Map()
    
    // WebSocket连接
    this.ws = null
    this.wsReconnectDelay = 1000
    this.wsMaxReconnectDelay = 30000
    this.wsReconnectAttempts = 0
    
    // 数据订阅回调
    this.dataCallbacks = new Map()
    
    // 实时数据COS路径
    this.realtimeBasePath = 'https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/realtime'
    
    console.log('🔄 实时数据服务初始化')
  }
  
  // 延迟初始化历史数据服务（避免循环依赖）
  getHistoricalService() {
    if (!this.historicalService) {
      // 动态导入避免循环依赖
      const StreamDataService = require('./StreamDataService').default || require('./StreamDataService')
      this.historicalService = new StreamDataService()
    }
    return this.historicalService
  }
  
  /**
   * 获取混合数据：历史数据 + 实时数据
   * @param {string} symbol - 交易对
   * @param {string} timeframe - 时间周期
   * @param {string} year - 年份
   * @returns {Promise<Object>} 包含历史和实时数据的对象
   */
  async getMixedKlineData(symbol, timeframe, year) {
    console.log(`🔄 获取混合数据: ${symbol} ${timeframe} ${year}`)
    
    try {
      // 1. 获取历史数据
      const historicalData = await this.getHistoricalService().loadYearData(symbol, timeframe, year)
      
      // 2. 如果是当前年份(2025)，尝试添加实时数据
      if (year === '2025') {
        const realtimeData = await this.loadRealtimeData(symbol, timeframe)
        
        if (realtimeData && realtimeData.length > 0) {
          console.log(`📡 找到实时数据: ${realtimeData.length} 条记录`)
          return this.mergeHistoricalAndRealtime(historicalData, realtimeData)
        }
      }
      
      return historicalData
      
    } catch (error) {
      console.error(`❌ 获取混合数据失败: ${symbol} ${timeframe} ${year}`, error)
      // 如果出错，至少返回历史数据
      return await this.getHistoricalService().loadYearData(symbol, timeframe, year)
    }
  }
  
  /**
   * 加载实时数据
   * @param {string} symbol - 交易对
   * @param {string} timeframe - 时间周期
   * @returns {Promise<Array>} 实时K线数据数组
   */
  async loadRealtimeData(symbol, timeframe) {
    const cacheKey = `${symbol}_${timeframe}`
    
    // 检查缓存（5分钟内的数据直接使用缓存）
    const lastUpdate = this.lastRealtimeUpdate.get(cacheKey)
    const now = Date.now()
    
    if (lastUpdate && (now - lastUpdate) < 5 * 60 * 1000) {
      console.log(`📋 使用缓存的实时数据: ${symbol} ${timeframe}`)
      return this.realtimeCache.get(cacheKey) || []
    }
    
    try {
      // 构建实时数据URL
      const dateStr = new Date().toISOString().split('T')[0].replace(/-/g, '')
      const url = `${this.realtimeBasePath}/${symbol}/${timeframe}/${symbol}_${timeframe}_${dateStr}_realtime.json`
      
      console.log(`📡 加载实时数据: ${url}`)
      
      const response = await fetch(url)
      
      if (!response.ok) {
        if (response.status === 404) {
          console.log(`ℹ️ 暂无实时数据: ${symbol} ${timeframe}`)
          return []
        }
        throw new Error(`HTTP ${response.status}`)
      }
      
      // 处理压缩数据
      const arrayBuffer = await response.arrayBuffer()
      const uint8Array = new Uint8Array(arrayBuffer)
      
      // 检查是否为gzip压缩
      const isGzipped = uint8Array[0] === 0x1f && uint8Array[1] === 0x8b
      
      let jsonStr
      if (isGzipped) {
        // 使用DecompressionStream解压
        const ds = new DecompressionStream('gzip')
        const decompressedStream = new Response(uint8Array).body.pipeThrough(ds)
        const decompressedArrayBuffer = await new Response(decompressedStream).arrayBuffer()
        jsonStr = new TextDecoder().decode(decompressedArrayBuffer)
      } else {
        jsonStr = new TextDecoder().decode(uint8Array)
      }
      
      const data = JSON.parse(jsonStr)
      const klineData = data.data || []
      
      // 更新缓存
      this.realtimeCache.set(cacheKey, klineData)
      this.lastRealtimeUpdate.set(cacheKey, now)
      
      console.log(`✅ 实时数据加载成功: ${symbol} ${timeframe} - ${klineData.length} 条记录`)
      
      return klineData
      
    } catch (error) {
      console.error(`❌ 加载实时数据失败: ${symbol} ${timeframe}`, error)
      return []
    }
  }
  
  /**
   * 合并历史数据和实时数据
   * @param {Object} historicalData - 历史数据对象
   * @param {Array} realtimeData - 实时数据数组
   * @returns {Object} 合并后的数据对象
   */
  mergeHistoricalAndRealtime(historicalData, realtimeData) {
    try {
      if (!historicalData || !historicalData.data || !Array.isArray(historicalData.data)) {
        console.warn('⚠️ 历史数据格式异常，只返回实时数据')
        return {
          metadata: {
            ...historicalData?.metadata,
            has_realtime: true,
            realtime_records: realtimeData.length
          },
          data: realtimeData
        }
      }
      
      const historicalKlines = historicalData.data
      const combinedKlines = [...historicalKlines]
      
      // 获取历史数据的最后时间戳
      const lastHistoricalTime = historicalKlines.length > 0 
        ? historicalKlines[historicalKlines.length - 1][0] 
        : 0
      
      // 添加比历史数据更新的实时数据
      let addedCount = 0
      realtimeData.forEach(realtimeKline => {
        const realtimeTime = realtimeKline[0]
        
        // 只添加比历史数据更新的数据
        if (realtimeTime > lastHistoricalTime) {
          // 检查是否已存在相同时间戳的数据
          const existingIndex = combinedKlines.findIndex(k => k[0] === realtimeTime)
          if (existingIndex >= 0) {
            // 更新现有数据（实时数据优先）
            combinedKlines[existingIndex] = realtimeKline
          } else {
            // 添加新数据
            combinedKlines.push(realtimeKline)
            addedCount++
          }
        }
      })
      
      // 按时间排序
      combinedKlines.sort((a, b) => a[0] - b[0])
      
      console.log(`🔀 数据合并完成: 历史${historicalKlines.length}条 + 实时${addedCount}条 = 总计${combinedKlines.length}条`)
      
      return {
        metadata: {
          ...historicalData.metadata,
          has_realtime: true,
          realtime_records: addedCount,
          total_records: combinedKlines.length,
          last_update: new Date().toISOString()
        },
        data: combinedKlines
      }
      
    } catch (error) {
      console.error('❌ 数据合并失败:', error)
      return historicalData
    }
  }
  
  /**
   * 连接WebSocket获取实时更新
   * @param {Array} symbols - 交易对列表
   * @param {Array} intervals - 时间周期列表
   */
  connectWebSocket(symbols, intervals) {
    console.log('⚠️ WebSocket连接已禁用，使用定时刷新模式');
    return;
    
    // 原WebSocket连接代码已禁用
    /*
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.log('⚠️ WebSocket已连接，跳过重复连接')
      return
    }
    
    const streams = []
    symbols.forEach(symbol => {
      intervals.forEach(interval => {
        streams.push(`${symbol.toLowerCase()}@kline_${interval}`)
      })
    })
    
    const wsUrl = `wss://stream.binance.com:9443/stream?streams=${streams.join('/')}`
    
    console.log(`📡 连接WebSocket: ${streams.length}个数据流`)
    
    this.ws = new WebSocket(wsUrl)
    
    this.ws.onopen = () => {
      console.log('✅ WebSocket连接成功')
      this.wsReconnectAttempts = 0
      this.wsReconnectDelay = 1000
    }
    
    this.ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data)
        this.handleWebSocketKlineData(message)
      } catch (error) {
        console.error('❌ WebSocket消息解析失败:', error)
      }
    }
    
    this.ws.onclose = (event) => {
      console.log(`⚠️ WebSocket连接关闭: ${event.code} - ${event.reason}`)
      this.scheduleWebSocketReconnect()
    }
    
    this.ws.onerror = (error) => {
      console.error('❌ WebSocket错误:', error)
    }
  }
  
  /**
   * 处理WebSocket K线数据
   */
  handleWebSocketKlineData(message) {
    if (!message.data || message.data.e !== 'kline') {
      return
    }
    
    const klineData = message.data
    const symbol = klineData.s
    const interval = klineData.k.i
    const isComplete = klineData.k.x
    
    // 转换为标准格式
    const kline = [
      parseInt(klineData.k.t),      // timestamp
      parseFloat(klineData.k.o),    // open
      parseFloat(klineData.k.h),    // high
      parseFloat(klineData.k.l),    // low
      parseFloat(klineData.k.c),    // close
      parseFloat(klineData.k.v),    // volume
    ]
    
    // 更新实时缓存
    this.updateRealtimeCache(symbol, interval, kline)
    
    // 通知订阅者
    this.notifyDataSubscribers(symbol, interval, kline, isComplete)
  }
  
  /**
   * 更新实时缓存
   */
  updateRealtimeCache(symbol, interval, kline) {
    const cacheKey = `${symbol}_${interval}`
    const cache = this.realtimeCache.get(cacheKey) || []
    const timestamp = kline[0]
    
    // 查找是否已存在相同时间戳的K线
    const existingIndex = cache.findIndex(k => k[0] === timestamp)
    
    if (existingIndex >= 0) {
      // 更新现有K线
      cache[existingIndex] = kline
    } else {
      // 添加新K线
      cache.push(kline)
      
      // 保持缓存大小
      if (cache.length > 200) {
        cache.sort((a, b) => a[0] - b[0])
        cache.splice(0, cache.length - 200)
      }
    }
    
    this.realtimeCache.set(cacheKey, cache)
    this.lastRealtimeUpdate.set(cacheKey, Date.now())
  }
  
  /**
   * 订阅数据更新
   * @param {string} symbol - 交易对
   * @param {string} interval - 时间周期
   * @param {Function} callback - 回调函数
   */
  subscribeToUpdates(symbol, interval, callback) {
    const key = `${symbol}_${interval}`
    const callbacks = this.dataCallbacks.get(key) || []
    callbacks.push(callback)
    this.dataCallbacks.set(key, callbacks)
    
    console.log(`📺 订阅数据更新: ${key}`)
  }
  
  /**
   * 取消订阅
   */
  unsubscribeFromUpdates(symbol, interval, callback) {
    const key = `${symbol}_${interval}`
    const callbacks = this.dataCallbacks.get(key) || []
    const index = callbacks.indexOf(callback)
    if (index >= 0) {
      callbacks.splice(index, 1)
      this.dataCallbacks.set(key, callbacks)
    }
  }
  
  /**
   * 通知数据订阅者
   */
  notifyDataSubscribers(symbol, interval, kline, isComplete) {
    const key = `${symbol}_${interval}`
    const callbacks = this.dataCallbacks.get(key) || []
    
    callbacks.forEach(callback => {
      try {
        callback({ symbol, interval, kline, isComplete })
      } catch (error) {
        console.error('❌ 数据订阅回调失败:', error)
      }
    })
  }
  
  /**
   * WebSocket重连
   */
  scheduleWebSocketReconnect() {
    this.wsReconnectAttempts++
    const delay = Math.min(this.wsReconnectDelay * Math.pow(2, this.wsReconnectAttempts), this.wsMaxReconnectDelay)
    
    console.log(`🔄 WebSocket将在 ${delay}ms 后重连 (第${this.wsReconnectAttempts}次)`)
    
    setTimeout(() => {
      // 重新连接，使用默认参数
      const symbols = ['BTCUSDT', 'ETHUSDT', 'LTCUSDT', 'XRPUSDT', 'DOGEUSDT', 'SOLUSDT', 'TRUMPUSDT']
      const intervals = ['1m', '15m', '1h', '4h', '1d']
      this.connectWebSocket(symbols, intervals)
    }, delay)
  }
  
  /**
   * 清理缓存
   */
  clearCache() {
    console.log('🗑️ 清理实时数据缓存')
    this.realtimeCache.clear()
    this.lastRealtimeUpdate.clear()
  }
  
  /**
   * 获取服务状态
   */
  getStatus() {
    return {
      wsConnected: this.ws && this.ws.readyState === WebSocket.OPEN,
      cacheSize: this.realtimeCache.size,
      totalCachedRecords: Array.from(this.realtimeCache.values()).reduce((sum, cache) => sum + cache.length, 0),
      subscriptions: this.dataCallbacks.size
    }
  }
  
  /**
   * 断开连接
   */
  disconnect() {
    console.log('🛑 关闭实时数据服务')
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    this.clearCache()
  }
}

// 创建单例
const realTimeDataService = new RealTimeDataService()

export default realTimeDataService