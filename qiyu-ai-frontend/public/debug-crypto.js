/**
 * 临时调试脚本 - 直接在浏览器控制台粘贴使用
 * 用于测试多时间周期数据修复效果
 */

// 创建临时的debugCrypto对象
window.tempDebugCrypto = {
  // 清理缓存并刷新图表
  async refreshCharts() {
    try {
      console.log('🗑️ 正在清理所有数据缓存...')
      
      // 清理localStorage
      const keys = Object.keys(localStorage)
      const cryptoKeys = keys.filter(key => 
        key.includes('crypto') || 
        key.includes('BTCUSDT') || 
        key.includes('kline') ||
        key.includes('stream')
      )
      
      cryptoKeys.forEach(key => {
        localStorage.removeItem(key)
        console.log(`🗑️ 已清理: ${key}`)
      })
      
      console.log('🔄 触发页面刷新以重新加载数据...')
      window.location.reload()
      
    } catch (error) {
      console.error('❌ 缓存清理失败:', error)
    }
  },

  // 测试特定URL是否可访问
  async testUrl(url) {
    try {
      console.log(`🧪 测试URL访问: ${url}`)
      const response = await fetch(url)
      
      if (response.ok) {
        const data = await response.json()
        console.log(`✅ URL可访问 (${response.status})`)
        console.log(`📊 数据条数: ${data.data ? data.data.length : '未知'}`)
        return data
      } else {
        console.log(`❌ URL不可访问 (${response.status})`)
        return null
      }
    } catch (error) {
      console.error(`❌ 测试失败:`, error)
      return null
    }
  },

  // 测试多时间周期数据访问
  async testTimeframeAccess(symbol = 'BTCUSDT', date = '07-30') {
    const baseUrl = 'https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/20250726'
    const timeframes = ['15m', '1h', '4h', '1d']
    
    console.log(`🧪 测试多时间周期数据访问: ${symbol} ${date}`)
    
    for (const tf of timeframes) {
      const url = `${baseUrl}/${symbol}/${tf}/daily/${symbol}_${tf}_${date}_2025_compressed.json`
      console.log(`\n⏰ 测试 ${tf}:`)
      await this.testUrl(url)
    }
  },

  // 显示使用说明
  help() {
    console.log(`
🔧 临时调试工具使用说明:
================================

1. 清理缓存并刷新页面:
   tempDebugCrypto.refreshCharts()

2. 测试多时间周期数据访问:
   tempDebugCrypto.testTimeframeAccess('BTCUSDT', '07-30')

3. 测试单个URL:
   tempDebugCrypto.testUrl('https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/20250726/BTCUSDT/1h/daily/BTCUSDT_1h_07-30_2025_compressed.json')

4. 显示帮助:
   tempDebugCrypto.help()

💡 修复完成后，前端应该能加载7月16日之后的多时间周期数据
    `)
  }
}

// 显示启用消息
console.log('🚀 临时调试工具已加载!')
console.log('📖 输入 tempDebugCrypto.help() 查看使用说明')

// 自动运行帮助
tempDebugCrypto.help()