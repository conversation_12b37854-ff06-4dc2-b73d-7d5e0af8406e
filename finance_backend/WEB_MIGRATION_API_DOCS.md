# Web端迁移API文档

## 概述

本文档描述了从微信小程序迁移到Web端所需的API调用规范和业务逻辑。后端API保持不变，主要差异在于前端实现方式。

## 核心API接口

### 1. AI分析接口

- **地址**: `https://weilaigongfang.com/api/ai/chat/`
- **方法**: `POST`
- **超时**: 600秒（10分钟）
- **认证**: JWT Bearer Token

**请求格式**:

```json
{
  "messages": [
    {
      "role": "user", 
      "content": "分析请求内容"
    }
  ],
  "cryptoData": {
    "dailyKlines": [],
    "shortTermKlines": [],
    "ticker": {},
    "orderbook": {},
    "btc_auxiliary_data": {},
    "crypto_context": "格式化的分析上下文"
  },
  "period": "分析周期",
  "stream": true,
  "max_tokens": 32768
}
```

**响应格式** (流式):

```
data: {"choices":[{"delta":{"content":"分析内容片段"}}]}

data: [DONE]
```

### 2. 币安数据代理接口

#### K线数据

- **地址**: `https://weilaigongfang.com/api/ai/quotation/binance/api/v3/klines`
- **方法**: `GET`
- **参数**: `symbol`, `interval`, `limit`

#### 实时价格数据

- **地址**: `https://weilaigongfang.com/api/ai/quotation/binance/api/v3/ticker/24hr`
- **方法**: `GET`
- **参数**: `symbol`

#### 订单簿数据

- **地址**: `https://weilaigongfang.com/api/ai/quotation/binance/api/v3/depth`
- **方法**: `GET`
- **参数**: `symbol`, `limit=20`

### 3. 用户认证接口

- **登录**: `POST /api/user/login/`
- **令牌验证**: `POST /api/user/verify-token/`
- **用户信息**: `GET /api/user/profile/`

## 数据获取流程

### 主要分析流程

1. **获取K线数据** → `fetchKlineData(symbol, analysisType)`
2. **获取实时价格** → `fetchTickerData(symbol)`
3. **获取订单簿** → `fetchOrderbookData(symbol, 20)`
4. **获取BTC辅助数据** (非BTC币种) → `fetchBTCAuxiliaryData(analysisType)`
5. **处理分析数据** → `processAnalysisData(...)`
6. **发送AI分析** → `streamChat(...)`

### BTC辅助数据获取

```javascript
// 非BTC币种分析时同时获取BTC数据作为市场参考
if (mainSymbol !== 'BTC') {
  const btcData = {
    klineData: await fetchKlineData('BTC', analysisType),
    tickerData: await fetchTickerData('BTC'), 
    orderbookData: await fetchOrderbookData('BTC', 20)
  };
}
```

## 关键配置参数

### API配置

```javascript
const API_CONFIG = {
  BASE_URL: 'https://weilaigongfang.com',
  PROXY_URL: 'https://weilaigongfang.com/api/ai/quotation',
  TIMEOUT: 600000, // 10分钟
  MAX_TOKENS: 32768
};
```

### 币种映射

```javascript
const COIN_INFO = {
  'BTCUSDT': { name: 'Bitcoin', symbol: 'BTC' },
  'ETHUSDT': { name: 'Ethereum', symbol: 'ETH' },
  // ... 其他币种
};
```

### 分析类型

```javascript
const ANALYSIS_TYPES = [
  { value: 'long_term', label: '长期分析' },
  { value: 'week', label: '一周分析' },
  { value: 'three_day', label: '三日分析' },
  { value: 'one_day', label: '一日分析' },
  { value: 'four_hour', label: '闪电分析' }
];
```

## 数据处理规范

### K线数据格式化

```javascript
const formatted_klines = klines.map(item => ({
  date: new Date(item[0]).toISOString().split('T')[0],
  open: String(item[1] || "0"),
  high: String(item[2] || "0"),
  low: String(item[3] || "0"),
  close: String(item[4] || "0")
}));
```

### 实时价格格式化

```javascript
const real_time_price = {
  symbol: `${symbol}USDT`,
  price: String(tickerData.lastPrice || "0"),
  change_percent: String(tickerData.priceChangePercent || "0"),
  volume: String(tickerData.volume || "0"),
  time: String(tickerData.timestamp || new Date().toISOString())
};
```

### 上下文构建

```javascript
const crypto_context = `
以下是最近的加密货币价格报告, 请在回答中参考这些数据:
实时价格: ${JSON.stringify(real_time_price, null, 2)}
近90天K线数据: ${JSON.stringify(formatted_daily_klines, null, 2)}
${symbol}订单簿数据(20档深度): ${JSON.stringify(orderbookData, null, 2)}
${btcData ? `BTC辅助数据(作为市场参考): ${JSON.stringify(btcFormatted, null, 2)}` : ''}
${btcData?.orderbookData ? `BTC订单簿数据(20档深度): ${JSON.stringify(btcData.orderbookData, null, 2)}` : ''}
请基于以上数据进行分析。
`;
```

## 小程序与Web端差异

### HTTP请求

- **小程序**: `wx.request()`
- **Web**: `fetch()` 或 `axios`

### 数据存储

- **小程序**: `wx.setStorageSync()`, `wx.getStorageSync()`
- **Web**: `localStorage`, `sessionStorage`

### 认证头设置

```javascript
// Web端请求头
const headers = {
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json',
  'Accept': 'application/json'
};
```

### 流式响应处理

```javascript
// Web端流式响应处理示例
const response = await fetch(url, {
  method: 'POST',
  headers,
  body: JSON.stringify(requestData)
});

const reader = response.body.getReader();
const decoder = new TextDecoder();

while (true) {
  const { done, value } = await reader.read();
  if (done) break;
  
  const chunk = decoder.decode(value);
  const lines = chunk.split('\n');
  
  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const data = line.slice(6);
      if (data === '[DONE]') return;
    
      try {
        const parsed = JSON.parse(data);
        const content = parsed.choices?.[0]?.delta?.content;
        if (content) {
          // 处理内容片段 (无需打字机效果，直接渲染)
          updateContent(content);
        }
      } catch (e) {
        console.warn('解析数据失败:', e);
      }
    }
  }
}
```

## 错误处理

### API错误处理

```javascript
try {
  const response = await fetch(url, options);
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
  return await response.json();
} catch (error) {
  console.error('API请求失败:', error);
  // 实现重试逻辑或降级处理
}
```

### 数据获取降级策略

1. 主API失败 → 备用API
2. 备用API失败 → 代理API
3. 全部失败 → 显示错误信息

## 性能优化建议

1. **并发请求**: 同时获取K线、价格、订单簿数据
2. **数据缓存**: 缓存不常变化的配置和币种信息
3. **请求去重**: 避免重复请求相同数据
4. **超时控制**: 设置合理的请求超时时间
5. **错误边界**: 实现完善的错误处理和用户提示

## 测试要点

1. **流式响应**: 确保正确处理分块数据
2. **BTC辅助数据**: 验证非BTC币种分析时包含BTC数据
3. **订单簿数据**: 确认20档深度数据正确传递
4. **错误恢复**: 测试API失败时的降级处理
5. **认证流程**: 验证JWT token的正确使用

## 部署注意事项

1. **CORS设置**: 确保后端允许Web端域名的跨域请求
2. **HTTPS**: 生产环境必须使用HTTPS
3. **环境变量**: 将API密钥等敏感信息存储在环境变量中
4. **CDN**: 考虑使用CDN加速静态资源加载

---

**联系方式**: 如有技术问题请联系后端团队进行确认和调试。
