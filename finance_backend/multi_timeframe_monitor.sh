#!/bin/bash

# 多时间周期数据自动监控和修复脚本
# 检查各时间周期数据完整性，自动修复严重缺失

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

LOG_FILE="logs/multi_timeframe_monitor.log"
REPORT_DIR="logs/multi_timeframe_reports"

# 确保目录存在
mkdir -p "$REPORT_DIR"

# 写日志函数
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log_message "🔍 开始多时间周期数据监控..."

# 检查最近7天的数据完整性
START_DATE=$(date -d '7 days ago' '+%Y-%m-%d')
END_DATE=$(date '+%Y-%m-%d')
REPORT_FILE="$REPORT_DIR/monitor_$(date '+%Y%m%d_%H%M%S').txt"

log_message "📅 检查日期范围: $START_DATE ~ $END_DATE"

# 执行多时间周期完整性检查
python3 check_multi_timeframe_integrity.py \
    --start-date="$START_DATE" \
    --end-date="$END_DATE" \
    --output="$REPORT_FILE" >> "$LOG_FILE" 2>&1

EXIT_CODE=$?

if [ $EXIT_CODE -eq 0 ]; then
    log_message "✅ 多时间周期完整性检查完成"
else
    log_message "⚠️ 多时间周期完整性检查发现问题"
fi

# 分析检查结果
if [ -f "$REPORT_FILE" ]; then
    # 提取各时间周期覆盖率
    COVERAGE_1M=$(grep -E "1m.*:" "$REPORT_FILE" | grep -o '[0-9.]*%' | head -1 | sed 's/%//')
    COVERAGE_15M=$(grep -E "15m.*:" "$REPORT_FILE" | grep -o '[0-9.]*%' | head -1 | sed 's/%//')
    COVERAGE_1H=$(grep -E "1h.*:" "$REPORT_FILE" | grep -o '[0-9.]*%' | head -1 | sed 's/%//')
    COVERAGE_4H=$(grep -E "4h.*:" "$REPORT_FILE" | grep -o '[0-9.]*%' | head -1 | sed 's/%//')
    COVERAGE_1D=$(grep -E "1d.*:" "$REPORT_FILE" | grep -o '[0-9.]*%' | head -1 | sed 's/%//')
    
    log_message "📊 时间周期覆盖率:"
    log_message "  1m: ${COVERAGE_1M:-未知}%"
    log_message "  15m: ${COVERAGE_15M:-未知}%"
    log_message "  1h: ${COVERAGE_1H:-未知}%"
    log_message "  4h: ${COVERAGE_4H:-未知}%"
    log_message "  1d: ${COVERAGE_1D:-未知}%"
    
    # 检查是否需要自动修复
    NEED_REPAIR=false
    
    # 如果任何非1分钟时间周期覆盖率低于50%，触发自动修复
    for coverage in "$COVERAGE_15M" "$COVERAGE_1H" "$COVERAGE_4H" "$COVERAGE_1D"; do
        if [ -n "$coverage" ] && (( $(echo "$coverage < 50" | bc -l) )); then
            NEED_REPAIR=true
            break
        fi
    done
    
    if [ "$NEED_REPAIR" = true ]; then
        log_message "🚨 检测到严重的多时间周期数据缺失，启动自动修复"
        
        # 只修复最近3天的数据（避免过度负载）
        REPAIR_START=$(date -d '3 days ago' '+%Y-%m-%d')
        REPAIR_END=$(date '+%Y-%m-%d')
        
        log_message "🔧 开始自动修复: $REPAIR_START ~ $REPAIR_END"
        
        # 逐个时间周期修复
        for timeframe in "15m" "1h" "4h" "1d"; do
            for symbol in "BTCUSDT" "ETHUSDT" "SOLUSDT" "DOGEUSDT" "XRPUSDT"; do
                log_message "  修复 $symbol $timeframe"
                
                if timeout 600 python3 manage.py repair_multi_timeframe_data \
                    --symbol="$symbol" \
                    --timeframe="$timeframe" \
                    --start="$REPAIR_START" \
                    --end="$REPAIR_END" >> "$LOG_FILE" 2>&1; then
                    log_message "  ✅ $symbol $timeframe 修复成功"
                else
                    log_message "  ❌ $symbol $timeframe 修复失败"
                fi
                
                # 避免过载
                sleep 2
            done
        done
        
        log_message "🎯 自动修复完成"
    else
        log_message "✅ 多时间周期数据质量良好，无需自动修复"
    fi
else
    log_message "❌ 完整性检查报告生成失败"
fi

# 清理旧报告（保留最近20个）
find "$REPORT_DIR" -name "monitor_*.txt" -type f | sort -r | tail -n +21 | xargs rm -f 2>/dev/null

log_message "🏁 多时间周期监控完成"
log_message "📄 报告文件: $REPORT_FILE"
