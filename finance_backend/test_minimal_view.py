#!/usr/bin/env python3
"""
最小化测试视图 - 用于测试导入问题
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'finance_project.settings')
django.setup()

# 测试各种导入
print("🔍 测试导入...")

try:
    from ai_analysis.views import ChatAPIView
    print("✅ ChatAPIView导入成功")
except Exception as e:
    print(f"❌ ChatAPIView导入失败: {e}")
    import traceback
    traceback.print_exc()

try:
    from historical_data.timestamp_analysis_service import TimestampAnalysisService
    print("✅ TimestampAnalysisService导入成功")
except Exception as e:
    print(f"❌ TimestampAnalysisService导入失败: {e}")
    import traceback
    traceback.print_exc()

try:
    # 测试实例化
    service = TimestampAnalysisService()
    print("✅ TimestampAnalysisService实例化成功")
except Exception as e:
    print(f"❌ TimestampAnalysisService实例化失败: {e}")
    import traceback
    traceback.print_exc()

try:
    # 测试ChatAPIView中的数据转换方法
    view = ChatAPIView()
    print("✅ ChatAPIView实例化成功")
    
    # 测试数据转换方法
    test_data = {
        'daily_data': {
            'test_day': {
                'klines': [
                    {
                        'open_time': 1753718400000,
                        'open': 100.0,
                        'high': 105.0,
                        'low': 95.0,
                        'close': 102.0,
                        'volume': 1000.0
                    }
                ]
            }
        },
        'symbol': 'BTCUSDT',
        'target_timestamp': 1753718400000
    }
    
    result = view._convert_timestamp_data_to_crypto_data(test_data)
    if result:
        print("✅ 数据转换方法测试成功")
    else:
        print("❌ 数据转换方法返回None")
        
except Exception as e:
    print(f"❌ ChatAPIView测试失败: {e}")
    import traceback
    traceback.print_exc()

print("🏁 测试完成")