/* 底部样式 */
.qiyu-footer {
  position: relative;
  width: 100%;
  height: 75px;
  margin-top: auto;
}

.footer-bg {
  position: absolute;
  width: 100%;
  height: 99px;
  left: 0px;
  top: 17px;
  background: var(--footer-gradient);
  border-top: 1px solid #BCBCBC;
  transition: background 0.5s ease;
}

/* Logo 区域 */
.footer-logo {
  position: absolute;
  width: 82px;
  height: 82px;
  left: 62px;
  top: 17px;
}

.footer-logo-image {
  position: absolute;
  left: 0%;
  right: 0%;
  top: 0%;
  bottom: 0%;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 版权信息 */
.footer-copyright {
  position: absolute;
  width: 285px;
  height: 22px;
  left: 205px;
  top: 52px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 22px;
  display: flex;
  align-items: center;
  color: #FFFFFF;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 公司链接 */
.footer-company {
  position: absolute;
  width: 372px;
  height: 22px;
  left: 505px;
  top: 52px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0px;
  gap: 32px;
}

.footer-link {
  width: 72px;
  height: 22px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 22px;
  color: #FFFFFF;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: none;
  flex-grow: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer-link:hover {
  color: rgba(255, 255, 255, 0.8);
}

/* 联系方式文字 */
.footer-contact-text {
  position: absolute;
  width: 471px;
  height: 57px;
  left: 848px;
  top: 30px;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 57px;
  text-align: center;
  color: #FFFFFF;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 微信图标 */
.footer-wechat {
  position: absolute;
  width: 42px;
  height: 37px;
  left: 1334px;
  top: 46px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px;
  gap: 10px;
}

.wechat-icon {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 8px;
  gap: 10px;
  width: 42px;
  height: 37px;
  border: 1px solid #FFFFFF;
  border-radius: 9px;
  flex: none;
  order: 0;
  align-self: stretch;
  flex-grow: 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.wechat-icon:hover {
  background: rgba(255, 255, 255, 0.1);
}

.wechat-icon .anticon {
  width: 29px;
  height: 29px;
  color: #FFFFFF;
  font-size: 20px;
  flex: none;
  order: 0;
  flex-grow: 0;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .qiyu-footer {
    height: 70px; /* 约93% */
  }

  .footer-bg {
    height: 92px;
    top: 16px;
  }

  .footer-logo {
    width: 76px;
    height: 76px;
    left: 58px;
    top: 16px;
  }

  .footer-copyright {
    left: 190px;
    top: 48px;
    font-size: 17px;
    width: 270px;
  }

  .footer-company {
    left: 470px;
    top: 48px;
    gap: 28px;
    height: 22px;
  }

  .footer-contact-text {
    width: 440px;
    left: 790px;
    top: 28px;
    font-size: 18px;
  }

  .footer-wechat {
    left: 1240px;
    top: 43px;
  }
}

@media (max-width: 1200px) {
  .qiyu-footer {
    height: 65px; /* 约87% */
  }

  .footer-bg {
    height: 86px;
    top: 14px;
  }

  .footer-logo {
    width: 70px;
    height: 70px;
    left: 50px;
    top: 14px;
  }

  .footer-copyright {
    left: 170px;
    top: 44px;
    font-size: 16px;
    width: 240px;
  }

  .footer-company {
    left: 420px;
    top: 44px;
    gap: 24px;
    width: 320px;
    height: 22px;
  }

  .footer-link {
    font-size: 16px;
  }

  .footer-contact-text {
    width: 380px;
    left: 720px;
    top: 24px;
    font-size: 16px;
  }

  .footer-wechat {
    left: 1120px;
    top: 38px;
  }
}

@media (max-width: 1024px) {
  .qiyu-footer {
    height: 60px; /* 约80% */
  }

  .footer-bg {
    height: 79px;
    top: 12px;
  }

  .footer-logo {
    width: 64px;
    height: 64px;
    left: 40px;
    top: 12px;
  }

  .footer-copyright {
    left: 140px;
    top: 38px;
    font-size: 14px;
    width: 210px;
  }

  .footer-company {
    left: 360px;
    top: 38px;
    gap: 20px;
    width: 280px;
    height: 22px;
  }

  .footer-link {
    font-size: 14px;
    width: 60px;
  }

  .footer-contact-text {
    width: 320px;
    left: 640px;
    top: 20px;
    font-size: 14px;
  }

  .footer-wechat {
    left: 980px;
    top: 32px;
    width: 36px;
    height: 32px;
  }

  .wechat-icon {
    width: 36px;
    height: 32px;
    padding: 6px;
  }

  .wechat-icon .anticon {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .qiyu-footer {
    height: 55px; /* 约73% */
  }

  .footer-bg {
    height: 72px;
    top: 10px;
  }

  .footer-logo {
    width: 56px;
    height: 56px;
    left: 30px;
    top: 10px;
  }

  .footer-copyright {
    left: 100px;
    top: 32px;
    font-size: 12px;
    width: 170px;
  }

  .footer-company {
    left: 280px;
    top: 32px;
    gap: 16px;
    width: 200px;
    height: 22px;
  }

  .footer-link {
    font-size: 12px;
    width: 50px;
  }

  .footer-contact-text {
    width: 240px;
    left: 480px;
    top: 16px;
    font-size: 12px;
  }

  .footer-wechat {
    left: 720px;
    top: 26px;
    width: 32px;
    height: 28px;
  }

  .wechat-icon {
    width: 32px;
    height: 28px;
    padding: 4px;
  }

  .wechat-icon .anticon {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .qiyu-footer {
    height: 45px; /* 约60% */
  }

  .footer-bg {
    height: 59px;
    top: 8px;
  }

  .footer-logo {
    width: 45px;
    height: 45px;
    left: 20px;
    top: 8px;
  }

  .footer-copyright {
    left: 80px;
    top: 26px;
    font-size: 10px;
    width: 130px;
  }

  .footer-company {
    left: 220px;
    top: 26px;
    gap: 12px;
    width: 140px;
    height: 22px;
  }

  .footer-link {
    font-size: 10px;
    width: 40px;
  }

  .footer-contact-text {
    width: 160px;
    left: 360px;
    top: 12px;
    font-size: 9px;
    line-height: 40px;
  }

  .footer-wechat {
    left: 520px;
    top: 20px;
    width: 26px;
    height: 22px;
  }

  .wechat-icon {
    width: 26px;
    height: 22px;
    padding: 3px;
  }

  .wechat-icon .anticon {
    font-size: 12px;
  }
}
