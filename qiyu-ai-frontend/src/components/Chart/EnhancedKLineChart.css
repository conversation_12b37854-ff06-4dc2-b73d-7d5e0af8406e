.enhanced-kline-chart {
  background-color: #1a1a1a;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 紧凑工具栏样式 - 年份和指标在同一行 */
.chart-toolbar-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px; /* 减少内边距 */
  background-color: #2a2a2a;
  border-bottom: 1px solid #444;
  min-height: 28px; /* 大幅减少高度 */
  font-size: 11px; /* 减小字体 */
}

/* 原工具栏样式保留（如果其他地方还在使用） */
.chart-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background-color: #2a2a2a;
  border-bottom: 1px solid #333;
  height: 40px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-title {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
}

.realtime-status {
  color: #26a641;
  font-size: 12px;
  padding: 2px 6px;
  background-color: rgba(38, 166, 65, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(38, 166, 65, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.toolbar-center {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

/* 紧凑工具栏各部分样式 */
.toolbar-left-compact {
  display: flex;
  align-items: center;
  gap: 6px;
}

.toolbar-center-compact {
  display: flex;
  align-items: center;
  gap: 4px;
}

.toolbar-right-compact {
  display: flex;
  align-items: center;
}

/* 年份徽章样式 */
.chart-year-badge {
  color: #fff;
  font-size: 12px;
  font-weight: 600;
  background: linear-gradient(135deg, #447A53, #26A69A);
  padding: 2px 8px;
  border-radius: 10px;
  border: 1px solid rgba(68, 122, 83, 0.3);
}

/* 紧凑实时状态 */
.realtime-status-compact {
  font-size: 10px;
  animation: pulse 2s infinite;
}

/* 图表容器样式 */
.chart-wrapper {
  position: relative;
  flex: 1;
}

.chart-container {
  width: 100%;
  height: 100%;
}

/* 加载状态样式 */
.chart-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: #fff;
  z-index: 1000;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #333;
  border-top: 3px solid #26A69A;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态样式 */
.chart-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ef5350;
  font-size: 14px;
  text-align: center;
  z-index: 1000;
}

/* 自定义Ant Design Switch在深色主题下的样式 */
.chart-toolbar .ant-switch,
.chart-toolbar-compact .ant-switch {
  background-color: #434343;
}

.chart-toolbar .ant-switch-checked,
.chart-toolbar-compact .ant-switch-checked {
  background-color: #26A69A;
}

.chart-toolbar .ant-switch-inner,
.chart-toolbar-compact .ant-switch-inner {
  color: #fff;
  font-size: 9px; /* 紧凑模式下更小的字体 */
}

/* 紧凑工具栏的Switch特殊样式 */
.chart-toolbar-compact .ant-switch {
  min-width: 36px; /* 减少Switch宽度 */
  height: 18px; /* 减少Switch高度 */
}

.chart-toolbar-compact .ant-switch-inner {
  font-size: 8px;
  line-height: 16px;
}

/* 自定义按钮样式 */
.chart-toolbar .ant-btn,
.chart-toolbar-compact .ant-btn {
  border: none;
  background: transparent;
}

.chart-toolbar .ant-btn:hover,
.chart-toolbar-compact .ant-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 紧凑工具栏按钮特殊样式 */
.chart-toolbar-compact .ant-btn {
  padding: 2px 4px;
  height: 20px;
  min-width: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-toolbar {
    flex-direction: column;
    height: auto;
    padding: 8px;
    gap: 8px;
  }

  .chart-toolbar-compact {
    flex-direction: column;
    height: auto;
    padding: 4px;
    gap: 4px;
    min-height: 36px;
  }

  .toolbar-left,
  .toolbar-center,
  .toolbar-right,
  .toolbar-left-compact,
  .toolbar-center-compact,
  .toolbar-right-compact {
    width: 100%;
    justify-content: center;
  }

  .toolbar-center,
  .toolbar-center-compact {
    flex-wrap: wrap;
  }

  .chart-year-badge {
    font-size: 11px;
    padding: 1px 6px;
  }
}