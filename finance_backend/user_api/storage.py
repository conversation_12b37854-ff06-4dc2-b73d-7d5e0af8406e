import os
import uuid
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.conf import settings

class FileStorage:
    """文件存储工具类"""
    
    @staticmethod
    def upload_file(file, directory='uploads'):
        """
        上传文件到腾讯云COS
        
        参数:
        - file: 文件对象
        - directory: 存储目录
        
        返回:
        - 文件URL
        """
        if not file:
            return None
        
        # 生成唯一文件名
        ext = os.path.splitext(file.name)[1]
        filename = f"{uuid.uuid4().hex}{ext}"
        
        # 构建存储路径
        path = f"{directory}/{filename}"
        
        # 保存文件
        default_storage.save(path, ContentFile(file.read()))
        
        # 获取文件URL
        file_url = default_storage.url(path)
        
        return file_url
    
    @staticmethod
    def upload_base64_image(base64_data, directory='uploads'):
        """
        上传Base64编码的图片
        
        参数:
        - base64_data: Base64编码的图片数据
        - directory: 存储目录
        
        返回:
        - 文件URL
        """
        import base64
        from io import BytesIO
        
        if not base64_data:
            return None
        
        # 去除Base64前缀
        if ',' in base64_data:
            base64_data = base64_data.split(',')[1]
        
        # 解码Base64数据
        image_data = base64.b64decode(base64_data)
        
        # 生成唯一文件名
        filename = f"{uuid.uuid4().hex}.png"
        
        # 构建存储路径
        path = f"{directory}/{filename}"
        
        # 保存文件
        default_storage.save(path, ContentFile(image_data))
        
        # 获取文件URL
        file_url = default_storage.url(path)
        
        return file_url
    
    @staticmethod
    def delete_file(file_url):
        """
        删除文件
        
        参数:
        - file_url: 文件URL
        
        返回:
        - 是否删除成功
        """
        if not file_url:
            return False
        
        try:
            # 从URL中提取路径
            path = file_url.split('/')[-1]
            
            # 删除文件
            default_storage.delete(path)
            
            return True
        except Exception as e:
            print(f"删除文件失败: {e}")
            return False 