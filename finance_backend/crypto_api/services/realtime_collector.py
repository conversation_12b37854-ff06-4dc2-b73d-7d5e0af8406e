"""
实时数据收集器
负责WebSocket连接、数据缓冲、COS上传和断点重续
"""

import asyncio
import websockets
import json
import threading
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from .cos_service import COSRealtimeService
from .gap_recovery import GapRecoveryService

logger = logging.getLogger(__name__)

class RealtimeDataCollector:
    """实时数据收集器"""
    
    def __init__(self, symbol='BTCUSDT', upload_interval=300, debug=False):
        self.symbol = symbol.upper()
        self.upload_interval = upload_interval  # 上传间隔（秒）
        self.debug = debug
        
        # WebSocket配置 - 使用多个节点提高稳定性
        self.websocket_urls = [
            'wss://stream.binance.com:9443/ws',
            'wss://stream.binance.com:443/ws',
            'wss://stream1.binance.com:9443/ws',
            'wss://stream2.binance.com:9443/ws'
        ]
        self.stream_name = f"{self.symbol.lower()}@kline_1m"
        self.current_url_index = 0
        
        # 数据缓冲区
        self.data_buffer = {}  # timestamp -> kline_data
        self.buffer_lock = threading.Lock()
        
        # 运行状态
        self.is_running = False
        self.websocket_task = None
        self.upload_task = None
        
        # 服务依赖
        self.cos_service = COSRealtimeService(debug=debug)
        self.gap_recovery = GapRecoveryService(debug=debug)
        
        logger.info(f"🏗️ 实时数据收集器初始化完成: {symbol}")
    
    def start(self):
        """启动数据收集"""
        logger.info(f"🚀 启动 {self.symbol} 实时数据收集...")
        
        # 1. 检查并修复数据缺口
        self.check_and_resume_gaps()
        
        # 2. 启动定时上传任务
        self.start_upload_scheduler()
        
        # 3. 启动WebSocket连接
        logger.info("🚀 启动WebSocket实时数据收集...")
        self.start_websocket_connection()
    
    def stop(self):
        """停止数据收集"""
        logger.info("⏹️ 正在停止数据收集器...")
        
        self.is_running = False
        
        # 停止WebSocket任务
        if self.websocket_task:
            self.websocket_task.cancel()
        
        # 最后一次上传缓冲区数据
        self.upload_buffer_to_cos()
        
        logger.info("✅ 数据收集器已停止")
    
    def check_and_resume_gaps(self):
        """🎯 断点重续：检查并填补数据缺口"""
        try:
            logger.info("🔍 开始检查数据缺口...")
            logger.info(f"📊 检查交易对: {self.symbol}")
            
            # 获取最后成功上传的时间
            logger.info("📋 获取COS中最后数据时间...")
            last_upload_time = self.cos_service.get_last_upload_time(self.symbol)
            current_time = datetime.now()
            logger.info(f"⏰ 当前时间: {current_time}")
            
            if last_upload_time:
                logger.info(f"📋 找到最后数据时间: {last_upload_time}")
                gap_duration = current_time - last_upload_time
                logger.info(f"⏰ 计算时间差: {gap_duration}")
                
                # 如果缺口超过5分钟，启动缺口修复
                if gap_duration.total_seconds() > 300:
                    gap_minutes = int(gap_duration.total_seconds() / 60)
                    logger.warning(f"⚠️ 发现数据缺口: {gap_duration} (约{gap_minutes}分钟)")
                    logger.info(f"🔧 开始缺口修复: {last_upload_time} → {current_time}")
                    logger.info(f"📊 预计需要补齐: ~{gap_minutes}条1分钟K线数据")
                    
                    success = self.gap_recovery.fill_gap(
                        symbol=self.symbol, 
                        start_time=last_upload_time,
                        end_time=current_time
                    )
                    if success:
                        logger.info("✅ 缺口修复完成，开始启动实时收集...")
                    else:
                        logger.error("❌ 缺口修复失败，但继续启动实时收集")
                else:
                    logger.info("✅ 数据连续，无需修复")
            else:
                logger.info("ℹ️ 首次运行，无历史数据")
                
        except Exception as e:
            logger.error(f"❌ 缺口检查失败: {e}")
    
    def start_upload_scheduler(self):
        """启动定时上传任务"""
        def upload_task():
            while self.is_running:
                time.sleep(self.upload_interval)
                if self.is_running:  # 双重检查，避免关闭时执行
                    self.upload_buffer_to_cos()
        
        self.upload_task = threading.Thread(target=upload_task, daemon=True)
        self.upload_task.start()
        
        logger.info(f"⏰ 定时上传任务已启动 (间隔: {self.upload_interval}秒)")
    
    def start_websocket_connection(self):
        """启动WebSocket连接"""
        self.is_running = True
        
        while self.is_running:
            try:
                # 运行WebSocket连接
                asyncio.run(self.run_websocket())
                
            except KeyboardInterrupt:
                logger.info("🛑 收到停止信号")
                break
                
            except Exception as e:
                logger.error(f"❌ WebSocket异常: {e}")
                if self.is_running:
                    logger.info("🔄 5秒后重新连接...")
                    time.sleep(5)
    
    async def run_websocket(self):
        """运行WebSocket连接 - 多节点重试"""
        # 🔄 轮换使用不同的WebSocket节点
        uri = f"{self.websocket_urls[self.current_url_index]}/{self.stream_name}"
        self.current_url_index = (self.current_url_index + 1) % len(self.websocket_urls)
        
        try:
            # 🔧 增加连接超时和ping配置
            connect_kwargs = {
                'uri': uri,
                'ping_interval': 20,  # 20秒ping间隔
                'ping_timeout': 10,   # 10秒ping超时
                'close_timeout': 10,  # 10秒关闭超时
                'open_timeout': 30,   # 30秒连接超时
                'max_size': 2**20,    # 1MB最大消息大小
            }
            
            logger.info(f"🔗 尝试连接WebSocket: {self.stream_name}")
            
            async with websockets.connect(**connect_kwargs) as websocket:
                logger.info(f"✅ WebSocket连接成功: {self.stream_name}")
                
                # 发送订阅消息
                subscribe_msg = {
                    "method": "SUBSCRIBE",
                    "params": [self.stream_name],
                    "id": 1
                }
                await websocket.send(json.dumps(subscribe_msg))
                logger.info(f"📡 已订阅数据流: {self.stream_name}")
                
                async for message in websocket:
                    if not self.is_running:
                        break
                        
                    try:
                        data = json.loads(message)
                        
                        # 跳过订阅确认消息
                        if 'result' in data or 'id' in data:
                            continue
                            
                        self.process_kline_data(data)
                        
                    except json.JSONDecodeError as e:
                        logger.error(f"❌ JSON解析失败: {e}")
                        continue
                        
        except websockets.exceptions.ConnectionClosed:
            logger.warning("⚠️ WebSocket连接关闭")
            
        except asyncio.TimeoutError:
            logger.error("❌ WebSocket连接超时")
            
        except Exception as e:
            logger.error(f"❌ WebSocket运行异常: {e}")
            raise
    
    def process_kline_data(self, data):
        """处理K线数据"""
        try:
            # 检查数据格式
            if 'k' not in data:
                return
                
            kline = data['k']
            
            # 只处理已确认的K线（isFinal=True）
            if not kline.get('x', False):
                return
            
            # 提取K线数据
            timestamp = int(kline['t'])  # 开盘时间
            open_price = float(kline['o'])
            high_price = float(kline['h'])
            low_price = float(kline['l'])
            close_price = float(kline['c'])
            volume = float(kline['v'])
            turnover = float(kline['q'])  # 成交额
            
            # 构建数据记录
            kline_record = {
                'timestamp': timestamp,
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': volume,
                'turnover': turnover
            }
            
            # 添加到缓冲区
            with self.buffer_lock:
                self.data_buffer[timestamp] = kline_record
            
            if self.debug:
                dt = datetime.fromtimestamp(timestamp / 1000)
                logger.debug(f"📊 收到K线数据: {dt.strftime('%H:%M:%S')} - {close_price}")
            
        except Exception as e:
            logger.error(f"❌ K线数据处理失败: {e}")
    
    def upload_buffer_to_cos(self):
        """上传缓冲区数据到COS - 改进的错误恢复机制"""
        if not self.data_buffer:
            logger.debug("📝 缓冲区为空，跳过上传")
            return
        
        try:
            # 🎯 修复：不立即清空缓冲区，等上传成功后再清空
            with self.buffer_lock:
                # 复制缓冲区数据，但保留原数据
                buffer_copy = dict(self.data_buffer)
            
            if not buffer_copy:
                return
            
            # 按时间戳排序
            sorted_data = sorted(buffer_copy.values(), key=lambda x: x['timestamp'])
            
            logger.info(f"📤 开始上传缓冲区数据: {len(sorted_data)} 条记录")
            
            # 上传到COS
            success = self.cos_service.upload_realtime_data(
                symbol=self.symbol,
                data=sorted_data
            )
            
            if success:
                # 🎯 修复：只有上传成功后才清空缓冲区
                with self.buffer_lock:
                    # 只清除已上传的数据（基于时间戳）
                    uploaded_timestamps = {item['timestamp'] for item in sorted_data}
                    self.data_buffer = {
                        ts: data for ts, data in self.data_buffer.items() 
                        if ts not in uploaded_timestamps
                    }
                    
                first_time = datetime.fromtimestamp(sorted_data[0]['timestamp'] / 1000)
                last_time = datetime.fromtimestamp(sorted_data[-1]['timestamp'] / 1000)
                logger.info(f"✅ COS上传成功: {first_time.strftime('%H:%M')} ~ {last_time.strftime('%H:%M')}")
                logger.info(f"🧹 缓冲区清理完成，剩余 {len(self.data_buffer)} 条未上传数据")
            else:
                logger.error("❌ COS上传失败，数据保留在缓冲区中")
                # 数据仍在缓冲区中，下次会重试
                        
        except Exception as e:
            logger.error(f"❌ 缓冲区上传异常: {e}")
            # 异常情况下数据仍在缓冲区中
    
    def get_status(self) -> Dict:
        """获取收集器状态"""
        with self.buffer_lock:
            buffer_size = len(self.data_buffer)
            
        return {
            'symbol': self.symbol,
            'is_running': self.is_running,
            'buffer_size': buffer_size,
            'upload_interval': self.upload_interval,
            'websocket_url': f"{self.websocket_url}/{self.stream_name}",
            'last_check': datetime.now().isoformat()
        }