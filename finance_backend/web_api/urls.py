from django.urls import path
from . import views, sms_views

urlpatterns = [
    # 用户认证API (原Supabase)
    path('auth/register/', views.register, name='web_register'),
    path('auth/login/', views.login, name='web_login'),
    path('auth/profile/', views.user_profile, name='web_user_profile'),
    
    # SMS认证API (新的腾讯云SMS)
    path('sms/send-code/', sms_views.send_sms_code, name='sms_send_code'),
    path('sms/register/', sms_views.sms_register, name='sms_register'),
    path('sms/login/', sms_views.sms_login, name='sms_login'),
    path('sms/password-login/', sms_views.password_login, name='sms_password_login'),
    path('sms/profile/', sms_views.user_profile, name='sms_user_profile'),
    
    # 代理和充值API
    path('purchase/', sms_views.purchase_credits, name='purchase_credits'),
    path('agent/dashboard/', sms_views.agent_dashboard, name='agent_dashboard'),
    path('credit/transactions/', sms_views.credit_transactions, name='credit_transactions'),

    # 配置API
    path('packages/', sms_views.get_package_configs, name='get_package_configs'),
    path('commission-configs/', sms_views.get_commission_configs, name='get_commission_configs'),
    
    # 历史数据API (Web专用)
    path('chart/', views.web_chart_data, name='web_chart_data'),
    path('indicators/', views.web_technical_indicators, name='web_technical_indicators'),
    path('symbols/', views.web_supported_symbols, name='web_supported_symbols'),
]