#!/bin/bash

# 设置定时监控任务
# 每3小时检查一次多收集器状态

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "🔧 设置定时监控任务..."

# 添加到crontab（每3小时运行一次，在：00:15, 03:15, 06:15, 09:15, 12:15, 15:15, 18:15, 21:15）
(crontab -l 2>/dev/null; echo "# 多收集器监控 - 每3小时检查一次") | crontab -
(crontab -l 2>/dev/null; echo "15 */3 * * * cd $SCRIPT_DIR && ./monitor_collector.sh") | crontab -

echo "✅ 定时任务已设置："
crontab -l | grep monitor_collector

echo ""
echo "📋 监控计划："
echo "- 每3小时检查一次 (00:15, 03:15, 06:15, 09:15, 12:15, 15:15, 18:15, 21:15)"
echo "- 检查进程状态、数据收集、上传活动"
echo "- 如果异常则自动重启"
echo "- 日志保存在: logs/monitor.log"
echo ""
echo "🛠️ 手动测试命令："
echo "cd $SCRIPT_DIR && ./monitor_collector.sh"