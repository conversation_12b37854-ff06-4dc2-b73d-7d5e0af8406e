# Generated manually for server-side persistence

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='RealTimeKlineData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('symbol', models.CharField(db_index=True, max_length=20)),
                ('timeframe', models.CharField(db_index=True, max_length=10)),
                ('timestamp', models.BigIntegerField(db_index=True)),
                ('open_price', models.DecimalField(decimal_places=8, max_digits=20)),
                ('high_price', models.DecimalField(decimal_places=8, max_digits=20)),
                ('low_price', models.DecimalField(decimal_places=8, max_digits=20)),
                ('close_price', models.DecimalField(decimal_places=8, max_digits=20)),
                ('volume', models.DecimalField(decimal_places=8, max_digits=20)),
                ('turnover', models.DecimalField(blank=True, decimal_places=8, max_digits=20, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('source', models.CharField(default='websocket', max_length=20)),
                ('uploaded_to_cos', models.BooleanField(default=False)),
            ],
            options={
                'db_table': 'realtime_kline_data',
                'ordering': ['timestamp'],
            },
        ),
        migrations.CreateModel(
            name='DataSyncStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('symbol', models.CharField(max_length=20)),
                ('timeframe', models.CharField(max_length=10)),
                ('last_sync_timestamp', models.BigIntegerField()),
                ('total_records', models.IntegerField(default=0)),
                ('last_cos_upload', models.DateTimeField(blank=True, null=True)),
                ('sync_errors', models.IntegerField(default=0)),
                ('last_error_message', models.TextField(blank=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'data_sync_status',
            },
        ),
        migrations.AddIndex(
            model_name='realtimeklinedata',
            index=models.Index(fields=['symbol', 'timeframe', 'timestamp'], name='realtime_kl_symbol_55a6c5_idx'),
        ),
        migrations.AddIndex(
            model_name='realtimeklinedata',
            index=models.Index(fields=['created_at'], name='realtime_kl_created_aba0d1_idx'),
        ),
        migrations.AddIndex(
            model_name='realtimeklinedata',
            index=models.Index(fields=['uploaded_to_cos'], name='realtime_kl_uploade_1baa12_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='realtimeklinedata',
            unique_together={('symbol', 'timeframe', 'timestamp')},
        ),
        migrations.AlterUniqueTogether(
            name='datasyncstatus',
            unique_together={('symbol', 'timeframe')},
        ),
    ]