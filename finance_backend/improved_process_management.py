#!/usr/bin/env python3
"""
改进的进程管理方案
结合systemd、健康检查和智能恢复机制
"""

import os
import psutil
import time
import logging
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Optional

class ProcessManager:
    """智能进程管理器"""
    
    def __init__(self, work_dir: str):
        self.work_dir = Path(work_dir)
        self.logger = self._setup_logger()
        self.pid_dir = self.work_dir / "pids"
        self.pid_dir.mkdir(exist_ok=True)
        
    def _setup_logger(self):
        logger = logging.getLogger('ProcessManager')
        handler = logging.FileHandler(self.work_dir / 'logs' / 'process_manager.log')
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
        return logger
    
    def find_process_by_pattern(self, pattern: str, exclude_current: bool = True) -> List[psutil.Process]:
        """安全地查找进程，避免误杀"""
        processes = []
        current_pid = os.getpid()
        
        for proc in psutil.process_iter(['pid', 'cmdline', 'create_time']):
            try:
                if proc.info['cmdline']:
                    cmdline = ' '.join(proc.info['cmdline'])
                    if pattern in cmdline:
                        if exclude_current and proc.info['pid'] == current_pid:
                            continue
                        processes.append(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
                
        return processes
    
    def graceful_stop_process(self, process: psutil.Process, timeout: int = 30) -> bool:
        """优雅停止进程"""
        try:
            self.logger.info(f"尝试优雅停止进程 {process.pid}")
            process.terminate()  # 发送SIGTERM
            
            # 等待进程自然退出
            try:
                process.wait(timeout=timeout)
                self.logger.info(f"进程 {process.pid} 已优雅退出")
                return True
            except psutil.TimeoutExpired:
                self.logger.warning(f"进程 {process.pid} 在{timeout}秒内未退出，强制杀死")
                process.kill()  # 强制杀死
                return True
                
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            self.logger.error(f"停止进程失败: {e}")
            return False
    
    def cleanup_stale_processes(self, patterns: List[str], max_age_hours: int = 24):
        """清理僵尸进程"""
        current_time = datetime.now()
        
        for pattern in patterns:
            processes = self.find_process_by_pattern(pattern)
            
            for proc in processes:
                try:
                    create_time = datetime.fromtimestamp(proc.create_time())
                    age_hours = (current_time - create_time).total_seconds() / 3600
                    
                    if age_hours > max_age_hours:
                        self.logger.warning(f"发现僵尸进程 {proc.pid} (运行{age_hours:.1f}小时)")
                        self.graceful_stop_process(proc)
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
    
    def ensure_single_instance(self, service_name: str, start_command: List[str]) -> bool:
        """确保单实例运行"""
        pid_file = self.pid_dir / f"{service_name}.pid"
        
        # 检查PID文件
        if pid_file.exists():
            try:
                old_pid = int(pid_file.read_text().strip())
                if psutil.pid_exists(old_pid):
                    proc = psutil.Process(old_pid)
                    if any(cmd in ' '.join(proc.cmdline()) for cmd in start_command):
                        self.logger.info(f"{service_name} 已在运行 (PID: {old_pid})")
                        return True
                    else:
                        self.logger.warning(f"PID {old_pid} 不是预期的服务进程")
                        
                # 清理过期的PID文件
                pid_file.unlink()
                        
            except (ValueError, psutil.NoSuchProcess, psutil.AccessDenied):
                pid_file.unlink()
        
        # 启动新实例
        try:
            self.logger.info(f"启动 {service_name}: {' '.join(start_command)}")
            proc = subprocess.Popen(
                start_command,
                cwd=self.work_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                start_new_session=True  # 避免信号传播
            )
            
            # 记录PID
            pid_file.write_text(str(proc.pid))
            self.logger.info(f"{service_name} 启动成功 (PID: {proc.pid})")
            return True
            
        except Exception as e:
            self.logger.error(f"启动 {service_name} 失败: {e}")
            return False


class DatabaseTaskMonitor:
    """数据库任务监控器"""
    
    def __init__(self):
        self.logger = logging.getLogger('TaskMonitor')
        
    def cleanup_stuck_predictions(self, timeout_hours: int = 2):
        """清理卡住的预测任务"""
        try:
            # 这里需要Django环境
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'finance_project.settings')
            import django
            django.setup()
            
            from ai_analysis.models import PredictionRecord
            from django.utils import timezone
            
            cutoff_time = timezone.now() - timedelta(hours=timeout_hours)
            stuck_records = PredictionRecord.objects.filter(
                prediction_status='processing',
                created_at__lt=cutoff_time
            )
            
            count = stuck_records.count()
            if count > 0:
                stuck_records.update(prediction_status='failed')
                self.logger.warning(f"清理了 {count} 个超时的预测任务")
                
                # 发送告警通知（可以集成钉钉、企业微信等）
                self._send_alert(f"发现并清理了 {count} 个卡住的AI预测任务")
                
        except Exception as e:
            self.logger.error(f"清理预测任务失败: {e}")
    
    def _send_alert(self, message: str):
        """发送告警通知"""
        # 这里可以集成各种通知方式
        self.logger.warning(f"告警: {message}")


def main():
    """主函数 - 可以被cron调用"""
    manager = ProcessManager("/home/<USER>/qiyuai-web/finance_backend")
    monitor = DatabaseTaskMonitor()
    
    # 1. 清理僵尸进程
    manager.cleanup_stale_processes([
        "collect_realtime_data",
        "collect_multi_data"
    ], max_age_hours=48)
    
    # 2. 确保数据收集器单实例运行
    manager.ensure_single_instance(
        "multi_collector",
        ["python3", "manage.py", "collect_multi_data", "--daemon", "--debug"]
    )
    
    # 3. 清理卡住的数据库任务
    monitor.cleanup_stuck_predictions(timeout_hours=2)
    
    print("进程管理任务完成")


if __name__ == "__main__":
    main()