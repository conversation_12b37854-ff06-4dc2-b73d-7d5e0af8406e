================================================================================
🔍 数据完整性检查报告
================================================================================
📅 检查时间: 2025-07-30 14:30:10
📊 检查期间: 2025-07-27 ~ 2025-07-30
⏱️  检查天数: 4 天

📈 总体统计
----------------------------------------
期望分钟数: 28,800
实际分钟数: 16,941
总体覆盖率: 58.82%
重要缺失: 13 个时段

📊 分交易对统计
----------------------------------------
BTCUSDT:
  覆盖率: 80.59%
  缺失时段: 3 个
  实际/期望: 4642/5760
ETHUSDT:
  覆盖率: 80.59%
  缺失时段: 3 个
  实际/期望: 4642/5760
SOLUSDT:
  覆盖率: 63.72%
  缺失时段: 5 个
  实际/期望: 3670/5760
DOGEUSDT:
  覆盖率: 55.45%
  缺失时段: 3 个
  实际/期望: 3194/5760
XRPUSDT:
  覆盖率: 13.77%
  缺失时段: 4 个
  实际/期望: 793/5760

🚨 重要缺失时段 (>60分钟)
----------------------------------------
XRPUSDT 2025-07-27
  时间: 2025-07-27 00:00:00 ~ 2025-07-27 23:59:00
  持续: 1440 分钟

XRPUSDT 2025-07-28
  时间: 2025-07-28 00:00:00 ~ 2025-07-28 23:59:00
  持续: 1440 分钟

SOLUSDT 2025-07-29
  时间: 2025-07-29 00:00:00 ~ 2025-07-29 23:34:59
  持续: 1415 分钟

DOGEUSDT 2025-07-29
  时间: 2025-07-29 00:00:00 ~ 2025-07-29 23:34:59
  持续: 1415 分钟

XRPUSDT 2025-07-29
  时间: 2025-07-29 00:00:00 ~ 2025-07-29 23:34:59
  持续: 1415 分钟

SOLUSDT 2025-07-30
  时间: 2025-07-30 12:48:00 ~ 2025-07-30 23:59:59
  持续: 672 分钟

DOGEUSDT 2025-07-30
  时间: 2025-07-30 12:48:00 ~ 2025-07-30 23:59:59
  持续: 672 分钟

XRPUSDT 2025-07-30
  时间: 2025-07-30 12:48:00 ~ 2025-07-30 23:59:59
  持续: 672 分钟

BTCUSDT 2025-07-30
  时间: 2025-07-30 13:22:00 ~ 2025-07-30 23:59:59
  持续: 638 分钟

ETHUSDT 2025-07-30
  时间: 2025-07-30 13:22:00 ~ 2025-07-30 23:59:59
  持续: 638 分钟

🔧 修复建议
----------------------------------------
python3 manage.py repair_data_gaps --symbol=BTCUSDT --start='2025-07-28T00:00:00' --end='2025-07-28T07:58:59'
python3 manage.py repair_data_gaps --symbol=BTCUSDT --start='2025-07-30T13:22:00' --end='2025-07-30T23:59:59'
python3 manage.py repair_data_gaps --symbol=ETHUSDT --start='2025-07-28T00:00:00' --end='2025-07-28T07:58:59'
python3 manage.py repair_data_gaps --symbol=ETHUSDT --start='2025-07-30T13:22:00' --end='2025-07-30T23:59:59'
python3 manage.py repair_data_gaps --symbol=SOLUSDT --start='2025-07-29T00:00:00' --end='2025-07-29T23:34:59'
