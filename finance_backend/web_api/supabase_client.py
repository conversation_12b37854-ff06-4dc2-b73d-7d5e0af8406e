"""
Supabase客户端配置
"""

from supabase import create_client, Client
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

class SupabaseService:
    """Supabase服务类"""
    
    def __init__(self):
        try:
            # 从解码的JWT获取项目URL  
            # yawlsydetappxqhncqyt 是项目ID
            self.url = "https://yawlsydetappxqhncqyt.supabase.co"
            self.anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlhd2xzeWRldGFwcHhxaG5jcXl0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE1NDI3MDgsImV4cCI6MjA2NzExODcwOH0.I8NgY2zZ70wdZq_Z8qMdUTD73flV93BGgBRAeY_ttdQ"
            self.service_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlhd2xzeWRldGFwcHhxaG5jcXl0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTU0MjcwOCwiZXhwIjoyMDY3MTE4NzA4fQ.lk7YgaqEBoixpbEGr9Pet7-5bdgHxGI32FFLs7k8m0E"
            
            # 创建客户端实例
            self.client: Client = create_client(self.url, self.anon_key)
            self.admin_client: Client = create_client(self.url, self.service_key)
            
            logger.info("✅ Supabase客户端初始化成功")
            
        except Exception as e:
            logger.error(f"❌ Supabase客户端初始化失败: {e}")
            raise
    
    def sign_up_with_phone(self, phone: str, password: str, user_data: dict = None):
        """
        手机号注册
        
        参数:
        - phone: 手机号
        - password: 密码
        - user_data: 额外用户数据
        
        返回:
        - (success: bool, result: dict, error: str)
        """
        try:
            # 使用邮箱格式注册 (<EMAIL>)
            email = f"{phone}@temp.finance.com"
            
            response = self.client.auth.sign_up({
                "email": email,
                "password": password,
                "options": {
                    "data": {
                        "phone": phone,
                        "nickname": user_data.get("nickname", f"用户{phone[-4:]}") if user_data else f"用户{phone[-4:]}",
                        "credits": 3
                    }
                }
            })
            
            if response.user:
                # 在web_users表中创建记录
                user_record = {
                    "id": response.user.id,
                    "phone": phone,
                    "email": email,
                    "nickname": user_data.get("nickname", f"用户{phone[-4:]}") if user_data else f"用户{phone[-4:]}",
                    "credits": 3
                }
                
                # 插入到web_users表
                insert_result = self.admin_client.table("web_users").insert(user_record).execute()
                
                logger.info(f"✅ 用户注册成功: {phone}")
                return True, response, None
            else:
                return False, None, "注册失败"
                
        except Exception as e:
            logger.error(f"❌ 注册失败: {e}")
            return False, None, str(e)
    
    def sign_in_with_phone(self, phone: str, password: str):
        """
        手机号登录
        
        参数:
        - phone: 手机号
        - password: 密码
        
        返回:
        - (success: bool, session: dict, user_data: dict, error: str)
        """
        try:
            email = f"{phone}@temp.finance.com"
            
            response = self.client.auth.sign_in_with_password({
                "email": email,
                "password": password
            })
            
            if response.session:
                # 获取用户详细信息
                user_data = self.get_user_by_id(response.user.id)
                
                logger.info(f"✅ 用户登录成功: {phone}")
                return True, response.session, user_data, None
            else:
                return False, None, None, "登录失败"
                
        except Exception as e:
            logger.error(f"❌ 登录失败: {e}")
            return False, None, None, str(e)
    
    def get_user_by_id(self, user_id: str):
        """根据用户ID获取用户信息"""
        try:
            result = self.admin_client.table("web_users").select("*").eq("id", user_id).execute()
            
            if result.data:
                return result.data[0]
            return None
            
        except Exception as e:
            logger.error(f"❌ 获取用户信息失败: {e}")
            return None
    
    def get_user_by_phone(self, phone: str):
        """根据手机号获取用户信息"""
        try:
            result = self.admin_client.table("web_users").select("*").eq("phone", phone).execute()
            
            if result.data:
                return result.data[0]
            return None
            
        except Exception as e:
            logger.error(f"❌ 获取用户信息失败: {e}")
            return None
    
    def verify_session(self, access_token: str):
        """
        验证session token
        
        参数:
        - access_token: JWT访问令牌
        
        返回:
        - (valid: bool, user_data: dict, error: str)
        """
        try:
            # 设置session
            self.client.auth.set_session(access_token, None)
            
            # 获取当前用户
            user = self.client.auth.get_user()
            
            if user.user:
                user_data = self.get_user_by_id(user.user.id)
                return True, user_data, None
            else:
                return False, None, "无效的token"
                
        except Exception as e:
            logger.error(f"❌ Token验证失败: {e}")
            return False, None, str(e)
    
    def update_user_credits(self, user_id: str, credits: int):
        """更新用户积分"""
        try:
            result = self.admin_client.table("web_users").update({"credits": credits}).eq("id", user_id).execute()
            
            if result.data:
                logger.info(f"✅ 更新用户积分成功: {user_id} -> {credits}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"❌ 更新用户积分失败: {e}")
            return False
    
    def log_api_usage(self, user_id: str, endpoint: str):
        """记录API使用"""
        try:
            usage_log = {
                "user_id": user_id,
                "endpoint": endpoint,
                "request_count": 1
            }
            
            self.admin_client.table("api_usage_logs").insert(usage_log).execute()
            return True
            
        except Exception as e:
            logger.error(f"❌ 记录API使用失败: {e}")
            return False

# 全局实例
supabase_service = SupabaseService()