-- 创建数据库
CREATE DATABASE IF NOT EXISTS finance_db DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE finance_db;

-- 创建微信用户表
CREATE TABLE IF NOT EXISTS wechat_user (
    id INT AUTO_INCREMENT PRIMARY KEY,
    openid VARCHAR(100) NOT NULL UNIQUE COMMENT '微信OpenID',
    unionid VARCHAR(100) NULL COMMENT '微信UnionID',
    nickname VARCHAR(100) NULL COMMENT '昵称',
    avatar_url VARCHAR(500) NULL COMMENT '头像URL',
    phone VARCHAR(20) NULL COMMENT '手机号',
    credits INT NOT NULL DEFAULT 3 COMMENT '剩余分析次数',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_openid (openid),
    INDEX idx_phone (phone)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信用户表';

-- 创建积分记录表
CREATE TABLE IF NOT EXISTS credit_record (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    type TINYINT NOT NULL COMMENT '类型:1初始赠送,2每日签到,3观看广告,4消费使用,5充值购买',
    amount INT NOT NULL COMMENT '数量',
    description VARCHAR(200) NULL COMMENT '描述',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES wechat_user(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分记录表';

-- 创建管理员账户
INSERT INTO wechat_user (openid, nickname, credits, is_active) 
VALUES ('admin_openid', '管理员', 9999, TRUE);

-- 添加初始积分记录
INSERT INTO credit_record (user_id, type, amount, description)
VALUES (1, 5, 9999, '管理员初始积分'); 