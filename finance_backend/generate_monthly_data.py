#!/usr/bin/env python3
"""
专门处理2025年1-6月数据生成月度数据
处理币安微秒级时间戳和COS分块传输格式
"""

import os
import sys
import django
import requests
import json
import pandas as pd
from datetime import datetime
import re

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'finance_project.settings')
django.setup()

from crypto_api.services.cos_service import COSRealtimeService

def clean_chunked_data(response_text):
    """
    清理COS分块传输数据
    处理格式: '6B4\r\n{"symbol":"BTCUSDT",...}\r\n0\r\n\r\n'
    """
    try:
        # 方法1: 直接尝试JSON解析
        return json.loads(response_text)
    except:
        pass
    
    try:
        # 方法2: 移除HTTP分块传输标记
        lines = response_text.split('\n')
        for line in lines:
            line = line.strip()
            if line and not line.isdigit() and line != '0' and not line.startswith('\\'):
                try:
                    return json.loads(line)
                except:
                    continue
    except:
        pass
    
    try:
        # 方法3: 正则表达式提取JSON
        json_pattern = r'\\{[^\\}]*"symbol"[^\\}]*\\}'
        matches = re.findall(json_pattern, response_text, re.DOTALL)
        if matches:
            # 找最长的匹配（最可能是完整的JSON）
            longest_match = max(matches, key=len)
            return json.loads(longest_match)
    except:
        pass
    
    # 方法4: 查找大括号包围的内容
    try:
        start = response_text.find('{')
        end = response_text.rfind('}')
        if start != -1 and end != -1 and end > start:
            json_str = response_text[start:end+1]
            return json.loads(json_str)
    except:
        pass
    
    raise ValueError(f"无法解析响应数据: {response_text[:200]}...")

def convert_timestamp(timestamp):
    """
    处理币安微秒级时间戳转换
    """
    if timestamp > 1e15:  # 微秒时间戳
        return timestamp / 1000000
    elif timestamp > 1e12:  # 毫秒时间戳  
        return timestamp / 1000
    else:  # 秒时间戳
        return timestamp

def generate_monthly_data():
    """生成2025年1-6月月度数据"""
    print("📅 开始生成2025年1-6月月度数据...")
    
    cos_service = COSRealtimeService()
    monthly_data = []
    base_url = 'https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/20250726'
    
    # 收集1-6月数据
    for month in range(1, 7):
        month_str = f'{month:02d}'
        url = f'{base_url}/BTCUSDT/1m/BTCUSDT_1m_{month_str}_2025_compressed.json'
        
        print(f"📥 处理{month}月数据: {url}")
        
        try:
            response = requests.get(url, timeout=60)
            print(f"   HTTP状态: {response.status_code}")
            print(f"   响应长度: {len(response.text)} 字符")
            print(f"   内容类型: {response.headers.get('content-type', 'unknown')}")
            
            if response.status_code == 200:
                # 清理分块传输数据
                try:
                    data = clean_chunked_data(response.text)
                    print(f"   ✅ JSON解析成功")
                except Exception as e:
                    print(f"   ❌ JSON解析失败: {str(e)}")
                    # 尝试保存原始响应用于调试
                    with open(f'/tmp/debug_month_{month}.txt', 'w') as f:
                        f.write(response.text[:2000])  # 只保存前2000字符
                    print(f"   📝 已保存调试数据到 /tmp/debug_month_{month}.txt")
                    continue
                
                if 'data' in data and len(data['data']) > 0:
                    print(f"   📊 原始数据条数: {len(data['data'])}")
                    
                    # 处理数据
                    df = pd.DataFrame(data['data'], columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                    
                    # 转换时间戳
                    df['timestamp'] = df['timestamp'].apply(convert_timestamp)
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
                    df.set_index('timestamp', inplace=True)
                    
                    print(f"   📅 时间范围: {df.index[0]} ~ {df.index[-1]}")
                    
                    # 按月聚合
                    monthly = df.resample('ME').agg({  # 使用'ME'替代过时的'M'
                        'open': 'first',
                        'high': 'max',
                        'low': 'min',
                        'close': 'last',
                        'volume': 'sum'
                    }).dropna()
                    
                    if len(monthly) > 0:
                        month_record = monthly.iloc[0]
                        timestamp_ms = int(monthly.index[0].timestamp() * 1000)
                        
                        month_entry = [
                            timestamp_ms,
                            float(month_record['open']),
                            float(month_record['high']),
                            float(month_record['low']),
                            float(month_record['close']),
                            float(month_record['volume'])
                        ]
                        
                        monthly_data.append(month_entry)
                        print(f"   ✅ {month}月数据处理成功")
                        print(f"      时间戳: {timestamp_ms}")
                        print(f"      OHLCV: {month_record['open']:.2f}/{month_record['high']:.2f}/{month_record['low']:.2f}/{month_record['close']:.2f}/{month_record['volume']:.0f}")
                    else:
                        print(f"   ⚠️ {month}月无有效聚合数据")
                else:
                    print(f"   ⚠️ {month}月数据格式异常或为空")
            else:
                print(f"   ❌ {month}月文件不存在: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ {month}月处理失败: {str(e)}")
    
    print(f"\\n📊 月度数据汇总: {len(monthly_data)} 条记录")
    
    # 上传月数据
    if monthly_data:
        monthly_file_data = {
            'symbol': 'BTCUSDT',
            'timeframe': '1mo',
            'date_range': '2025-01-01 to 2025-06-30',
            'total_records': len(monthly_data),
            'generated_at': datetime.now().isoformat(),
            'data': monthly_data
        }
        
        print("\\n📤 上传月度数据到COS...")
        try:
            cos_path = 'crypto-kline-data-v2/20250726/BTCUSDT/1mo/BTCUSDT_1mo_2025_compressed.json'
            result = cos_service.upload_json_to_cos(cos_path, monthly_file_data)
            if result:
                print(f"✅ 月数据上传成功: {cos_path}")
                print(f"✅ 总记录数: {len(monthly_data)}")
                
                # 验证上传
                verify_url = f'https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/{cos_path}'
                verify_response = requests.get(verify_url, timeout=30)
                if verify_response.status_code == 200:
                    verify_data = verify_response.json()
                    print(f"✅ 验证成功: 上传文件包含 {len(verify_data.get('data', []))} 条记录")
                else:
                    print(f"⚠️ 验证失败: HTTP {verify_response.status_code}")
            else:
                print("❌ 月数据上传失败")
        except Exception as e:
            print(f"❌ 上传失败: {str(e)}")
    else:
        print("❌ 无月数据可上传")
    
    return len(monthly_data)

if __name__ == '__main__':
    print("🚀 开始生成2025年月度数据...")
    print(f"⏰ 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    result = generate_monthly_data()
    
    print(f"\\n🎉 月度数据生成完成!")
    print(f"📊 成功生成 {result} 个月的数据")
    
    if result > 0:
        print("\\n🔗 访问链接:")
        print("https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/20250726/BTCUSDT/1mo/BTCUSDT_1mo_2025_compressed.json")