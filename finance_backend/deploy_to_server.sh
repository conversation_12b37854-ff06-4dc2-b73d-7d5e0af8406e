#!/bin/bash

# 服务器部署脚本
# 用于将实时数据收集器部署到服务器

echo "🚀 准备服务器部署..."

# 创建部署包
DEPLOY_DIR="deploy_package"
rm -rf $DEPLOY_DIR
mkdir -p $DEPLOY_DIR

echo "📦 复制必要文件..."

# 复制核心代码
cp -r crypto_api $DEPLOY_DIR/
cp -r finance_project $DEPLOY_DIR/
cp manage.py $DEPLOY_DIR/
cp requirements.txt $DEPLOY_DIR/

# 复制部署脚本
cp start_realtime_collector.sh $DEPLOY_DIR/
cp deploy_daemon.sh $DEPLOY_DIR/

# 创建环境配置模板
cat > $DEPLOY_DIR/server_env_template.sh << 'EOF'
#!/bin/bash
# 服务器环境变量配置模板
# 请根据实际情况修改这些值

export SECRET_KEY="your-django-secret-key"
export DEBUG="False"
export ALLOWED_HOSTS="your-server-ip,localhost"

# COS配置
export AWS_ACCESS_KEY_ID="your-cos-access-key"
export AWS_SECRET_ACCESS_KEY="your-cos-secret-key" 
export AWS_STORAGE_BUCKET_NAME="your-bucket-name"

# 数据库配置（如果需要）
export DATABASE_URL="sqlite:///./db.sqlite3"

echo "✅ 环境变量已加载"
EOF

# 创建服务器安装脚本
cat > $DEPLOY_DIR/install_on_server.sh << 'EOF'
#!/bin/bash
# 服务器安装脚本

echo "🔧 服务器环境安装..."

# 更新系统
sudo apt-get update

# 安装Python和pip
sudo apt-get install -y python3 python3-pip python3-venv

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 数据库迁移
python manage.py migrate

echo "✅ 服务器环境安装完成"
echo "📝 请编辑 server_env_template.sh 配置环境变量"
echo "🚀 然后运行: source server_env_template.sh && ./start_realtime_collector.sh"
EOF

# 创建systemd服务文件模板
cat > $DEPLOY_DIR/realtime-collector.service << 'EOF'
[Unit]
Description=Cryptocurrency Realtime Data Collector
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/home/<USER>/realtime_collector
Environment=PYTHONPATH=/home/<USER>/realtime_collector
EnvironmentFile=/home/<USER>/realtime_collector/server_env_template.sh
ExecStart=/home/<USER>/realtime_collector/venv/bin/python manage.py collect_realtime_data --symbol=BTCUSDT --daemon
Restart=always
RestartSec=10
StandardOutput=append:/home/<USER>/realtime_collector/logs/collector.log
StandardError=append:/home/<USER>/realtime_collector/logs/collector.log

[Install]
WantedBy=multi-user.target
EOF

# 创建快速测试脚本
cat > $DEPLOY_DIR/test_server_network.py << 'EOF'
#!/usr/bin/env python3
"""
服务器网络连接测试
测试Binance API和COS访问是否正常
"""

import requests
import time
from datetime import datetime

def test_binance_api():
    """测试Binance API连接"""
    print("🌐 测试Binance API连接...")
    
    endpoints = [
        'https://api.binance.com/api/v3/ping',
        'https://api1.binance.com/api/v3/ping',
        'https://api2.binance.com/api/v3/ping'
    ]
    
    for i, endpoint in enumerate(endpoints):
        try:
            start_time = time.time()
            response = requests.get(endpoint, timeout=10)
            end_time = time.time()
            
            if response.status_code == 200:
                print(f"✅ API镜像 {i+1}: 连接成功 ({end_time-start_time:.2f}s)")
                return True
            else:
                print(f"❌ API镜像 {i+1}: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ API镜像 {i+1}: {e}")
    
    return False

def test_cos_access():
    """测试COS访问"""
    print("🗂️ 测试COS访问...")
    
    test_url = "https://finance-1324685443.cos.ap-guangzhou.myqcloud.com"
    
    try:
        start_time = time.time()
        response = requests.head(test_url, timeout=10)
        end_time = time.time()
        
        print(f"✅ COS访问正常 ({end_time-start_time:.2f}s)")
        return True
    except Exception as e:
        print(f"❌ COS访问失败: {e}")
        return False

def test_kline_data():
    """测试K线数据获取"""
    print("📊 测试K线数据获取...")
    
    url = "https://api.binance.com/api/v3/klines"
    params = {
        'symbol': 'BTCUSDT',
        'interval': '1m',
        'limit': 1
    }
    
    try:
        start_time = time.time()
        response = requests.get(url, params=params, timeout=10)
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            if data:
                kline = data[0]
                timestamp = int(kline[0])
                price = float(kline[4])
                kline_time = datetime.fromtimestamp(timestamp / 1000)
                
                print(f"✅ K线数据获取成功 ({end_time-start_time:.2f}s)")
                print(f"📈 最新价格: ${price} (时间: {kline_time})")
                return True
    except Exception as e:
        print(f"❌ K线数据获取失败: {e}")
    
    return False

if __name__ == '__main__':
    print("🔍 服务器网络连接测试")
    print("=" * 50)
    
    results = []
    results.append(test_binance_api())
    results.append(test_cos_access()) 
    results.append(test_kline_data())
    
    print("\n" + "=" * 50)
    success_count = sum(results)
    
    if success_count == 3:
        print("🎉 所有测试通过！服务器网络环境良好")
        print("✅ 可以正常部署实时数据收集器")
    else:
        print(f"⚠️ {success_count}/3 测试通过")
        print("❌ 请检查服务器网络配置")
EOF

chmod +x $DEPLOY_DIR/*.sh
chmod +x $DEPLOY_DIR/test_server_network.py

echo "✅ 部署包创建完成: $DEPLOY_DIR/"
echo ""
echo "📋 部署步骤:"
echo "1. 将 $DEPLOY_DIR/ 文件夹上传到服务器"
echo "2. 在服务器上运行: chmod +x *.sh && ./install_on_server.sh"
echo "3. 配置环境变量: 编辑 server_env_template.sh"
echo "4. 测试网络: python3 test_server_network.py"
echo "5. 启动收集器: source server_env_template.sh && ./start_realtime_collector.sh"
echo ""
echo "🎯 建议先运行网络测试，确认服务器能正常访问Binance API和COS"