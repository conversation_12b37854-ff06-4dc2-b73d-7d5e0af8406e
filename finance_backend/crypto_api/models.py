from django.db import models
from django.utils import timezone

class RealTimeKlineData(models.Model):
    """实时K线数据存储模型 - 服务器端数据持久化"""
    
    # 基本信息
    symbol = models.CharField(max_length=20, db_index=True)  # 交易对
    timeframe = models.CharField(max_length=10, db_index=True)  # 时间周期
    timestamp = models.BigIntegerField(db_index=True)  # 时间戳（毫秒）
    
    # K线数据
    open_price = models.DecimalField(max_digits=20, decimal_places=8)
    high_price = models.DecimalField(max_digits=20, decimal_places=8)
    low_price = models.DecimalField(max_digits=20, decimal_places=8)
    close_price = models.DecimalField(max_digits=20, decimal_places=8)
    volume = models.DecimalField(max_digits=20, decimal_places=8)
    turnover = models.DecimalField(max_digits=20, decimal_places=8, null=True, blank=True)
    
    # 元数据
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    source = models.CharField(max_length=20, default='websocket')  # 数据来源
    uploaded_to_cos = models.BooleanField(default=False)  # 是否已上传COS
    
    class Meta:
        db_table = 'realtime_kline_data'
        unique_together = ['symbol', 'timeframe', 'timestamp']  # 防重复
        indexes = [
            models.Index(fields=['symbol', 'timeframe', 'timestamp']),
            models.Index(fields=['created_at']),
            models.Index(fields=['uploaded_to_cos']),
        ]
        ordering = ['timestamp']
    
    def __str__(self):
        return f"{self.symbol} {self.timeframe} {self.timestamp}"

class DataSyncStatus(models.Model):
    """数据同步状态追踪 - 用于监控前后端数据同步"""
    
    symbol = models.CharField(max_length=20)
    timeframe = models.CharField(max_length=10)
    last_sync_timestamp = models.BigIntegerField()  # 最后同步的时间戳
    total_records = models.IntegerField(default=0)
    last_cos_upload = models.DateTimeField(null=True, blank=True)
    sync_errors = models.IntegerField(default=0)  # 同步错误计数
    last_error_message = models.TextField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'data_sync_status'
        unique_together = ['symbol', 'timeframe']
    
    def __str__(self):
        return f"{self.symbol} {self.timeframe} - {self.total_records} records"
