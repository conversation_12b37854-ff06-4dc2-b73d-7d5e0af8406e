/**
 * KLineChart Pro 自定义数据源
 * 符合官方Datafeed接口标准
 */
import klineDataService from './KlineDataService';

class ProDatafeed {
  constructor() {
    this.supportedResolutions = ['1', '5', '15', '30', '60', '240', '1D', '1W', '1M'];
    this.symbolsCache = new Map();
  }

  // 初始化方法
  onReady(callback) {
    console.log('🚀 ProDatafeed onReady');
    setTimeout(() => {
      callback({
        supported_resolutions: this.supportedResolutions,
        supports_marks: false,
        supports_timescale_marks: false,
        supports_time: true,
        futures_regex: /^(.+)(\d{4})([FGJ-N][1-9]?)$/
      });
    }, 0);
  }

  // 搜索交易对
  searchSymbols(userInput, exchange, symbolType, onResultReadyCallback) {
    console.log('🔍 搜索交易对:', userInput);
    
    const symbols = [
      {
        symbol: 'BTCUSDT',
        full_name: 'BINANCE:BTCUSDT',
        description: 'Bitcoin / USDT',
        exchange: 'BINANCE',
        ticker: 'BTCUSDT',
        type: 'crypto'
      },
      {
        symbol: 'ETHUSDT',
        full_name: 'BINANCE:ETHUSDT', 
        description: 'Ethereum / USDT',
        exchange: 'BINANCE',
        ticker: 'ETHUSDT',
        type: 'crypto'
      },
      {
        symbol: 'LTCUSDT',
        full_name: 'BINANCE:LTCUSDT',
        description: 'Litecoin / USDT', 
        exchange: 'BINANCE',
        ticker: 'LTCUSDT',
        type: 'crypto'
      },
      {
        symbol: 'XRPUSDT',
        full_name: 'BINANCE:XRPUSDT',
        description: 'XRP / USDT',
        exchange: 'BINANCE', 
        ticker: 'XRPUSDT',
        type: 'crypto'
      },
      {
        symbol: 'DOGEUSDT',
        full_name: 'BINANCE:DOGEUSDT',
        description: 'Dogecoin / USDT',
        exchange: 'BINANCE',
        ticker: 'DOGEUSDT', 
        type: 'crypto'
      },
      {
        symbol: 'SOLUSDT',
        full_name: 'BINANCE:SOLUSDT',
        description: 'Solana / USDT',
        exchange: 'BINANCE',
        ticker: 'SOLUSDT',
        type: 'crypto'
      }
    ];

    const filteredSymbols = userInput 
      ? symbols.filter(s => 
          s.symbol.toLowerCase().includes(userInput.toLowerCase()) ||
          s.description.toLowerCase().includes(userInput.toLowerCase())
        )
      : symbols;

    setTimeout(() => onResultReadyCallback(filteredSymbols), 0);
  }

  // 获取交易对信息
  resolveSymbol(symbolName, onSymbolResolvedCallback, onResolveErrorCallback) {
    console.log('🔍 解析交易对:', symbolName);

    // 提取交易对名称（去掉交易所前缀）
    const symbol = symbolName.includes(':') ? symbolName.split(':')[1] : symbolName;
    
    const symbolInfo = {
      ticker: symbol,
      name: symbol,
      description: `${symbol} on Binance`,
      type: 'crypto',
      session: '24x7',
      timezone: 'Etc/UTC',
      exchange: 'BINANCE',
      minmov: 1,
      pricescale: 100,
      has_intraday: true,
      has_weekly_and_monthly: true,
      supported_resolutions: this.supportedResolutions,
      volume_precision: 8,
      data_status: 'streaming'
    };

    this.symbolsCache.set(symbolName, symbolInfo);
    
    setTimeout(() => onSymbolResolvedCallback(symbolInfo), 0);
  }

  // 获取历史K线数据
  getBars(symbolInfo, resolution, periodParams, onHistoryCallback, onErrorCallback) {
    console.log('📊 获取K线数据:', symbolInfo.ticker, resolution, periodParams);

    this.fetchKlineData(symbolInfo.ticker, resolution, periodParams)
      .then(bars => {
        console.log(`✅ 获取到 ${bars.length} 条K线数据`);
        onHistoryCallback(bars, { noData: bars.length === 0 });
      })
      .catch(error => {
        console.error('❌ 获取K线数据失败:', error);
        onErrorCallback(error.message);
      });
  }

  // 订阅实时数据
  subscribeBars(symbolInfo, resolution, onRealtimeCallback, subscriberUID, onResetCacheNeededCallback) {
    console.log('📡 订阅实时数据:', symbolInfo.ticker, resolution, subscriberUID);
    
    // 暂时不实现实时数据，返回空的订阅
    // 实际项目中可以连接WebSocket获取实时数据
  }

  // 取消订阅
  unsubscribeBars(subscriberUID) {
    console.log('🔕 取消订阅:', subscriberUID);
  }

  // 获取K线数据的核心方法
  async fetchKlineData(symbol, resolution, periodParams) {
    try {
      let data = null;
      
      // 根据时间范围确定数据源
      const { from, to, firstDataRequest } = periodParams;
      const timeRange = to - from;
      const oneYear = 365 * 24 * 60 * 60 * 1000;
      
      if (timeRange > oneYear * 2) {
        // 请求时间跨度大于2年，使用概览数据
        data = await klineDataService.getOverviewData(symbol);
      } else {
        // 尝试获取当前年数据
        try {
          data = await klineDataService.getCurrentYearData(symbol, this.mapResolutionToInterval(resolution));
        } catch (err) {
          // 回退到2024年数据
          data = await klineDataService.getYearlyData(symbol, 2024);
        }
      }

      if (!data || !data.data || data.data.length === 0) {
        return [];
      }

      // 转换为TradingView格式
      const bars = data.data
        .filter(item => item.timestamp >= from * 1000 && item.timestamp <= to * 1000)
        .map(item => ({
          time: item.timestamp,
          open: item.open,
          high: item.high,
          low: item.low,
          close: item.close,
          volume: item.volume
        }))
        .sort((a, b) => a.time - b.time);

      return bars;
    } catch (error) {
      console.error('获取K线数据失败:', error);
      throw error;
    }
  }

  // 分辨率映射
  mapResolutionToInterval(resolution) {
    const resolutionMap = {
      '1': '1m',
      '5': '5m',
      '15': '15m',
      '30': '30m',
      '60': '1h',
      '240': '4h',
      '1D': '1d',
      '1W': '1w',
      '1M': '1M'
    };
    return resolutionMap[resolution] || '1d';
  }

  // 获取服务器时间
  getServerTime(callback) {
    callback(Math.floor(Date.now() / 1000));
  }
}

export default ProDatafeed;