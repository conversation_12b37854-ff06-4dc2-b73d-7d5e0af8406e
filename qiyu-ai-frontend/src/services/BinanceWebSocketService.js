/**
 * 简化的WebSocket服务 - 禁用实时连接
 * 改为使用定时刷新模式
 */

class BinanceWebSocketService {
  constructor() {
    console.log('WebSocket服务已禁用，使用定时刷新模式');
  }
  
  subscribeKline() {
    console.log('WebSocket订阅已禁用');
    return 'disabled';
  }
  
  unsubscribe() {
    console.log('WebSocket取消订阅已禁用');
  }
  
  closeAllConnections() {
    console.log('WebSocket连接清理已禁用');
  }
}

const binanceWebSocketService = new BinanceWebSocketService();
export default binanceWebSocketService;
export { BinanceWebSocketService };
