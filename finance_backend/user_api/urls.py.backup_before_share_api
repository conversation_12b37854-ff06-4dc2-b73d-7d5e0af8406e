from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    GetOpenidView,
    WechatRegisterView, WechatLoginView, PhoneBindView, GetOpenidView, 
    TokenRefreshView, TokenVerifyView, UserViewSet, CreditRecordViewSet, TestMockView,
    AdminUsersView, AdminSearchUsersView, AdminAddCreditsView, DebugTokenView, SimpleAdminUsersView, SimpleAdminSearchUsersView, SimpleAdminAddCreditsView
)

# 创建路由器
router = DefaultRouter()
router.register(r'users', UserViewSet)
router.register(r'credit-records', CreditRecordViewSet)

# API URL配置
urlpatterns = [
    # 微信用户注册
    # 获取OpenID
    path('get-openid/', GetOpenidView.as_view(), name='get_openid'),
    # 获取OpenID
    path('get-openid/', GetOpenidView.as_view(), name='get_openid'),
    path('register/', WechatRegisterView.as_view(), name='wechat_register'),
    # 微信登录
    path('login/', WechatLoginView.as_view(), name='wechat_login'),
    # Token刷新
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    # Token验证
    path('verify-token/', TokenVerifyView.as_view(), name='token_verify'),
    # 手机号绑定
    path('bind-phone/', PhoneBindView.as_view(), name='phone_bind'),
    # 测试接口
    path('test/mock/', TestMockView.as_view(), name='test_mock'),
    
    # 管理员API
    path('admin/users/', AdminUsersView.as_view(), name='admin_users'),
    path('admin/search/', AdminSearchUsersView.as_view(), name='admin_search'),
    path('admin/add-credits/', AdminAddCreditsView.as_view(), name='admin_add_credits'),
    # 简化的管理员API（无需JWT）
    path("simple-admin/users/", SimpleAdminUsersView.as_view(), name="simple_admin_users"),
    path("simple-admin/search/", SimpleAdminSearchUsersView.as_view(), name="simple_admin_search"),
    path("simple-admin/add-credits/", SimpleAdminAddCreditsView.as_view(), name="simple_admin_add_credits"),
    path("debug/token/", DebugTokenView.as_view(), name="debug_token"),
    
    # 视图集路由
    path('', include(router.urls)),
]
