================================================================================
🔍 数据完整性检查报告
================================================================================
📅 检查时间: 2025-07-30 13:05:45
📊 检查期间: 2025-07-30 ~ 2025-07-30
⏱️  检查天数: 1 天

📈 总体统计
----------------------------------------
期望分钟数: 7,200
实际分钟数: 354
总体覆盖率: 4.92%
重要缺失: 11 个时段

📊 分交易对统计
----------------------------------------
BTCUSDT:
  覆盖率: 1.53%
  缺失时段: 3 个
  实际/期望: 22/1440
ETHUSDT:
  覆盖率: 5.76%
  缺失时段: 2 个
  实际/期望: 83/1440
SOLUSDT:
  覆盖率: 5.76%
  缺失时段: 2 个
  实际/期望: 83/1440
DOGEUSDT:
  覆盖率: 5.76%
  缺失时段: 2 个
  实际/期望: 83/1440
XRPUSDT:
  覆盖率: 5.76%
  缺失时段: 2 个
  实际/期望: 83/1440

🚨 重要缺失时段 (>60分钟)
----------------------------------------
BTCUSDT 2025-07-30
  时间: 2025-07-30 00:00:00 ~ 2025-07-30 11:24:59
  持续: 685 分钟

ETHUSDT 2025-07-30
  时间: 2025-07-30 00:00:00 ~ 2025-07-30 11:24:59
  持续: 685 分钟

SOLUSDT 2025-07-30
  时间: 2025-07-30 00:00:00 ~ 2025-07-30 11:24:59
  持续: 685 分钟

DOGEUSDT 2025-07-30
  时间: 2025-07-30 00:00:00 ~ 2025-07-30 11:24:59
  持续: 685 分钟

XRPUSDT 2025-07-30
  时间: 2025-07-30 00:00:00 ~ 2025-07-30 11:24:59
  持续: 685 分钟

BTCUSDT 2025-07-30
  时间: 2025-07-30 12:48:00 ~ 2025-07-30 23:59:59
  持续: 672 分钟

ETHUSDT 2025-07-30
  时间: 2025-07-30 12:48:00 ~ 2025-07-30 23:59:59
  持续: 672 分钟

SOLUSDT 2025-07-30
  时间: 2025-07-30 12:48:00 ~ 2025-07-30 23:59:59
  持续: 672 分钟

DOGEUSDT 2025-07-30
  时间: 2025-07-30 12:48:00 ~ 2025-07-30 23:59:59
  持续: 672 分钟

XRPUSDT 2025-07-30
  时间: 2025-07-30 12:48:00 ~ 2025-07-30 23:59:59
  持续: 672 分钟

🔧 修复建议
----------------------------------------
python3 manage.py collect_realtime_data --symbol=BTCUSDT --repair-gap --start='2025-07-30T00:00:00' --end='2025-07-30T11:24:59'
python3 manage.py collect_realtime_data --symbol=BTCUSDT --repair-gap --start='2025-07-30T11:31:00' --end='2025-07-30T12:31:59'
python3 manage.py collect_realtime_data --symbol=BTCUSDT --repair-gap --start='2025-07-30T12:48:00' --end='2025-07-30T23:59:59'
python3 manage.py collect_realtime_data --symbol=ETHUSDT --repair-gap --start='2025-07-30T00:00:00' --end='2025-07-30T11:24:59'
python3 manage.py collect_realtime_data --symbol=ETHUSDT --repair-gap --start='2025-07-30T12:48:00' --end='2025-07-30T23:59:59'
