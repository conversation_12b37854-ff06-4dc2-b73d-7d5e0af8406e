.chart-page {
  min-height: 100vh;
  background-color: #0d1117;
  color: #f0f6fc;
}

.page-header {
  text-align: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #161b22 0%, #21262d 100%);
  border-bottom: 1px solid #30363d;
}

.page-header h1 {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 12px 0;
  color: #f0f6fc;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.page-header p {
  font-size: 16px;
  color: #8b949e;
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.page-content {
  padding: 0;
}

.page-footer {
  padding: 40px 20px;
  background-color: #161b22;
  border-top: 1px solid #21262d;
  margin-top: 20px;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin: 0 auto;
}

.feature-item {
  background-color: #21262d;
  padding: 24px;
  border-radius: 12px;
  border: 1px solid #30363d;
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border-color: #58a6ff;
}

.feature-item h4 {
  color: #f0f6fc;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.feature-item ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-item li {
  color: #8b949e;
  font-size: 14px;
  line-height: 1.6;
  padding: 6px 0;
  position: relative;
  padding-left: 20px;
}

.feature-item li::before {
  content: '•';
  color: #26A69A;
  font-weight: bold;
  position: absolute;
  left: 0;
  top: 6px;
}

.feature-item li:hover {
  color: #f0f6fc;
  transform: translateX(4px);
  transition: all 0.2s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 24px 16px;
  }
  
  .page-header h1 {
    font-size: 24px;
  }
  
  .page-header p {
    font-size: 14px;
  }
  
  .page-footer {
    padding: 24px 16px;
  }
  
  .feature-list {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .feature-item {
    padding: 16px;
  }
  
  .feature-item h4 {
    font-size: 16px;
  }
  
  .feature-item li {
    font-size: 13px;
  }
}

/* 动画效果 */
.page-header {
  animation: fadeInDown 0.8s ease-out;
}

.page-content {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.page-footer {
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条美化 */
.chart-page::-webkit-scrollbar {
  width: 8px;
}

.chart-page::-webkit-scrollbar-track {
  background-color: #0d1117;
}

.chart-page::-webkit-scrollbar-thumb {
  background-color: #30363d;
  border-radius: 4px;
}

.chart-page::-webkit-scrollbar-thumb:hover {
  background-color: #484f58;
}