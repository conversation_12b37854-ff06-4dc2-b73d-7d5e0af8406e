/**
 * 自动AI分析服务 - 每15分钟自动进行AI分析
 * 为Dashboard的AI预测模块提供数据
 */

import aiAnalysisService from './AIAnalysisService';

class AutoAIAnalysisService {
  constructor() {
    this.isRunning = false;
    this.intervalId = null;
    this.analysisInterval = 15 * 60 * 1000; // 15分钟
    this.subscribers = new Set();
    this.lastAnalysisResults = new Map(); // 存储最新的分析结果
    this.analysisHistory = new Map(); // 存储历史分析记录
    
    // 默认分析的币种列表
    this.defaultSymbols = ['BTC', 'ETH', 'SOL', 'XRP', 'DOGE'];
    
    // 分析状态
    this.analysisStatus = {
      isAnalyzing: false,
      currentSymbol: null,
      progress: 0,
      lastUpdate: null,
      errors: []
    };
  }

  /**
   * 订阅分析结果更新
   */
  subscribe(callback) {
    this.subscribers.add(callback);
    
    // 立即返回当前结果
    if (this.lastAnalysisResults.size > 0) {
      callback({
        type: 'results_update',
        data: Object.fromEntries(this.lastAnalysisResults),
        status: this.analysisStatus
      });
    }
    
    return () => {
      this.subscribers.delete(callback);
    };
  }

  /**
   * 通知所有订阅者
   */
  notifySubscribers(event) {
    this.subscribers.forEach(callback => {
      try {
        callback(event);
      } catch (error) {
        console.error('通知订阅者失败:', error);
      }
    });
  }

  /**
   * 开始自动分析
   */
  start(symbols = this.defaultSymbols) {
    if (this.isRunning) {
      console.log('自动分析已在运行中');
      return;
    }

    this.isRunning = true;
    this.defaultSymbols = symbols;
    
    console.log(`🚀 启动自动AI分析服务，分析币种: ${symbols.join(', ')}`);
    console.log(`⏰ 分析间隔: ${this.analysisInterval / 1000 / 60} 分钟`);
    
    // 立即执行一次分析
    this.performAnalysis();
    
    // 设置定时器
    this.intervalId = setInterval(() => {
      this.performAnalysis();
    }, this.analysisInterval);
    
    this.notifySubscribers({
      type: 'service_started',
      data: { symbols, interval: this.analysisInterval }
    });
  }

  /**
   * 停止自动分析
   */
  stop() {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    
    this.analysisStatus.isAnalyzing = false;
    this.analysisStatus.currentSymbol = null;
    
    console.log('🛑 自动AI分析服务已停止');
    
    this.notifySubscribers({
      type: 'service_stopped',
      data: {}
    });
  }

  /**
   * 执行分析
   */
  async performAnalysis() {
    if (this.analysisStatus.isAnalyzing) {
      console.log('⏳ 上一轮分析尚未完成，跳过本次分析');
      return;
    }

    this.analysisStatus.isAnalyzing = true;
    this.analysisStatus.progress = 0;
    this.analysisStatus.errors = [];
    this.analysisStatus.lastUpdate = new Date().toISOString();
    
    console.log(`🔄 开始新一轮AI分析 - ${new Date().toLocaleString()}`);
    
    this.notifySubscribers({
      type: 'analysis_started',
      data: { symbols: this.defaultSymbols }
    });

    const results = {};
    const totalSymbols = this.defaultSymbols.length;
    
    for (let i = 0; i < totalSymbols; i++) {
      const symbol = this.defaultSymbols[i];
      this.analysisStatus.currentSymbol = symbol;
      this.analysisStatus.progress = Math.round((i / totalSymbols) * 100);
      
      try {
        console.log(`📊 分析 ${symbol} (${i + 1}/${totalSymbols})`);
        
        this.notifySubscribers({
          type: 'analysis_progress',
          data: {
            symbol,
            progress: this.analysisStatus.progress,
            current: i + 1,
            total: totalSymbols
          }
        });

        // 执行AI分析
        const result = await this.analyzeSymbol(symbol);
        results[symbol] = result;
        
        // 更新最新结果
        this.lastAnalysisResults.set(symbol, result);
        
        // 保存到历史记录
        this.saveToHistory(symbol, result);
        
        console.log(`✅ ${symbol} 分析完成`);
        
      } catch (error) {
        console.error(`❌ ${symbol} 分析失败:`, error);
        
        const errorInfo = {
          symbol,
          error: error.message,
          timestamp: new Date().toISOString()
        };
        
        this.analysisStatus.errors.push(errorInfo);
        
        // 设置错误结果
        results[symbol] = {
          symbol,
          error: error.message,
          timestamp: new Date().toISOString(),
          status: 'error'
        };
      }
      
      // 避免请求过于频繁，每个币种之间间隔2秒
      if (i < totalSymbols - 1) {
        await this.sleep(2000);
      }
    }
    
    this.analysisStatus.isAnalyzing = false;
    this.analysisStatus.currentSymbol = null;
    this.analysisStatus.progress = 100;
    
    console.log(`🎉 本轮分析完成，成功: ${Object.keys(results).length - this.analysisStatus.errors.length}，失败: ${this.analysisStatus.errors.length}`);
    
    this.notifySubscribers({
      type: 'analysis_completed',
      data: {
        results,
        errors: this.analysisStatus.errors,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * 分析单个币种
   */
  async analyzeSymbol(symbol) {
    const analysisOptions = {
      positionAmount: 300,
      positionPercentage: 2.7,
      isHolding: true
    };

    // 使用快速分析模式
    const analysisResult = await aiAnalysisService.analyzeWithAI(
      symbol, 
      'quick', 
      analysisOptions
    );

    // 解析分析结果，提取关键信息
    const parsedResult = this.parseAnalysisResult(analysisResult, symbol);
    
    return {
      symbol,
      rawResult: analysisResult,
      ...parsedResult,
      timestamp: new Date().toISOString(),
      status: 'success'
    };
  }

  /**
   * 解析AI分析结果，提取关键信息
   */
  parseAnalysisResult(result, symbol) {
    const parsed = {
      sentiment: 'neutral', // bullish, bearish, neutral
      confidence: 0,
      targetPrice: null,
      stopLoss: null,
      timeframe: '24h',
      keyPoints: [],
      recommendation: 'hold' // buy, sell, hold
    };

    if (!result || typeof result !== 'string') {
      return parsed;
    }

    const text = result.toLowerCase();
    
    // 分析情绪
    if (text.includes('看多') || text.includes('买入') || text.includes('上涨') || text.includes('bullish')) {
      parsed.sentiment = 'bullish';
    } else if (text.includes('看空') || text.includes('卖出') || text.includes('下跌') || text.includes('bearish')) {
      parsed.sentiment = 'bearish';
    }
    
    // 提取建议
    if (text.includes('买入') || text.includes('建议持有并加仓')) {
      parsed.recommendation = 'buy';
    } else if (text.includes('卖出') || text.includes('减仓')) {
      parsed.recommendation = 'sell';
    }
    
    // 提取价格目标（简单正则匹配）
    const priceMatches = result.match(/目标.*?(\d+,?\d*\.?\d*)/g);
    if (priceMatches && priceMatches.length > 0) {
      const priceStr = priceMatches[0].match(/(\d+,?\d*\.?\d*)/);
      if (priceStr) {
        parsed.targetPrice = parseFloat(priceStr[1].replace(',', ''));
      }
    }
    
    // 提取关键点（按句号分割，取前3个要点）
    const sentences = result.split(/[。！？\n]/).filter(s => s.trim().length > 10);
    parsed.keyPoints = sentences.slice(0, 3).map(s => s.trim());
    
    // 简单的置信度评估
    if (text.includes('强烈') || text.includes('明确') || text.includes('确定')) {
      parsed.confidence = 0.8;
    } else if (text.includes('可能') || text.includes('建议')) {
      parsed.confidence = 0.6;
    } else {
      parsed.confidence = 0.4;
    }
    
    return parsed;
  }

  /**
   * 保存到历史记录
   */
  saveToHistory(symbol, result) {
    if (!this.analysisHistory.has(symbol)) {
      this.analysisHistory.set(symbol, []);
    }
    
    const history = this.analysisHistory.get(symbol);
    history.push(result);
    
    // 只保留最近24小时的记录（假设每15分钟一次，24小时=96次）
    if (history.length > 96) {
      history.splice(0, history.length - 96);
    }
  }

  /**
   * 获取最新分析结果
   */
  getLatestResults() {
    return Object.fromEntries(this.lastAnalysisResults);
  }

  /**
   * 获取历史分析记录
   */
  getHistory(symbol) {
    return this.analysisHistory.get(symbol) || [];
  }

  /**
   * 获取分析状态
   */
  getStatus() {
    return { ...this.analysisStatus };
  }

  /**
   * 手动触发分析
   */
  async triggerAnalysis(symbols = null) {
    if (symbols) {
      const originalSymbols = this.defaultSymbols;
      this.defaultSymbols = symbols;
      await this.performAnalysis();
      this.defaultSymbols = originalSymbols;
    } else {
      await this.performAnalysis();
    }
  }

  /**
   * 工具函数：延迟
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 创建全局实例
const autoAIAnalysisService = new AutoAIAnalysisService();

export default autoAIAnalysisService;
