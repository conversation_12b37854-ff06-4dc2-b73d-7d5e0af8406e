import React, { useState } from 'react';
import { Avatar, Dropdown, message } from 'antd';
import { UserOutlined, SettingOutlined, LogoutOutlined, CaretDownOutlined, CrownOutlined, WalletOutlined } from '@ant-design/icons';
import Atropos from 'atropos/react';
import Button from '../Common/Button';
import { useAuth } from '../../contexts/AuthContext';
import './Nav.css';

const Nav = ({ onSymbolChange, selectedSymbol = 'BTCUSDT' }) => {
  const [activeSymbol, setActiveSymbol] = useState(selectedSymbol);
  const { user, profile, signOut } = useAuth();
  const [userRole] = useState('user'); // 'user' | 'admin'

  const isLoggedIn = !!user;

  // 格式化手机号显示
  const formatPhoneNumber = (phone) => {
    if (!phone) return '';
    // 格式化为 134****2592
    if (phone.length === 11) {
      return `${phone.slice(0, 3)}****${phone.slice(-4)}`;
    }
    return phone;
  };

  // 币种列表
  const symbols = [
    { symbol: 'BTCUSDT', interactive: true },
    { symbol: 'ETHUSDT', interactive: true },
    { symbol: 'DOGEUSDT', interactive: true },
    { symbol: 'LTCUSDT', interactive: true },
    { symbol: 'TRUMPUSDT', interactive: true },
    { symbol: 'SOLUSDT', interactive: true },
    { symbol: 'XRPUSDT', interactive: true },
    { symbol: 'PIUSDT', interactive: false } // PI币不可交互
  ];



  const handleSymbolClick = (symbolObj) => {
    if (!symbolObj.interactive) return; // PI币不可交互
    
    setActiveSymbol(symbolObj.symbol);
    onSymbolChange && onSymbolChange(symbolObj.symbol);
    
    // 添加用户反馈
    console.log(`🔄 切换到币种: ${symbolObj.symbol}`);
  };

  const handleLogin = () => {
    console.log('跳转到登录页面');
    // 跳转到认证页面（默认显示登录）
    window.location.href = '/auth';
  };

  const handleRegister = () => {
    console.log('跳转到注册页面');
    // 跳转到认证页面（需要切换到注册）
    window.location.href = '/auth?mode=register';
  };

  const handleLogout = async () => {
    try {
      await signOut();
      message.success('退出登录成功');
      // 可以选择跳转到首页
      // window.location.href = '/';
    } catch (error) {
      console.error('退出登录失败:', error);
      message.error('退出登录失败');
    }
  };

  const handlePointsCenter = () => {
    console.log('跳转到个人中心');
    window.location.href = '/profile';
  };

  const handleAdminCenter = () => {
    console.log('跳转到管理中心');
    // 这里可以跳转到管理中心页面
  };

  // 用户菜单项
  const getUserMenuItems = () => {
    const baseItems = [
      {
        key: 'balance',
        label: (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <WalletOutlined />
            <span>余额: {user?.credits || profile?.credits || 0}</span>
          </div>
        ),
        disabled: true,
      },
      {
        type: 'divider',
      },
    ];

    if (userRole === 'admin') {
      return [
        ...baseItems,
        {
          key: 'admin',
          label: '管理中心',
          icon: <CrownOutlined />,
          onClick: handleAdminCenter,
        },
        {
          key: 'logout',
          label: '退出登录',
          icon: <LogoutOutlined />,
          onClick: handleLogout,
        },
      ];
    } else {
      return [
        ...baseItems,
        {
          key: 'points',
          label: '个人中心',
          icon: <SettingOutlined />,
          onClick: handlePointsCenter,
        },
        {
          key: 'logout',
          label: '退出登录',
          icon: <LogoutOutlined />,
          onClick: handleLogout,
        },
      ];
    }
  };



  return (
    <nav className="qiyu-nav">
      <div className="nav-container">
        {/* Logo 区域 */}
        <div className="nav-logo">
          <img
            src="/logo_textless.png"
            alt="旗鱼AI"
            className="logo-image"
          />
          <span className="logo-title">旗鱼AI</span>
        </div>

        {/* 币种选择区域 */}
        <div className="nav-symbols">
          {symbols.map((symbolObj) => (
            <Button
              key={symbolObj.symbol}
              variant={activeSymbol === symbolObj.symbol ? 'primary' : 'ghost'}
              size="small"
              onClick={() => handleSymbolClick(symbolObj)}
              className={`symbol-btn ${!symbolObj.interactive ? 'pi-coin' : ''}`}
              disabled={!symbolObj.interactive}
            >
              <span>{symbolObj.symbol}</span>
              {symbolObj.interactive && (
                <div className="symbol-shine-effect"></div>
              )}
            </Button>
          ))}
        </div>

        {/* 用户区域 */}
        <div className="nav-user">
          {isLoggedIn ? (
            /* 已登录状态 - 显示头像和下拉菜单 */
            <Dropdown
              menu={{
                items: getUserMenuItems(),
              }}
              placement="bottomRight"
              trigger={['click']}
            >
              <div className="user-avatar-wrapper">
                <Avatar
                  size={32}
                  icon={<UserOutlined />}
                  className="user-avatar"
                />
                <span className="user-name">
                  {formatPhoneNumber(user?.phone) || '用户'}
                </span>
                <CaretDownOutlined className="user-dropdown-icon" />
              </div>
            </Dropdown>
          ) : (
            /* 未登录状态 - 显示登录注册按钮 */
            <div className="auth-buttons">
              <button className="auth-link" onClick={handleLogin}>
                登录
              </button>
              <span className="auth-divider">|</span>
              <button className="auth-link" onClick={handleRegister}>
                注册
              </button>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Nav;
