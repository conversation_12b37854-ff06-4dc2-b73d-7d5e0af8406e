#!/usr/bin/env python3
"""
自动AI预测任务管理器 - 串行处理避免内存问题
"""
import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from django.utils import timezone
from django.db import transaction
from decimal import Decimal

from .models import PredictionRecord, AutoPredictionTask
from .ai_prediction_api import AIPredictionAPIView
from historical_data.timestamp_analysis_service import TimestampAnalysisService

logger = logging.getLogger(__name__)


class AutoPredictionManager:
    """自动预测任务管理器 - 串行执行保证系统稳定性"""
    
    def __init__(self):
        self.timestamp_service = TimestampAnalysisService()
        self.prediction_api = AIPredictionAPIView()
        self.processing = False
        
    def run_auto_predictions(self, force_all: bool = False) -> Dict:
        """
        运行自动预测任务
        
        Args:
            force_all: 强制运行所有任务，忽略时间间隔
            
        Returns:
            执行结果统计
        """
        if self.processing:
            logger.warning("自动预测任务正在运行中，跳过本次执行")
            return {'status': 'skipped', 'reason': '任务运行中'}
        
        try:
            self.processing = True
            start_time = time.time()
            
            logger.info("🚀 开始运行自动AI预测任务")
            
            # 获取需要执行的任务
            tasks_to_run = self._get_pending_tasks(force_all)
            
            if not tasks_to_run:
                logger.info("没有需要执行的预测任务")
                return {
                    'status': 'completed',
                    'total_tasks': 0,
                    'successful': 0,
                    'failed': 0,
                    'skipped': 0,
                    'execution_time': round(time.time() - start_time, 2)
                }
            
            # 串行执行任务（避免内存问题）
            results = {
                'status': 'completed',
                'total_tasks': len(tasks_to_run),
                'successful': 0,
                'failed': 0,
                'skipped': 0,
                'task_details': [],
                'execution_time': 0
            }
            
            for i, task in enumerate(tasks_to_run, 1):
                logger.info(f"📊 执行预测任务 ({i}/{len(tasks_to_run)}): {task.symbol}")
                
                task_start = time.time()
                task_result = self._execute_single_prediction(task)
                task_duration = time.time() - task_start
                
                # 更新统计
                if task_result['status'] == 'success':
                    results['successful'] += 1
                elif task_result['status'] == 'failed':
                    results['failed'] += 1
                else:
                    results['skipped'] += 1
                
                results['task_details'].append({
                    'symbol': task.symbol,
                    'status': task_result['status'],
                    'duration': round(task_duration, 2),
                    'message': task_result.get('message', '')
                })
                
                # 更新任务统计
                self._update_task_stats(task, task_result['status'] == 'success')
                
                # 避免请求过于频繁，间隔1秒
                if i < len(tasks_to_run):
                    time.sleep(1)
            
            results['execution_time'] = round(time.time() - start_time, 2)
            
            logger.info(f"✅ 自动预测任务完成: 成功{results['successful']}, 失败{results['failed']}, 跳过{results['skipped']}, 耗时{results['execution_time']}秒")
            
            return results
            
        except Exception as e:
            logger.error(f"自动预测任务执行失败: {e}", exc_info=True)
            return {
                'status': 'error',
                'error': str(e),
                'execution_time': round(time.time() - start_time, 2) if 'start_time' in locals() else 0
            }
        finally:
            self.processing = False
    
    def _get_pending_tasks(self, force_all: bool = False) -> List[AutoPredictionTask]:
        """获取需要执行的任务"""
        now = timezone.now()
        
        if force_all:
            # 强制执行所有启用的任务
            tasks = AutoPredictionTask.objects.filter(is_active=True).order_by('priority', 'symbol')
            logger.info(f"强制执行模式：找到 {tasks.count()} 个启用的任务")
        else:
            # 只执行到期的任务
            tasks = AutoPredictionTask.objects.filter(
                is_active=True,
                next_run_at__lte=now
            ).order_by('priority', 'symbol')
            logger.info(f"定时执行模式：找到 {tasks.count()} 个到期的任务")
        
        return list(tasks)
    
    def _execute_single_prediction(self, task: AutoPredictionTask) -> Dict:
        """执行单个预测任务"""
        try:
            # 使用当前时间戳作为预测目标
            current_timestamp = int(time.time() * 1000)
            current_datetime = datetime.fromtimestamp(current_timestamp / 1000)
            
            logger.info(f"开始预测 {task.symbol}: {current_datetime}")
            
            # 创建预测记录
            prediction_record = PredictionRecord.objects.create(
                symbol=task.symbol,
                interval=task.interval,
                prediction_timestamp=current_timestamp,
                prediction_datetime=current_datetime,
                prediction_status='processing',
                current_price=0  # 临时值，后续更新
            )
            
            try:
                # 获取时间戳分析数据
                analysis_result = self.timestamp_service.get_timestamp_analysis_data(
                    timestamp=current_timestamp,
                    symbol=task.symbol,
                    interval=task.interval
                )
                
                if 'error' in analysis_result:
                    raise Exception(f"时间戳分析失败: {analysis_result['error']}")
                
                # 构建AI预测提示词
                ai_prompt = self.prediction_api._build_prediction_prompt(analysis_result)
                
                # 调用AI分析
                ai_prediction = self.prediction_api._call_deepseek_api(ai_prompt)
                
                # 提取市场数据
                current_price = self.prediction_api._extract_current_price(analysis_result)
                support_resistance = analysis_result.get('support_resistance', {})
                
                # 更新预测记录
                prediction_record.current_price = Decimal(str(current_price))
                prediction_record.support_level = Decimal(str(support_resistance.get('support', 0))) if support_resistance.get('support') else None
                prediction_record.resistance_level = Decimal(str(support_resistance.get('resistance', 0))) if support_resistance.get('resistance') else None
                prediction_record.technical_indicators = analysis_result.get('technical_indicators', {})
                prediction_record.btc_reference = analysis_result.get('btc_reference', {})
                prediction_record.analysis_summary = ai_prediction.get('analysis_summary', '')
                prediction_record.prediction_distribution = ai_prediction.get('prediction_distribution', {})
                prediction_record.market_sentiment = ai_prediction.get('market_sentiment', {})
                prediction_record.confidence_level = ai_prediction.get('confidence_level', '中')
                prediction_record.data_points_count = analysis_result.get('total_data_points', 0)
                prediction_record.historical_years_count = analysis_result.get('historical_years_count', 0)
                prediction_record.raw_ai_response = ai_prediction.get('raw_ai_response', '')
                prediction_record.prediction_status = 'completed'
                prediction_record.processing_time_seconds = (timezone.now() - prediction_record.created_at).total_seconds()
                
                prediction_record.save()
                
                logger.info(f"✅ {task.symbol} 预测完成: {ai_prediction.get('analysis_summary', '')[:50]}...")
                
                return {
                    'status': 'success',
                    'prediction_id': prediction_record.id,
                    'message': f'预测完成，价格: {current_price}'
                }
                
            except Exception as e:
                # 更新预测记录为失败状态
                prediction_record.prediction_status = 'failed'
                prediction_record.error_message = str(e)
                prediction_record.processing_time_seconds = (timezone.now() - prediction_record.created_at).total_seconds()
                prediction_record.save()
                
                logger.error(f"❌ {task.symbol} 预测失败: {e}")
                raise e
                
        except Exception as e:
            return {
                'status': 'failed',
                'error': str(e),
                'message': f'预测失败: {str(e)[:100]}'
            }
    
    def _update_task_stats(self, task: AutoPredictionTask, success: bool):
        """更新任务统计信息"""
        with transaction.atomic():
            # 重新获取任务对象避免并发更新问题
            task = AutoPredictionTask.objects.select_for_update().get(id=task.id)
            
            task.last_run_at = timezone.now()
            task.next_run_at = timezone.now() + timedelta(minutes=15)  # 15分钟后再次运行
            task.total_runs += 1
            
            if success:
                task.success_runs += 1
            else:
                task.failed_runs += 1
            
            task.save()
    
    def setup_default_tasks(self, symbols: List[str] = None) -> Dict:
        """设置默认的自动预测任务"""
        if symbols is None:
            symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'XRPUSDT', 'SOLUSDT', 'DOGEUSDT']
        
        created_count = 0
        updated_count = 0
        
        for i, symbol in enumerate(symbols, 1):
            task, created = AutoPredictionTask.objects.get_or_create(
                symbol=symbol,
                defaults={
                    'interval': '1m',
                    'is_active': True,
                    'priority': i,  # 按顺序设置优先级
                    'next_run_at': timezone.now()  # 立即可运行
                }
            )
            
            if created:
                created_count += 1
                logger.info(f"✅ 创建自动预测任务: {symbol}")
            else:
                # 确保现有任务是启用状态
                if not task.is_active:
                    task.is_active = True
                    task.save()
                    updated_count += 1
                    logger.info(f"🔄 启用自动预测任务: {symbol}")
        
        return {
            'total_symbols': len(symbols),
            'created': created_count,
            'updated': updated_count,
            'symbols': symbols
        }
    
    def get_task_status(self) -> Dict:
        """获取任务状态概览"""
        tasks = AutoPredictionTask.objects.all().order_by('priority')
        active_count = tasks.filter(is_active=True).count()
        
        recent_predictions = PredictionRecord.objects.filter(
            created_at__gte=timezone.now() - timedelta(hours=1)
        ).count()
        
        return {
            'total_tasks': tasks.count(),
            'active_tasks': active_count,
            'inactive_tasks': tasks.count() - active_count,
            'recent_predictions_1h': recent_predictions,
            'processing': self.processing,
            'tasks': [
                {
                    'symbol': task.symbol,
                    'is_active': task.is_active,
                    'priority': task.priority,
                    'last_run_at': task.last_run_at.isoformat() if task.last_run_at else None,
                    'next_run_at': task.next_run_at.isoformat() if task.next_run_at else None,
                    'success_rate': task.success_rate,
                    'total_runs': task.total_runs,
                }
                for task in tasks
            ]
        }