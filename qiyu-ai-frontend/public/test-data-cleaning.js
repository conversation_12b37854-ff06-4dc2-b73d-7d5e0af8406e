/**
 * 测试数据清洗功能的脚本
 * 在浏览器控制台中运行，验证异常数据过滤效果
 */

// 测试数据清洗功能
window.testDataCleaning = {
  async testAbnormalDataFilter() {
    console.log('🧪 开始测试数据清洗功能...')
    
    try {
      // 清除现有缓存
      if (window.streamCryptoDataService) {
        window.streamCryptoDataService.clearCache()
        console.log('🗑️ 已清除数据缓存')
      }
      
      // 强制重新加载7月30日数据
      console.log('🔄 重新加载7月30日数据...')
      const dataService = await import('/src/services/StreamDataService.js')
      const service = dataService.default
      
      // 加载7月30日数据
      const data = await service.loadYearDataWithRetry('BTCUSDT', '1m', '2025')
      
      console.log(`📊 加载完成: ${data.length} 条记录`)
      
      // 检查是否还有异常价格
      const abnormalCount = data.filter(item => 
        item.open < 30000 || item.high < 30000 || 
        item.low < 30000 || item.close < 30000 ||
        item.open > 200000 || item.high > 200000 || 
        item.low > 200000 || item.close > 200000
      ).length
      
      console.log(`🔍 异常数据检查结果: ${abnormalCount} 条异常数据`)
      
      if (abnormalCount === 0) {
        console.log('✅ 数据清洗成功！没有异常价格数据')
        
        // 显示价格范围
        const prices = data.flatMap(item => [item.open, item.high, item.low, item.close])
        const minPrice = Math.min(...prices)
        const maxPrice = Math.max(...prices)
        console.log(`💰 价格范围: $${minPrice.toFixed(2)} ~ $${maxPrice.toFixed(2)}`)
        
        return true
      } else {
        console.log('❌ 仍有异常数据，需要进一步检查')
        
        // 显示前几个异常数据
        const abnormalData = data.filter(item => 
          item.open < 30000 || item.high < 30000 || 
          item.low < 30000 || item.close < 30000
        ).slice(0, 5)
        
        console.table(abnormalData.map(item => ({
          时间: new Date(item.timestamp).toLocaleString(),
          开盘: item.open,
          最高: item.high,
          最低: item.low,
          收盘: item.close
        })))
        
        return false
      }
      
    } catch (error) {
      console.error('❌ 测试失败:', error)
      return false
    }
  },

  // 测试图表显示
  async testChartDisplay() {
    console.log('📈 测试图表显示...')
    
    try {
      // 触发图表刷新
      if (window.location.pathname.includes('dashboard')) {
        console.log('🔄 触发页面刷新以更新图表...')
        window.location.reload()
      } else {
        console.log('💡 请访问Dashboard页面查看图表效果')
        console.log('🔗 URL: http://localhost:3000/dashboard')
      }
      
    } catch (error) {
      console.error('❌ 图表测试失败:', error)
    }
  },

  // 显示使用说明
  help() {
    console.log(`
🧪 数据清洗测试工具:
====================

1. 测试异常数据过滤:
   testDataCleaning.testAbnormalDataFilter()

2. 测试图表显示:
   testDataCleaning.testChartDisplay()

3. 显示帮助:
   testDataCleaning.help()

💡 预期结果:
- 异常价格数据（< $30,000 或 > $200,000）应被过滤
- 图表不再显示直线或强制缩放
- 控制台显示数据清洗日志
    `)
  }
}

// 自动显示帮助
console.log('🚀 数据清洗测试工具已加载!')
console.log('📖 输入 testDataCleaning.help() 查看使用说明')
testDataCleaning.help()