#!/bin/bash

# 自动数据缺口修复脚本
# 基于完整性检查报告自动执行缺口修复

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

LOG_FILE="logs/auto_repair.log"

# 写日志函数
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# 检查是否提供了报告文件
if [ $# -eq 0 ]; then
    # 查找最新的完整性报告
    LATEST_REPORT=$(find logs/integrity_reports/ -name "*.txt" -type f -exec ls -t {} + 2>/dev/null | head -1)
    
    if [ -z "$LATEST_REPORT" ]; then
        log_message "❌ 未找到完整性检查报告文件"
        echo "用法: $0 [报告文件路径]"
        echo "或先运行: ./quick_integrity_check.sh"
        exit 1
    fi
    
    REPORT_FILE="$LATEST_REPORT"
else
    REPORT_FILE="$1"
fi

if [ ! -f "$REPORT_FILE" ]; then
    log_message "❌ 报告文件不存在: $REPORT_FILE"
    exit 1
fi

log_message "🔧 开始自动缺口修复..."
log_message "📄 使用报告文件: $REPORT_FILE"

# 提取修复命令
REPAIR_COMMANDS=$(grep -A 50 "🔧 修复建议" "$REPORT_FILE" | grep "python3 manage.py repair_data_gaps" | head -10)

if [ -z "$REPAIR_COMMANDS" ]; then
    log_message "✅ 报告中无需修复的缺口"
    exit 0
fi

# 统计修复命令数量
COMMAND_COUNT=$(echo "$REPAIR_COMMANDS" | wc -l)
log_message "📊 发现 $COMMAND_COUNT 个修复命令"

# 询问是否执行修复
echo "🔍 发现以下修复命令:"
echo "$REPAIR_COMMANDS"
echo ""
read -p "是否执行这些修复命令? (y/N): " -n 1 -r
echo

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log_message "❌ 用户取消修复操作"
    exit 0
fi

log_message "🚀 开始执行修复命令..."

# 逐个执行修复命令
SUCCESS_COUNT=0
FAILED_COUNT=0

while IFS= read -r command; do
    if [ -n "$command" ]; then
        log_message "🔧 执行: $command"
        
        # 提取symbol参数用于日志
        SYMBOL=$(echo "$command" | grep -o -- '--symbol=[A-Z]*' | cut -d'=' -f2)
        START_TIME=$(echo "$command" | grep -o -- "--start='[^']*'" | cut -d"'" -f2)
        END_TIME=$(echo "$command" | grep -o -- "--end='[^']*'" | cut -d"'" -f2)
        
        log_message "  📊 修复 $SYMBOL: $START_TIME ~ $END_TIME"
        
        # 执行命令（去除多余引号）
        clean_command=$(echo "$command" | sed "s/'//g")
        if timeout 300 $clean_command >> "$LOG_FILE" 2>&1; then
            log_message "  ✅ 修复成功: $SYMBOL"
            ((SUCCESS_COUNT++))
        else
            log_message "  ❌ 修复失败: $SYMBOL"
            ((FAILED_COUNT++))
        fi
        
        # 短暂等待，避免过度负载
        sleep 2
    fi
done <<< "$REPAIR_COMMANDS"

log_message "📊 修复完成统计:"
log_message "  ✅ 成功: $SUCCESS_COUNT 个"
log_message "  ❌ 失败: $FAILED_COUNT 个"

if [ $FAILED_COUNT -eq 0 ]; then
    log_message "🎉 所有缺口修复成功！"
    
    # 建议重新检查完整性
    log_message "💡 建议运行完整性检查验证修复结果:"
    log_message "   ./quick_integrity_check.sh"
    
    exit 0
else
    log_message "⚠️ 部分修复失败，请检查日志详情"
    exit 1
fi