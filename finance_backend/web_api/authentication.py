"""
Web API认证中间件
"""

from rest_framework.authentication import BaseAuthentication
from rest_framework.exceptions import AuthenticationFailed
from django.contrib.auth.models import AnonymousUser
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from .supabase_client import supabase_service
from .models import WebUser
import logging

logger = logging.getLogger(__name__)

class SupabaseAuthentication(BaseAuthentication):
    """Supabase认证类"""
    
    def authenticate(self, request):
        """
        认证请求
        
        返回:
        - (user, auth) 或 None
        """
        # 获取Authorization header
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        
        if not auth_header:
            return None
        
        try:
            # 解析Bearer token
            if not auth_header.startswith('Bearer '):
                return None
            
            token = auth_header.split(' ')[1]
            
            # 验证token
            valid, user_data, error = supabase_service.verify_session(token)
            
            if not valid or not user_data:
                raise AuthenticationFailed(f'认证失败: {error}')
            
            # 创建用户对象
            user = SupabaseUser(user_data)
            
            return (user, None)
            
        except Exception as e:
            logger.error(f"❌ 认证异常: {e}")
            raise AuthenticationFailed('认证失败')
    
    def authenticate_header(self, request):
        """返回认证header名称"""
        return 'Bearer'


class SupabaseUser:
    """Supabase用户对象"""
    
    def __init__(self, user_data):
        self.id = user_data.get('id')
        self.phone = user_data.get('phone')
        self.email = user_data.get('email')
        self.nickname = user_data.get('nickname', '')
        self.credits = user_data.get('credits', 0)
        self.is_authenticated = True
        self.is_anonymous = False
        self.is_active = True
        
        # 存储完整用户数据
        self._user_data = user_data
    
    def __str__(self):
        return f"SupabaseUser({self.phone})"
    
    def get_user_data(self):
        """获取完整用户数据"""
        return self._user_data
    
    @property
    def username(self):
        """兼容Django用户接口"""
        return self.phone or self.email
    
    def has_perm(self, perm, obj=None):
        """权限检查（简化版）"""
        return True
    
    def has_perms(self, perm_list, obj=None):
        """批量权限检查"""
        return True
    
    def has_module_perms(self, app_label):
        """模块权限检查"""
        return True


class SupabasePermission:
    """Supabase权限类"""
    
    @staticmethod
    def check_credits(user, required_credits=1):
        """检查用户积分"""
        if not hasattr(user, 'credits'):
            return False
        return user.credits >= required_credits
    
    @staticmethod
    def consume_credits(user, credits=1):
        """消耗用户积分"""
        if not SupabasePermission.check_credits(user, credits):
            return False
        
        new_credits = user.credits - credits
        success = supabase_service.update_user_credits(user.id, new_credits)
        
        if success:
            user.credits = new_credits
        
        return success


class CustomJWTAuthentication(JWTAuthentication):
    """支持UUID用户ID的JWT认证"""
    
    def get_user(self, validated_token):
        """
        尝试从给定的验证令牌中获取用户
        """
        try:
            user_id = validated_token.get('user_id')
            if user_id is None:
                raise InvalidToken('Token contained no recognizable user identification')
            
            # 支持UUID字符串ID
            user = WebUser.objects.get(id=user_id)
            
            if not user.is_active:
                raise InvalidToken('User account is disabled')
                
            return user
            
        except WebUser.DoesNotExist:
            raise InvalidToken('User not found')
        except Exception as e:
            raise InvalidToken(f'Error getting user: {str(e)}')