/**
 * API客户端工具
 * 自动处理JWT token的添加和请求拦截
 */

class ApiClient {
  constructor(baseURL = 'http://localhost:8000') {
    this.baseURL = baseURL;
  }

  /**
   * 获取认证头
   */
  getAuthHeaders() {
    const token = localStorage.getItem('access_token');
    const headers = {
      'Content-Type': 'application/json',
    };

    // 如果有token，添加到请求头
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  }

  /**
   * 通用请求方法
   */
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      ...options,
      headers: {
        ...this.getAuthHeaders(),
        ...options.headers,
      },
    };

    try {
      console.log(`🌐 API请求: ${config.method || 'GET'} ${url}`);
      if (config.headers['Authorization']) {
        console.log('🔐 包含JWT认证头');
      }

      const response = await fetch(url, config);
      
      if (!response.ok) {
        if (response.status === 401) {
          // Token过期或无效，清除本地存储
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
          console.warn('🔐 认证失败，已清除本地token');
          throw new Error('认证失败，请重新登录');
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`✅ API请求成功: ${url}`);
      return data;
    } catch (error) {
      console.error(`❌ API请求失败: ${url}`, error);
      throw error;
    }
  }

  /**
   * GET请求
   */
  async get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;
    return this.request(url, { method: 'GET' });
  }

  /**
   * POST请求
   */
  async post(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  /**
   * PUT请求
   */
  async put(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  /**
   * DELETE请求
   */
  async delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' });
  }

  /**
   * 检查是否已认证
   */
  isAuthenticated() {
    return !!localStorage.getItem('access_token');
  }

  /**
   * 获取用户信息（需要认证）
   */
  async getUserProfile() {
    return this.get('/api/web/sms/profile/');
  }

  /**
   * 获取积分交易记录（需要认证）
   */
  async getCreditTransactions(page = 1, pageSize = 10) {
    return this.get('/api/web/credit/transactions/', { page, page_size: pageSize });
  }

  /**
   * 购买积分套餐（需要认证）
   */
  async purchaseCredits(packageType, amount) {
    return this.post('/api/web/purchase/', {
      package: packageType,
      amount: amount,
    });
  }

  /**
   * 获取AI分析数据（可选认证）
   */
  async getAIAnalysis(symbol, analysisType = 'quick') {
    return this.post('/api/ai/analysis/', {
      symbol: symbol,
      analysis_type: analysisType,
    });
  }

  /**
   * 获取图表数据（可选认证）
   */
  async getChartData(symbol, interval = '1d') {
    return this.get('/api/web/chart/', { symbol, interval });
  }

  /**
   * 获取技术指标（可选认证）
   */
  async getTechnicalIndicators(symbol, indicators = ['MA', 'MACD', 'RSI']) {
    return this.post('/api/web/technical-indicators/', {
      symbol: symbol,
      indicators: indicators,
    });
  }
}

// 创建全局实例
const apiClient = new ApiClient();

export default apiClient;
