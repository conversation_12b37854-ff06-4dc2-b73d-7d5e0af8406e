/**
 * 禁用的Binance WebSocket服务
 * 已替换为定时刷新模式，更稳定可靠
 */

class BinanceWebSocket {
  constructor() {
    console.log('Binance WebSocket服务已禁用，使用定时刷新模式');
  }

  subscribe(symbol, interval, callback) {
    console.log(`WebSocket订阅已禁用: ${symbol} ${interval}`);
    return 'disabled';
  }

  unsubscribe(symbol, interval, callback) {
    console.log(`WebSocket取消订阅已禁用: ${symbol} ${interval}`);
  }

  getConnectionStatus(symbol, interval) {
    return 'disabled';
  }

  getStats() {
    return {
      connected: false,
      subscriptions: 0,
      totalMessages: 0,
      errors: 0
    };
  }

  disconnect() {
    console.log('WebSocket断开连接已禁用');
  }
}

const binanceWebSocket = new BinanceWebSocket();
export default binanceWebSocket;