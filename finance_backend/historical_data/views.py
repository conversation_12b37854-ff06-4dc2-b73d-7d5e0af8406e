"""
历史数据API视图
"""

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
from django.core.cache import cache
from datetime import datetime, timedelta
import logging

from .services import HistoricalDataService
from .models import HistoricalKlineData, TechnicalIndicatorData, AIAnalysisResult

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_chart_data(request):
    """
    获取图表数据API
    
    参数:
    - symbol: 交易对 (必需)
    - interval: 时间间隔 (默认15m)
    - period: 时间范围 (默认1d)
    
    示例:
    GET /api/historical/chart?symbol=BTCUSDT&interval=15m&period=1w
    """
    try:
        # 获取参数
        symbol = request.GET.get('symbol', '').upper()
        interval = request.GET.get('interval', '15m')
        period = request.GET.get('period', '1d')
        
        # 验证参数
        if not symbol:
            return Response({
                'error': 'symbol参数是必需的'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        valid_symbols = ['BTCUSDT', 'ETHUSDT', 'DOGEUSDT', 'LTCUSDT', 'TRUMPUSDT', 'SOLUSDT', 'XRPUSDT']
        if symbol not in valid_symbols:
            return Response({
                'error': f'不支持的交易对，支持: {", ".join(valid_symbols)}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        valid_intervals = ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d', '3d', '1w', '1mo']
        if interval not in valid_intervals:
            return Response({
                'error': f'不支持的时间间隔，支持: {", ".join(valid_intervals)}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        valid_periods = ['1d', '1w', '1m', '3m', '6m', '1y']
        if period not in valid_periods:
            return Response({
                'error': f'不支持的时间范围，支持: {", ".join(valid_periods)}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 缓存键
        cache_key = f"chart_data_{symbol}_{interval}_{period}"
        
        # 尝试从缓存获取
        cached_data = cache.get(cache_key)
        if cached_data:
            logger.info(f"从缓存返回图表数据: {symbol} {interval} {period}")
            return Response(cached_data)
        
        # 获取数据
        service = HistoricalDataService()
        chart_data = service.get_chart_data(symbol, interval, period)
        
        if 'error' in chart_data:
            return Response(chart_data, status=status.HTTP_404_NOT_FOUND)
        
        # 添加元数据
        chart_data['generated_at'] = timezone.now().isoformat()
        chart_data['cache_ttl'] = 300  # 5分钟缓存
        
        # 缓存数据
        cache.set(cache_key, chart_data, 300)  # 缓存5分钟
        
        logger.info(f"返回图表数据: {symbol} {interval} {period}, {chart_data['data_points']} 个数据点")
        
        return Response(chart_data)
        
    except Exception as e:
        logger.error(f"获取图表数据失败: {e}")
        return Response({
            'error': '服务器内部错误'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_technical_indicators(request):
    """
    获取技术指标数据API
    
    参数:
    - symbol: 交易对 (必需)
    - interval: 时间间隔 (默认15m)
    
    示例:
    GET /api/historical/indicators?symbol=BTCUSDT&interval=15m
    """
    try:
        symbol = request.GET.get('symbol', '').upper()
        interval = request.GET.get('interval', '15m')
        
        if not symbol:
            return Response({
                'error': 'symbol参数是必需的'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 缓存键
        cache_key = f"indicators_{symbol}_{interval}"
        
        # 尝试从缓存获取
        cached_data = cache.get(cache_key)
        if cached_data:
            return Response(cached_data)
        
        # 获取数据并计算指标
        service = HistoricalDataService()
        df = service.load_recent_data(symbol, interval, 200)
        
        if df.empty:
            return Response({
                'error': '未找到数据'
            }, status=status.HTTP_404_NOT_FOUND)
        
        indicators = service.calculate_technical_indicators(df)
        
        if not indicators:
            return Response({
                'error': '计算技术指标失败'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        # 添加元数据
        response_data = {
            'symbol': symbol,
            'interval': interval,
            'timestamp': timezone.now().isoformat(),
            'indicators': indicators
        }
        
        # 缓存数据
        cache.set(cache_key, response_data, 180)  # 缓存3分钟
        
        return Response(response_data)
        
    except Exception as e:
        logger.error(f"获取技术指标失败: {e}")
        return Response({
            'error': '服务器内部错误'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def get_supported_symbols(request):
    """
    获取支持的交易对列表
    
    示例:
    GET /api/historical/symbols
    """
    symbols = [
        {
            'symbol': 'BTCUSDT',
            'name': 'Bitcoin',
            'base_asset': 'BTC',
            'quote_asset': 'USDT',
            'start_date': '2017-08-01'
        },
        {
            'symbol': 'ETHUSDT',
            'name': 'Ethereum',
            'base_asset': 'ETH',
            'quote_asset': 'USDT',
            'start_date': '2017-08-01'
        },
        {
            'symbol': 'DOGEUSDT',
            'name': 'Dogecoin',
            'base_asset': 'DOGE',
            'quote_asset': 'USDT',
            'start_date': '2019-07-01'
        },
        {
            'symbol': 'LTCUSDT',
            'name': 'Litecoin',
            'base_asset': 'LTC',
            'quote_asset': 'USDT',
            'start_date': '2017-12-01'
        },
        {
            'symbol': 'TRUMPUSDT',
            'name': 'Trump Token',
            'base_asset': 'TRUMP',
            'quote_asset': 'USDT',
            'start_date': '2025-01-01'
        },
        {
            'symbol': 'SOLUSDT',
            'name': 'Solana',
            'base_asset': 'SOL',
            'quote_asset': 'USDT',
            'start_date': '2020-08-01'
        },
        {
            'symbol': 'XRPUSDT',
            'name': 'Ripple',
            'base_asset': 'XRP',
            'quote_asset': 'USDT',
            'start_date': '2018-05-01'
        }
    ]
    
    return Response({
        'symbols': symbols,
        'total_count': len(symbols),
        'supported_intervals': ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d', '3d', '1w', '1mo'],
        'supported_periods': ['1d', '1w', '1m', '3m', '6m', '1y']
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def analyze_with_ai(request):
    """
    AI分析接口
    
    POST数据:
    {
        "symbol": "BTCUSDT",
        "interval": "15m",
        "analysis_type": "technical"  // technical, fundamental, sentiment
    }
    """
    try:
        symbol = request.data.get('symbol', '').upper()
        interval = request.data.get('interval', '15m')
        analysis_type = request.data.get('analysis_type', 'technical')
        
        if not symbol:
            return Response({
                'error': 'symbol参数是必需的'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 获取历史数据和技术指标
        service = HistoricalDataService()
        df = service.load_recent_data(symbol, interval, 100)
        
        if df.empty:
            return Response({
                'error': '未找到历史数据'
            }, status=status.HTTP_404_NOT_FOUND)
        
        indicators = service.calculate_technical_indicators(df)
        
        # 这里可以集成AI分析逻辑
        # 目前返回基础的技术分析
        
        # 简单的规则分析
        recommendation = "HOLD"
        confidence = 50.0
        
        if indicators.get('rsi', 50) > 70:
            recommendation = "SELL"
            confidence = 75.0
        elif indicators.get('rsi', 50) < 30:
            recommendation = "BUY"
            confidence = 75.0
        elif indicators.get('ma5', 0) > indicators.get('ma10', 0):
            recommendation = "BUY"
            confidence = 60.0
        
        analysis_result = {
            'symbol': symbol,
            'interval': interval,
            'analysis_type': analysis_type,
            'timestamp': timezone.now().isoformat(),
            'current_price': indicators.get('current_price', 0),
            'technical_indicators': indicators,
            'recommendation': recommendation,
            'confidence': confidence,
            'analysis': f"基于技术指标分析，当前RSI为{indicators.get('rsi', 0):.2f}，"
                      f"MA5为{indicators.get('ma5', 0):.4f}，MA10为{indicators.get('ma10', 0):.4f}。"
                      f"建议{recommendation}，信心度{confidence}%。"
        }
        
        return Response(analysis_result)
        
    except Exception as e:
        logger.error(f"AI分析失败: {e}")
        return Response({
            'error': '分析失败'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_chart_indicators(request):
    """
    获取图表指标时间序列API
    
    参数:
    - symbol: 交易对 (必需)
    - interval: 时间间隔 (默认15m)
    - period: 时间范围 (默认1w)
    - indicators: 指标列表，逗号分隔 (如: macd,rsi,bbands)
    
    示例:
    GET /api/historical/chart-indicators?symbol=BTCUSDT&interval=15m&period=1w&indicators=macd,rsi,bbands
    """
    try:
        symbol = request.GET.get('symbol', '').upper()
        interval = request.GET.get('interval', '15m')
        period = request.GET.get('period', '1w')
        indicators_param = request.GET.get('indicators', 'macd,rsi,bbands,sma_5,sma_10,sma_20')
        
        if not symbol:
            return Response({
                'error': 'symbol参数是必需的'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 解析指标列表
        indicators = [ind.strip() for ind in indicators_param.split(',') if ind.strip()]
        
        # 缓存键
        cache_key = f"chart_indicators_{symbol}_{interval}_{period}_{'-'.join(indicators)}"
        
        # 尝试从缓存获取
        cached_data = cache.get(cache_key)
        if cached_data:
            return Response(cached_data)
        
        # 获取数据
        service = HistoricalDataService()
        df = service.load_recent_data(symbol, interval, 
                                    period_limits.get(period, 672))
        
        if df.empty:
            return Response({
                'error': '未找到数据'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 获取图表指标
        chart_indicators = service.indicator_service.get_chart_indicators(
            service.indicator_service.prepare_dataframe(df.to_dict('records')),
            indicators
        )
        
        if not chart_indicators:
            return Response({
                'error': '计算图表指标失败'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        response_data = {
            'symbol': symbol,
            'interval': interval,
            'period': period,
            'indicators': chart_indicators,
            'timestamp': timezone.now().isoformat()
        }
        
        # 缓存数据
        cache.set(cache_key, response_data, 300)  # 缓存5分钟
        
        return Response(response_data)
        
    except Exception as e:
        logger.error(f"获取图表指标失败: {e}")
        return Response({
            'error': '服务器内部错误'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 定义period限制常量
period_limits = {
    '1d': 96,      # 1天 = 96个15分钟
    '1w': 672,     # 1周 = 672个15分钟
    '1m': 2880,    # 1月 = 2880个15分钟
    '3m': 8640,    # 3月
    '6m': 17280,   # 6月
    '1y': 35040,   # 1年
}