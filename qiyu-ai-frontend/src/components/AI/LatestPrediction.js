import React from 'react';
import { Tag, Empty, Tooltip } from 'antd';
import { ClockCircleOutlined, FireOutlined } from '@ant-design/icons';

const LatestPrediction = ({ prediction, symbol = 'BTCUSDT' }) => {
  if (!prediction) {
    return (
      <div style={{
        background: 'rgba(255, 255, 255, 0.03)',
        border: '1px solid rgba(255, 255, 255, 0.08)',
        borderRadius: '12px',
        padding: '16px',
        color: '#fff',
        textAlign: 'center'
      }}>
        <Empty 
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="暂无最新预测"
          style={{ margin: '20px 0' }}
        />
      </div>
    );
  }

  const getSentimentColor = (sentimentScore) => {
    if (sentimentScore > 0) return '#52c41a';  // Green for bullish
    if (sentimentScore < 0) return '#ff4d4f';  // Red for bearish
    return '#faad14';  // Orange for neutral
  };

  const getSentimentText = (sentimentScore) => {
    if (sentimentScore > 0) return '看涨';
    if (sentimentScore < 0) return '看跌';
    return '中性';
  };

  const formatTime = (timeStr) => {
    try {
      const date = new Date(timeStr);
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return timeStr;
    }
  };

  const formatPrice = (price) => {
    if (!price || price === 0) return '数据异常';
    return `$${parseFloat(price).toLocaleString()}`;
  };

  return (
    <div style={{
      background: 'rgba(255, 255, 255, 0.03)',
      border: '1px solid rgba(255, 255, 255, 0.08)',
      padding: '5px 16px 5px 16px',
      color: '#fff',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      overflowY: 'auto'
    }}>
      {/* Header with all non-content elements in one row */}
      <div style={{ 
        display: 'flex', 
        alignItems: 'center',
        gap: 8,
        marginBottom: 8,
        flexShrink: 0,
        flexWrap: 'wrap'
      }}>
        <FireOutlined style={{ color: '#ff4d4f', fontSize: 14 }} />
        <Tag color="red" size="small">最新预测</Tag>
        <ClockCircleOutlined style={{ color: '#999', fontSize: 12 }} />
        <span style={{ fontSize: 12, color: '#ccc' }}>
          {formatTime(prediction.created_at)}
        </span>
        <Tag 
          color={getSentimentColor(prediction.sentiment_score)}
          size="small"
        >
          {getSentimentText(prediction.sentiment_score)}
        </Tag>
      </div>

      {/* Analysis summary with tooltip */}
      <div style={{ 
        fontSize: 13, 
        color: '#ccc', 
        lineHeight: '1.5',
        marginBottom: 8,
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        minHeight: 0,
        overflowY: 'auto'
      }}>
        <Tooltip 
          title={prediction.analysis_summary || '暂无分析总结'}
          placement="topLeft"
          overlayStyle={{ maxWidth: '400px' }}
        >
          <div style={{
            wordBreak: 'break-word',
            whiteSpace: 'pre-wrap',
            cursor: 'help',
            flex: 1,
            overflow: 'auto',
            minHeight: 0
          }}>
            {prediction.analysis_summary || '暂无分析总结'}
          </div>
        </Tooltip>
      </div>

    </div>
  );
};

export default LatestPrediction;