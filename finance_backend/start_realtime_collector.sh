#!/bin/bash

# 实时数据收集器启动脚本
# 用于启动后端常驻WebSocket数据收集服务

echo "🚀 启动实时数据收集器..."

# 设置环境变量
export DJANGO_SETTINGS_MODULE=finance_project.settings

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 激活conda环境
echo "📦 激活conda环境: qiyu-web"
source ~/miniconda3/etc/profile.d/conda.sh 2>/dev/null || source ~/anaconda3/etc/profile.d/conda.sh 2>/dev/null
conda activate qiyu-web

# 验证环境激活
if [ "$CONDA_DEFAULT_ENV" = "qiyu-web" ]; then
    echo "✅ conda环境已激活: $CONDA_DEFAULT_ENV"
else
    echo "⚠️ conda环境激活可能失败，当前环境: $CONDA_DEFAULT_ENV"
    echo "💡 请手动运行: conda activate qiyu-web"
fi

# 检查必要的包
echo "🔍 检查依赖包..."
python -c "import websockets; print('✅ websockets已安装')" 2>/dev/null || {
    echo "❌ websockets未安装，正在安装..."
    pip install websockets
}

python -c "import boto3; print('✅ boto3已安装')" 2>/dev/null || {
    echo "❌ boto3未安装，正在安装..."
    pip install boto3
}

# 启动数据收集器
echo "🎯 启动BTCUSDT实时数据收集..."
echo "💡 按Ctrl+C停止服务"
echo "📁 日志将保存到 realtime_collector.log"

# 启动命令（带日志记录）
python manage.py collect_realtime_data \
    --symbol=BTCUSDT \
    --interval=300 \
    --daemon \
    2>&1 | tee realtime_collector.log

echo "✅ 实时数据收集器已停止"