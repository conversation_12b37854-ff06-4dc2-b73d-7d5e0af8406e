"""
AI分析提示词模板
包含不同场景下的AI分析提示词模板
"""

import json
from typing import Dict, List, Optional


class AIPromptTemplates:
    """AI提示词模板类"""
    
    @staticmethod
    def build_basic_analysis_prompt(
        real_time_price: Dict,
        formatted_daily_klines: List[Dict],
        formatted_short_term_klines: List[Dict],
        latest_date: str
    ) -> str:
        """构建基础分析提示词"""
        return f"""以下是截至{latest_date}的加密货币价格数据，请在回答中参考这些数据：

【实时价格数据】: {json.dumps(real_time_price, ensure_ascii=False)}

【90天日K线数据】（用于长期趋势分析，共{len(formatted_daily_klines)}条记录）: {json.dumps(formatted_daily_klines, ensure_ascii=False)}

【短期K线数据】（用于短期技术分析，共{len(formatted_short_term_klines)}条记录）: {json.dumps(formatted_short_term_klines[-100:], ensure_ascii=False)}

重要提示：
- 90天日K线数据适用于长期趋势分析、支撑阻力位判断
- 短期K线数据适用于短线交易信号、入场时机判断
- 请根据用户的分析需求选择合适的数据进行分析
- 所有数据均为真实市场数据，请务必基于这些数据进行专业分析

请基于以上数据进行分析，给出持仓建议。"""

    @staticmethod
    def build_historical_comparison_prompt(
        real_time_price: Dict,
        formatted_daily_klines: List[Dict],
        formatted_short_term_klines: List[Dict],
        latest_date: str,
        target_date: str,
        historical_data: Dict
    ) -> str:
        """构建历史同期对比分析提示词"""
        
        historical_analysis_section = f"""

【历史同期对比分析】（目标日期：{target_date}）:
- 分析范围：{historical_data.get('target_date', target_date)}前后3天
- 历史年份数据：{historical_data.get('total_years', 0)}年
- 历史同期数据：{json.dumps(historical_data.get('historical_data', {}), ensure_ascii=False)}
- 目标时间段技术指标：{json.dumps(historical_data.get('target_indicators', {}), ensure_ascii=False)}
- 历史同期技术指标对比：{json.dumps(historical_data.get('historical_indicators', {}), ensure_ascii=False)}

【历史同期分析要求】：
- 请重点关注历史同期的价格走势和技术指标表现
- 对比分析目标时间与历史同期的相似性和差异性
- 基于历史同期数据预测未来走势方向（多/空）
- 给出明确的情绪分析（非常看跌/看跌/其他/看涨/非常看涨）
- 分析历史同期的成功率和失败率模式"""
        
        return f"""以下是截至{latest_date}的加密货币价格数据，请在回答中参考这些数据：

【实时价格数据】: {json.dumps(real_time_price, ensure_ascii=False)}

【90天日K线数据】（用于长期趋势分析，共{len(formatted_daily_klines)}条记录）: {json.dumps(formatted_daily_klines, ensure_ascii=False)}

【短期K线数据】（用于短期技术分析，共{len(formatted_short_term_klines)}条记录）: {json.dumps(formatted_short_term_klines[-100:], ensure_ascii=False)}{historical_analysis_section}

【分析框架要求】：
1. 技术分析层面：
   - 基于90天日K线数据分析长期趋势
   - 基于短期K线数据分析短线信号
   - 结合技术指标（RSI、MACD、KDJ等）判断买卖点

2. 历史对比层面：
   - 对比历史同期的价格走势模式
   - 分析历史同期技术指标的表现
   - 识别历史重复模式和异常情况

3. 预测输出要求：
   - 明确预测方向：多（看涨）/空（看跌）
   - 情绪分析等级：非常看跌/看跌/其他/看涨/非常看涨
   - 置信度评估：0-100%
   - 风险等级：高/中/低

4. 分析结构：
   - 思考过程：详细的分析逻辑
   - 关键发现：重要的技术信号和历史模式
   - 最终结论：综合判断和投资建议

请基于以上数据进行深度分析，给出专业的投资建议。"""

    @staticmethod
    def build_structured_response_template() -> str:
        """构建结构化响应模板说明"""
        return """
请按照以下JSON格式输出分析结果：

{
    "prediction": "多" | "空",
    "sentiment": "非常看跌" | "看跌" | "其他" | "看涨" | "非常看涨",
    "confidence": 0.85,
    "risk_level": "高" | "中" | "低",
    "thinking_process": "详细的分析思考过程...",
    "key_findings": [
        "关键发现1",
        "关键发现2",
        "关键发现3"
    ],
    "historical_insights": {
        "pattern_match": "历史模式匹配度",
        "success_rate": "历史成功率",
        "anomalies": "异常情况说明"
    },
    "technical_signals": {
        "trend": "趋势方向",
        "support_resistance": "支撑阻力位",
        "indicators": "技术指标信号"
    },
    "conclusion": "最终投资建议和结论",
    "timestamp": "分析时间戳"
}
"""

    @staticmethod
    def build_quick_analysis_prompt(symbol: str, analysis_type: str) -> str:
        """构建快速分析提示词"""
        analysis_periods = {
            'quick': '未来24小时',
            'four_hour': '未来4小时',
            'one_day': '未来24小时',
            'three_day': '未来3天',
            'week': '未来一周',
            'long_term': '长期趋势'
        }
        
        period = analysis_periods.get(analysis_type, '未来24小时')
        
        return f"""请对{symbol}进行{analysis_type}分析，预测{period}的价格走势。

分析要求：
1. 基于提供的K线数据和技术指标
2. 给出明确的方向预测（多/空）
3. 评估风险等级和置信度
4. 提供具体的操作建议

请简洁明了地给出分析结果。"""

    @staticmethod
    def build_web_analysis_prompt(symbol: str, crypto_context: str, target_date: str = None) -> str:
        """构建Web版本专用的AI分析提示词 - 专注于三大要素"""
        date_context = f"基于{target_date}的市场数据，" if target_date else "基于当前市场数据，"
        
        return f"""你是一位专业的加密货币分析师。{date_context}请进行深度技术分析。

{crypto_context}

请按以下结构提供分析报告：

## 1. 市场情绪分析
从以下5个等级中选择当前市场情绪：
- **非常看涨** - 市场极度乐观，买盘强劲
- **看涨** - 市场情绪积极，多头占主导
- **其他** - 市场情绪中性，多空平衡
- **看跌** - 市场情绪偏负面，空头力量增强
- **非常看跌** - 市场极度悲观，抛售压力巨大

请明确选择其中一个等级，并说明判断依据。

## 2. BTC和ETH多空预测

### BTC技术分析：
- **MACD指标**：DIF线、DEA线、MACD柱状图数值及趋势
- **KDJ指标**：K值、D值、J值的具体数值及超买/超卖判断
- **RSI指标**：RSI数值、强弱区间判断、背离信号
- **多空判断**：明确看多/看空，给出具体理由
- **关键价位**：重要支撑位、阻力位、短期目标价

### ETH技术分析：
- **MACD指标**：DIF线、DEA线、MACD柱状图数值及趋势
- **KDJ指标**：K值、D值、J值的具体数值及超买/超卖判断
- **RSI指标**：RSI数值、强弱区间判断、背离信号
- **多空判断**：明确看多/看空，给出具体理由
- **关键价位**：重要支撑位、阻力位、短期目标价

## 3. AI分析思考过程
- **数据解读思路**：如何从K线图形中识别趋势和形态
- **指标权重分配**：MACD、KDJ、RSI在当前市况下的重要性排序
- **信号确认逻辑**：多个指标如何相互验证形成交易信号
- **风险因素识别**：当前市场面临的主要风险点
- **结论推导过程**：从技术面分析到最终判断的完整推理链条
- **置信度评估**：对本次分析结果的信心程度（1-10分，10分最高）

注意：请基于提供的真实K线数据和技术指标进行分析，给出准确、专业的判断。"""

    @staticmethod
    def build_structured_prediction_prompt(
        symbol: str,
        target_date: str,
        current_price: float,
        support_level: float,
        resistance_level: float,
        technical_indicators: Dict,
        prev_day_price: float,
        historical_prices: List[float],
        btc_data: Dict = None
    ) -> str:
        """构建结构化多空预测提示词 - 匹配用户需求格式"""
        
        # 格式化技术指标
        macd_info = technical_indicators.get('MACD', {})
        kdj_info = technical_indicators.get('KDJ', {})
        rsi_info = technical_indicators.get('RSI', {})
        
        # 格式化BTC参考数据
        btc_section = ""
        if btc_data and 'price_change' in btc_data:
            btc_change = btc_data['price_change']
            btc_indicators = btc_data.get('technical_indicators', {})
            btc_macd = btc_indicators.get('MACD', {})
            
            btc_section = f"""
6. BTC 同时段走势：{btc_change.get('trend', '无数据')} {btc_change.get('change_percent', 0):+.1f}%，MACD {'红柱增强' if btc_macd.get('MACD', 0) > 0 else '绿柱增强' if btc_macd.get('MACD', 0) < 0 else '零轴附近'}"""
        
        return f"""请根据以下行情数据，为目标币种生成多空预测和分析总结：

【目标币种：{symbol.replace('USDT', '')}】
【目标日期：{target_date}】

1. 当前价格：{current_price:.0f} USDT
2. 支撑位：{support_level:.0f}，阻力位：{resistance_level:.0f}
3. 当日指标：
   - MACD DIF: {macd_info.get('DIF', 0):.2f}
   - 日线KDJ{kdj_info.get('trend', '金叉' if kdj_info.get('K', 0) > kdj_info.get('D', 0) else '死叉')}
   - RSI：{rsi_info.get('value', 50):.0f}（{rsi_info.get('state', '正常')}）
4. 目标日前一天价格：{prev_day_price:.0f}
5. 目标日前后三天价格：{', '.join([f'{p:.0f}' for p in historical_prices[:3]])}{btc_section}

请给出：
- 简洁的策略分析总结（最多150字）
- 多空预测分布（百分比 + 选项：看涨、非常看涨、中性、非常看跌、看跌）
- BTC 与该币种的市场情绪（多头比例，BTC/{symbol.replace('USDT', '')}，单位 %）

输出格式为：
【分析总结】
...
【多空预测】
看涨: xx%，非常看涨: xx%，中性: xx%，非常看跌: xx%，看跌: xx%
【市场情绪】
BTC 多头占比: xx%，{symbol.replace('USDT', '')} 多头占比: xx%
"""


# 使用示例
if __name__ == "__main__":
    templates = AIPromptTemplates()
    
    # 测试基础分析提示词
    basic_prompt = templates.build_basic_analysis_prompt(
        real_time_price={"symbol": "BTCUSDT", "price": "45000"},
        formatted_daily_klines=[],
        formatted_short_term_klines=[],
        latest_date="2024-01-15 10:30:00"
    )
    
    print("基础分析提示词:")
    print(basic_prompt)
    
    # 测试结构化响应模板
    response_template = templates.build_structured_response_template()
    print("\n结构化响应模板:")
    print(response_template)
