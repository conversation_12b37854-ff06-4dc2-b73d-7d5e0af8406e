// 使用Stream API正确处理COS分块传输的数据服务
class StreamCryptoDataService {
  constructor() {
    this.cache = new Map()
    this.loggedInitializations = new Set() // 跟踪已记录的初始化

    // 多路径配置：不同年份使用不同的COS路径
    this.cosPaths = {
      // 历史数据路径（20250724）- 2017-2024年
      'legacy': 'https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/finance-1324685443/finance-1324685443/finance-1324685443/crypto-kline-data-v2/20250724',
      // 2025年数据路径（20250726）
      '2025': 'https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/20250726'
    }
  }

  // 根据年份获取对应的COS路径
  getCosPathForYear(year) {
    if (year === '2025') {
      return this.cosPaths['2025']
    } else {
      return this.cosPaths['legacy']
    }
  }

  // 🎯 获取支持的货币列表
  getSupportedSymbols() {
    return ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'DOGEUSDT', 'ADAUSDT', 'DOTUSDT', 'LINKUSDT', 'LTCUSDT', 'XRPUSDT']
  }

  // 🎯 获取支持的时间周期列表
  getSupportedTimeframes() {
    return ['1m', '15m', '1h', '4h', '1d', '1mo']
  }

  // 使用Stream API处理分块传输
  async loadDataWithStream(url) {
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    // 使用ReadableStream处理分块数据
    const reader = response.body.getReader()
    const decoder = new TextDecoder('utf-8')
    
    let result = ''
    let chunks = []
    
    try {
      while (true) {
        const { done, value } = await reader.read()
        
        if (done) {
          break
        }
        
        chunks.push(value)
        
        // 解码当前块
        const chunk = decoder.decode(value, { stream: true })
        result += chunk
      }
      
      // 最终解码
      const finalChunk = decoder.decode()
      if (finalChunk) {
        result += finalChunk
      }
      
      // 清理分块传输标记
      const cleanedData = this.cleanChunkedData(result)
      
      // 解析JSON
      const jsonData = JSON.parse(cleanedData)
      
      return jsonData
      
    } finally {
      reader.releaseLock()
    }
  }

  // 清理分块传输标记的改进版本
  cleanChunkedData(rawData) {
    // 如果数据以{开头，可能没有分块标记
    if (rawData.trim().startsWith('{')) {
      return rawData.trim()
    }
    
    // 移除分块传输的size headers
    let cleaned = rawData
      // 移除开头的分块大小（十六进制数字 + CRLF）
      .replace(/^[0-9a-fA-F]+\r?\n/, '')
      // 移除中间的分块大小标记
      .replace(/\r?\n[0-9a-fA-F]+\r?\n/g, '')
      // 移除结尾的0标记
      .replace(/\r?\n0\r?\n\r?\n?$/, '')
      // 移除孤立的十六进制行
      .replace(/^\s*[0-9a-fA-F]+\s*$/gm, '')
      .trim()
    
    // 确保数据以{开头和}结尾
    const firstBrace = cleaned.indexOf('{')
    const lastBrace = cleaned.lastIndexOf('}')
    
    if (firstBrace >= 0 && lastBrace >= 0 && lastBrace > firstBrace) {
      cleaned = cleaned.substring(firstBrace, lastBrace + 1)
    }
    
    return cleaned
  }

  // 带重试机制的数据加载
  async loadYearDataWithRetry(symbol = 'BTCUSDT', timeframe = '1m', year = '2024', maxRetries = 3) {
    const cacheKey = `${symbol}_${timeframe}_${year}`

    // 检查缓存
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    // 🎯 2025年数据：智能合并历史数据 + 断层数据
    if (year === '2025') {
      return this.loadMerged2025Data(symbol, timeframe, maxRetries)
    }


    // 其他情况使用原有逻辑
    const cosBasePath = this.getCosPathForYear(year)
    const dataURL = `${cosBasePath}/${symbol}/${timeframe}/${symbol}_${timeframe}_${year}_compressed.json`

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const jsonData = await this.loadDataWithStream(dataURL)
        const klineData = jsonData.data || []

        if (klineData.length === 0) {
          throw new Error('数据为空')
        }

        // 转换数据格式
        const rawProcessedData = klineData.map(item => {
          if (Array.isArray(item)) {
            // 🎯 检查时间戳格式，如果是微秒则转换为毫秒
            const rawTimestamp = item[0]
            const timestamp = rawTimestamp > 1e15 ? Math.floor(rawTimestamp / 1000) : rawTimestamp
            
            return {
              timestamp: timestamp, // 转换后的毫秒时间戳
              open: parseFloat(item[1]),
              high: parseFloat(item[2]),
              low: parseFloat(item[3]),
              close: parseFloat(item[4]),
              volume: parseFloat(item[5]),
              turnover: parseFloat(item[1]) * parseFloat(item[5])
            }
          } else {
            // 对象格式数据的时间戳处理
            const rawTimestamp = item.timestamp
            const timestamp = rawTimestamp > 1e15 ? Math.floor(rawTimestamp / 1000) : rawTimestamp
            
            return {
              timestamp: timestamp, // 转换后的毫秒时间戳
              open: parseFloat(item.open),
              high: parseFloat(item.high),
              low: parseFloat(item.low),
              close: parseFloat(item.close),
              volume: parseFloat(item.volume),
              turnover: item.turnover || (parseFloat(item.open) * parseFloat(item.volume))
            }
          }
        })

        // 🧹 跳过数据清洗，直接使用原始处理后的数据
        const processedData = rawProcessedData

        // 验证数据与请求的年份是否匹配
        if (processedData.length > 0) {
          const firstYear = new Date(processedData[0].timestamp).getFullYear()
          const lastYear = new Date(processedData[processedData.length - 1].timestamp).getFullYear()
          const requestedYear = parseInt(year)

          if (firstYear !== requestedYear && lastYear !== requestedYear) {
            console.warn(`⚠️ [数据源] 年份不匹配! 请求${requestedYear}年，但数据是${firstYear}-${lastYear}年`)
          }
        }

        // 缓存数据
        this.cache.set(cacheKey, processedData)

        // 📊 只在首次加载时记录简化日志
        if (!this.loggedInitializations.has(cacheKey)) {
          console.log(`📊 [数据源] ${symbol} ${timeframe} ${year}年: ${processedData.length}条记录`)
          this.loggedInitializations.add(cacheKey)
        }

        return processedData
        
      } catch (error) {
        console.error(`❌ [数据源] 尝试 ${attempt} 失败:`, error.message)
        
        if (attempt === maxRetries) {
          console.error(`💥 [数据源] 所有重试失败，使用回退数据`)
          return this.generateFallbackData(symbol, timeframe, year)
        }
        
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
      }
    }
  }

  // 专门处理2025年1分钟数据的方法（月度+每日数据+断层数据）
  async load2025MinuteData(symbol, maxRetries = 3) {
    const cacheKey = `${symbol}_1m_2025`
    
    // 检查缓存
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    const cosBasePath = this.getCosPathForYear('2025')
    const allData = []

    try {
      // 🎯 首先尝试从RealTimeDataManager获取断层数据
      const gapData = this.getGapDataFromManager(symbol, '1m')
      if (gapData && gapData.length > 0) {
        console.log(`📥 使用断层修复数据: ${symbol} 1m (${gapData.length} 条记录)`)
        allData.push(...gapData.map(item => [
          item.timestamp,
          item.open,
          item.high,
          item.low,
          item.close,
          item.volume
        ]))
      }
      // 1. 加载月度数据 (1-6月)
      for (let month = 1; month <= 6; month++) {
        const monthStr = month.toString().padStart(2, '0')
        const monthlyURL = `${cosBasePath}/${symbol}/1m/${symbol}_1m_${monthStr}_2025_compressed.json`
        
        try {
          const jsonData = await this.loadDataWithStream(monthlyURL)
          const klineData = jsonData.data || []
          allData.push(...klineData)
          
          if (!this.loggedInitializations.has(`${symbol}_1m_${monthStr}_2025`)) {
            console.log(`📊 [月度数据] ${symbol} 1m ${monthStr}月: ${klineData.length}条记录`)
            this.loggedInitializations.add(`${symbol}_1m_${monthStr}_2025`)
          }
        } catch (error) {
          console.warn(`⚠️ [月度数据] ${symbol} ${monthStr}月加载失败:`, error.message)
        }
      }

      // 2. 加载每日数据 (7月1-31日) - 动态检测当前日期
      const currentDate = new Date()
      const currentMonth = currentDate.getMonth() + 1  // 0-11 -> 1-12
      const currentDay = currentDate.getDate()
      
      // 如果是7月份，加载到当前日期；否则加载整月31天
      const maxDay = currentMonth === 7 && currentDate.getFullYear() === 2025 ? currentDay : 31
      
      console.log(`📅 动态加载每日数据: 7月1日-${maxDay}日 (当前: ${currentDate.toLocaleDateString()})`)
      
      for (let day = 1; day <= maxDay; day++) {
        const dayStr = day.toString().padStart(2, '0')
        const dailyURL = `${cosBasePath}/${symbol}/1m/daily/${symbol}_1m_07-${dayStr}_2025_compressed.json`
        
        try {
          const jsonData = await this.loadDataWithStream(dailyURL)
          const klineData = jsonData.data || []
          allData.push(...klineData)
          
          if (!this.loggedInitializations.has(`${symbol}_1m_07-${dayStr}_2025`)) {
            console.log(`📊 [每日数据] ${symbol} 1m 07-${dayStr}: ${klineData.length}条记录`)
            this.loggedInitializations.add(`${symbol}_1m_07-${dayStr}_2025`)
          }
        } catch (error) {
          console.warn(`⚠️ [每日数据] ${symbol} 07-${dayStr}加载失败:`, error.message)
        }
      }

      // 3. 按时间戳排序并转换数据格式
      allData.sort((a, b) => a[0] - b[0]) // 按时间戳排序

      const rawProcessedData = allData.map(item => {
        if (Array.isArray(item)) {
          // 🎯 2025年数据使用微秒时间戳，需要转换为毫秒
          const rawTimestamp = item[0]
          const timestamp = rawTimestamp > 1e15 ? Math.floor(rawTimestamp / 1000) : rawTimestamp
          
          return {
            timestamp: timestamp, // 转换后的毫秒时间戳
            open: parseFloat(item[1]),
            high: parseFloat(item[2]),
            low: parseFloat(item[3]),
            close: parseFloat(item[4]),
            volume: parseFloat(item[5]),
            turnover: parseFloat(item[1]) * parseFloat(item[5])
          }
        } else {
          // 对象格式数据的时间戳处理
          const rawTimestamp = item.timestamp
          const timestamp = rawTimestamp > 1e15 ? Math.floor(rawTimestamp / 1000) : rawTimestamp
          
          return {
            timestamp: timestamp,
            open: parseFloat(item.open),
            high: parseFloat(item.high),
            low: parseFloat(item.low),
            close: parseFloat(item.close),
            volume: parseFloat(item.volume),
            turnover: item.turnover || (parseFloat(item.open) * parseFloat(item.volume))
          }
        }
      })

      // 🧹 数据清洗：过滤异常价格数据
      const processedData = this.cleanAbnormalPriceData(rawProcessedData)

      // 缓存数据
      this.cache.set(cacheKey, processedData)

      // 记录加载完成日志
      if (!this.loggedInitializations.has(cacheKey)) {
        console.log(`📊 [2025数据] ${symbol} 1m 合并完成: ${processedData.length}条记录`)
        if (processedData.length > 0) {
          const firstDate = new Date(processedData[0].timestamp).toLocaleDateString()
          const lastDate = new Date(processedData[processedData.length - 1].timestamp).toLocaleDateString()
          console.log(`📅 [2025数据] 时间范围: ${firstDate} - ${lastDate}`)
        }
        this.loggedInitializations.add(cacheKey)
      }

      return processedData

    } catch (error) {
      console.error(`❌ [2025数据] ${symbol} 1m 加载失败:`, error.message)
      return this.generateFallbackData(symbol, '1m', '2025')
    }
  }

  // 生成回退数据
  generateFallbackData(symbol, timeframe, year) {
    console.warn(`🚨 [数据源] 使用回退数据: ${symbol} ${timeframe} ${year} (模拟数据)`)

    const data = []
    const startTime = new Date(parseInt(year), 0, 1).getTime()
    let basePrice = symbol === 'BTCUSDT' ? 50000 : 2000

    for (let i = 0; i < 1000; i++) {
      const timestamp = startTime + i * 60 * 1000

      basePrice += (Math.random() - 0.5) * 100
      const high = basePrice + Math.random() * 50
      const low = basePrice - Math.random() * 50
      const open = low + Math.random() * (high - low)
      const close = low + Math.random() * (high - low)

      data.push({
        timestamp,
        open: parseFloat(open.toFixed(2)),
        high: parseFloat(high.toFixed(2)),
        low: parseFloat(low.toFixed(2)),
        close: parseFloat(close.toFixed(2)),
        volume: parseFloat((Math.random() * 20 + 5).toFixed(5)),
        turnover: parseFloat(((high + low) / 2 * (Math.random() * 20 + 5)).toFixed(2))
      })
    }

    return data
  }

  // 时间周期配置
  getTimeframeConfig() {
    return {
      '1m': { label: '1分钟', folder: '1m' },
      '15m': { label: '15分钟', folder: '15m' },
      '1h': { label: '1小时', folder: '1h' },
      '4h': { label: '4小时', folder: '4h' },
      '1d': { label: '1天', folder: '1d' },
      '1mo': { label: '1月', folder: '1mo' }
    }
  }

  // 清除缓存
  clearCache() {
    this.cache.clear()
    this.loggedInitializations.clear()
    console.log('🗑️ [数据源] 缓存已清除，下次加载将使用新的JSON数据格式')
  }

  // 🎯 智能合并2025年数据：历史数据 + 断层数据
  async loadMerged2025Data(symbol, timeframe, maxRetries = 3) {
    const cacheKey = `${symbol}_${timeframe}_2025`
    
    console.log(`🔄 智能合并2025年数据: ${symbol} ${timeframe}`)
    
    const allDataSources = []
    
    try {
      // 1. 🚀 优先获取服务器端实时数据
      const serverData = await this.getServerDataFromManager(symbol, timeframe)
      if (serverData && serverData.length > 0) {
        console.log(`📥 找到服务器数据: ${serverData.length} 条记录`)
        allDataSources.push({
          source: 'server',
          data: serverData.map(item => ({
            timestamp: item.timestamp,
            open: item.open,
            high: item.high,
            low: item.low,
            close: item.close,
            volume: item.volume,
            turnover: item.turnover
          }))
        })
      }
      
      // 2. 回退：获取内存缓冲区数据（兼容旧版本）
      const gapData = this.getGapDataFromManager(symbol, timeframe)
      if (gapData && gapData.length > 0) {
        console.log(`📥 找到缓冲区数据: ${gapData.length} 条记录`)
        allDataSources.push({
          source: 'buffer',
          data: gapData.map(item => ({
            timestamp: item.timestamp,
            open: item.open,
            high: item.high,
            low: item.low,
            close: item.close,
            volume: item.volume,
            turnover: item.turnover
          }))
        })
      }
      
      // 2. 尝试获取历史COS数据
      let historicalData = []
      
      if (timeframe === '1m') {
        // 1分钟数据：尝试加载月度+每日数据
        historicalData = await this.loadHistorical2025MinuteData(symbol, maxRetries)
      } else {
        // 其他时间周期：尝试标准路径
        historicalData = await this.loadHistorical2025StandardData(symbol, timeframe, maxRetries)
      }
      
      if (historicalData && historicalData.length > 0) {
        console.log(`📥 找到历史COS数据: ${historicalData.length} 条记录`)
        allDataSources.push({
          source: 'historical',
          data: historicalData
        })
      }
      
      // 3. 合并所有数据源
      if (allDataSources.length === 0) {
        console.warn(`⚠️ 没有找到任何2025年数据: ${symbol} ${timeframe}`)
        return []
      }
      
      // 🎯 安全合并数据，带错误恢复
      let mergedData = []
      try {
        console.log(`🔄 准备合并 ${allDataSources.length} 个数据源...`)
        mergedData = this.mergeDataSources(allDataSources)
        console.log(`✅ 数据合并完成: ${symbol} ${timeframe} (${mergedData.length} 条记录)`)
        
        // 显示数据来源统计
        const sourceStats = allDataSources.map(source => ({
          来源: source.source === 'server' ? '🚀服务器数据' : 
                source.source === 'buffer' ? '📦缓冲区数据' : 
                source.source === 'gap' ? '🔧断层修复' : '📚历史COS',
          记录数: source.data.length,
          时间范围: source.data.length > 0 ? 
            `${new Date(source.data[0].timestamp).toLocaleDateString()} ~ ${new Date(source.data[source.data.length-1].timestamp).toLocaleDateString()}` : '-'
        }))
        console.table(sourceStats)
        
      } catch (mergeError) {
        console.error(`❌ 数据合并失败，尝试单独处理: ${mergeError.message}`)
        
        // 失败时，优先返回断层数据
        const gapSource = allDataSources.find(source => source.source === 'gap')
        const historicalSource = allDataSources.find(source => source.source === 'historical')
        
        if (gapSource && gapSource.data.length > 0) {
          console.log(`🔄 使用断层数据作为回退: ${gapSource.data.length} 条记录`)
          mergedData = [...gapSource.data].sort((a, b) => a.timestamp - b.timestamp)
        } else if (historicalSource && historicalSource.data.length > 0) {
          console.log(`🔄 使用历史数据作为回退: ${historicalSource.data.length} 条记录`)
          mergedData = [...historicalSource.data].sort((a, b) => a.timestamp - b.timestamp)
        } else {
          console.warn(`⚠️ 所有数据源都不可用`)
          mergedData = []
        }
      }
      
      // 缓存合并后的数据
      if (mergedData.length > 0) {
        this.cache.set(cacheKey, mergedData)
      }
      
      return mergedData
      
    } catch (error) {
      console.error(`❌ 2025年数据合并失败: ${error.message}`)
      
      // 失败时仅返回断层数据
      const gapData = this.getGapDataFromManager(symbol, timeframe)
      if (gapData && gapData.length > 0) {
        console.log(`🔄 回退使用断层数据: ${gapData.length} 条记录`)
        return gapData.map(item => ({
          timestamp: item.timestamp,
          open: item.open,
          high: item.high,
          low: item.low,
          close: item.close,
          volume: item.volume,
          turnover: item.turnover
        }))
      }
      
      return []
    }
  }

  // 合并多个数据源（高度优化版，避免栈溢出）
  mergeDataSources(dataSources) {
    console.log('🔄 开始合并数据源...')
    
    if (!dataSources || dataSources.length === 0) {
      console.warn('⚠️ 没有数据源需要合并')
      return []
    }
    
    // 直接使用Map进行合并和去重，避免中间数组
    const uniqueMap = new Map()
    let totalProcessed = 0
    
    dataSources.forEach((source, index) => {
      console.log(`📥 处理数据源 ${index + 1}: ${source.source} (${source.data.length} 条记录)`)
      
      if (!source.data || source.data.length === 0) {
        console.log(`⚠️ 数据源 ${index + 1} 为空，跳过`)
        return
      }
      
      // 直接遍历添加到Map，避免创建中间数组
      source.data.forEach(item => {
        if (item && typeof item.timestamp === 'number') {
          uniqueMap.set(item.timestamp, item)
          totalProcessed++
        }
      })
      
      console.log(`✅ 数据源 ${index + 1} 处理完成，累计处理 ${totalProcessed} 条记录`)
    })
    
    console.log(`📊 数据合并完成，去重后 ${uniqueMap.size} 条记录`)
    
    // 转换为数组并排序（使用更高效的方法）
    console.log('🔄 最终排序...')
    const uniqueData = Array.from(uniqueMap.values())
    
    // 使用快速排序，避免复杂比较
    uniqueData.sort((a, b) => {
      const diff = a.timestamp - b.timestamp
      return diff < 0 ? -1 : diff > 0 ? 1 : 0
    })
    
    console.log(`✅ 合并完成，最终 ${uniqueData.length} 条记录`)
    
    if (uniqueData.length > 0) {
      const firstDate = new Date(uniqueData[0].timestamp).toLocaleString()
      const lastDate = new Date(uniqueData[uniqueData.length - 1].timestamp).toLocaleString()
      console.log(`📅 时间范围: ${firstDate} ~ ${lastDate}`)
    }
    
    return uniqueData
  }

  // 加载历史2025年1分钟数据（月度+每日）
  async loadHistorical2025MinuteData(symbol, maxRetries) {
    const cosBasePath = this.getCosPathForYear('2025')
    const allData = []

    try {
      // 1. 加载月度数据 (1-6月)
      for (let month = 1; month <= 6; month++) {
        const monthStr = month.toString().padStart(2, '0')
        const monthlyURL = `${cosBasePath}/${symbol}/1m/${symbol}_1m_${monthStr}_2025_compressed.json`
        
        try {
          const jsonData = await this.loadDataWithStream(monthlyURL)
          const klineData = jsonData.data || []
          allData.push(...klineData)
        } catch (error) {
          // 月度数据加载失败不影响其他数据
          console.warn(`⚠️ 月度数据加载失败: ${monthStr}月 - ${error.message}`)
        }
      }

      // 2. 加载每日数据 (7月1-31日) - 动态检测当前日期
      const currentDate = new Date()
      const currentMonth = currentDate.getMonth() + 1  // 0-11 -> 1-12
      const currentDay = currentDate.getDate()
      
      // 如果是7月份，加载到当前日期；否则加载整月31天
      const maxDay = currentMonth === 7 && currentDate.getFullYear() === 2025 ? currentDay : 31
      
      console.log(`📅 历史数据动态加载: 7月1日-${maxDay}日 (当前: ${currentDate.toLocaleDateString()})`)
      
      for (let day = 1; day <= maxDay; day++) {
        const dayStr = day.toString().padStart(2, '0')
        const dailyURL = `${cosBasePath}/${symbol}/1m/daily/${symbol}_1m_07-${dayStr}_2025_compressed.json`
        
        try {
          const jsonData = await this.loadDataWithStream(dailyURL)
          const klineData = jsonData.data || []
          allData.push(...klineData)
        } catch (error) {
          // 每日数据加载失败不影响其他数据
          console.warn(`⚠️ 每日数据加载失败: 07-${dayStr} - ${error.message}`)
        }
      }

      // 转换数据格式
      return this.processHistoricalData(allData)
      
    } catch (error) {
      console.warn(`⚠️ 历史1分钟数据加载失败: ${error.message}`)
      return []
    }
  }

  // 加载历史2025年标准数据
  async loadHistorical2025StandardData(symbol, timeframe, maxRetries) {
    const cosBasePath = this.getCosPathForYear('2025')
    
    // 🎯 加载年度合并文件 (1月-7月16日)
    const yearlyDataURL = `${cosBasePath}/${symbol}/${timeframe}/${symbol}_${timeframe}_2025_compressed.json`
    
    let yearlyData = []
    let dailyData = []
    
    try {
      const jsonData = await this.loadDataWithStream(yearlyDataURL)
      const klineData = jsonData.data || []
      yearlyData = this.processHistoricalData(klineData)
      console.log(`📥 [年度文件] ${symbol} ${timeframe} 加载成功: ${yearlyData.length} 条记录`)
    } catch (error) {
      console.warn(`⚠️ 年度文件加载失败: ${timeframe} - ${error.message}`)
    }
    
    // 🔄 同时加载7月16日后的每日文件
    console.log(`🔄 [合并策略] 同时加载每日文件以获取7月16日后的数据...`)
    try {
      dailyData = await this.loadDailyTimeframeData(symbol, timeframe, cosBasePath)
      console.log(`📥 [每日文件] ${symbol} ${timeframe} 加载成功: ${dailyData.length} 条记录`)
    } catch (error) {
      console.warn(`⚠️ 每日文件加载失败: ${timeframe} - ${error.message}`)
    }
    
    // 🎯 智能合并：年度数据 + 每日数据，去重并排序
    if (yearlyData.length > 0 && dailyData.length > 0) {
      const mergedData = [...yearlyData, ...dailyData]
      
      // 按时间戳去重
      const uniqueData = mergedData.reduce((acc, current) => {
        const exists = acc.find(item => item.timestamp === current.timestamp)
        if (!exists) {
          acc.push(current)
        }
        return acc
      }, [])
      
      // 按时间戳排序
      uniqueData.sort((a, b) => a.timestamp - b.timestamp)
      
      console.log(`✅ [数据合并] ${symbol} ${timeframe}: 年度${yearlyData.length} + 每日${dailyData.length} = 合并后${uniqueData.length}条`)
      return uniqueData
    } else if (yearlyData.length > 0) {
      console.log(`📊 [仅年度] ${symbol} ${timeframe}: ${yearlyData.length} 条记录`)
      return yearlyData
    } else if (dailyData.length > 0) {
      console.log(`📊 [仅每日] ${symbol} ${timeframe}: ${dailyData.length} 条记录`)
      return dailyData
    } else {
      console.warn(`⚠️ [无数据] ${symbol} ${timeframe}: 年度和每日文件都无法加载`)
      return []
    }
  }

  // 🎯 加载2025年每日多时间周期数据  
  async loadDailyTimeframeData(symbol, timeframe, cosBasePath) {
    const allDailyData = []
    
    // 从7月1日开始到今天
    const startDate = new Date('2025-07-01')
    const endDate = new Date()
    
    console.log(`🔄 [每日加载] ${symbol} ${timeframe} 从 ${startDate.toDateString()} 到 ${endDate.toDateString()}`)
    console.log(`📂 [每日加载] 基础路径: ${cosBasePath}`)
    
    let successCount = 0
    let totalDays = 0
    
    for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
      totalDays++
      const dateStr = date.toISOString().slice(0, 10) // YYYY-MM-DD
      const formattedDate = dateStr.slice(5) // MM-DD 格式 (从YYYY-MM-DD中提取MM-DD)
      
      // 构建每日文件路径
      const dailyURL = `${cosBasePath}/${symbol}/${timeframe}/daily/${symbol}_${timeframe}_${formattedDate}_2025_compressed.json`
      
      // 添加详细的路径日志
      if (totalDays <= 5 || totalDays % 10 === 0) { // 只记录前5天和每10天
        console.log(`📅 [每日路径] ${dateStr} (${formattedDate}): ${dailyURL}`)
      }
      
      try {
        const jsonData = await this.loadDataWithStream(dailyURL)
        const klineData = jsonData.data || []
        
        if (klineData.length > 0) {
          allDailyData.push(...this.processHistoricalData(klineData))
          successCount++
        }
      } catch (error) {
        // 静默处理缺失的每日文件，这是正常的
        if (date.getDate() % 10 === 1) { // 每10天记录一次进度
          console.log(`📅 [进度] ${symbol} ${timeframe} 已检查 ${totalDays} 天，成功加载 ${successCount} 天`)
        }
      }
    }
    
    // 按时间戳排序
    allDailyData.sort((a, b) => a.timestamp - b.timestamp)
    
    console.log(`✅ [每日合并] ${symbol} ${timeframe} 完成: ${allDailyData.length} 条记录 (${successCount}/${totalDays} 天)`)
    
    if (allDailyData.length > 0) {
      const firstDate = new Date(allDailyData[0].timestamp).toLocaleDateString()
      const lastDate = new Date(allDailyData[allDailyData.length - 1].timestamp).toLocaleDateString()
      console.log(`📅 [时间范围] ${symbol} ${timeframe}: ${firstDate} ~ ${lastDate}`)
    }
    
    return allDailyData
  }

  // 处理历史数据格式
  processHistoricalData(klineData) {
    const processedData = klineData.map(item => {
      if (Array.isArray(item)) {
        // 🎯 检查时间戳格式，如果是微秒则转换为毫秒
        const rawTimestamp = item[0]
        const timestamp = rawTimestamp > 1e15 ? Math.floor(rawTimestamp / 1000) : rawTimestamp
        
        return {
          timestamp: timestamp,
          open: parseFloat(item[1]),
          high: parseFloat(item[2]),
          low: parseFloat(item[3]),
          close: parseFloat(item[4]),
          volume: parseFloat(item[5]),
          turnover: parseFloat(item[1]) * parseFloat(item[5])
        }
      } else {
        const rawTimestamp = item.timestamp
        const timestamp = rawTimestamp > 1e15 ? Math.floor(rawTimestamp / 1000) : rawTimestamp
        
        return {
          timestamp: timestamp,
          open: parseFloat(item.open),
          high: parseFloat(item.high),
          low: parseFloat(item.low),
          close: parseFloat(item.close),
          volume: parseFloat(item.volume),
          turnover: item.turnover || (parseFloat(item.open) * parseFloat(item.volume))
        }
      }
    })
    
    // return this.cleanAbnormalPriceData(processedData)
    return processedData;
  }

  // 🧹 数据清洗：过滤异常价格数据
  cleanAbnormalPriceData(data) {
    if (!data || data.length === 0) return data
    
    const originalLength = data.length
    let cleanedData = []
    let filteredCount = 0
    
    // 根据不同交易对设置价格范围
    const getPriceRange = (symbol) => {
      switch (symbol.toUpperCase()) {
        case 'BTCUSDT':
          return { min: 30000, max: 200000 } // BTC合理价格范围
        case 'ETHUSDT':
          return { min: 1000, max: 10000 }   // ETH合理价格范围
        default:
          return { min: 0.01, max: 1000000 } // 通用范围
      }
    }
    
    // 如果没有symbol信息，使用BTC的范围作为默认值
    const priceRange = getPriceRange('BTCUSDT')
    
    for (let i = 0; i < data.length; i++) {
      const item = data[i]
      const { open, high, low, close } = item
      
      // 检查所有价格是否在合理范围内
      const pricesValid = [open, high, low, close].every(price => 
        price >= priceRange.min && price <= priceRange.max
      )
      
      // 检查高低价的逻辑关系
      const priceLogicValid = low <= high && 
                             open >= low && open <= high && 
                             close >= low && close <= high
      
      if (pricesValid && priceLogicValid) {
        cleanedData.push(item)
      } else {
        filteredCount++
        if (filteredCount <= 5) { // 只记录前5个异常数据
          console.warn(`🧹 过滤异常价格数据: 时间${new Date(item.timestamp).toLocaleString()} O:${open} H:${high} L:${low} C:${close}`)
        }
      }
    }
    
    if (filteredCount > 0) {
      console.log(`🧹 数据清洗完成: 原始${originalLength}条，过滤${filteredCount}条异常数据，保留${cleanedData.length}条`)
    }
    
    return cleanedData
  }

  // 🚀 从服务器获取实时数据（替代localStorage断层数据）
  async getServerDataFromManager(symbol, timeframe) {
    try {
      console.log(`🔍 从服务器查询实时数据: ${symbol} ${timeframe}`)
      
      const response = await fetch('http://localhost:8000/api/crypto/realtime/query/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          symbol: symbol,
          timeframe: timeframe,
          limit: 2000 // 获取最近2000条记录
        })
      })
      
      const result = await response.json()
      
      if (result.success && result.data.length > 0) {
        console.log(`✅ 服务器数据获取成功: ${symbol} ${timeframe} (${result.data.length} 条记录)`)
        return result.data
      }
      
      console.log(`⚠️ 服务器暂无数据: ${symbol} ${timeframe}`)
      return []
      
    } catch (error) {
      console.warn('⚠️ 服务器数据获取失败:', error)
      
      // 回退到内存缓冲区数据
      if (typeof window !== 'undefined' && window.realTimeDataManager) {
        const bufferKey = `${symbol}_${timeframe}`
        const gapData = window.realTimeDataManager.dataBuffer.get(bufferKey)
        return gapData || []
      }
      
      return []
    }
  }

  // 保留原方法作为备用（兼容性）
  getGapDataFromManager(symbol, timeframe) {
    try {
      // 优先从全局realTimeDataManager获取数据
      if (typeof window !== 'undefined' && window.realTimeDataManager) {
        const bufferKey = `${symbol}_${timeframe}`
        const gapData = window.realTimeDataManager.dataBuffer.get(bufferKey)
        return gapData || []
      }
      
      return []
    } catch (error) {
      console.warn('⚠️ 获取缓冲区数据失败:', error)
      return []
    }
  }

  // 专门清除2025年1分钟数据的缓存
  clear2025Cache() {
    const keysToRemove = []
    for (const key of this.cache.keys()) {
      if (key.includes('_1m_2025')) {
        keysToRemove.push(key)
      }
    }
    
    keysToRemove.forEach(key => {
      this.cache.delete(key)
      this.loggedInitializations.delete(key)
    })
    
    console.log(`🗑️ [数据源] 已清除2025年1分钟数据缓存 (${keysToRemove.length}个条目)`)
    console.log('🎯 [时间戳修复] 下次加载将使用修复后的微秒→毫秒转换逻辑')
  }
}

// 导出单例
const streamCryptoDataService = new StreamCryptoDataService()
export default streamCryptoDataService