# K线图组件集成文档

## 概述

基于官方KLineCharts v9.x示例，为项目创建了专业的K线图组件系统，支持多年数据对比、技术指标分析和Binance实时数据更新。

## 组件结构

```
src/
├── components/Chart/
│   ├── EnhancedKLineChart.tsx      # 单个增强K线图组件
│   ├── EnhancedKLineChart.css      # 样式文件
│   ├── MultiYearKLineChart.tsx     # 多年K线图组件
│   └── MultiYearKLineChart.css     # 多年组件样式
├── services/
│   └── BinanceDataService.ts       # Binance数据服务
└── pages/
    ├── ChartPage.tsx               # 示例页面
    └── ChartPage.css               # 页面样式
```

## 核心特性

### ✨ 基础功能
- **深色主题**: 专业的深色界面设计
- **上海时区**: 时间显示基于Asia/Shanghai时区
- **十字光标**: 跟随鼠标的十字光标和价格提示
- **响应式设计**: 支持全屏模式和移动端适配

### 📊 技术指标
- **BOLL 布林带**: 主图叠加显示
- **RSI 相对强弱指标**: 独立副图显示，可开关
- **KDJ 随机指标**: 独立副图显示，可开关  
- **MACD**: 仅在最新年份(2025年)显示，独立副图

### 📈 数据管理
- **多年数据**: 每年一个独立主图，标签页切换
- **模拟数据**: 基于官方demo的数据生成算法
- **实时更新**: 集成Binance WebSocket实时数据流
- **自动降级**: 实时数据连接失败时自动使用模拟数据

## 使用方法

### 1. 基础使用

```tsx
import MultiYearKLineChart from './components/Chart/MultiYearKLineChart'

function App() {
  return (
    <div>
      <MultiYearKLineChart
        symbol="BTCUSDT"
        years={['2025', '2024', '2023', '2022']}
        enableRealTime={true}
      />
    </div>
  )
}
```

### 2. 单个K线图

```tsx
import EnhancedKLineChart from './components/Chart/EnhancedKLineChart'

function SingleChart() {
  return (
    <EnhancedKLineChart
      symbol="BTCUSDT"
      year="2025"
      width="100%"
      height={600}
      showMACD={true}
      enableRealTime={true}
      onDataUpdate={(data) => console.log('数据更新:', data.length)}
    />
  )
}
```

### 3. 完整页面示例

```tsx
import ChartPage from './pages/ChartPage'

// 直接使用完整的图表页面
function App() {
  return <ChartPage />
}
```

## API 参数

### MultiYearKLineChart Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| symbol | string | 'BTCUSDT' | 交易对符号 |
| years | string[] | ['2025', '2024', '2023', '2022'] | 显示的年份列表 |
| enableRealTime | boolean | false | 是否启用实时数据 |
| onBinanceConnect | () => void | - | Binance连接成功回调 |

### EnhancedKLineChart Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| symbol | string | 'BTCUSDT' | 交易对符号 |
| year | string | '2025' | 显示年份 |
| width | string \| number | '100%' | 图表宽度 |
| height | string \| number | 600 | 图表高度 |
| showMACD | boolean | false | 是否显示MACD指标 |
| enableRealTime | boolean | false | 是否启用实时数据 |
| onDataUpdate | (data: KLineData[]) => void | - | 数据更新回调 |

## 指标配置

### 主图指标
- **BOLL布林带**: 默认参数[20, 2]，叠加在主图K线上

### 副图指标
- **RSI**: 参数[6, 12, 24]，独立面板显示
- **KDJ**: 参数[9, 3, 3]，独立面板显示
- **MACD**: 参数[12, 26, 9]，仅2025年显示

所有指标都可以通过工具栏开关进行控制。

## 实时数据

### Binance WebSocket集成

组件集成了Binance WebSocket实时数据流：

```typescript
// 服务会自动处理连接、重连和数据更新
import binanceDataService from './services/BinanceDataService'

// 连接实时数据
await binanceDataService.connect('BTCUSDT', '1m')

// 订阅数据更新
binanceDataService.subscribe('key', (data) => {
  console.log('实时数据:', data)
})
```

### 特性
- 自动重连机制(最多5次)
- 数据验证和错误处理
- 历史数据和实时数据无缝切换
- 仅在当前年份启用实时数据

## 样式自定义

### 主题配置

所有组件使用深色主题，主要颜色配置：

```css
:root {
  --primary-color: #26A69A;     /* 主色调 */
  --background-dark: #0d1117;   /* 深色背景 */
  --surface-dark: #161b22;      /* 表面颜色 */
  --border-color: #21262d;      /* 边框颜色 */
  --text-primary: #f0f6fc;      /* 主要文字 */
  --text-secondary: #8b949e;    /* 次要文字 */
}
```

### 响应式断点

```css
/* 桌面端 */
@media (min-width: 1200px) { ... }

/* 平板端 */
@media (max-width: 1200px) { ... }

/* 移动端 */
@media (max-width: 768px) { ... }
```

## 数据格式

### KLineData接口

```typescript
interface KLineData {
  timestamp: number   // 时间戳
  open: number       // 开盘价
  high: number       // 最高价
  low: number        // 最低价
  close: number      // 收盘价
  volume: number     // 成交量
  turnover?: number  // 成交额(可选)
}
```

## 注意事项

1. **依赖要求**: 需要安装 `klinecharts@^9.0.1` 和 `antd@^5.x`
2. **网络连接**: 实时数据需要访问 Binance API，请确保网络连接正常
3. **CORS问题**: 生产环境可能需要配置代理来避免CORS限制
4. **性能考虑**: 实时数据更新会触发图表重渲染，大量数据时注意性能
5. **错误处理**: 组件内置了完善的错误处理和降级机制

## 故障排除

### 常见问题

1. **"Cannot read properties of undefined (reading 'filter')"**
   - 原因: klinecharts版本不兼容或数据格式错误
   - 解决: 确保使用v9.x版本并检查数据格式

2. **实时数据连接失败**
   - 原因: 网络问题或Binance API限制
   - 解决: 检查网络连接，组件会自动降级到模拟数据

3. **指标显示异常**
   - 原因: 数据量不足或参数配置错误
   - 解决: 确保有足够的历史数据用于指标计算

4. **样式显示问题**
   - 原因: CSS文件未正确导入
   - 解决: 确保所有CSS文件都已正确导入

## 开发调试

启用详细日志输出：

```typescript
// 在开发模式下查看详细日志
console.log('📊 K线数据:', data)
console.log('✅ 指标更新完成')
console.log('🔌 WebSocket连接状态:', connected)
```

所有关键操作都有相应的控制台输出，便于调试和监控。