import React, { useState } from 'react';
import { Card, Select, Button, Space, Typography, Row, Col, Alert } from 'antd';
import { RobotOutlined, TestTubeOutlined } from '@ant-design/icons';
import EnhancedAIAnalyzer from '../components/Analysis/EnhancedAIAnalyzer';

const { Option } = Select;
const { Title, Text } = Typography;

const AIAnalysisTestPage = () => {
  const [selectedSymbol, setSelectedSymbol] = useState('BTC');
  const [testResults, setTestResults] = useState([]);

  // 支持的交易对
  const supportedSymbols = [
    { value: 'BTC', label: '比特币 (BTC)', description: '最主要的加密货币' },
    { value: 'ETH', label: '以太坊 (ETH)', description: '智能合约平台' },
    { value: 'SOL', label: '索拉纳 (SOL)', description: '高性能区块链' },
    { value: 'DOGE', label: '狗狗币 (DOGE)', description: '模因币代表' },
    { value: 'ADA', label: '卡尔达诺 (ADA)', description: '学术区块链' },
    { value: 'XRP', label: '瑞波币 (XRP)', description: '跨境支付' },
    { value: 'LTC', label: '莱特币 (LTC)', description: '比特币分叉' },
    { value: 'DOT', label: '波卡 (DOT)', description: '跨链协议' },
    { value: 'MATIC', label: '马蒂奇 (MATIC)', description: '以太坊扩容' }
  ];

  const handleAnalysisComplete = (result) => {
    const newResult = {
      timestamp: new Date().toISOString(),
      symbol: selectedSymbol,
      result: result.substring(0, 200) + '...', // 只保存前200字符
      success: true
    };
    setTestResults(prev => [newResult, ...prev.slice(0, 4)]); // 保留最近5次结果
  };

  const runQuickTest = async () => {
    console.log('🧪 运行快速测试...');
    // 这里可以添加自动化测试逻辑
  };

  return (
    <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        {/* 页面标题 */}
        <Card style={{ marginBottom: '24px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <TestTubeOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
            <div>
              <Title level={2} style={{ margin: 0 }}>AI分析模块测试</Title>
              <Text type="secondary">测试前端AI分析模块的数据接收和处理能力</Text>
            </div>
          </div>
        </Card>

        <Row gutter={[24, 24]}>
          {/* 左侧：测试控制面板 */}
          <Col xs={24} lg={8}>
            <Card title="测试控制" size="small">
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text strong>选择交易对：</Text>
                  <Select
                    value={selectedSymbol}
                    onChange={setSelectedSymbol}
                    style={{ width: '100%', marginTop: '8px' }}
                  >
                    {supportedSymbols.map(symbol => (
                      <Option key={symbol.value} value={symbol.value}>
                        <div>
                          <div>{symbol.label}</div>
                          <div style={{ fontSize: '12px', color: '#999' }}>
                            {symbol.description}
                          </div>
                        </div>
                      </Option>
                    ))}
                  </Select>
                </div>

                <Button 
                  type="primary" 
                  icon={<TestTubeOutlined />}
                  onClick={runQuickTest}
                  block
                >
                  运行快速测试
                </Button>

                <Alert
                  message="测试说明"
                  description={
                    <ul style={{ margin: '8px 0', paddingLeft: '16px' }}>
                      <li>测试数据获取：K线、价格、订单簿</li>
                      <li>验证BTC辅助数据（非BTC币种）</li>
                      <li>检查数据完整性和格式</li>
                      <li>测试流式AI分析响应</li>
                      <li>验证错误处理和降级机制</li>
                    </ul>
                  }
                  type="info"
                  showIcon
                  size="small"
                />
              </Space>
            </Card>

            {/* 测试结果历史 */}
            {testResults.length > 0 && (
              <Card title="测试历史" size="small" style={{ marginTop: '16px' }}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  {testResults.map((result, index) => (
                    <div key={index} style={{ 
                      padding: '8px', 
                      background: result.success ? '#f6ffed' : '#fff2f0',
                      border: `1px solid ${result.success ? '#b7eb8f' : '#ffccc7'}`,
                      borderRadius: '4px',
                      fontSize: '12px'
                    }}>
                      <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
                        {result.symbol} - {new Date(result.timestamp).toLocaleTimeString()}
                      </div>
                      <div style={{ color: '#666' }}>
                        {result.result}
                      </div>
                    </div>
                  ))}
                </Space>
              </Card>
            )}
          </Col>

          {/* 右侧：AI分析器 */}
          <Col xs={24} lg={16}>
            <EnhancedAIAnalyzer 
              symbol={selectedSymbol}
              onAnalysisComplete={handleAnalysisComplete}
            />
          </Col>
        </Row>

        {/* 功能特性说明 */}
        <Card title="改造功能特性" style={{ marginTop: '24px' }}>
          <Row gutter={[16, 16]}>
            <Col xs={24} md={12}>
              <Card size="small" title="✅ 数据接收增强">
                <ul style={{ margin: 0, paddingLeft: '16px' }}>
                  <li>完整的K线数据获取（日线+短期）</li>
                  <li>实时价格数据验证</li>
                  <li>20档订单簿深度数据</li>
                  <li>BTC辅助数据（非BTC币种）</li>
                  <li>数据格式验证和转换</li>
                </ul>
              </Card>
            </Col>
            
            <Col xs={24} md={12}>
              <Card size="small" title="🔧 错误处理机制">
                <ul style={{ margin: 0, paddingLeft: '16px' }}>
                  <li>数据获取失败自动降级</li>
                  <li>网络错误智能重试</li>
                  <li>详细的错误信息展示</li>
                  <li>调试信息收集和导出</li>
                  <li>数据完整性评分</li>
                </ul>
              </Card>
            </Col>
            
            <Col xs={24} md={12}>
              <Card size="small" title="📊 流式响应优化">
                <ul style={{ margin: 0, paddingLeft: '16px' }}>
                  <li>多格式SSE数据解析</li>
                  <li>实时进度显示</li>
                  <li>数据获取状态跟踪</li>
                  <li>流式内容实时更新</li>
                  <li>响应完成状态管理</li>
                </ul>
              </Card>
            </Col>
            
            <Col xs={24} md={12}>
              <Card size="small" title="🛠️ 开发调试工具">
                <ul style={{ margin: 0, paddingLeft: '16px' }}>
                  <li>数据完整性检查面板</li>
                  <li>请求响应调试信息</li>
                  <li>系统环境信息收集</li>
                  <li>调试数据导出功能</li>
                  <li>错误堆栈跟踪</li>
                </ul>
              </Card>
            </Col>
          </Row>
        </Card>
      </div>
    </div>
  );
};

export default AIAnalysisTestPage;