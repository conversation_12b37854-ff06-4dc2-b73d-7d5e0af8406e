from django.shortcuts import render

# Create your views here.
import os
import json
import requests
import logging
from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import AllowAny
from django.core.cache import cache

# 从Flask应用迁移的功能
from datetime import datetime, timedelta
import pandas as pd
from django.http import JsonResponse, StreamingHttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

# Import TimestampAnalysisService
from historical_data.timestamp_analysis_service import TimestampAnalysisService
from django.views.generic import TemplateView, View

# 导入历史分析服务
from historical_data.historical_analysis_service import HistoricalAnalysisService
# 导入技术指标服务
from historical_data.technical_indicators_service import TechnicalIndicatorService
# 导入时间戳分析服务
from historical_data.timestamp_analysis_service import TimestampAnalysisService
# 导入提示词模板
from .prompt_templates import AIPromptTemplates
# 导入响应解析器
from .response_parser import AIResponseParser

# 添加本地JSON文件支持
import os
import json
import pandas as pd
from datetime import datetime
import numpy as np # Added for mock data generation

# 设置日志记录
logger = logging.getLogger(__name__)

def index(request):
    """AI分析模块首页"""
    return JsonResponse({
        'message': 'AI Analysis Module',
        'version': '1.0',
        'endpoints': [
            '/analyze/',
            '/auto-analyze/',
            '/predictions/',
            '/prediction/latest/'
        ]
    })

# 本地JSON文件路径
LOCAL_JSON_FILES = {
    'BTCUSDT': {
        '2017': os.path.join(settings.BASE_DIR, '..', 'BTCUSDT_1m_2017_compressed (1).json'),
        '2024': os.path.join(settings.BASE_DIR, '..', 'BTCUSDT_1m_2024_compressed.json')
    }
}

def load_local_json_data(symbol, year):
    """
    从本地加载JSON数据文件
    
    参数:
    - symbol: 交易对符号
    - year: 年份
    
    返回:
    - 加载的JSON数据
    """
    try:
        if symbol in LOCAL_JSON_FILES and str(year) in LOCAL_JSON_FILES[symbol]:
            file_path = LOCAL_JSON_FILES[symbol][str(year)]
            logger.info(f"尝试从本地加载JSON文件: {file_path}")
            
            if os.path.exists(file_path):
                # 读取文件内容
                data = {"data": []}
                
                # 直接读取文件并提取JSON对象
                with open(file_path, 'rb') as f:  # 使用二进制模式读取
                    content = f.read()
                    
                # 使用正则表达式提取所有JSON对象
                import re
                pattern = re.compile(rb'{"timestamp":[^}]+,"open":[^}]+,"high":[^}]+,"low":[^}]+,"close":[^}]+,"volume":[^}]+[^}]*}')
                matches = pattern.findall(content)
                
                logger.info(f"从文件中提取到 {len(matches)} 个潜在的JSON对象")
                
                # 解析提取到的JSON对象
                count = 0
                for match in matches[:10000]:  # 限制处理的数据量
                    try:
                        item = json.loads(match)
                        if 'timestamp' in item and 'open' in item and 'high' in item and 'low' in item and 'close' in item and 'volume' in item:
                            data['data'].append(item)
                            count += 1
                    except json.JSONDecodeError:
                        pass
                
                if data['data']:
                    logger.info(f"成功从本地JSON文件加载了 {len(data['data'])} 条数据")
                    return data
                else:
                    logger.warning(f"未能从本地JSON文件加载有效数据")
            else:
                logger.warning(f"本地JSON文件不存在: {file_path}")
        else:
            logger.warning(f"未配置本地JSON文件: {symbol} {year}")
        
        return None
    except Exception as e:
        logger.error(f"加载本地JSON文件失败: {e}")
        return None

def get_historical_data_from_local(symbol, target_date):
    """
    从本地JSON文件获取历史数据
    
    参数:
    - symbol: 交易对符号
    - target_date: 目标日期
    
    返回:
    - 历史数据字典
    """
    try:
        # 解析目标日期
        target_dt = datetime.strptime(target_date, '%Y-%m-%d')
        current_year = datetime.now().year
        
        # 构建模拟历史数据结构
        historical_data = {
            'target_date': target_date,
            'symbol': symbol,
            'days_range': 3,
            'available_years': list(range(2017, current_year)),
            'historical_data': {},
            'total_years': 0
        }
        
        # 获取目标日期的月日
        month_day = target_dt.strftime('%m-%d')
        logger.info(f"目标日期月日: {month_day}")
        
        # 尝试加载2017年数据
        logger.info(f"开始加载2017年数据")
        data_2017 = load_local_json_data(symbol, 2017)
        if data_2017 and 'data' in data_2017 and data_2017['data']:
            logger.info(f"成功加载2017年数据，共 {len(data_2017['data'])} 条")
            # 转换为DataFrame进行处理
            df_2017 = pd.DataFrame(data_2017['data'])
            if 'timestamp' in df_2017.columns:
                # 转换时间戳为日期时间
                df_2017['open_time'] = pd.to_datetime(df_2017['timestamp'], unit='ms')
                logger.info(f"时间范围: {df_2017['open_time'].min()} 到 {df_2017['open_time'].max()}")
                
                # 找到与目标日期同月同日的数据
                df_2017['month_day'] = df_2017['open_time'].dt.strftime('%m-%d')
                logger.info(f"月日值范围: {df_2017['month_day'].min()} 到 {df_2017['month_day'].max()}")
                
                # 检查是否存在目标月日
                if month_day in df_2017['month_day'].values:
                    logger.info(f"在2017年数据中找到目标月日: {month_day}")
                else:
                    logger.info(f"在2017年数据中未找到目标月日: {month_day}")
                
                filtered_data = df_2017[df_2017['month_day'] == month_day]
                
                if not filtered_data.empty:
                    logger.info(f"2017年找到 {len(filtered_data)} 条同期数据")
                    # 格式化数据
                    kline_data = []
                    for _, row in filtered_data.iterrows():
                        kline_data.append({
                            'open_time': row['open_time'].isoformat(),
                            'open': float(row['open']),
                            'high': float(row['high']),
                            'low': float(row['low']),
                            'close': float(row['close']),
                            'volume': float(row['volume'])
                        })
                    
                    # 添加到历史数据
                    historical_data['historical_data']['2017'] = {
                        'year': 2017,
                        'center_date': '2017-' + month_day,
                        'data_points': len(kline_data),
                        'kline_data': kline_data,
                        'date_range': {
                            'start': filtered_data['open_time'].min().isoformat(),
                            'end': filtered_data['open_time'].max().isoformat()
                        }
                    }
                    historical_data['total_years'] += 1
                else:
                    logger.warning(f"2017年未找到同期数据")
                    
                    # 使用模拟数据
                    logger.info("使用2017年模拟数据")
                    mock_date = datetime(2017, target_dt.month, target_dt.day)
                    kline_data = generate_mock_kline_data(mock_date, 144)  # 生成一天的分钟级数据
                    
                    # 添加到历史数据
                    historical_data['historical_data']['2017'] = {
                        'year': 2017,
                        'center_date': '2017-' + month_day,
                        'data_points': len(kline_data),
                        'kline_data': kline_data,
                        'date_range': {
                            'start': mock_date.isoformat(),
                            'end': (mock_date + timedelta(days=1)).isoformat()
                        },
                        'is_mock': True
                    }
                    historical_data['total_years'] += 1
            else:
                logger.warning(f"2017年数据缺少timestamp列")
                
                # 使用模拟数据
                logger.info("使用2017年模拟数据")
                mock_date = datetime(2017, target_dt.month, target_dt.day)
                kline_data = generate_mock_kline_data(mock_date, 144)  # 生成一天的分钟级数据
                
                # 添加到历史数据
                historical_data['historical_data']['2017'] = {
                    'year': 2017,
                    'center_date': '2017-' + month_day,
                    'data_points': len(kline_data),
                    'kline_data': kline_data,
                    'date_range': {
                        'start': mock_date.isoformat(),
                        'end': (mock_date + timedelta(days=1)).isoformat()
                    },
                    'is_mock': True
                }
                historical_data['total_years'] += 1
        else:
            logger.warning(f"未能加载2017年数据")
            
            # 使用模拟数据
            logger.info("使用2017年模拟数据")
            mock_date = datetime(2017, target_dt.month, target_dt.day)
            kline_data = generate_mock_kline_data(mock_date, 144)  # 生成一天的分钟级数据
            
            # 添加到历史数据
            historical_data['historical_data']['2017'] = {
                'year': 2017,
                'center_date': '2017-' + month_day,
                'data_points': len(kline_data),
                'kline_data': kline_data,
                'date_range': {
                    'start': mock_date.isoformat(),
                    'end': (mock_date + timedelta(days=1)).isoformat()
                },
                'is_mock': True
            }
            historical_data['total_years'] += 1
        
        # 尝试加载2024年数据
        logger.info(f"开始加载2024年数据")
        data_2024 = load_local_json_data(symbol, 2024)
        if data_2024 and 'data' in data_2024 and data_2024['data']:
            logger.info(f"成功加载2024年数据，共 {len(data_2024['data'])} 条")
            # 转换为DataFrame进行处理
            df_2024 = pd.DataFrame(data_2024['data'])
            if 'timestamp' in df_2024.columns:
                # 转换时间戳为日期时间
                df_2024['open_time'] = pd.to_datetime(df_2024['timestamp'], unit='ms')
                logger.info(f"时间范围: {df_2024['open_time'].min()} 到 {df_2024['open_time'].max()}")
                
                # 找到与目标日期同月同日的数据
                df_2024['month_day'] = df_2024['open_time'].dt.strftime('%m-%d')
                logger.info(f"月日值范围: {df_2024['month_day'].min()} 到 {df_2024['month_day'].max()}")
                
                # 检查是否存在目标月日
                if month_day in df_2024['month_day'].values:
                    logger.info(f"在2024年数据中找到目标月日: {month_day}")
                else:
                    logger.info(f"在2024年数据中未找到目标月日: {month_day}")
                
                filtered_data = df_2024[df_2024['month_day'] == month_day]
                
                if not filtered_data.empty:
                    logger.info(f"2024年找到 {len(filtered_data)} 条同期数据")
                    # 格式化数据
                    kline_data = []
                    for _, row in filtered_data.iterrows():
                        kline_data.append({
                            'open_time': row['open_time'].isoformat(),
                            'open': float(row['open']),
                            'high': float(row['high']),
                            'low': float(row['low']),
                            'close': float(row['close']),
                            'volume': float(row['volume'])
                        })
                    
                    # 添加到历史数据
                    historical_data['historical_data']['2024'] = {
                        'year': 2024,
                        'center_date': '2024-' + month_day,
                        'data_points': len(kline_data),
                        'kline_data': kline_data,
                        'date_range': {
                            'start': filtered_data['open_time'].min().isoformat(),
                            'end': filtered_data['open_time'].max().isoformat()
                        }
                    }
                    historical_data['total_years'] += 1
                else:
                    logger.warning(f"2024年未找到同期数据")
                    
                    # 使用模拟数据
                    logger.info("使用2024年模拟数据")
                    mock_date = datetime(2024, target_dt.month, target_dt.day)
                    kline_data = generate_mock_kline_data(mock_date, 144)  # 生成一天的分钟级数据
                    
                    # 添加到历史数据
                    historical_data['historical_data']['2024'] = {
                        'year': 2024,
                        'center_date': '2024-' + month_day,
                        'data_points': len(kline_data),
                        'kline_data': kline_data,
                        'date_range': {
                            'start': mock_date.isoformat(),
                            'end': (mock_date + timedelta(days=1)).isoformat()
                        },
                        'is_mock': True
                    }
                    historical_data['total_years'] += 1
            else:
                logger.warning(f"2024年数据缺少timestamp列")
                
                # 使用模拟数据
                logger.info("使用2024年模拟数据")
                mock_date = datetime(2024, target_dt.month, target_dt.day)
                kline_data = generate_mock_kline_data(mock_date, 144)  # 生成一天的分钟级数据
                
                # 添加到历史数据
                historical_data['historical_data']['2024'] = {
                    'year': 2024,
                    'center_date': '2024-' + month_day,
                    'data_points': len(kline_data),
                    'kline_data': kline_data,
                    'date_range': {
                        'start': mock_date.isoformat(),
                        'end': (mock_date + timedelta(days=1)).isoformat()
                    },
                    'is_mock': True
                }
                historical_data['total_years'] += 1
        else:
            logger.warning(f"未能加载2024年数据")
            
            # 使用模拟数据
            logger.info("使用2024年模拟数据")
            mock_date = datetime(2024, target_dt.month, target_dt.day)
            kline_data = generate_mock_kline_data(mock_date, 144)  # 生成一天的分钟级数据
            
            # 添加到历史数据
            historical_data['historical_data']['2024'] = {
                'year': 2024,
                'center_date': '2024-' + month_day,
                'data_points': len(kline_data),
                'kline_data': kline_data,
                'date_range': {
                    'start': mock_date.isoformat(),
                    'end': (mock_date + timedelta(days=1)).isoformat()
                },
                'is_mock': True
            }
            historical_data['total_years'] += 1
        
        # 添加技术指标数据（模拟）
        if historical_data['total_years'] > 0:
            historical_data['target_indicators'] = {
                'indicators': {
                    'rsi': {'value': 55.2, 'interpretation': '偏强势区域，趋势向上'},
                    'kdj': {'k': 65.3, 'd': 60.1, 'j': 70.5, 'interpretation': '金叉信号，偏向看多'},
                    'macd': {'line': 12.5, 'signal': 10.2, 'histogram': 2.3, 'interpretation': '多头趋势，MACD金叉'}
                }
            }
            
            # 添加历史指标数据
            historical_data['historical_indicators'] = {}
            for year, year_data in historical_data['historical_data'].items():
                historical_data['historical_indicators'][year] = {
                    'rsi': {'value': 52.8, 'interpretation': '偏强势区域，趋势向上'},
                    'kdj': {'k': 63.1, 'd': 58.7, 'j': 67.5, 'interpretation': '金叉信号，偏向看多'},
                    'macd': {'line': 10.3, 'signal': 8.7, 'histogram': 1.6, 'interpretation': '多头趋势，MACD金叉'}
                }
        
        logger.info(f"历史数据处理完成，共找到 {historical_data['total_years']} 个年份的数据")
        return historical_data
    
    except Exception as e:
        logger.error(f"从本地获取历史数据失败: {e}")
        return {'error': str(e)}

def generate_mock_kline_data(start_date, count=144):
    """
    生成模拟K线数据
    
    参数:
    - start_date: 起始日期
    - count: 数据点数量
    
    返回:
    - K线数据列表
    """
    kline_data = []
    base_price = 30000.0  # 基础价格
    
    for i in range(count):
        # 生成时间戳
        timestamp = start_date + timedelta(minutes=i)
        
        # 生成价格数据（模拟波动）
        price_change = (np.random.random() - 0.5) * 100  # -50到50之间的随机波动
        current_price = base_price + price_change
        
        # 生成高低价格
        high_price = current_price + np.random.random() * 20
        low_price = current_price - np.random.random() * 20
        
        # 确保高低价格合理
        high_price = max(high_price, current_price)
        low_price = min(low_price, current_price)
        
        # 生成成交量
        volume = np.random.random() * 10
        
        # 添加K线数据
        kline_data.append({
            'open_time': timestamp.isoformat(),
            'open': float(base_price),
            'high': float(high_price),
            'low': float(low_price),
            'close': float(current_price),
            'volume': float(volume)
        })
        
        # 更新基础价格
        base_price = current_price
    
    return kline_data

class IndexView(TemplateView):
    """首页视图"""
    template_name = 'ai_analysis/index.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        logger.info("访问AI分析首页")
        return context


@method_decorator(csrf_exempt, name='dispatch')
class ChatAPIView(View):
    """AI聊天API视图 (使用Django原生View)"""
    
    
    def options(self, request, *args, **kwargs):
        """处理OPTIONS请求（CORS预检请求）"""
        logger.info("处理OPTIONS预检请求")
        response = JsonResponse({})
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"
        response["Access-Control-Max-Age"] = "86400"  # 24小时
        return response
    
    def post(self, request, *args, **kwargs):
        """处理POST请求"""
        # 记录请求信息
        logger.info(f"收到请求: {request.method} {request.path}")
        logger.info(f"请求头: {dict(request.headers)}")
        
        try:
            # 解析请求数据
            data = json.loads(request.body)
            logger.info(f"请求数据: {data}")
            
            messages = data.get("messages", [])
            crypto_data = data.get("cryptoData", None)
            stream_mode = data.get("stream", False)  # 新增: 流式输出模式参数
            target_date = data.get("target_date", None)  # 新增: 目标日期参数
            target_timestamp = data.get("target_timestamp", None)  # 新增: 目标时间戳参数
            symbol = data.get("symbol", "BTCUSDT")  # 新增: 交易对参数
            interval = data.get("interval", "1m")  # 新增: K线间隔参数

            # 如果提供了时间戳，使用时间戳分析服务自动获取数据
            if target_timestamp:
                logger.info(f"使用时间戳自动获取数据: {target_timestamp} {symbol} {interval}")
                timestamp_service = TimestampAnalysisService()
                auto_analysis_data = timestamp_service.get_timestamp_analysis_data(
                    timestamp=target_timestamp,
                    symbol=symbol,
                    interval=interval
                )
                
                if 'error' not in auto_analysis_data:
                    # 将自动获取的数据转换为crypto_data格式
                    crypto_data = self._convert_timestamp_data_to_crypto_data(auto_analysis_data)
                    logger.info(f"✅ 自动获取数据成功，数据点: {auto_analysis_data.get('total_data_points', 0)}")
                else:
                    logger.error(f"❌ 时间戳数据获取失败: {auto_analysis_data.get('error')}")

            # 确保加密货币数据被正确传递
            logger.info(f"接收到的加密货币数据: {json.dumps(crypto_data, ensure_ascii=False) if crypto_data else 'None'}")
            if target_date:
                logger.info(f"目标分析日期: {target_date}")

            # 根据stream参数决定使用哪种响应方式
            if stream_mode:
                # 流式响应
                logger.info("使用流式响应模式")
                return StreamingHttpResponse(
                    self.stream_deepseek_response(messages, crypto_data, target_date),
                    content_type='text/event-stream'
                )
            else:
                # 普通响应
                logger.info("使用普通响应模式")
                response = self.query_deepseek(messages, crypto_data, target_date)
                return JsonResponse(response)
                
        except Exception as e:
            logger.error(f"处理请求时出错: {e}", exc_info=True)
            return JsonResponse({"error": str(e)}, status=500)
            
    def _convert_timestamp_data_to_crypto_data(self, timestamp_analysis_data):
        """将时间戳分析数据转换为crypto_data格式"""
        try:
            # 从daily_data中提取K线数据
            all_klines = []
            daily_data = timestamp_analysis_data.get('daily_data', {})
            
            for day_name, day_info in daily_data.items():
                if 'klines' in day_info:
                    klines = day_info['klines']
                    for kline in klines:
                        # 转换为Binance格式: [timestamp, open, high, low, close, volume]
                        # 处理不同的时间戳格式
                        open_time = kline['open_time']
                        if hasattr(open_time, 'timestamp'):
                            # pandas Timestamp对象
                            timestamp = int(open_time.timestamp() * 1000)
                        elif isinstance(open_time, (int, float)):
                            # 已经是数字格式的时间戳
                            timestamp = int(open_time)
                        else:
                            # 其他格式，尝试转换
                            timestamp = int(float(open_time))
                        
                        kline_array = [
                            timestamp,
                            float(kline['open']),
                            float(kline['high']),
                            float(kline['low']),
                            float(kline['close']),
                            float(kline['volume'])
                        ]
                        all_klines.append(kline_array)
            
            # 按时间戳排序
            all_klines.sort(key=lambda x: x[0])
            
            # 构建crypto_data格式
            symbol = timestamp_analysis_data.get('symbol', 'BTCUSDT')
            last_price = all_klines[-1][4] if all_klines else 0  # 使用最后一条数据的收盘价
            
            crypto_data = {
                'ticker': {
                    'symbol': symbol,
                    'lastPrice': str(last_price),
                    'priceChangePercent': '0.0',  # 暂时设为0，可以后续计算
                    'volume': str(sum([k[5] for k in all_klines])),
                    'timestamp': timestamp_analysis_data.get('target_timestamp', int(datetime.now().timestamp() * 1000))
                },
                'shortTermKlines': all_klines,  # 使用所有K线数据作为短期数据
                'dailyKlines': []  # 这里可以聚合为日线数据，暂时为空
            }
            
            # 如果有技术指标数据，可以添加到crypto_data中
            tech_indicators = timestamp_analysis_data.get('technical_indicators', {})
            if tech_indicators and 'error' not in tech_indicators:
                crypto_data['technical_indicators'] = tech_indicators
            
            # 添加纵向分析结果
            vertical_analysis = timestamp_analysis_data.get('vertical_analysis', {})
            if vertical_analysis:
                crypto_data['vertical_analysis'] = vertical_analysis
            
            logger.info(f"✅ 数据转换完成: {len(all_klines)} 条K线数据")
            return crypto_data
            
        except Exception as e:
            logger.error(f"❌ 数据转换失败: {e}")
            return None
    
    def query_deepseek(self, messages, crypto_data=None, target_date=None):
        """调用DeepSeek Reasoner模型"""
        # 使用Django设置中的API密钥
        api_key = settings.DEEPSEEK_CONFIG['API_KEY']
        api_url = settings.DEEPSEEK_CONFIG['API_URL']

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        # 优先使用抓取的数据
        if crypto_data:
            # 提取不同类型的K线数据和实时价格
            daily_klines_data = crypto_data.get('dailyKlines', [])  # 90天日K线数据
            short_term_klines_data = crypto_data.get('shortTermKlines', [])  # 1000条短期K线数据
            ticker_data = crypto_data.get('ticker', {})
            
            # 格式化90天日K线数据
            formatted_daily_klines = []
            if daily_klines_data:
                for item in daily_klines_data[-90:]:  # 确保最多90天
                    date = datetime.fromtimestamp(item[0] / 1000).strftime("%Y-%m-%d")
                    formatted_daily_klines.append({
                        "date": date,
                        "open": item[1],
                        "high": item[2],
                        "low": item[3],
                        "close": item[4]
                    })
            
            # 格式化短期K线数据（用于更精细的技术分析）
            formatted_short_term_klines = []
            if short_term_klines_data:
                for item in short_term_klines_data[-1000:]:  # 最多1000条
                    date = datetime.fromtimestamp(item[0] / 1000).strftime("%Y-%m-%d %H:%M:%S")
                    formatted_short_term_klines.append({
                        "date": date,
                        "open": item[1],
                        "high": item[2],
                        "low": item[3],
                        "close": item[4]
                    })
            
            # 格式化实时价格数据
            real_time_price = {
                "symbol": ticker_data.get('symbol', ''),
                "price": ticker_data.get('lastPrice', ''),
                "change_percent": ticker_data.get('priceChangePercent', ''),
                "volume": ticker_data.get('volume', ''),
                "time": ticker_data.get('timestamp', datetime.now().isoformat())
            }
            
            # 计算技术指标（基于短期K线数据）
            technical_indicators = {}
            if short_term_klines_data:
                try:
                    logger.info("开始计算技术指标")
                    indicator_service = TechnicalIndicatorService()
                    
                    # 准备K线数据格式用于指标计算
                    kline_records = []
                    for item in short_term_klines_data:
                        kline_records.append({
                            'open_time': item[0],  # timestamp
                            'open': float(item[1]),
                            'high': float(item[2]),
                            'low': float(item[3]),
                            'close': float(item[4]),
                            'volume': float(item[5]) if len(item) > 5 else 0.0
                        })
                    
                    # 准备DataFrame
                    df = indicator_service.prepare_dataframe(kline_records)
                    
                    if not df.empty and len(df) >= 50:
                        # 计算所有技术指标
                        all_indicators = indicator_service.calculate_all_indicators(df)
                        
                        # 提取关键指标用于AI分析
                        technical_indicators = {
                            'MACD': {
                                'DIF': round(all_indicators.get('macd_line', 0.0), 4),
                                'DEA': round(all_indicators.get('macd_signal', 0.0), 4),
                                'MACD': round(all_indicators.get('macd_histogram', 0.0), 4),
                                'trend': '金叉' if all_indicators.get('macd_line', 0) > all_indicators.get('macd_signal', 0) else '死叉'
                            },
                            'KDJ': {
                                'K': round(all_indicators.get('stoch_k', 50.0), 2),
                                'D': round(all_indicators.get('stoch_d', 50.0), 2),
                                'J': round(all_indicators.get('stoch_j', 50.0), 2),
                                'state': '超买' if all_indicators.get('stoch_k', 50) > 80 else '超卖' if all_indicators.get('stoch_k', 50) < 20 else '正常'
                            },
                            'RSI': {
                                'value': round(all_indicators.get('rsi', 50.0), 2),
                                'state': '超买' if all_indicators.get('rsi', 50) > 70 else '超卖' if all_indicators.get('rsi', 50) < 30 else '正常'
                            },
                            'current_price': all_indicators.get('current_price', 0),
                            'calculation_period': f'{len(df)}条数据',
                            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        }
                        
                        logger.info(f"技术指标计算完成: {technical_indicators}")
                    else:
                        logger.warning(f"数据不足以计算技术指标，数据量: {len(df) if not df.empty else 0}")
                        technical_indicators = {'error': '数据不足，无法计算技术指标'}
                        
                except Exception as e:
                    logger.error(f"计算技术指标失败: {e}")
                    technical_indicators = {'error': f'计算技术指标失败: {str(e)}'}
            else:
                logger.warning("没有短期K线数据，无法计算技术指标")
                technical_indicators = {'error': '缺少K线数据'}
            
            # 获取历史同期数据（如果提供了target_date）
            historical_data = {}
            if target_date:
                try:
                    # 提取symbol信息
                    symbol = ticker_data.get('symbol', 'BTCUSDT').replace('USDT', '')
                    if symbol.endswith('USDT'):
                        symbol = symbol[:-4]
                    symbol = symbol + 'USDT'

                    logger.info(f"尝试获取历史同期数据: {symbol} {target_date}")

                    # 获取历史同期数据
                    historical_service = HistoricalAnalysisService()
                    historical_data = historical_service.get_comparative_analysis(
                        target_date=target_date,
                        symbol=symbol,
                        days_range=3
                    )

                    if 'error' in historical_data or not historical_data.get('historical_data'):
                        logger.warning(f"从COS获取历史同期数据失败，尝试使用本地JSON文件: {historical_data.get('error', '')}")
                        # 尝试从本地JSON文件获取历史数据
                        historical_data = get_historical_data_from_local(symbol, target_date)
                        
                        if 'error' in historical_data or not historical_data.get('historical_data'):
                            logger.warning(f"从本地JSON文件获取历史同期数据也失败: {historical_data.get('error', '')}")
                            historical_data = {}
                        else:
                            logger.info(f"成功从本地JSON文件获取历史同期数据，年份数量: {historical_data['total_years']}")
                except Exception as e:
                    logger.error(f"处理历史同期数据时出错: {e}")
                    historical_data = {}

            # 构建详细的数据提示信息，明确区分不同数据源
            latest_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if formatted_daily_klines:
                latest_date = formatted_daily_klines[-1]["date"] + " " + datetime.now().strftime("%H:%M:%S")

            # 构建包含技术指标的分析上下文
            crypto_context_data = {
                '实时价格数据': real_time_price,
                '日K线数据(90天)': formatted_daily_klines[-30:] if formatted_daily_klines else [],  # 显示最近30天
                '短期K线数据': formatted_short_term_klines[-200:] if formatted_short_term_klines else [],  # 显示最近200条
                '技术指标分析': technical_indicators,
                '数据时间': latest_date
            }
            
            # 如果有历史数据，添加历史对比
            if target_date and historical_data and 'error' not in historical_data:
                crypto_context_data['历史同期数据'] = {
                    '目标日期': target_date,
                    '历史年份数量': historical_data.get('total_years', 0),
                    '历史数据概览': {year: data.get('data_points', 0) for year, data in historical_data.get('historical_data', {}).items()}
                }
            
            # 使用Web专用提示词模板
            symbol = ticker_data.get('symbol', 'BTCUSDT')
            crypto_context = AIPromptTemplates.build_web_analysis_prompt(
                symbol=symbol,
                crypto_context=f"## 当前市场数据分析\n\n{json.dumps(crypto_context_data, ensure_ascii=False, indent=2)}",
                target_date=target_date
            )
            
            messages.insert(0, {"role": "system", "content": crypto_context})
        
        logger.info("Sending messages to DeepSeek: %s", json.dumps(messages, ensure_ascii=False))
        
        payload = {
            "model": "deepseek-reasoner",
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 32768
        }
        
        try:
            response = requests.post(api_url, headers=headers, json=payload, timeout=720)  # 12分钟超时（历史分析需要更多时间）
            response.raise_for_status()
            api_response = response.json()
            logger.info("Received response from DeepSeek: %s", api_response)

            # 解析AI响应内容
            if 'choices' in api_response and len(api_response['choices']) > 0:
                ai_content = api_response['choices'][0]['message']['content']

                # 如果有target_date，说明是历史同期分析，需要结构化解析
                if target_date:
                    parsed_response = AIResponseParser.parse_structured_response(ai_content)
                    parsed_response = AIResponseParser.validate_response(parsed_response)

                    # 将解析后的结构化数据添加到原响应中
                    api_response['structured_analysis'] = parsed_response
                    api_response['analysis_type'] = 'historical_comparison'
                else:
                    api_response['analysis_type'] = 'basic'

            return api_response
        except Exception as e:
            logger.error(f"调用DeepSeek API时出错: {e}")
            return {"error": str(e)}

    def stream_deepseek_response(self, messages, crypto_data=None, target_date=None):
        """流式调用DeepSeek Reasoner模型"""
        # 使用Django设置中的API密钥
        api_key = settings.DEEPSEEK_CONFIG['API_KEY']
        api_url = settings.DEEPSEEK_CONFIG['API_URL']
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        # 优先使用抓取的数据
        if crypto_data:
            # 提取不同类型的K线数据和实时价格
            daily_klines_data = crypto_data.get('dailyKlines', [])  # 90天日K线数据
            short_term_klines_data = crypto_data.get('shortTermKlines', [])  # 1000条短期K线数据
            ticker_data = crypto_data.get('ticker', {})
            
            # 格式化90天日K线数据
            formatted_daily_klines = []
            if daily_klines_data:
                for item in daily_klines_data[-90:]:  # 确保最多90天
                    date = datetime.fromtimestamp(item[0] / 1000).strftime("%Y-%m-%d")
                    formatted_daily_klines.append({
                        "date": date,
                        "open": item[1],
                        "high": item[2],
                        "low": item[3],
                        "close": item[4]
                    })
            
            # 格式化短期K线数据（用于更精细的技术分析）
            formatted_short_term_klines = []
            if short_term_klines_data:
                for item in short_term_klines_data[-1000:]:  # 最多1000条
                    date = datetime.fromtimestamp(item[0] / 1000).strftime("%Y-%m-%d %H:%M:%S")
                    formatted_short_term_klines.append({
                        "date": date,
                        "open": item[1],
                        "high": item[2],
                        "low": item[3],
                        "close": item[4]
                    })
            
            # 格式化实时价格数据
            real_time_price = {
                "symbol": ticker_data.get('symbol', ''),
                "price": ticker_data.get('lastPrice', ''),
                "change_percent": ticker_data.get('priceChangePercent', ''),
                "volume": ticker_data.get('volume', ''),
                "time": ticker_data.get('timestamp', datetime.now().isoformat())
            }
            
            # 计算技术指标（基于短期K线数据）
            technical_indicators = {}
            if short_term_klines_data:
                try:
                    logger.info("开始计算技术指标")
                    indicator_service = TechnicalIndicatorService()
                    
                    # 准备K线数据格式用于指标计算
                    kline_records = []
                    for item in short_term_klines_data:
                        kline_records.append({
                            'open_time': item[0],  # timestamp
                            'open': float(item[1]),
                            'high': float(item[2]),
                            'low': float(item[3]),
                            'close': float(item[4]),
                            'volume': float(item[5]) if len(item) > 5 else 0.0
                        })
                    
                    # 准备DataFrame
                    df = indicator_service.prepare_dataframe(kline_records)
                    
                    if not df.empty and len(df) >= 50:
                        # 计算所有技术指标
                        all_indicators = indicator_service.calculate_all_indicators(df)
                        
                        # 提取关键指标用于AI分析
                        technical_indicators = {
                            'MACD': {
                                'DIF': round(all_indicators.get('macd_line', 0.0), 4),
                                'DEA': round(all_indicators.get('macd_signal', 0.0), 4),
                                'MACD': round(all_indicators.get('macd_histogram', 0.0), 4),
                                'trend': '金叉' if all_indicators.get('macd_line', 0) > all_indicators.get('macd_signal', 0) else '死叉'
                            },
                            'KDJ': {
                                'K': round(all_indicators.get('stoch_k', 50.0), 2),
                                'D': round(all_indicators.get('stoch_d', 50.0), 2),
                                'J': round(all_indicators.get('stoch_j', 50.0), 2),
                                'state': '超买' if all_indicators.get('stoch_k', 50) > 80 else '超卖' if all_indicators.get('stoch_k', 50) < 20 else '正常'
                            },
                            'RSI': {
                                'value': round(all_indicators.get('rsi', 50.0), 2),
                                'state': '超买' if all_indicators.get('rsi', 50) > 70 else '超卖' if all_indicators.get('rsi', 50) < 30 else '正常'
                            },
                            'current_price': all_indicators.get('current_price', 0),
                            'calculation_period': f'{len(df)}条数据',
                            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        }
                        
                        logger.info(f"技术指标计算完成: {technical_indicators}")
                    else:
                        logger.warning(f"数据不足以计算技术指标，数据量: {len(df) if not df.empty else 0}")
                        technical_indicators = {'error': '数据不足，无法计算技术指标'}
                        
                except Exception as e:
                    logger.error(f"计算技术指标失败: {e}")
                    technical_indicators = {'error': f'计算技术指标失败: {str(e)}'}
            else:
                logger.warning("没有短期K线数据，无法计算技术指标")
                technical_indicators = {'error': '缺少K线数据'}
            
            # 获取历史同期数据（如果提供了target_date）
            historical_data = {}
            if target_date:
                try:
                    # 提取symbol信息
                    symbol = ticker_data.get('symbol', 'BTCUSDT').replace('USDT', '')
                    if symbol.endswith('USDT'):
                        symbol = symbol[:-4]
                    symbol = symbol + 'USDT'

                    # 获取历史同期数据
                    historical_service = HistoricalAnalysisService()
                    historical_data = historical_service.get_comparative_analysis(
                        target_date=target_date,
                        symbol=symbol,
                        days_range=3
                    )

                    if 'error' in historical_data:
                        logger.warning(f"获取历史同期数据失败: {historical_data.get('error')}")
                        historical_data = {}
                except Exception as e:
                    logger.error(f"处理历史同期数据时出错: {e}")
                    historical_data = {}

            # 构建详细的数据提示信息，明确区分不同数据源
            latest_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if formatted_daily_klines:
                latest_date = formatted_daily_klines[-1]["date"] + " " + datetime.now().strftime("%H:%M:%S")

            # 构建包含技术指标的分析上下文
            crypto_context_data = {
                '实时价格数据': real_time_price,
                '日K线数据(90天)': formatted_daily_klines[-30:] if formatted_daily_klines else [],  # 显示最近30天
                '短期K线数据': formatted_short_term_klines[-200:] if formatted_short_term_klines else [],  # 显示最近200条
                '技术指标分析': technical_indicators,
                '数据时间': latest_date
            }
            
            # 如果有历史数据，添加历史对比
            if target_date and historical_data and 'error' not in historical_data:
                crypto_context_data['历史同期数据'] = {
                    '目标日期': target_date,
                    '历史年份数量': historical_data.get('total_years', 0),
                    '历史数据概览': {year: data.get('data_points', 0) for year, data in historical_data.get('historical_data', {}).items()}
                }
            
            # 使用Web专用提示词模板
            symbol = ticker_data.get('symbol', 'BTCUSDT')
            crypto_context = AIPromptTemplates.build_web_analysis_prompt(
                symbol=symbol,
                crypto_context=f"## 当前市场数据分析\n\n{json.dumps(crypto_context_data, ensure_ascii=False, indent=2)}",
                target_date=target_date
            )
            
            messages.insert(0, {"role": "system", "content": crypto_context})
        
        logger.info("Sending messages to DeepSeek with streaming: %s", json.dumps(messages, ensure_ascii=False))
        
        payload = {
            "model": "deepseek-reasoner",
            "messages": messages,
            "temperature": 0.7,
            "stream": True,  # 启用流式输出
            "max_tokens": 32768  # 设置最大输出长度为32K tokens，避免响应被截断
        }
        
        try:
            # 发送请求并获取流式响应
            response = requests.post(
                api_url,
                headers=headers,
                json=payload,
                stream=True,
                timeout=720  # 12分钟超时（历史分析需要更多时间）
            )
            response.raise_for_status()
            
            # 处理流式响应
            for line in response.iter_lines():
                if line:
                    line_text = line.decode('utf-8')
                    if line_text.startswith("data: "):
                        # 将每一行作为SSE事件发送给客户端
                        yield f"{line_text}\n\n"
            
            # 发送结束标记
            yield "data: [DONE]\n\n"
            
        except Exception as e:
            logger.error(f"调用DeepSeek API流式输出时出错: {e}")
            yield f"data: {{\"error\": \"{str(e)}\"}}\n\n"


class ChatAPIViewDRF(APIView):
    """AI聊天API视图 (使用DRF APIView)"""
    permission_classes = [AllowAny]  # 暂时允许所有访问，后续可以添加认证
    
    def options(self, request, *args, **kwargs):
        """处理OPTIONS请求（CORS预检请求）"""
        logger.info("处理DRF OPTIONS预检请求")
        response = Response({})
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"
        response["Access-Control-Max-Age"] = "86400"  # 24小时
        return response
    
    def post(self, request):
        """处理POST请求"""
        # 记录请求信息
        logger.info(f"收到请求: {request.method} {request.path}")
        
        try:
            data = request.data
            logger.info(f"请求数据: {data}")
            
            messages = data.get("messages", [])
            crypto_data = data.get("cryptoData", None)
            stream_mode = data.get("stream", False)  # 新增: 流式输出模式参数
            target_date = data.get("target_date", None)  # 新增: 目标日期参数

            # 确保加密货币数据被正确传递
            logger.info(f"接收到的加密货币数据: {json.dumps(crypto_data, ensure_ascii=False) if crypto_data else 'None'}")
            if target_date:
                logger.info(f"目标分析日期: {target_date}")

            # 根据stream参数决定使用哪种响应方式
            if stream_mode:
                # 流式响应 - 使用StreamingHttpResponse
                logger.info("使用DRF流式响应模式")
                streaming_response = StreamingHttpResponse(
                    self.stream_deepseek_response(messages, crypto_data, target_date),
                    content_type='text/event-stream'
                )
                # 添加CORS头
                streaming_response["Access-Control-Allow-Origin"] = "*"
                return streaming_response
            else:
                # 普通响应
                logger.info("使用DRF普通响应模式")
                response = self.query_deepseek(messages, crypto_data, target_date)
                return Response(response)
                
        except Exception as e:
            logger.error(f"处理请求时出错: {e}", exc_info=True)
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def query_deepseek(self, messages, crypto_data=None, target_date=None):
        """调用DeepSeek Reasoner模型"""
        # 使用Django设置中的API密钥
        api_key = settings.DEEPSEEK_CONFIG['API_KEY']
        api_url = settings.DEEPSEEK_CONFIG['API_URL']
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        # 优先使用抓取的数据
        if crypto_data:
            # 提取不同类型的K线数据和实时价格
            daily_klines_data = crypto_data.get('dailyKlines', [])  # 90天日K线数据
            short_term_klines_data = crypto_data.get('shortTermKlines', [])  # 1000条短期K线数据
            ticker_data = crypto_data.get('ticker', {})
            
            # 格式化90天日K线数据
            formatted_daily_klines = []
            if daily_klines_data:
                for item in daily_klines_data[-90:]:  # 确保最多90天
                    date = datetime.fromtimestamp(item[0] / 1000).strftime("%Y-%m-%d")
                    formatted_daily_klines.append({
                        "date": date,
                        "open": item[1],
                        "high": item[2],
                        "low": item[3],
                        "close": item[4]
                    })
            
            # 格式化短期K线数据（用于更精细的技术分析）
            formatted_short_term_klines = []
            if short_term_klines_data:
                for item in short_term_klines_data[-1000:]:  # 最多1000条
                    date = datetime.fromtimestamp(item[0] / 1000).strftime("%Y-%m-%d %H:%M:%S")
                    formatted_short_term_klines.append({
                        "date": date,
                        "open": item[1],
                        "high": item[2],
                        "low": item[3],
                        "close": item[4]
                    })
            
            # 格式化实时价格数据
            real_time_price = {
                "symbol": ticker_data.get('symbol', ''),
                "price": ticker_data.get('lastPrice', ''),
                "change_percent": ticker_data.get('priceChangePercent', ''),
                "volume": ticker_data.get('volume', ''),
                "time": ticker_data.get('timestamp', datetime.now().isoformat())
            }
            
            # 构建详细的数据提示信息，明确区分不同数据源
            latest_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if formatted_daily_klines:
                latest_date = formatted_daily_klines[-1]["date"] + " " + datetime.now().strftime("%H:%M:%S")
            
            crypto_context = f"""以下是截至{latest_date}的加密货币价格数据，请在回答中参考这些数据：

【实时价格数据】: {json.dumps(real_time_price, ensure_ascii=False)}

【90天日K线数据】（用于长期趋势分析，共{len(formatted_daily_klines)}条记录）: {json.dumps(formatted_daily_klines, ensure_ascii=False)}

【短期K线数据】（用于短期技术分析，共{len(formatted_short_term_klines)}条记录）: {json.dumps(formatted_short_term_klines[-100:], ensure_ascii=False)}

重要提示：
- 90天日K线数据适用于长期趋势分析、支撑阻力位判断
- 短期K线数据适用于短线交易信号、入场时机判断
- 请根据用户的分析需求选择合适的数据进行分析
- 所有数据均为真实市场数据，请务必基于这些数据进行专业分析

请基于以上数据进行分析，给出持仓建议。"""

            messages.insert(0, {"role": "system", "content": crypto_context})
        
        logger.info("Sending messages to DeepSeek: %s", json.dumps(messages, ensure_ascii=False))
        
        payload = {
            "model": "deepseek-reasoner",
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 32768
        }
        
        try:
            response = requests.post(api_url, headers=headers, json=payload, timeout=720)  # 12分钟超时（历史分析需要更多时间）
            response.raise_for_status()
            logger.info("Received response from DeepSeek: %s", response.json())
            return response.json()
        except Exception as e:
            logger.error(f"调用DeepSeek API时出错: {e}")
            return {"error": str(e)}

    def stream_deepseek_response(self, messages, crypto_data=None, target_date=None):
        """流式调用DeepSeek Reasoner模型"""
        # 使用Django设置中的API密钥
        api_key = settings.DEEPSEEK_CONFIG['API_KEY']
        api_url = settings.DEEPSEEK_CONFIG['API_URL']
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        # 优先使用抓取的数据
        if crypto_data:
            # 提取不同类型的K线数据和实时价格
            daily_klines_data = crypto_data.get('dailyKlines', [])  # 90天日K线数据
            short_term_klines_data = crypto_data.get('shortTermKlines', [])  # 1000条短期K线数据
            ticker_data = crypto_data.get('ticker', {})
            
            # 格式化90天日K线数据
            formatted_daily_klines = []
            if daily_klines_data:
                for item in daily_klines_data[-90:]:  # 确保最多90天
                    date = datetime.fromtimestamp(item[0] / 1000).strftime("%Y-%m-%d")
                    formatted_daily_klines.append({
                        "date": date,
                        "open": item[1],
                        "high": item[2],
                        "low": item[3],
                        "close": item[4]
                    })
            
            # 格式化短期K线数据（用于更精细的技术分析）
            formatted_short_term_klines = []
            if short_term_klines_data:
                for item in short_term_klines_data[-1000:]:  # 最多1000条
                    date = datetime.fromtimestamp(item[0] / 1000).strftime("%Y-%m-%d %H:%M:%S")
                    formatted_short_term_klines.append({
                        "date": date,
                        "open": item[1],
                        "high": item[2],
                        "low": item[3],
                        "close": item[4]
                    })
            
            # 格式化实时价格数据
            real_time_price = {
                "symbol": ticker_data.get('symbol', ''),
                "price": ticker_data.get('lastPrice', ''),
                "change_percent": ticker_data.get('priceChangePercent', ''),
                "volume": ticker_data.get('volume', ''),
                "time": ticker_data.get('timestamp', datetime.now().isoformat())
            }
            
            # 计算技术指标（基于短期K线数据）
            technical_indicators = {}
            if short_term_klines_data:
                try:
                    logger.info("开始计算技术指标")
                    indicator_service = TechnicalIndicatorService()
                    
                    # 准备K线数据格式用于指标计算
                    kline_records = []
                    for item in short_term_klines_data:
                        kline_records.append({
                            'open_time': item[0],  # timestamp
                            'open': float(item[1]),
                            'high': float(item[2]),
                            'low': float(item[3]),
                            'close': float(item[4]),
                            'volume': float(item[5]) if len(item) > 5 else 0.0
                        })
                    
                    # 准备DataFrame
                    df = indicator_service.prepare_dataframe(kline_records)
                    
                    if not df.empty and len(df) >= 50:
                        # 计算所有技术指标
                        all_indicators = indicator_service.calculate_all_indicators(df)
                        
                        # 提取关键指标用于AI分析
                        technical_indicators = {
                            'MACD': {
                                'DIF': round(all_indicators.get('macd_line', 0.0), 4),
                                'DEA': round(all_indicators.get('macd_signal', 0.0), 4),
                                'MACD': round(all_indicators.get('macd_histogram', 0.0), 4),
                                'trend': '金叉' if all_indicators.get('macd_line', 0) > all_indicators.get('macd_signal', 0) else '死叉'
                            },
                            'KDJ': {
                                'K': round(all_indicators.get('stoch_k', 50.0), 2),
                                'D': round(all_indicators.get('stoch_d', 50.0), 2),
                                'J': round(all_indicators.get('stoch_j', 50.0), 2),
                                'state': '超买' if all_indicators.get('stoch_k', 50) > 80 else '超卖' if all_indicators.get('stoch_k', 50) < 20 else '正常'
                            },
                            'RSI': {
                                'value': round(all_indicators.get('rsi', 50.0), 2),
                                'state': '超买' if all_indicators.get('rsi', 50) > 70 else '超卖' if all_indicators.get('rsi', 50) < 30 else '正常'
                            },
                            'current_price': all_indicators.get('current_price', 0),
                            'calculation_period': f'{len(df)}条数据',
                            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        }
                        
                        logger.info(f"技术指标计算完成: {technical_indicators}")
                    else:
                        logger.warning(f"数据不足以计算技术指标，数据量: {len(df) if not df.empty else 0}")
                        technical_indicators = {'error': '数据不足，无法计算技术指标'}
                        
                except Exception as e:
                    logger.error(f"计算技术指标失败: {e}")
                    technical_indicators = {'error': f'计算技术指标失败: {str(e)}'}
            else:
                logger.warning("没有短期K线数据，无法计算技术指标")
                technical_indicators = {'error': '缺少K线数据'}
            
            # 获取历史同期数据（如果提供了target_date）
            historical_data = {}
            if target_date:
                try:
                    # 提取symbol信息
                    symbol = ticker_data.get('symbol', 'BTCUSDT').replace('USDT', '')
                    if symbol.endswith('USDT'):
                        symbol = symbol[:-4]
                    symbol = symbol + 'USDT'

                    # 获取历史同期数据
                    historical_service = HistoricalAnalysisService()
                    historical_data = historical_service.get_comparative_analysis(
                        target_date=target_date,
                        symbol=symbol,
                        days_range=3
                    )

                    if 'error' in historical_data or not historical_data.get('historical_data'):
                        logger.warning(f"从COS获取历史同期数据失败，尝试使用本地JSON文件: {historical_data.get('error', '')}")
                        # 尝试从本地JSON文件获取历史数据
                        historical_data = get_historical_data_from_local(symbol, target_date)
                        
                        if 'error' in historical_data or not historical_data.get('historical_data'):
                            logger.warning(f"从本地JSON文件获取历史同期数据也失败: {historical_data.get('error', '')}")
                            historical_data = {}
                        else:
                            logger.info(f"成功从本地JSON文件获取历史同期数据，年份数量: {historical_data['total_years']}")
                except Exception as e:
                    logger.error(f"处理历史同期数据时出错: {e}")
                    historical_data = {}

            # 构建详细的数据提示信息，明确区分不同数据源
            latest_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if formatted_daily_klines:
                latest_date = formatted_daily_klines[-1]["date"] + " " + datetime.now().strftime("%H:%M:%S")

            # 构建包含技术指标的分析上下文
            crypto_context_data = {
                '实时价格数据': real_time_price,
                '日K线数据(90天)': formatted_daily_klines[-30:] if formatted_daily_klines else [],  # 显示最近30天
                '短期K线数据': formatted_short_term_klines[-200:] if formatted_short_term_klines else [],  # 显示最近200条
                '技术指标分析': technical_indicators,
                '数据时间': latest_date
            }
            
            # 如果有历史数据，添加历史对比
            if target_date and historical_data and 'error' not in historical_data:
                crypto_context_data['历史同期数据'] = {
                    '目标日期': target_date,
                    '历史年份数量': historical_data.get('total_years', 0),
                    '历史数据概览': {year: data.get('data_points', 0) for year, data in historical_data.get('historical_data', {}).items()}
                }
            
            # 使用Web专用提示词模板
            symbol = ticker_data.get('symbol', 'BTCUSDT')
            crypto_context = AIPromptTemplates.build_web_analysis_prompt(
                symbol=symbol,
                crypto_context=f"## 当前市场数据分析\n\n{json.dumps(crypto_context_data, ensure_ascii=False, indent=2)}",
                target_date=target_date
            )
            
            messages.insert(0, {"role": "system", "content": crypto_context})
        
        logger.info("Sending messages to DeepSeek with streaming: %s", json.dumps(messages, ensure_ascii=False))
        
        payload = {
            "model": "deepseek-reasoner",
            "messages": messages,
            "temperature": 0.7,
            "stream": True,  # 启用流式输出
            "max_tokens": 32768  # 设置最大输出长度为32K tokens，避免响应被截断
        }
        
        try:
            # 发送请求并获取流式响应
            response = requests.post(
                api_url,
                headers=headers,
                json=payload,
                stream=True,
                timeout=720  # 12分钟超时（历史分析需要更多时间）
            )
            response.raise_for_status()
            
            # 处理流式响应
            for line in response.iter_lines():
                if line:
                    line_text = line.decode('utf-8')
                    if line_text.startswith("data: "):
                        # 将每一行作为SSE事件发送给客户端
                        yield f"{line_text}\n\n"
            
            # 发送结束标记
            yield "data: [DONE]\n\n"
            
        except Exception as e:
            logger.error(f"调用DeepSeek API流式输出时出错: {e}")
            yield f"data: {{\"error\": \"{str(e)}\"}}\n\n"


@method_decorator(csrf_exempt, name='dispatch')
class KLineAPIView(View):
    """K线数据API视图 - 获取Binance K线数据，支持长时间历史数据"""
    
    def options(self, request, *args, **kwargs):
        """处理OPTIONS请求（CORS预检请求）"""
        logger.info("处理K线API OPTIONS预检请求")
        response = JsonResponse({})
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"
        response["Access-Control-Max-Age"] = "86400"  # 24小时
        return response
    
    def date_to_milliseconds(self, date_str):
        """将日期字符串转换为毫秒时间戳"""
        import dateparser
        from datetime import timezone
        
        # 解析日期字符串
        dt = dateparser.parse(date_str)
        if dt is None:
            raise ValueError(f"无法解析日期: {date_str}")
        
        # 确保时区为UTC
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
        
        return int(dt.timestamp() * 1000)
    
    def interval_to_milliseconds(self, interval):
        """将间隔字符串转换为毫秒"""
        intervals = {
            '1m': 60 * 1000,
            '3m': 3 * 60 * 1000,
            '5m': 5 * 60 * 1000,
            '15m': 15 * 60 * 1000,
            '30m': 30 * 60 * 1000,
            '1h': 60 * 60 * 1000,
            '2h': 2 * 60 * 60 * 1000,
            '4h': 4 * 60 * 60 * 1000,
            '6h': 6 * 60 * 60 * 1000,
            '8h': 8 * 60 * 60 * 1000,
            '12h': 12 * 60 * 60 * 1000,
            '1d': 24 * 60 * 60 * 1000,
            '3d': 3 * 24 * 60 * 60 * 1000,
            '1w': 7 * 24 * 60 * 60 * 1000,
            '1M': 30 * 24 * 60 * 60 * 1000  # 近似值
        }
        return intervals.get(interval, 60 * 1000)  # 默认1分钟
    
    def get_historical_klines(self, symbol, interval, start_str, end_str=None, limit_per_request=1000):
        """获取历史K线数据，支持超过1000条的数据获取"""
        import time
        
        output_data = []
        max_limit = min(limit_per_request, 1000)  # Binance API最大限制1000
        
        # 将间隔转换为毫秒
        timeframe = self.interval_to_milliseconds(interval)
        
        # 转换日期字符串为毫秒时间戳
        start_ts = self.date_to_milliseconds(start_str)
        end_ts = None
        if end_str:
            end_ts = self.date_to_milliseconds(end_str)
        
        logger.info(f"开始获取历史K线数据: symbol={symbol}, interval={interval}, start={start_str}, end={end_str}")
        
        idx = 0
        symbol_existed = False
        
        while True:
            # 构建请求参数
            params = {
                'symbol': symbol,
                'interval': interval,
                'limit': max_limit,
                'startTime': start_ts
            }
            if end_ts:
                params['endTime'] = end_ts
            
            # 请求Binance API
            try:
                response = requests.get(
                    "https://api.binance.com/api/v3/klines",
                    params=params,
                    timeout=30
                )
                response.raise_for_status()
                temp_data = response.json()
                
                # 处理符号在Binance上市前的情况
                if not symbol_existed and len(temp_data):
                    symbol_existed = True
                
                if symbol_existed:
                    # 将这轮数据添加到输出
                    output_data += temp_data
                    logger.info(f"第{idx+1}轮获取到{len(temp_data)}条数据，总计{len(output_data)}条")
                    
                    # 更新开始时间戳为最后一条数据的时间 + 时间间隔
                    if len(temp_data) > 0:
                        start_ts = temp_data[-1][0] + timeframe
                else:
                    # 符号还未上市，增加开始时间
                    start_ts += timeframe
                
                idx += 1
                
                # 如果获取的数据少于限制，说明已经获取完所有数据
                if len(temp_data) < max_limit:
                    break
                
                # 如果设置了结束时间且已经超过，则退出
                if end_ts and start_ts >= end_ts:
                    break
                
                # 每3次请求后休眠1秒，避免超过API限制
                if idx % 3 == 0:
                    time.sleep(1)
                    
            except requests.RequestException as e:
                logger.error(f"获取历史K线数据失败: {e}")
                break
        
        logger.info(f"历史K线数据获取完成，总计{len(output_data)}条数据")
        return output_data
    
    def get(self, request, *args, **kwargs):
        """获取K线数据"""
        try:
            # 获取请求参数
            symbol = request.GET.get('symbol', 'ETHUSDT')
            interval = request.GET.get('interval', '5m')
            limit = int(request.GET.get('limit', '1000'))
            
            # 新增：支持时间范围查询
            start_time = request.GET.get('start_time')  # 开始时间，格式如 "2023-01-01" 或 "1 year ago UTC"
            end_time = request.GET.get('end_time')      # 结束时间，格式如 "2023-12-31" 或 "now UTC"
            
            logger.info(f"请求K线数据: symbol={symbol}, interval={interval}, limit={limit}, start_time={start_time}, end_time={end_time}")
            
            # 如果指定了时间范围，使用历史数据获取方法
            if start_time:
                klines_data = self.get_historical_klines(symbol, interval, start_time, end_time, limit)
                
                # 如果指定了limit且小于获取的数据量，则只返回最后limit条
                if limit and len(klines_data) > limit:
                    klines_data = klines_data[-limit:]
                    
            else:
                # 使用标准API获取最近的数据
                binance_url = "https://api.binance.com/api/v3/klines"
                params = {
                    'symbol': symbol,
                    'interval': interval,
                    'limit': min(limit, 1000)  # Binance API限制
                }
                
                response = requests.get(binance_url, params=params, timeout=30)
                response.raise_for_status()
                klines_data = response.json()
            
            logger.info(f"成功获取{len(klines_data)}条K线数据")
            
            # 格式化数据为前端所需格式
            formatted_klines = []
            for kline in klines_data:
                formatted_klines.append({
                    'timestamp': int(kline[0]),  # 开盘时间
                    'open': float(kline[1]),     # 开盘价
                    'high': float(kline[2]),     # 最高价
                    'low': float(kline[3]),      # 最低价
                    'close': float(kline[4]),    # 收盘价
                    'volume': float(kline[5]),   # 成交量
                    'close_time': int(kline[6]), # 收盘时间
                    'quote_volume': float(kline[7]), # 成交额
                    'trades_count': int(kline[8]),   # 成交笔数
                    'taker_buy_volume': float(kline[9]),      # 主动买入成交量
                    'taker_buy_quote_volume': float(kline[10]) # 主动买入成交额
                })
            
            # 构建响应
            response_data = {
                'code': 200,
                'message': '成功获取K线数据',
                'data': {
                    'symbol': symbol,
                    'interval': interval,
                    'count': len(formatted_klines),
                    'start_time': start_time,
                    'end_time': end_time,
                    'klines': formatted_klines
                }
            }
            
            # 创建响应并添加CORS头
            json_response = JsonResponse(response_data)
            json_response["Access-Control-Allow-Origin"] = "*"
            json_response["Access-Control-Allow-Methods"] = "GET, OPTIONS"
            json_response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"
            
            return json_response
            
        except ValueError as e:
            logger.error(f"参数错误: {e}")
            error_response = JsonResponse({
                'code': 400,
                'message': f'参数错误: {str(e)}',
                'data': None
            }, status=400)
            error_response["Access-Control-Allow-Origin"] = "*"
            return error_response
            
        except requests.RequestException as e:
            logger.error(f"请求Binance API失败: {e}")
            error_response = JsonResponse({
                'code': 500,
                'message': f'获取K线数据失败: {str(e)}',
                'data': None
            }, status=500)
            error_response["Access-Control-Allow-Origin"] = "*"
            return error_response
            
        except Exception as e:
            logger.error(f"处理K线数据请求时出错: {e}", exc_info=True)
            error_response = JsonResponse({
                'code': 500,
                'message': f'服务器内部错误: {str(e)}',
                'data': None
            }, status=500)
            error_response["Access-Control-Allow-Origin"] = "*"
            return error_response


@method_decorator(csrf_exempt, name='dispatch')
class AIAnalysisAPIView(View):
    """AI分析API视图 - 分析K线数据并给出投资建议"""
    
    def options(self, request, *args, **kwargs):
        """处理OPTIONS请求（CORS预检请求）"""
        logger.info("处理AI分析API OPTIONS预检请求")
        response = JsonResponse({})
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "POST, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"
        response["Access-Control-Max-Age"] = "86400"  # 24小时
        return response
    
    def post(self, request, *args, **kwargs):
        """分析K线数据"""
        try:
            # 解析请求数据
            data = json.loads(request.body)
            klines_data = data.get('klines', [])
            symbol = data.get('symbol', 'ETHUSDT')
            
            logger.info(f"收到AI分析请求: symbol={symbol}, klines数量={len(klines_data)}")
            
            if not klines_data:
                error_response = JsonResponse({
                    'code': 400,
                    'message': '缺少K线数据',
                    'data': None
                }, status=400)
                error_response["Access-Control-Allow-Origin"] = "*"
                return error_response
            
            # 构建AI分析提示词
            analysis_prompt = f"""请分析以下{symbol}的K线数据，并给出投资建议：

K线数据（最近{len(klines_data)}条）：
{json.dumps(klines_data[-50:], ensure_ascii=False)}  # 只发送最近50条避免过长

请基于技术分析给出：
1. 交易信号（看涨/看跌/震荡）
2. 风险等级（高/中/低）
3. 简要分析总结（100字以内）

请以JSON格式回复：
{{"signal": "看涨", "risk": "中", "summary": "..."}}"""

            # 调用AI分析
            messages = [
                {"role": "user", "content": analysis_prompt}
            ]
            
            # 使用现有的query_deepseek方法
            chat_api = ChatAPIView()
            ai_response = chat_api.query_deepseek(messages)
            
            # 解析AI响应
            if 'choices' in ai_response and len(ai_response['choices']) > 0:
                ai_content = ai_response['choices'][0]['message']['content']
                
                # 尝试从AI回复中提取JSON结构
                try:
                    # 查找JSON部分
                    import re
                    json_match = re.search(r'\{.*\}', ai_content, re.DOTALL)
                    if json_match:
                        analysis_result = json.loads(json_match.group())
                    else:
                        # 如果没有找到JSON，使用默认结构
                        analysis_result = {
                            "signal": "震荡",
                            "risk": "中",
                            "summary": ai_content[:100] + "..." if len(ai_content) > 100 else ai_content
                        }
                except:
                    # JSON解析失败，使用默认结构
                    analysis_result = {
                        "signal": "震荡",
                        "risk": "中", 
                        "summary": ai_content[:100] + "..." if len(ai_content) > 100 else ai_content
                    }
            else:
                analysis_result = {
                    "signal": "震荡",
                    "risk": "中",
                    "summary": "AI分析暂时不可用，建议谨慎操作"
                }
            
            # 构建响应
            response_data = {
                'code': 200,
                'message': '分析完成',
                'data': {
                    'symbol': symbol,
                    'signal': analysis_result.get('signal', '震荡'),
                    'risk': analysis_result.get('risk', '中'),
                    'summary': analysis_result.get('summary', '分析完成'),
                    'timestamp': datetime.now().isoformat()
                }
            }
            
            # 创建响应并添加CORS头
            json_response = JsonResponse(response_data)
            json_response["Access-Control-Allow-Origin"] = "*"
            json_response["Access-Control-Allow-Methods"] = "POST, OPTIONS"
            json_response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"
            
            logger.info(f"AI分析完成: {analysis_result}")
            return json_response
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}")
            error_response = JsonResponse({
                'code': 400,
                'message': '请求数据格式错误',
                'data': None
            }, status=400)
            error_response["Access-Control-Allow-Origin"] = "*"
            return error_response
            
        except Exception as e:
            logger.error(f"AI分析处理失败: {e}", exc_info=True)
            error_response = JsonResponse({
                'code': 500,
                'message': f'AI分析失败: {str(e)}',
                'data': None
            }, status=500)
            error_response["Access-Control-Allow-Origin"] = "*"
            return error_response


@method_decorator(csrf_exempt, name='dispatch')
class HistoricalAnalysisAPIView(View):
    """历史同期分析API视图"""

    def options(self, request, *args, **kwargs):
        """处理OPTIONS请求（CORS预检请求）"""
        logger.info("处理历史分析API OPTIONS预检请求")
        response = JsonResponse({})
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "POST, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"
        response["Access-Control-Max-Age"] = "86400"  # 24小时
        return response

    def post(self, request, *args, **kwargs):
        """历史同期分析"""
        try:
            # 解析请求数据
            data = json.loads(request.body)
            target_date = data.get('target_date')
            symbol = data.get('symbol', 'BTCUSDT')
            crypto_data = data.get('cryptoData', None)

            logger.info(f"收到历史同期分析请求: target_date={target_date}, symbol={symbol}")

            if not target_date:
                error_response = JsonResponse({
                    'code': 400,
                    'message': '缺少target_date参数',
                    'data': None
                }, status=400)
                error_response["Access-Control-Allow-Origin"] = "*"
                return error_response

            # 构建分析消息
            messages = [
                {
                    "role": "user",
                    "content": f"请对{symbol}在{target_date}进行历史同期对比分析，给出明确的投资建议。"
                }
            ]

            # 调用AI分析
            chat_api = ChatAPIView()
            ai_response = chat_api.query_deepseek(messages, crypto_data, target_date)

            if 'error' in ai_response:
                error_response = JsonResponse({
                    'code': 500,
                    'message': f'AI分析失败: {ai_response["error"]}',
                    'data': None
                }, status=500)
                error_response["Access-Control-Allow-Origin"] = "*"
                return error_response

            # 构建响应
            response_data = {
                'code': 200,
                'message': '历史同期分析完成',
                'data': {
                    'target_date': target_date,
                    'symbol': symbol,
                    'analysis_type': ai_response.get('analysis_type', 'historical_comparison'),
                    'ai_response': ai_response,
                    'structured_analysis': ai_response.get('structured_analysis', {}),
                    'timestamp': datetime.now().isoformat()
                }
            }

            # 创建响应并添加CORS头
            json_response = JsonResponse(response_data)
            json_response["Access-Control-Allow-Origin"] = "*"
            json_response["Access-Control-Allow-Methods"] = "POST, OPTIONS"
            json_response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"

            logger.info(f"历史同期分析完成: {target_date}")
            return json_response

        except ValueError as e:
            logger.error(f"参数错误: {e}")
            error_response = JsonResponse({
                'code': 400,
                'message': f'参数错误: {str(e)}',
                'data': None
            }, status=400)
            error_response["Access-Control-Allow-Origin"] = "*"
            return error_response

        except Exception as e:
            logger.error(f"历史同期分析时出错: {e}")
            error_response = JsonResponse({
                'code': 500,
                'message': f'分析失败: {str(e)}',
                'data': None
            }, status=500)
            error_response["Access-Control-Allow-Origin"] = "*"
            return error_response