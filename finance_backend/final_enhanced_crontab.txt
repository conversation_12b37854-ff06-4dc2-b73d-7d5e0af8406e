# 最终增强版Cron调度 - 完整监控和防护机制

# 统一监控检查 - 每10分钟执行自动检查和修复
*/10 * * * * cd /home/<USER>/qiyuai-web/finance_backend && python3 unified_monitor.py --auto >> logs/unified_monitor.log 2>&1

# K线数据更新 - 每5分钟执行，带超时保护
1,6,11,16,21,26,31,36,41,46,51,56 * * * * cd /home/<USER>/qiyuai-web/finance_backend && timeout 300 python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=1m --start=$(date +\%Y-\%m-\%d) --end=$(date +\%Y-\%m-\%d) >> logs/auto_update.log 2>&1

# 15分钟数据更新 - 每15分钟的第2分钟执行
2,17,32,47 * * * * cd /home/<USER>/qiyuai-web/finance_backend && timeout 600 python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=15m --start=$(date +\%Y-\%m-\%d) --end=$(date +\%Y-\%m-\%d) >> logs/auto_update.log 2>&1

# 1小时数据更新 - 每小时第7分钟执行
7 * * * * cd /home/<USER>/qiyuai-web/finance_backend && timeout 900 python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=1h --start=$(date +\%Y-\%m-\%d) --end=$(date +\%Y-\%m-\%d) >> logs/auto_update.log 2>&1

# 4小时数据更新 - 每4小时第12分钟执行
12 */4 * * * cd /home/<USER>/qiyuai-web/finance_backend && timeout 1200 python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=4h --start=$(date +\%Y-\%m-\%d) --end=$(date +\%Y-\%m-\%d) >> logs/auto_update.log 2>&1

# 日数据更新 - 每天凌晨1:18更新昨日数据
18 1 * * * cd /home/<USER>/qiyuai-web/finance_backend && timeout 1800 python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=1d --start=$(date -d "1 day ago" +\%Y-\%m-\%d) --end=$(date -d "1 day ago" +\%Y-\%m-\%d) >> logs/auto_update.log 2>&1

# 当日数据更新 - 每天凌晨2:05更新当日数据
5 2 * * * cd /home/<USER>/qiyuai-web/finance_backend && timeout 1800 python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=1d --start=$(date +\%Y-\%m-\%d) --end=$(date +\%Y-\%m-\%d) >> logs/auto_update.log 2>&1

# AI预测任务 - 使用增强版命令，带高级锁管理
3,18,33,48 * * * * cd /home/<USER>/qiyuai-web/finance_backend && timeout 900 python3 manage.py cron_predictions_enhanced --lock-timeout=900 --force-timeout=1800 >> logs/predictions/cron_predictions.log 2>&1

# 紧急健康检查 - 每小时第30分钟执行深度检查
30 * * * * cd /home/<USER>/qiyuai-web/finance_backend && python3 unified_monitor.py --status >> logs/hourly_status.log 2>&1

# 日志管理 - 每小时第55分钟清理大日志文件
55 * * * * cd /home/<USER>/qiyuai-web/finance_backend && find logs/ -name "*.log" -size +100M -exec sh -c 'tail -n 10000 "$1" > "$1.tmp" && mv "$1.tmp" "$1"' _ {} \;

# 数据库维护 - 每天凌晨3:30清理旧记录
30 3 * * * cd /home/<USER>/qiyuai-web/finance_backend && python3 -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'finance_project.settings')
import django
django.setup()
from ai_analysis.models import PredictionRecord
from django.utils import timezone
from datetime import timedelta
cutoff = timezone.now() - timedelta(days=7)
deleted = PredictionRecord.objects.filter(prediction_status='failed', created_at__lt=cutoff).delete()
print(f'清理了 {deleted[0]} 条7天前的失败记录')
" >> logs/cleanup.log 2>&1

# 锁文件清理 - 每6小时清理过期锁文件
0 */6 * * * cd /home/<USER>/qiyuai-web/finance_backend && python3 advanced_lock_manager.py --cleanup >> logs/lock_cleanup.log 2>&1
