import React, { useState, useCallback } from 'react';
import { <PERSON>ton, Card, Progress, Typography, Space, Alert } from 'antd';
import { ReloadOutlined, CalculatorOutlined } from '@ant-design/icons';
import { 
  BollingerBands, 
  RSI, 
  Stochastic, 
  MACD,
  SMA,
  EMA
} from 'technicalindicators';
import cryptoDataService from '../../services/StreamDataService';
import './IndicatorCalculator.css';

const { Title, Text } = Typography;

const IndicatorCalculator = ({ 
  symbol = 'BTCUSDT', 
  timeframe = '1h', 
  years = [],
  onCalculationComplete 
}) => {
  const [isCalculating, setIsCalculating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentYear, setCurrentYear] = useState('');
  const [results, setResults] = useState(null);
  const [error, setError] = useState(null);

  // 计算单个年份的所有技术指标
  const calculateYearIndicators = useCallback(async (year) => {
    try {
      console.log(`📊 开始计算${year}年${symbol}的技术指标...`);
      
      // 获取K线数据
      const klineData = await cryptoDataService.getKLineData(symbol, timeframe, year);
      
      if (!klineData || klineData.length === 0) {
        console.warn(`⚠️ ${year}年没有数据`);
        return null;
      }

      // 提取价格数据
      const closes = klineData.map(item => parseFloat(item.close));
      const highs = klineData.map(item => parseFloat(item.high));
      const lows = klineData.map(item => parseFloat(item.low));
      const opens = klineData.map(item => parseFloat(item.open));
      const volumes = klineData.map(item => parseFloat(item.volume));

      console.log(`📈 ${year}年数据量: ${klineData.length}, 价格范围: ${Math.min(...closes).toFixed(2)} - ${Math.max(...closes).toFixed(2)}`);

      // 计算各种技术指标
      const indicators = {};

      // 1. 布林带 (BOLL)
      try {
        indicators.BOLL = BollingerBands.calculate({
          period: 20,
          stdDev: 2,
          values: closes
        });
        console.log(`✅ BOLL计算完成: ${indicators.BOLL.length}个数据点`);
      } catch (err) {
        console.error('❌ BOLL计算失败:', err);
        indicators.BOLL = [];
      }

      // 2. RSI
      try {
        indicators.RSI = RSI.calculate({
          period: 14,
          values: closes
        });
        console.log(`✅ RSI计算完成: ${indicators.RSI.length}个数据点`);
      } catch (err) {
        console.error('❌ RSI计算失败:', err);
        indicators.RSI = [];
      }

      // 3. KDJ (使用Stochastic)
      try {
        indicators.KDJ = Stochastic.calculate({
          high: highs,
          low: lows,
          close: closes,
          period: 14,
          signalPeriod: 3
        });
        console.log(`✅ KDJ计算完成: ${indicators.KDJ.length}个数据点`);
      } catch (err) {
        console.error('❌ KDJ计算失败:', err);
        indicators.KDJ = [];
      }

      // 4. MACD
      try {
        indicators.MACD = MACD.calculate({
          fastPeriod: 12,
          slowPeriod: 26,
          signalPeriod: 9,
          values: closes
        });
        console.log(`✅ MACD计算完成: ${indicators.MACD.length}个数据点`);
      } catch (err) {
        console.error('❌ MACD计算失败:', err);
        indicators.MACD = [];
      }

      // 5. 移动平均线
      try {
        indicators.SMA20 = SMA.calculate({ period: 20, values: closes });
        indicators.SMA50 = SMA.calculate({ period: 50, values: closes });
        indicators.EMA12 = EMA.calculate({ period: 12, values: closes });
        indicators.EMA26 = EMA.calculate({ period: 26, values: closes });
        console.log(`✅ 移动平均线计算完成`);
      } catch (err) {
        console.error('❌ 移动平均线计算失败:', err);
      }

      // 计算统计信息
      const stats = {
        dataCount: klineData.length,
        priceRange: {
          min: Math.min(...closes),
          max: Math.max(...closes),
          avg: closes.reduce((a, b) => a + b, 0) / closes.length
        },
        volumeTotal: volumes.reduce((a, b) => a + b, 0),
        indicatorCounts: {
          BOLL: indicators.BOLL.length,
          RSI: indicators.RSI.length,
          KDJ: indicators.KDJ.length,
          MACD: indicators.MACD.length
        }
      };

      const result = {
        year,
        symbol,
        timeframe,
        calculatedAt: new Date().toISOString(),
        stats,
        indicators,
        // 最新值（用于AI分析）
        latestValues: {
          price: closes[closes.length - 1],
          BOLL: indicators.BOLL.length > 0 ? indicators.BOLL[indicators.BOLL.length - 1] : null,
          RSI: indicators.RSI.length > 0 ? indicators.RSI[indicators.RSI.length - 1] : null,
          KDJ: indicators.KDJ.length > 0 ? indicators.KDJ[indicators.KDJ.length - 1] : null,
          MACD: indicators.MACD.length > 0 ? indicators.MACD[indicators.MACD.length - 1] : null
        }
      };

      console.log(`✅ ${year}年指标计算完成:`, {
        数据量: stats.dataCount,
        价格范围: `${stats.priceRange.min.toFixed(2)} - ${stats.priceRange.max.toFixed(2)}`,
        指标数量: stats.indicatorCounts
      });

      return result;

    } catch (error) {
      console.error(`❌ ${year}年指标计算失败:`, error);
      throw error;
    }
  }, [symbol, timeframe]);

  // 批量计算历年指标
  const calculateAllIndicators = useCallback(async () => {
    if (!years || years.length === 0) {
      setError('没有可用的年份数据');
      return;
    }

    setIsCalculating(true);
    setProgress(0);
    setError(null);
    setResults(null);

    try {
      console.log(`🚀 开始批量计算${symbol} ${timeframe}的技术指标...`);
      console.log(`📅 计算年份: ${years.join(', ')}`);

      const allResults = {};
      const totalYears = years.length;

      for (let i = 0; i < totalYears; i++) {
        const year = years[i];
        setCurrentYear(year);
        setProgress(Math.round((i / totalYears) * 100));

        const yearResult = await calculateYearIndicators(year);
        if (yearResult) {
          allResults[year] = yearResult;
        }
      }

      setProgress(100);
      setResults(allResults);
      
      console.log(`🎉 所有指标计算完成!`, {
        总年份: totalYears,
        成功计算: Object.keys(allResults).length,
        结果概览: Object.entries(allResults).map(([year, data]) => ({
          年份: year,
          数据量: data.stats.dataCount,
          最新价格: data.latestValues.price?.toFixed(2)
        }))
      });

      // 回调给父组件
      if (onCalculationComplete) {
        onCalculationComplete(allResults);
      }

    } catch (error) {
      console.error('❌ 批量计算失败:', error);
      setError(error.message);
    } finally {
      setIsCalculating(false);
      setCurrentYear('');
    }
  }, [years, symbol, timeframe, calculateYearIndicators, onCalculationComplete]);

  return (
    <Card 
      title={
        <Space>
          <CalculatorOutlined />
          <span>技术指标计算器</span>
        </Space>
      }
      size="small"
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <Text strong>计算参数:</Text>
          <div style={{ marginTop: 8 }}>
            <Text>币种: {symbol} | 周期: {timeframe} | 年份: {years.join(', ')}</Text>
          </div>
        </div>

        {error && (
          <Alert 
            message="计算错误" 
            description={error} 
            type="error" 
            showIcon 
          />
        )}

        {isCalculating && (
          <div>
            <Text>正在计算 {currentYear} 年的指标数据...</Text>
            <Progress 
              percent={progress} 
              status="active"
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
            />
          </div>
        )}

        {results && (
          <Alert
            message="计算完成"
            description={`成功计算了 ${Object.keys(results).length} 个年份的技术指标数据`}
            type="success"
            showIcon
          />
        )}

        <Button
          type="primary"
          icon={<ReloadOutlined />}
          loading={isCalculating}
          onClick={calculateAllIndicators}
          disabled={!years || years.length === 0}
          style={{
            background: 'linear-gradient(135deg, #016A86 0%, #447A53 100%)',
            borderColor: '#447A53'
          }}
        >
          {isCalculating ? '计算中...' : '开始计算指标'}
        </Button>
      </Space>
    </Card>
  );
};

export default IndicatorCalculator;
