import React from 'react';
import { Pie } from '@ant-design/plots';

const PredictionPieChart = ({ predictionData, title = "AI多空预测" }) => {
  if (!predictionData || Object.keys(predictionData).length === 0) {
    return (
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center', 
        height: '100%',
        flexDirection: 'column',
        color: '#999'
      }}>
        <div style={{ fontSize: 24, marginBottom: 8 }}>📊</div>
        <div style={{ fontSize: '12px' }}>暂无预测数据</div>
      </div>
    );
  }

  // Convert prediction data to pie chart format, filtering out null/undefined/zero values
  const data = Object.entries(predictionData)
    .filter(([key, value]) => value != null && value > 0)
    .map(([key, value]) => ({
      type: key,
      value: value,
    }));

  console.log('🔍 PredictionPieChart received data:', predictionData);
  console.log('🔍 Converted chart data:', data);

  // Color mapping for different prediction types - 新配色方案
  const colorMapping = {
    '非常看涨': '#046B84',    // 深蓝绿
    '看涨': '#447A53',        // 深绿
    '中性': '#C63C40',        // 红色
    '看跌': '#68C081',        // 浅绿
    '非常看跌': '#0898BF'     // 亮蓝
  };

  const config = {
    data,
    angleField: 'value',
    colorField: 'type',
    radius: 0.75,
    innerRadius: 0.3,
    color: ({ type }) => colorMapping[type] || '#1890ff',
    label: false, // 不显示饼图内的数字
    legend: {
      position: 'left', // 左侧垂直布局
      layout: 'vertical',
      offsetX: 5,
      offsetY: 0,
      itemHeight: 16,
      itemWidth: 80,
      maxItemWidth: 80,
      itemName: {
        style: {
          fontSize: 9,
          fill: '#fff',
          fontWeight: '400'
        },
        formatter: (text, item) => {
          // 修复：直接从我们的数据源查找值，避免依赖图表库内部item结构
          const foundData = data.find(d => d.type === text);
          const value = foundData ? foundData.value : 0;
          return `${text} ${value}%`;
        }
      },
      marker: {
        symbol: 'circle',
        size: 5
      },
      flipPage: false, // 禁用翻页功能
      maxRow: 5, // 强制5行单列显示
      maxCol: 1, // 强制单列
      itemSpacing: 0, // 最小间距
      itemMarginBottom: 2 // 控制行间距
    },
    interactions: [
      {
        type: 'element-selected',
      },
      {
        type: 'element-active',
      },
    ],
    statistic: {
      title: false,
      content: false // 移除中心文字
    },
  };

  return (
    <div style={{ 
      height: '100%', 
      width: '100%', 
      display: 'flex', 
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      {/* 水平居中的饼图和图例 */}
      <div style={{ 
        width: '280px', // 固定宽度确保居中
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <Pie {...config} />
      </div>
    </div>
  );
};

export default PredictionPieChart;