/* Dashboard 整合样式 - 包含基础样式和高级功能 */

/* ===== 基础Dashboard样式 ===== */
.dashboard-layout {
  height: 100vh;
  background: linear-gradient(135deg, #0f1419 0%, #1a1f2e 50%, #2d3748 100%);
  display: flex;
  flex-direction: column;
}

.dashboard-content {
  flex: 1;
  padding: 24px;
  background: transparent;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  width: 100%;
  height: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow: hidden;
}

/* 主内容行 */
.main-content-row {
  flex: 1;
  overflow: hidden;
}

/* 左侧图表区域 */
.charts-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 右侧信息区域 */
.info-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 上半部分：饼图和多空预测 - 固定20.5vh */
.top-charts-section {
  height: 20.5vh; /* 固定20.5vh高度 */
  min-height: 20.5vh;
  max-height: 20.5vh;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  gap: 12px;
  flex-shrink: 0;
  position: relative;
  z-index: 10; /* 确保在K线图之上 */
  margin-bottom: 16px; /* 确保与下方有间距 */
  overflow: hidden; /* 防止内容溢出 */
}

/* 饼图容器 - 左半部分 */
.pie-chart-container {
  flex: 1; /* 占据1/2空间 */
  height: 100%;
  min-height: 100%;
  max-height: 100%;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 多空预测容器 - 右半部分 */
.prediction-container {
  flex: 1; /* 占据1/2空间 */
  height: 100%;
  min-height: 100%;
  max-height: 100%;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}



/* 标题水平居中 */
.chart-title {
  color: white;
  font-size: 14px;
  margin: 0 0 8px 0;
  font-weight: 500;
  text-align: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
}

/* 饼图内容区域 */
.pie-chart-placeholder {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
}

/* 多空预测内容区域 */
.prediction-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  overflow-y: auto;
}

/* AI预测分析内容区域 */
.ai-analysis-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 50px 16px 16px 15px;
  overflow-y: auto;
}

/* 右侧AI预测区域 - 统一容器，内部垂直滚动 */
.predictions-section {
  height: calc(100vh - 72px);
  min-height: calc(100vh - 72px); /* 确保最小高度 */
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 16px;
  display: flex !important;
  flex-direction: column;
  overflow-y: auto; /* 整个容器垂直滚动 */
  overflow-x: hidden;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 右侧上半部分：AI预测分析 - 与左侧top-charts高度一致 */
.predictions-top-section {
  height: 20.5vh; /* 与左侧top-charts-section高度完全一致 */
  min-height: 20.5vh;
  max-height: 20.5vh;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  margin-bottom: 16px; /* 与下方的间距 */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex-shrink: 0;
}

/* 余额按钮容器 */
.balance-button-container {
  display: flex;
  justify-content: center;
}

/* 余额按钮 */
.balance-button-3d {
  position: relative;
  border: none !important;
  box-shadow:
    0 4px 8px rgba(1, 106, 134, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
  height: 45px !important;
  width: 60% !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  transform: translateY(0px) !important;
  background: linear-gradient(135deg, #016A86 0%, #447A53 100%) !important;
  color: white !important;
  font-weight: 600 !important;
  border-radius: 5px !important;
}

.balance-button-3d:hover {
  background: linear-gradient(135deg, #1a7a9a 0%, #4a8259 100%) !important;
  box-shadow:
    0 6px 12px rgba(1, 106, 134, 0.4),
    0 3px 6px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  transform: translateY(-2px) !important;
  color: white !important;
}

/* 按下效果 - 游戏机按钮下沉 */
.balance-button-3d:active {
  background: linear-gradient(135deg, #014d5f 0%, #356041 100%) !important;
  box-shadow:
    0 1px 2px rgba(1, 106, 134, 0.2),
    0 1px 2px rgba(0, 0, 0, 0.4),
    inset 0 3px 6px rgba(0, 0, 0, 0.4) !important;
  transform: translateY(3px) scale(0.98) !important;
  transition: all 0.08s cubic-bezier(0.4, 0, 0.2, 1) !important;
  color: white !important;
}

/* 按钮释放时的弹回效果 */
.balance-button-3d:not(:active) {
  transition: all 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55) !important;
}

/* AI分析刷新按钮样式 - 复刻3D效果，适配小尺寸 */
.ai-refresh-button-3d {
  position: relative;
  border-radius: 6px;
  overflow: visible;
  display: inline-flex;
  align-items: center;
  height: 24px;
  width: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* AI刷新按钮样式 - 小尺寸游戏机物理按钮效果 */
.ai-refresh-button {
  position: relative;
  z-index: 1;
  border: none !important;
  box-shadow:
    0 2px 4px rgba(68, 122, 83, 0.3),
    0 1px 2px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
  height: 24px !important;
  width: 24px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  transform: translateY(0px) !important;
  background: linear-gradient(145deg, #447A53, #3d6b47) !important;
  border-radius: 6px !important;
}

.ai-refresh-button:hover {
  background: linear-gradient(145deg, #4a8259, #447A53) !important;
  box-shadow:
    0 3px 6px rgba(68, 122, 83, 0.4),
    0 2px 3px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  transform: translateY(-1px) !important;
}

/* AI刷新按钮按下效果 */
.ai-refresh-button:active {
  background: linear-gradient(145deg, #3d6b47, #356041) !important;
  box-shadow:
    0 1px 2px rgba(68, 122, 83, 0.2),
    0 1px 2px rgba(0, 0, 0, 0.4),
    inset 0 2px 4px rgba(0, 0, 0, 0.4) !important;
  transform: translateY(2px) scale(0.98) !important;
  transition: all 0.08s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* AI刷新按钮释放时的弹回效果 */
.ai-refresh-button:not(:active) {
  transition: all 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55) !important;
}

/* AI刷新按钮加载状态时的脉冲效果 */
.ai-refresh-button.ant-btn-loading {
  animation: aiRefreshPulse 1.5s ease-in-out infinite;
}

@keyframes aiRefreshPulse {
  0%, 100% {
    box-shadow:
      0 2px 4px rgba(68, 122, 83, 0.3),
      0 1px 2px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
  50% {
    box-shadow:
      0 4px 8px rgba(68, 122, 83, 0.6),
      0 2px 4px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
}

/* 右侧下半部分：历史预测列表 */
.predictions-bottom-section {
  flex: 1; /* 占据剩余空间 */
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 16px 5px 16px 5px; /* 添加内边距 */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0; /* 允许flex收缩 */
}

.predictions-card {
  height: 100%;
  padding: 0; /* 移除padding，让Card自然适应容器 */
}

.predictions-list {
  height: 100%; /* 占满整个容器，不再需要减去Card头部高度 */
  overflow-y: auto;
  padding-right: 8px; /* 为滚动条留出空间 */
  display: flex;
  flex-direction: column;
  gap: 16px; /* 预测项之间的间距 */
}

/* 预测项样式 - 调整为适合历史列表的高度 */
.prediction-item {
  min-height: 140px; /* 适合历史预测的最小高度 */
  padding: 16px; /* 增加内边距 */
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: all 0.3s ease;
  box-sizing: border-box; /* 确保padding不影响总高度 */
  flex-shrink: 0; /* 防止被压缩 */
}

.prediction-item.latest {
  background: #2a2a2a;
  border-color: #447A53;
  box-shadow: 0 0 10px rgba(68, 122, 83, 0.3);
}

.prediction-item:hover {
  border-color: #555;
  background: #252525;
}

/* 预测项内部元素样式 */
.prediction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.prediction-header span {
  color: #999;
  font-size: 12px;
}

.prediction-header .latest-badge {
  background: #447A53;
  color: #fff;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
}

.prediction-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.prediction-direction {
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.prediction-direction.bullish {
  color: #26A69A;
}

.prediction-direction.bearish {
  color: #EF5350;
}

.prediction-price-info {
  text-align: center;
  margin-bottom: 8px;
}

.prediction-price {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.prediction-change {
  font-size: 14px;
  font-weight: 500;
}

.prediction-change.positive {
  color: #26A69A;
}

.prediction-change.negative {
  color: #EF5350;
}

.prediction-confidence {
  text-align: center;
  color: #999;
  font-size: 12px;
}

.prediction-confidence span {
  color: #fff;
}

/* 下半部分：K线图 - 避免覆盖top-charts */
.bottom-chart-section,
.kline-chart-section {
  height: calc(100vh - 20.5vh ); 
  margin-top: 8px; /* 与top-charts的间距 */
  position: relative;
  z-index: 1; /* 确保在top-charts之下 */
}

/* K线图卡片样式 */
.kline-chart-section .ant-card {
  height: 100%;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
}

.kline-chart-section .ant-card-body {
  height: calc(100% - 60px); /* 减去卡片头部高度 */
  padding: 16px;
  overflow: hidden;
}

/* 图表控制区域 */
.kline-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-shrink: 0;
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 6px;
  padding: 12px 16px;
}

/* 标题区域 - 左侧标题和右侧时间选择器 */
.chart-title-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.chart-title-controls {
  display: flex;
  align-items: center;
}

/* 时间周期选择器样式 - 与GridKLineChart保持一致 */
.dashboard-global-toolbar .ant-radio-group {
  background-color: #21262d;
  border-radius: 6px;
  padding: 2px;
}

.dashboard-global-toolbar .ant-radio-button-wrapper {
  background-color: transparent;
  border-color: #30363d;
  color: #8b949e;
  font-size: 11px;
  padding: 2px 8px;
  height: 28px;
  line-height: 24px;
  border: 1px solid #30363d;
}

.dashboard-global-toolbar .ant-radio-button-wrapper:hover {
  color: #f0f6fc;
  border-color: #58a6ff;
}

.dashboard-global-toolbar .ant-radio-button-wrapper-checked {
  background-color: #58a6ff;
  border-color: #58a6ff;
  color: #ffffff;
}

.dashboard-global-toolbar .ant-radio-button-wrapper-checked:hover {
  background-color: #3d8bfd;
  border-color: #3d8bfd;
}

/* K线图网格容器 - 固定2*2布局，其他图表垂直滚动 */
.kline-charts-grid {
  overflow-y: auto;
  overflow-x: hidden;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr); /* 固定2行，每行高度相等 */
  gap: 8px;
  padding: 8px;
  background: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #333;
  height: calc(100vh - 20.5vh - 180px); /* 减去top-charts的20.5vh、导航栏和间距 */
  max-height: calc(100vh - 20.5vh - 180px);
  position: relative;
  z-index: 1; /* 确保在top-charts之下 */
}

/* 单个K线图容器 - 固定高度确保副图完整显示 */
.kline-chart-item {
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 4px;
  padding: 2px;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  box-sizing: border-box;
  height: calc((100vh - 20.5vh - 180px - 16px) / 2); /* (总高度 - padding) / 2 */
  min-height: calc((100vh - 20.5vh - 180px - 16px) / 2);
  max-height: calc((100vh - 20.5vh - 180px - 16px) / 2);
  overflow: hidden;
}

.kline-chart-title {
  color: rgba(255, 255, 255, 0.8);
  font-size: 10px; /* 进一步减小字体 */
  margin-bottom: 1px; /* 进一步减少边距 */
  text-align: center;
  font-weight: 500;
  flex-shrink: 0;
  height: 12px; /* 进一步减少标题高度 */
  line-height: 12px;
}

.kline-chart-content {
  flex: 1;
  overflow: hidden;
  min-height: 0;
  height: calc(100% - 12px); /* 对应标题高度调整 */
}

/* 当年图表突出显示 */
.kline-chart-item.current-year {
  border-color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

.kline-chart-item.current-year .kline-chart-title {
  color: #1890ff;
  font-weight: 600;
}



.predictions-header {
  margin-bottom: 20px;
  flex-shrink: 0;
}

.section-title {
  color: white;
  font-size: 18px;
  margin: 0 0 4px 0;
  font-weight: 600;
  text-align: center;
}

.section-subtitle {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  text-align: center;
}

.predictions-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding-right: 8px;
}

/* 滚动条样式 */
.kline-charts-grid::-webkit-scrollbar,
.predictions-list::-webkit-scrollbar,
.prediction-content::-webkit-scrollbar,
.predictions-section::-webkit-scrollbar {
  width: 6px;
}

.kline-charts-grid::-webkit-scrollbar-track,
.predictions-list::-webkit-scrollbar-track,
.prediction-content::-webkit-scrollbar-track,
.predictions-section::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.kline-charts-grid::-webkit-scrollbar-thumb,
.predictions-list::-webkit-scrollbar-thumb,
.prediction-content::-webkit-scrollbar-thumb,
.predictions-section::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.kline-charts-grid::-webkit-scrollbar-thumb:hover,
.predictions-list::-webkit-scrollbar-thumb:hover,
.prediction-content::-webkit-scrollbar-thumb:hover,
.predictions-section::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* ===== Dashboard高级样式覆盖 ===== */
.dashboard-layout .dashboard-content {
  background: transparent;
  min-height: calc(100vh - 140px);
}

/* AI预测分析样式 */
.analysis-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.analysis-icon {
  font-size: 20px;
  width: 32px;
  text-align: center;
}

.analysis-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.analysis-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.analysis-value {
  color: #26A69A;
  font-weight: 600;
  font-size: 14px;
}

.analysis-summary {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.summary-value.positive {
  color: #26A69A;
}

/* 饼图样式 */
.pie-chart-mock {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: conic-gradient(
    #26A69A 0deg 162deg,
    #EF5350 162deg 288deg,
    #FFA726 288deg 360deg
  );
  display: flex;
  align-items: center;
  justify-content: center;
}

.pie-center {
  width: 60px;
  height: 60px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.pie-label {
  color: white;
  font-size: 12px;
  font-weight: 600;
}

.pie-value {
  color: #26A69A;
  font-size: 14px;
  font-weight: 700;
}

.pie-legend {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color-1 { background: #26A69A; }
.legend-color-2 { background: #EF5350; }
.legend-color-3 { background: #FFA726; }

/* 预测项样式 */
.prediction-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.prediction-icon {
  font-size: 20px;
  width: 32px;
  text-align: center;
}

.prediction-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.prediction-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.prediction-value {
  color: #26A69A;
  font-weight: 600;
  font-size: 14px;
}

.prediction-item.bearish .prediction-value {
  color: #EF5350;
}

.prediction-summary {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.summary-label {
  color: rgba(255, 255, 255, 0.7);
}

.summary-value {
  color: white;
  font-weight: 600;
}

.summary-value.strong {
  color: #26A69A;
}

.dashboard-layout .content-wrapper {
  padding: 16px;
  margin: 0 auto;
}

/* 预测卡片样式 */
.prediction-card {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.prediction-card:hover {
  border-color: rgba(255, 255, 255, 0.2);
  background: rgba(0, 0, 0, 0.4);
}

.prediction-card.premium {
  border-color: #FFA726;
  background: rgba(255, 167, 38, 0.1);
}

.prediction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.prediction-time {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

.prediction-status {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.prediction-status.success {
  background: rgba(38, 166, 154, 0.2);
  color: #26A69A;
}

.prediction-status.partial {
  background: rgba(255, 167, 38, 0.2);
  color: #FFA726;
}

.prediction-status.failed {
  background: rgba(239, 83, 80, 0.2);
  color: #EF5350;
}

.prediction-status.premium-badge {
  background: rgba(255, 167, 38, 0.2);
  color: #FFA726;
  display: flex;
  align-items: center;
  gap: 4px;
}

.prediction-content.locked {
  filter: blur(2px);
  opacity: 0.6;
}

.prediction-symbol {
  color: white;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 8px;
}

.prediction-direction {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 500;
}

.prediction-direction.bullish {
  color: #26A69A;
}

.prediction-direction.bearish {
  color: #EF5350;
}

.prediction-confidence {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

.prediction-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.price-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
}

.price-value {
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.price-value.locked-text {
  color: #FFA726;
}

.prediction-result {
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  padding: 4px 8px;
  border-radius: 4px;
}

.prediction-result.success {
  background: rgba(38, 166, 154, 0.2);
  color: #26A69A;
}

.prediction-result.partial {
  background: rgba(255, 167, 38, 0.2);
  color: #FFA726;
}

.prediction-result.failed {
  background: rgba(239, 83, 80, 0.2);
  color: #EF5350;
}

.prediction-actions {
  margin-top: 12px;
}

.unlock-btn {
  background: #FFA726 !important;
  border-color: #FFA726 !important;
  color: white !important;
}

.unlock-btn:hover {
  background: #FF9800 !important;
  border-color: #FF9800 !important;
}

.predictions-footer {
  margin-top: 20px;
  text-align: center;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 16px;
}

.view-more-btn {
  color: #1890ff !important;
  font-size: 14px;
}

/* 控制面板 */
.control-panel {
  background: #1a1a1a !important;
  border: 1px solid #333 !important;
  margin-bottom: 16px;
}

.control-panel .ant-card-body {
  padding: 12px 16px !important;
}

.control-panel .symbol-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.control-panel .symbol-name {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
}

.control-panel .data-count {
  font-size: 12px;
  color: #999;
}

/* 图表网格容器 */
.charts-grid-container {
  background: transparent;
}

.chart-col {
  margin-bottom: 16px;
}

/* 图表容器全屏样式 */
.kline-card-fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999 !important;
  background: #0f0f0f !important;
  border: none !important;
  border-radius: 0 !important;
  margin: 0 !important;
}

.kline-card-fullscreen .ant-card-body {
  height: calc(100vh - 60px) !important;
  padding: 0 !important;
}

.kline-card-fullscreen .ant-card-head {
  background: #1a1a1a !important;
  border-bottom: 1px solid #333 !important;
}

/* 全屏模式下的图表容器 */
.kline-card-fullscreen .charts-grid-container {
  height: calc(100vh - 60px) !important;
}

.kline-card-fullscreen .charts-grid {
  height: 100% !important;
}

/* 全屏模式下的网格布局优化 */
.kline-card-fullscreen .kline-charts-grid {
  height: calc(100vh - 60px) !important;
  max-height: calc(100vh - 60px) !important;
  grid-template-rows: repeat(2, 1fr) !important;
  gap: 12px !important;
  padding: 12px !important;
}

.kline-card-fullscreen .kline-chart-item {
  padding: 8px !important;
}

.kline-card-fullscreen .kline-chart-title {
  height: 20px !important;
  line-height: 20px !important;
  font-size: 12px !important;
  margin-bottom: 4px !important;
}

.kline-card-fullscreen .kline-chart-content {
  height: calc(100% - 20px) !important;
}

/* 全屏模式下的处理 - 完全移除隐藏逻辑，让布局自然响应 */
/* 注释掉原来的隐藏逻辑，避免影响右侧预测区域 */
/*
.kline-card-fullscreen ~ * {
  display: none !important;
}
*/

/* 全屏模式下的K线图容器样式保持不变 */

/* 3D刷新按钮样式 - 复制Profile套餐卡片效果 */
.refresh-atropos {
  width: auto;
  height: 32px; /* 固定高度与其他按钮对齐 */
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
}

.refresh-button-3d {
  position: relative;
  border-radius: 8px;
  overflow: visible; /* 允许暴击效果溢出 */
  display: inline-flex;
  align-items: center;
  height: 32px; /* 与其他按钮相同高度 */
  cursor: pointer;
  transition: all 0.3s ease;
}



/* 背景光晕效果 */
.refresh-background-glow {
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(68, 122, 83, 0.3) 0%, transparent 70%);
  pointer-events: none;
  z-index: 0;
}

/* 刷新按钮样式 - 游戏机物理按钮效果 */
.refresh-button {
  position: relative;
  z-index: 1;
  border: none !important;
  box-shadow:
    0 4px 8px rgba(68, 122, 83, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
  height: 32px !important;
  display: inline-flex !important;
  align-items: center !important;
  transform: translateY(0px) !important;
  background: linear-gradient(145deg, #447A53, #3d6b47) !important;
}

.refresh-button:hover {
  background: linear-gradient(145deg, #4a8259, #447A53) !important;
  box-shadow:
    0 6px 12px rgba(68, 122, 83, 0.4),
    0 3px 6px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  transform: translateY(-2px) !important;
}

/* 按下效果 - 游戏机按钮下沉 */
.refresh-button:active {
  background: linear-gradient(145deg, #3d6b47, #356041) !important;
  box-shadow:
    0 1px 2px rgba(68, 122, 83, 0.2),
    0 1px 2px rgba(0, 0, 0, 0.4),
    inset 0 3px 6px rgba(0, 0, 0, 0.4) !important;
  transform: translateY(3px) scale(0.98) !important;
  transition: all 0.08s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 按钮释放时的弹回效果 */
.refresh-button:not(:active) {
  transition: all 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55) !important;
}

/* 光泽效果 */
.refresh-shine-effect {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.3s ease;
  pointer-events: none;
  z-index: 2;
}

.refresh-button-3d:hover .refresh-shine-effect {
  left: 100%;
}



/* 加载状态时的脉冲效果 */
.refresh-button.ant-btn-loading {
  animation: refreshPulse 1.5s ease-in-out infinite;
}

@keyframes refreshPulse {
  0%, 100% {
    box-shadow:
      0 4px 8px rgba(68, 122, 83, 0.3),
      0 2px 4px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
  50% {
    box-shadow:
      0 6px 16px rgba(68, 122, 83, 0.6),
      0 3px 8px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
}

/* 响应式设计 */
/* 针对1440宽度的特殊处理 */
@media (min-width: 1200px) and (max-width: 1600px) {
  .predictions-section {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}

@media (max-width: 768px) {
  .refresh-atropos {
    transform: scale(0.9);
  }

  .refresh-button {
    font-size: 12px !important;
    padding: 2px 8px !important;
  }
}

@media (max-width: 480px) {
  .refresh-atropos {
    transform: scale(0.8);
  }

  .refresh-button {
    font-size: 11px !important;
    padding: 1px 6px !important;
  }

  .refresh-background-glow {
    opacity: 0.5;
  }
}

/* 全屏按钮样式 */
.ant-btn:has(.anticon-fullscreen),
.ant-btn:has(.anticon-fullscreen-exit) {
  transition: all 0.3s ease !important;
}

.ant-btn:has(.anticon-fullscreen):hover,
.ant-btn:has(.anticon-fullscreen-exit):hover {
  background-color: #2a2a2a !important;
  border-color: #447A53 !important;
  color: #447A53 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(68, 122, 83, 0.2) !important;
}

/* 图表卡片 */
.chart-card {
  background: #1a1a1a !important;
  border: 1px solid #333 !important;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.chart-card:hover {
  border-color: #447A53;
  box-shadow: 0 4px 12px rgba(68, 122, 83, 0.2);
}

.chart-card .ant-card-head {
  background: #2a2a2a;
  border-bottom: 1px solid #333;
  padding: 0 16px;
  min-height: 40px;
}

.chart-card .ant-card-head-title {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 0;
}

.chart-card .ant-card-body {
  background: #1a1a1a;
  padding: 8px !important;
}

/* K线图卡片特殊样式 - 移除内边距 */
.kline-card .ant-card-body {
  padding: 0 !important;
}

/* 图表卡片标题 */
.chart-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-card-title .anticon {
  color: #447A53;
}

.current-badge {
  background: linear-gradient(135deg, #447A53, #26A69A);
  color: #fff;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 500;
  margin-left: auto;
}

/* 无数据占位符 */
.no-data-placeholder {
  text-align: center;
  padding: 60px 20px;
  background: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #333;
}

/* 控制面板组件样式 */
.control-panel .ant-select {
  background: #333;
  border-color: #555;
}

.control-panel .ant-select-selector {
  background-color: #333 !important;
  border-color: #555 !important;
  color: #fff !important;
}

.control-panel .ant-select-selection-item {
  color: #fff !important;
}

.control-panel .ant-switch {
  background-color: #333;
  border: 1px solid #555;
}

.control-panel .ant-switch-checked {
  background-color: #447A53;
}

.control-panel .ant-switch-inner {
  color: #fff;
  font-size: 10px;
}

.control-panel .ant-btn {
  background: transparent;
  border-color: #555;
  color: #999;
}

.control-panel .ant-btn:hover {
  border-color: #447A53;
  color: #447A53;
  background: rgba(68, 122, 83, 0.1);
}

@media (max-width: 768px) {
  .top-charts-section {
    flex-direction: column;
    gap: 8px;
    height: auto;
    min-height: 25vh;
  }

  .pie-chart-container,
  .prediction-container {
    height: 120px;
    min-height: 120px;
  }

  .prediction-item {
    height: 120px;
    min-height: 120px;
    max-height: 120px;
  }
}

/* 超小屏幕样式调整 */
@media (max-width: 480px) {
  .top-charts-section {
    padding: 8px;
    gap: 6px;
    min-height: 30vh;
  }

  .pie-chart-container,
  .prediction-container {
    height: 100px;
    min-height: 100px;
  }

  .prediction-item {
    height: 100px;
    min-height: 100px;
    max-height: 100px;
  }
}

/* 确保预测区域正常显示 */
.predictions-section {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

