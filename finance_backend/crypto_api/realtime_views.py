"""
实时数据持久化API视图
"""

import json
import logging
from decimal import Decimal
from datetime import datetime, timedelta
from django.http import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.db import transaction
from django.db.models import Max, Min, Count
from .models import RealTimeKlineData, DataSyncStatus

logger = logging.getLogger(__name__)

@method_decorator(csrf_exempt, name='dispatch')
class RealTimeDataUploadView(View):
    """实时数据上传接口 - 5分钟批量上传"""
    
    def post(self, request):
        try:
            data = json.loads(request.body)
            symbol = data.get('symbol', 'BTCUSDT')
            timeframe = data.get('timeframe', '1m')
            kline_data = data.get('data', [])
            
            if not kline_data:
                return JsonResponse({
                    'success': False,
                    'error': '没有数据需要上传'
                })
            
            success_count = 0
            error_count = 0
            
            # 批量插入数据，使用事务确保数据一致性
            with transaction.atomic():
                for item in kline_data:
                    try:
                        # 数据去重检查
                        if RealTimeKlineData.objects.filter(
                            symbol=symbol,
                            timeframe=timeframe,
                            timestamp=item['timestamp']
                        ).exists():
                            continue  # 跳过重复数据
                        
                        # 创建新记录
                        RealTimeKlineData.objects.create(
                            symbol=symbol,
                            timeframe=timeframe,
                            timestamp=item['timestamp'],
                            open_price=Decimal(str(item['open'])),
                            high_price=Decimal(str(item['high'])),
                            low_price=Decimal(str(item['low'])),
                            close_price=Decimal(str(item['close'])),
                            volume=Decimal(str(item['volume'])),
                            turnover=Decimal(str(item.get('turnover', item['close'] * item['volume']))),
                            source='websocket'
                        )
                        success_count += 1
                        
                    except Exception as e:
                        logger.error(f"数据插入失败: {item}, 错误: {e}")
                        error_count += 1
                
                # 更新同步状态
                sync_status, created = DataSyncStatus.objects.get_or_create(
                    symbol=symbol,
                    timeframe=timeframe,
                    defaults={
                        'last_sync_timestamp': max([item['timestamp'] for item in kline_data]),
                        'total_records': success_count
                    }
                )
                
                if not created:
                    sync_status.last_sync_timestamp = max([item['timestamp'] for item in kline_data])
                    sync_status.total_records += success_count
                    if error_count > 0:
                        sync_status.sync_errors += error_count
                        sync_status.last_error_message = f"本次上传失败 {error_count} 条记录"
                    sync_status.save()
            
            logger.info(f"数据上传完成: {symbol} {timeframe} - 成功: {success_count}, 失败: {error_count}")
            
            return JsonResponse({
                'success': True,
                'uploaded': success_count,
                'errors': error_count,
                'message': f'成功上传 {success_count} 条记录'
            })
            
        except Exception as e:
            logger.error(f"数据上传API异常: {e}")
            return JsonResponse({
                'success': False,
                'error': str(e)
            })

@method_decorator(csrf_exempt, name='dispatch')
class RealTimeDataQueryView(View):
    """实时数据查询接口 - 前端定期拉取"""
    
    def post(self, request):
        try:
            data = json.loads(request.body)
            symbol = data.get('symbol', 'BTCUSDT')
            timeframe = data.get('timeframe', '1m')
            start_time = data.get('start_time')  # 可选：指定开始时间
            limit = data.get('limit', 1000)  # 默认返回最近1000条
            
            # 构建查询条件
            queryset = RealTimeKlineData.objects.filter(
                symbol=symbol,
                timeframe=timeframe
            ).order_by('timestamp')
            
            if start_time:
                queryset = queryset.filter(timestamp__gte=start_time)
            else:
                # 默认返回最近的记录
                queryset = queryset[:limit]
            
            # 转换为前端需要的格式
            kline_data = []
            for record in queryset:
                kline_data.append({
                    'timestamp': record.timestamp,
                    'open': float(record.open_price),
                    'high': float(record.high_price),
                    'low': float(record.low_price),
                    'close': float(record.close_price),
                    'volume': float(record.volume),
                    'turnover': float(record.turnover or 0)
                })
            
            # 获取同步状态
            try:
                sync_status = DataSyncStatus.objects.get(
                    symbol=symbol,
                    timeframe=timeframe
                )
                status_info = {
                    'last_sync': sync_status.last_sync_timestamp,
                    'total_records': sync_status.total_records,
                    'sync_errors': sync_status.sync_errors
                }
            except DataSyncStatus.DoesNotExist:
                status_info = {
                    'last_sync': None,
                    'total_records': 0,
                    'sync_errors': 0
                }
            
            return JsonResponse({
                'success': True,
                'data': kline_data,
                'count': len(kline_data),
                'sync_status': status_info,
                'message': f'返回 {len(kline_data)} 条记录'
            })
            
        except Exception as e:
            logger.error(f"数据查询API异常: {e}")
            return JsonResponse({
                'success': False,
                'error': str(e)
            })

@method_decorator(csrf_exempt, name='dispatch')
class DataSyncStatusView(View):
    """数据同步状态查询接口"""
    
    def get(self, request):
        try:
            # 获取所有同步状态
            statuses = DataSyncStatus.objects.all()
            
            status_list = []
            for status in statuses:
                # 计算数据统计
                record_count = RealTimeKlineData.objects.filter(
                    symbol=status.symbol,
                    timeframe=status.timeframe
                ).count()
                
                # 获取时间范围
                time_range = RealTimeKlineData.objects.filter(
                    symbol=status.symbol,
                    timeframe=status.timeframe
                ).aggregate(
                    earliest=Min('timestamp'),
                    latest=Max('timestamp')
                )
                
                status_list.append({
                    'symbol': status.symbol,
                    'timeframe': status.timeframe,
                    'total_records': record_count,
                    'last_sync_timestamp': status.last_sync_timestamp,
                    'last_sync_time': datetime.fromtimestamp(status.last_sync_timestamp / 1000).isoformat() if status.last_sync_timestamp else None,
                    'sync_errors': status.sync_errors,
                    'last_error': status.last_error_message,
                    'time_range': {
                        'earliest': time_range['earliest'],
                        'latest': time_range['latest']
                    },
                    'updated_at': status.updated_at.isoformat()
                })
            
            return JsonResponse({
                'success': True,
                'statuses': status_list,
                'total_symbols': len(status_list)
            })
            
        except Exception as e:
            logger.error(f"同步状态查询异常: {e}")
            return JsonResponse({
                'success': False,
                'error': str(e)
            })

@method_decorator(csrf_exempt, name='dispatch')
class MergedDataView(View):
    """合并数据接口 - 整合服务器数据 + COS历史数据"""
    
    def post(self, request):
        try:
            data = json.loads(request.body)
            symbol = data.get('symbol', 'BTCUSDT')
            timeframe = data.get('timeframe', '1m')
            year = data.get('year', '2025')
            
            # 🎯 专门处理2025年数据合并
            if year == '2025':
                # 1. 获取服务器端实时数据
                server_data = list(RealTimeKlineData.objects.filter(
                    symbol=symbol,
                    timeframe=timeframe
                ).order_by('timestamp').values(
                    'timestamp', 'open_price', 'high_price', 'low_price', 
                    'close_price', 'volume', 'turnover'
                ))
                
                # 转换格式
                realtime_records = []
                for record in server_data:
                    realtime_records.append({
                        'timestamp': record['timestamp'],
                        'open': float(record['open_price']),
                        'high': float(record['high_price']),
                        'low': float(record['low_price']),
                        'close': float(record['close_price']),
                        'volume': float(record['volume']),
                        'turnover': float(record['turnover'] or 0)
                    })
                
                return JsonResponse({
                    'success': True,
                    'data': realtime_records,
                    'count': len(realtime_records),
                    'sources': {
                        'server_realtime': len(realtime_records),
                        'cos_historical': 0  # TODO: 整合COS历史数据
                    },
                    'message': f'返回服务器端实时数据 {len(realtime_records)} 条'
                })
            
            return JsonResponse({
                'success': False,
                'error': f'暂不支持 {year} 年数据合并'
            })
            
        except Exception as e:
            logger.error(f"合并数据API异常: {e}")
            return JsonResponse({
                'success': False,
                'error': str(e)
            })