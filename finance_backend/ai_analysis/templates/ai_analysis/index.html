<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加密货币分析API服务</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1, h2 {
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .api-info {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .api-endpoint {
            background-color: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
        }
        pre {
            background-color: #f1f1f1;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        code {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            background-color: #f1f1f1;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .method-get {
            background-color: #4caf50;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 0.8em;
        }
        .method-post {
            background-color: #ff9800;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 0.8em;
        }
        .example {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🚀 加密货币K线分析API服务</h1>
    <p>欢迎使用加密货币K线分析API服务！本服务提供Binance K线数据获取和AI智能分析功能。</p>
    
    <h2>📊 API接口列表</h2>
    
    <!-- K线数据API -->
    <div class="api-endpoint">
        <h3><span class="method-get">GET</span> /api/ai/kline/ - 获取K线数据</h3>
        <p><strong>功能：</strong>获取Binance交易所的K线数据，支持获取超过1000条的历史数据</p>
        
        <h4>请求参数：</h4>
        <ul>
            <li><code>symbol</code> (string) - 交易对，如 ETHUSDT, BTCUSDT (默认: ETHUSDT)</li>
            <li><code>interval</code> (string) - K线间隔：1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 6h, 8h, 12h, 1d, 3d, 1w, 1M (默认: 5m)</li>
            <li><code>limit</code> (int) - 返回数据条数 (默认: 1000)</li>
            <li><code>start_time</code> (string, 可选) - 开始时间，支持多种格式：
                <ul>
                    <li>"2023-01-01"</li>
                    <li>"1 year ago UTC"</li>
                    <li>"2023-01-01 00:00:00"</li>
                </ul>
            </li>
            <li><code>end_time</code> (string, 可选) - 结束时间，格式同start_time</li>
        </ul>
        
        <div class="example">
            <h4>使用示例：</h4>
            <pre><code># 获取最近1000条5分钟K线数据
GET /api/ai/kline/?symbol=ETHUSDT&interval=5m&limit=1000

# 获取2023年全年的日K线数据  
GET /api/ai/kline/?symbol=BTCUSDT&interval=1d&start_time=2023-01-01&end_time=2023-12-31

# 获取过去一年的周K线数据
GET /api/ai/kline/?symbol=ETHUSDT&interval=1w&start_time=1 year ago UTC&end_time=now UTC</code></pre>
        </div>
        
        <h4>响应格式：</h4>
        <pre><code>{
  "code": 200,
  "message": "成功获取K线数据",
  "data": {
    "symbol": "ETHUSDT",
    "interval": "5m",
    "count": 1000,
    "start_time": "2023-01-01",
    "end_time": "2023-12-31",
    "klines": [
      {
        "timestamp": 1672531200000,
        "open": 1200.50,
        "high": 1205.30,
        "low": 1199.80,
        "close": 1203.20,
        "volume": 1500.25,
        "close_time": 1672531500000,
        "quote_volume": 1805040.50,
        "trades_count": 485,
        "taker_buy_volume": 750.12,
        "taker_buy_quote_volume": 902520.25
      }
      // ... 更多K线数据
    ]
  }
}</code></pre>
    </div>

    <!-- AI分析API -->
    <div class="api-endpoint">
        <h3><span class="method-post">POST</span> /api/ai/analysis/ - AI智能分析</h3>
        <p><strong>功能：</strong>基于K线数据进行AI技术分析，给出交易信号和投资建议</p>
        
        <h4>请求体：</h4>
        <pre><code>{
  "symbol": "ETHUSDT",
  "klines": [
    {
      "timestamp": 1672531200000,
      "open": 1200.50,
      "high": 1205.30,
      "low": 1199.80,
      "close": 1203.20,
      "volume": 1500.25
    }
    // ... 更多K线数据，建议20-100条
  ]
}</code></pre>
        
        <div class="example">
            <h4>使用示例：</h4>
            <pre><code>curl -X POST "http://localhost:8000/api/ai/analysis/" \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "ETHUSDT",
    "klines": [...]
  }'</code></pre>
        </div>
        
        <h4>响应格式：</h4>
        <pre><code>{
  "code": 200,
  "message": "分析完成",
  "data": {
    "symbol": "ETHUSDT",
    "signal": "看涨",
    "risk": "中",
    "summary": "从技术指标来看，当前价格突破重要阻力位，成交量放大，短期有继续上涨动能...",
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}</code></pre>
    </div>

    <!-- Chat AI API -->
    <div class="api-endpoint">
        <h3><span class="method-post">POST</span> /api/ai/chat/ - AI对话分析</h3>
        <p><strong>功能：</strong>通用AI对话接口，支持加密货币相关问题咨询</p>
        
        <h4>请求体：</h4>
        <pre><code>{
  "messages": [
    {"role": "user", "content": "分析比特币最近的价格走势"}
  ],
  "cryptoData": {
    "klines": [...],  // 可选：K线数据
    "ticker": {...}   // 可选：实时价格数据
  }
}</code></pre>
        
        <h4>响应格式：</h4>
        <pre><code>{
  "choices": [
    {
      "message": {
        "role": "assistant",
        "content": "根据提供的数据，比特币最近的价格走势..."
      }
    }
  ]
}</code></pre>
    </div>

    <h2>🔧 测试API接口</h2>
    <div class="api-info">
        <h3>使用curl测试：</h3>
        <pre><code># 测试K线数据API
curl "http://localhost:8000/api/ai/kline/?symbol=ETHUSDT&interval=5m&limit=10"

# 测试历史数据获取（一个月前到现在的小时K线）
curl "http://localhost:8000/api/ai/kline/?symbol=BTCUSDT&interval=1h&start_time=1 month ago UTC&end_time=now UTC"

# 测试AI分析API
curl -X POST "http://localhost:8000/api/ai/analysis/" \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "ETHUSDT",
    "klines": [
      {
        "timestamp": 1672531200000,
        "open": 1200.50,
        "high": 1205.30,
        "low": 1199.80,
        "close": 1203.20,
        "volume": 1500.25
      }
    ]
  }'</code></pre>
    </div>

    <h2>📝 注意事项</h2>
    <div class="api-info">
        <ul>
            <li><strong>CORS支持：</strong>已配置跨域访问，前端端口支持8888</li>
            <li><strong>历史数据：</strong>使用start_time参数可以获取多年前的历史数据，不受1000条限制</li>
            <li><strong>API限制：</strong>遵循Binance API限制，自动控制请求频率</li>
            <li><strong>时间格式：</strong>支持自然语言时间格式，如"1 year ago UTC"、"2023-01-01"等</li>
            <li><strong>数据质量：</strong>AI分析建议使用20-100条K线数据以获得更准确的分析结果</li>
        </ul>
    </div>

    <p>本服务由Django提供，完全兼容原Flask API接口。</p>
</body>
</html> 