/**
 * AI分析服务 - 与后端AI API交互
 * 基于现有的小程序AI分析逻辑，适配Web前端
 * 增强版：支持数据验证、错误恢复和完整的数据流处理
 */

import AIAnalysisHelper from '../utils/aiAnalysisHelper';
import apiClient from '../utils/apiClient';

class AIAnalysisService {
  constructor() {
    this.baseURL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000';
    this.aiEndpoint = '/api/ai/chat/';
    this.quotationEndpoint = '/api/ai/quotation';
    
    // 分析类型配置
    this.analysisTypes = {
      'quick': { label: '快速分析', period: '未来24小时' },
      'four_hour': { label: '闪电分析', period: '未来4小时' },
      'one_day': { label: '一日分析', period: '未来24小时' },
      'three_day': { label: '三日分析', period: '未来3天' },
      'week': { label: '一周分析', period: '未来一周' },
      'long_term': { label: '长期分析', period: '长期' }
    };
    
    // 币种中文名称映射
    this.coinNameMap = {
      'BTC': '比特币',
      'ETH': '以太坊',
      'BNB': '币安币',
      'SOL': '索拉纳',
      'ADA': '卡尔达诺',
      'XRP': '瑞波币',
      'DOGE': '狗狗币',
      'DOT': '波卡',
      'MATIC': '马蒂奇',
      'LTC': '莱特币'
    };

    this.baseUrl = '/api/ai';
    this.cache = {
      predictions: {},
      lastFetch: null
    };
  }

  /**
   * 获取K线数据
   */
  async fetchKlineData(symbol, analysisType) {
    try {
      const intervals = this.getIntervalsForAnalysis(analysisType);
      const klineData = {};
      
      // 获取日K线数据（90天）
      const dailyData = await this.fetchBinanceKlines(symbol, '1d', 90);
      klineData.dailyKlines = dailyData;
      
      // 根据分析类型获取短期K线数据
      if (intervals.shortTerm) {
        const shortTermData = await this.fetchBinanceKlines(
          symbol, 
          intervals.shortTerm.interval, 
          intervals.shortTerm.limit
        );
        klineData.shortTermKlines = shortTermData;
      }
      
      return klineData;
    } catch (error) {
      console.error('获取K线数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取实时价格数据
   */
  async fetchTickerData(symbol) {
    try {
      const response = await fetch(
        `${this.baseURL}${this.quotationEndpoint}/binance/api/v3/ticker/24hr?symbol=${symbol}USDT`
      );
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      return {
        symbol: data.symbol,
        lastPrice: data.lastPrice,
        priceChangePercent: data.priceChangePercent,
        volume: data.volume,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('获取实时价格失败:', error);
      throw error;
    }
  }

  /**
   * 获取订单簿数据
   */
  async fetchOrderbookData(symbol, limit = 20) {
    try {
      const response = await fetch(
        `${this.baseURL}${this.quotationEndpoint}/binance/api/v3/depth?symbol=${symbol}USDT&limit=${limit}`
      );
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('获取订单簿数据失败:', error);
      return null;
    }
  }

  /**
   * 获取BTC辅助数据（用于非BTC币种的市场参考）
   */
  async fetchBTCAuxiliaryData(analysisType) {
    try {
      console.log('🔍 获取BTC辅助数据作为市场参考...');
      
      // 并行获取BTC的K线、价格和订单簿数据
      const [btcKlineData, btcTickerData, btcOrderbookData] = await Promise.all([
        this.fetchKlineData('BTC', analysisType),
        this.fetchTickerData('BTC'),
        this.fetchOrderbookData('BTC', 20)
      ]);

      return {
        klineData: btcKlineData,
        tickerData: btcTickerData,
        orderbookData: btcOrderbookData
      };
    } catch (error) {
      console.error('获取BTC辅助数据失败:', error);
      return null;
    }
  }

  /**
   * 获取币安K线数据
   */
  async fetchBinanceKlines(symbol, interval, limit) {
    try {
      const response = await fetch(
        `${this.baseURL}${this.quotationEndpoint}/binance/api/v3/klines?symbol=${symbol}USDT&interval=${interval}&limit=${limit}`
      );
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('获取K线数据失败:', error);
      throw error;
    }
  }

  /**
   * 根据分析类型获取时间间隔配置
   */
  getIntervalsForAnalysis(analysisType) {
    const configs = {
      'four_hour': { shortTerm: { interval: '1m', limit: 240 } }, // 4小时1分钟数据
      'one_day': { shortTerm: { interval: '5m', limit: 288 } },   // 24小时5分钟数据
      'three_day': { shortTerm: { interval: '15m', limit: 288 } }, // 3天15分钟数据
      'week': null,
      'long_term': null,
      'quick': { shortTerm: { interval: '1h', limit: 24 } }       // 24小时1小时数据
    };
    
    return configs[analysisType] || configs['quick'];
  }

  /**
   * 构建AI分析提示词
   */
  buildAnalysisPrompt(symbol, analysisType, positionAmount = 300, positionPercentage = 2.7, isHolding = true) {
    const config = this.analysisTypes[analysisType] || this.analysisTypes['quick'];
    const coinName = this.coinNameMap[symbol] || symbol;
    const period = config.period;
    const adviceType = isHolding ? '持仓建议' : '开仓建议';
    
    // 根据分析类型构建数据描述
    let dataDescription = "";
    switch(analysisType) {
      case 'four_hour':
        dataDescription = "（我已为你提供了90天的日K线数据和4小时的1分钟K线数据，请基于这些数据进行分析）";
        break;
      case 'one_day':
        dataDescription = "（我已为你提供了90天的日K线数据和24小时的5分钟K线数据，请基于这些数据进行分析）";
        break;
      case 'three_day':
        dataDescription = "（我已为你提供了90天的日K线数据和3天的15分钟K线数据，请基于这些数据进行分析）";
        break;
      case 'week':
      case 'long_term':
        dataDescription = "（我已为你提供了90天的真实日K线数据，请基于这些数据进行分析）";
        break;
      default:
        dataDescription = "（我已为你提供了90天的日K线数据和24小时的1小时K线数据，请基于这些数据进行分析）";
    }
    
    return `请对${coinName}(${symbol})进行${period}分析${dataDescription}，我${isHolding ? '持有' : '计划买入'}${positionAmount}个，占比${positionPercentage}%。给出${period}的${adviceType}。注意：我已通过klines字段提供了90天的K线数据，以及macd、rsi和kdj参数，且包含历年同期前后日1分钟k线的数据，请参考数据进行市场分析，预测多空，分析情绪并给出明确的回答是看多还是看空并给出2小时和12小时涨跌概率和目标点位。`;
  }

  /**
   * 处理分析数据
   */
  processAnalysisData(klineResult, tickerData, symbol, orderbookData = null, btcAuxiliaryData = null) {
    const dailyKlines = klineResult.dailyKlines || [];
    const shortTermKlines = klineResult.shortTermKlines || [];

    // 确保K线数据是正确的数组格式 [timestamp, open, high, low, close, volume]
    const processedDailyKlines = dailyKlines.map(item => {
      if (Array.isArray(item)) {
        return item; // 已经是数组格式
      } else if (item && typeof item === 'object') {
        // 从对象格式转换为数组格式
        return [
          item.timestamp || item.time || Date.now(),
          parseFloat(item.open || 0),
          parseFloat(item.high || 0),
          parseFloat(item.low || 0),
          parseFloat(item.close || 0),
          parseFloat(item.volume || 0)
        ];
      }
      return [Date.now(), 0, 0, 0, 0, 0]; // 默认值
    });

    const processedShortTermKlines = shortTermKlines.map(item => {
      if (Array.isArray(item)) {
        return item; // 已经是数组格式
      } else if (item && typeof item === 'object') {
        // 从对象格式转换为数组格式
        return [
          item.timestamp || item.time || Date.now(),
          parseFloat(item.open || 0),
          parseFloat(item.high || 0),
          parseFloat(item.low || 0),
          parseFloat(item.close || 0),
          parseFloat(item.volume || 0)
        ];
      }
      return [Date.now(), 0, 0, 0, 0, 0]; // 默认值
    });

    // 格式化日K线数据（用于显示）
    const formatted_daily_klines = processedDailyKlines.map(item => ({
      "date": new Date(item[0]).toISOString().split('T')[0],
      "open": String(item[1] || "0"),
      "high": String(item[2] || "0"),
      "low": String(item[3] || "0"),
      "close": String(item[4] || "0")
    }));

    // 格式化短期K线数据（用于显示）
    const formatted_short_term_klines = processedShortTermKlines.map(item => ({
      "date": new Date(item[0]).toISOString(),
      "open": String(item[1] || "0"),
      "high": String(item[2] || "0"),
      "low": String(item[3] || "0"),
      "close": String(item[4] || "0")
    }));
    
    // 格式化实时价格数据
    const real_time_price = {
      "symbol": String(tickerData?.symbol || `${symbol}USDT`),
      "price": String(tickerData?.lastPrice || "0"),
      "change_percent": String(tickerData?.priceChangePercent || "0"),
      "volume": String(tickerData?.volume || "0"),
      "time": String(tickerData?.timestamp || new Date().toISOString())
    };

    // 处理BTC辅助数据格式化
    let btcFormatted = null;
    if (btcAuxiliaryData && btcAuxiliaryData.klineData) {
      const btcDailyKlines = btcAuxiliaryData.klineData.dailyKlines || [];
      const btcShortTermKlines = btcAuxiliaryData.klineData.shortTermKlines || [];
      
      btcFormatted = {
        real_time_price: {
          "symbol": String(btcAuxiliaryData.tickerData?.symbol || "BTCUSDT"),
          "price": String(btcAuxiliaryData.tickerData?.lastPrice || "0"),
          "change_percent": String(btcAuxiliaryData.tickerData?.priceChangePercent || "0"),
          "volume": String(btcAuxiliaryData.tickerData?.volume || "0"),
          "time": String(btcAuxiliaryData.tickerData?.timestamp || new Date().toISOString())
        },
        formatted_daily_klines: btcDailyKlines.slice(-90).map(item => ({
          "date": new Date(Array.isArray(item) ? item[0] : item.timestamp).toISOString().split('T')[0],
          "open": String(Array.isArray(item) ? item[1] : item.open || "0"),
          "high": String(Array.isArray(item) ? item[2] : item.high || "0"),
          "low": String(Array.isArray(item) ? item[3] : item.low || "0"),
          "close": String(Array.isArray(item) ? item[4] : item.close || "0")
        })),
        formatted_short_term_klines: btcShortTermKlines.slice(-1000).map(item => ({
          "date": new Date(Array.isArray(item) ? item[0] : item.timestamp).toISOString(),
          "open": String(Array.isArray(item) ? item[1] : item.open || "0"),
          "high": String(Array.isArray(item) ? item[2] : item.high || "0"),
          "low": String(Array.isArray(item) ? item[3] : item.low || "0"),
          "close": String(Array.isArray(item) ? item[4] : item.close || "0")
        }))
      };
    }
    
    // 构建增强的加密货币上下文
    let crypto_context = `以下是最近的加密货币价格报告, 请在回答中参考这些数据:
实时价格: ${JSON.stringify(real_time_price, null, 2)}
近90天K线数据: ${JSON.stringify(formatted_daily_klines, null, 2)}`;

    // 添加订单簿数据
    if (orderbookData) {
      crypto_context += `\n${symbol}订单簿数据(20档深度): ${JSON.stringify(orderbookData, null, 2)}`;
    }

    // 添加BTC辅助数据（用于非BTC币种）
    if (btcFormatted && symbol !== 'BTC') {
      crypto_context += `\nBTC辅助数据(作为市场参考): ${JSON.stringify(btcFormatted, null, 2)}`;
      if (btcAuxiliaryData?.orderbookData) {
        crypto_context += `\nBTC订单簿数据(20档深度): ${JSON.stringify(btcAuxiliaryData.orderbookData, null, 2)}`;
      }
    }

    crypto_context += '\n请基于以上数据进行分析。';
    
    return {
      dailyKlines: processedDailyKlines,
      shortTermKlines: processedShortTermKlines,
      ticker: tickerData,
      orderbook: orderbookData,
      btc_auxiliary_data: btcFormatted,
      formatted_daily_klines,
      formatted_short_term_klines,
      real_time_price,
      klines: processedDailyKlines,
      formatted_klines: formatted_daily_klines,
      crypto_context
    };
  }

  /**
   * 发起AI分析请求（增强版）
   */
  async analyzeWithAI(symbol, analysisType = 'quick', options = {}) {
    const {
      positionAmount = 300,
      positionPercentage = 2.7,
      isHolding = true,
      onProgress = null,
      enableFallback = true
    } = options;

    let cryptoData = null;
    let debugInfo = null;

    try {
      console.log('🚀 开始AI分析:', { symbol, analysisType, options });

      // 1. 获取K线数据
      if (onProgress) onProgress('获取K线数据...');
      let klineResult;
      try {
        klineResult = await this.fetchKlineData(symbol, analysisType);
        const validation = AIAnalysisHelper.validateKlineData(klineResult.dailyKlines);
        if (!validation.valid) {
          throw new Error(`K线数据验证失败: ${validation.error}`);
        }
      } catch (error) {
        console.warn('⚠️ K线数据获取失败:', error.message);
        if (enableFallback) {
          const fallbackData = AIAnalysisHelper.createFallbackData(symbol, analysisType);
          klineResult = {
            dailyKlines: fallbackData.dailyKlines,
            shortTermKlines: fallbackData.shortTermKlines
          };
          console.log('🔄 使用降级K线数据');
        } else {
          throw error;
        }
      }
      
      // 2. 获取实时价格数据
      if (onProgress) onProgress('获取实时价格数据...');
      let tickerData;
      try {
        tickerData = await this.fetchTickerData(symbol);
        const validation = AIAnalysisHelper.validateTickerData(tickerData);
        if (!validation.valid) {
          throw new Error(`价格数据验证失败: ${validation.error}`);
        }
      } catch (error) {
        console.warn('⚠️ 价格数据获取失败:', error.message);
        if (enableFallback) {
          const fallbackData = AIAnalysisHelper.createFallbackData(symbol, analysisType);
          tickerData = fallbackData.ticker;
          console.log('🔄 使用降级价格数据');
        } else {
          throw error;
        }
      }
      
      // 3. 获取订单簿数据（可选）
      if (onProgress) onProgress('获取订单簿数据...');
      let orderbookData = null;
      try {
        orderbookData = await this.fetchOrderbookData(symbol, 20);
        if (orderbookData) {
          const validation = AIAnalysisHelper.validateOrderbookData(orderbookData);
          if (!validation.valid) {
            console.warn('⚠️ 订单簿数据验证失败:', validation.error);
            orderbookData = null;
          }
        }
      } catch (error) {
        console.warn('⚠️ 订单簿数据获取失败:', error.message);
        // 订单簿数据失败不影响主流程
      }
      
      // 4. 获取BTC辅助数据（如果不是BTC）
      let btcAuxiliaryData = null;
      if (symbol !== 'BTC') {
        if (onProgress) onProgress('获取BTC市场参考数据...');
        try {
          btcAuxiliaryData = await this.fetchBTCAuxiliaryData(analysisType);
        } catch (error) {
          console.warn('⚠️ BTC辅助数据获取失败:', error.message);
          // BTC辅助数据失败不影响主流程
        }
      }
      
      // 5. 处理数据
      if (onProgress) onProgress('处理分析数据...');
      cryptoData = this.processAnalysisData(
        klineResult, 
        tickerData, 
        symbol, 
        orderbookData, 
        btcAuxiliaryData
      );

      // 6. 数据完整性检查
      const integrityCheck = AIAnalysisHelper.checkDataIntegrity(cryptoData, symbol, analysisType);
      console.log('📊 数据完整性检查:', integrityCheck);
      
      if (integrityCheck.hasIssues) {
        console.warn('⚠️ 数据完整性问题:', integrityCheck.issues);
        if (!enableFallback) {
          throw new Error(`数据完整性检查失败: ${integrityCheck.issues.join(', ')}`);
        }
      }

      if (integrityCheck.hasWarnings) {
        console.warn('⚠️ 数据完整性警告:', integrityCheck.warnings);
      }
      
      // 7. 构建分析提示词
      const message = this.buildAnalysisPrompt(
        symbol, 
        analysisType, 
        positionAmount, 
        positionPercentage, 
        isHolding
      );
      
      // 8. 发起AI分析请求
      if (onProgress) onProgress('开始AI分析...');
      
      // 获取当前日期，用于历史同期分析
      const today = new Date();
      const target_date = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
      
      const requestData = {
        messages: [{ role: "user", content: message }],
        cryptoData: cryptoData,
        period: this.analysisTypes[analysisType]?.period || "未来24小时",
        stream: true,
        target_date: target_date,
        max_tokens: 32768
      };
      
      // 生成数据摘要用于调试
      const dataSummary = AIAnalysisHelper.generateDataSummary(cryptoData);
      console.log('📋 数据摘要:', dataSummary);
      
      console.log('🚀 发送AI分析请求:', {
        symbol,
        analysisType,
        hasOrderbook: !!orderbookData,
        hasBTCData: !!btcAuxiliaryData,
        dataIntegrityScore: integrityCheck.score,
        dataKeys: Object.keys(cryptoData)
      });
      
      return await this.streamChat(requestData, onProgress);
      
    } catch (error) {
      console.error('❌ AI分析失败:', error);
      
      // 收集调试信息
      debugInfo = AIAnalysisHelper.collectDebugInfo(symbol, analysisType, cryptoData, error);
      console.log('🔍 调试信息:', debugInfo);
      
      // 格式化错误信息
      const formattedError = AIAnalysisHelper.formatError(error, { symbol, analysisType });
      
      // 抛出增强的错误信息
      const enhancedError = new Error(formattedError.message);
      enhancedError.type = formattedError.type;
      enhancedError.title = formattedError.title;
      enhancedError.suggestion = formattedError.suggestion;
      enhancedError.debugInfo = debugInfo;
      
      throw enhancedError;
    }
  }

  /**
   * 流式聊天请求
   */
  async streamChat(requestData, onProgress = null) {
    try {
      console.log('📡 发起流式AI分析请求...');
      
      const response = await fetch(`${this.baseURL}${this.aiEndpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream'
        },
        body: JSON.stringify(requestData)
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('AI分析请求失败:', response.status, errorText);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      let result = '';
      
      console.log('📥 开始接收流式响应...');
      
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          console.log('✅ 流式响应接收完成');
          break;
        }
        
        buffer += decoder.decode(value, { stream: true });
        
        // 处理SSE数据 - 支持多种格式
        const lines = buffer.split('\n');
        buffer = '';
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const jsonStr = line.substring(6).trim();
            
            if (jsonStr === '[DONE]') {
              console.log('🏁 收到结束标记');
              continue;
            }
            
            if (jsonStr) {
              try {
                const chunk = JSON.parse(jsonStr);
                
                // 支持多种响应格式
                let content = '';
                if (chunk.choices && chunk.choices[0]) {
                  // OpenAI格式
                  if (chunk.choices[0].delta?.content) {
                    content = chunk.choices[0].delta.content;
                  } else if (chunk.choices[0].message?.content) {
                    content = chunk.choices[0].message.content;
                  }
                } else if (chunk.content) {
                  // 直接内容格式
                  content = chunk.content;
                }
                
                if (content) {
                  result += content;
                  
                  if (onProgress) {
                    onProgress(result, 'streaming');
                  }
                }
              } catch (e) {
                console.warn('解析SSE数据失败:', jsonStr, e);
                // 如果不是JSON，可能是纯文本内容
                if (jsonStr.length > 0 && !jsonStr.startsWith('{')) {
                  result += jsonStr;
                  if (onProgress) {
                    onProgress(result, 'streaming');
                  }
                }
              }
            }
          } else if (line.trim() && !line.startsWith(':')) {
            // 处理非SSE格式的行
            buffer += line + '\n';
          }
        }
      }
      
      if (onProgress) {
        onProgress(result, 'complete');
      }
      
      console.log('📊 AI分析完成，总长度:', result.length);
      return result;
      
    } catch (error) {
      console.error('流式聊天请求失败:', error);
      
      // 提供更详细的错误信息
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        throw new Error('网络连接失败，请检查网络连接或服务器状态');
      } else if (error.message.includes('HTTP 500')) {
        throw new Error('服务器内部错误，请稍后重试');
      } else if (error.message.includes('HTTP 429')) {
        throw new Error('请求过于频繁，请稍后重试');
      }
      
      throw error;
    }
  }

  // 获取最新的AI预测记录
  async getLatestPredictions(symbol = 'BTCUSDT', limit = 5) {
    try {
      // 如果有缓存且不超过5分钟，直接返回缓存
      const now = Date.now();
      const cacheKey = `${symbol}_${limit}`;
      
      if (
        this.cache.predictions[cacheKey] && 
        this.cache.lastFetch && 
        now - this.cache.lastFetch < 5 * 60 * 1000
      ) {
        console.log('🔍 [AIAnalysisService] 使用缓存的预测数据');
        return this.cache.predictions[cacheKey];
      }

      console.log(`🔍 [AIAnalysisService] 获取${symbol}最新${limit}条预测记录`);
      const response = await apiClient.get(`${this.baseUrl}/predictions/`, {
        params: { symbol, limit }
      });

      // 更新缓存
      this.cache.predictions[cacheKey] = response.data;
      this.cache.lastFetch = now;

      return response.data;
    } catch (error) {
      console.error('❌ [AIAnalysisService] 获取预测记录失败:', error);
      throw error;
    }
  }

  // 获取指定币种的最新预测
  async getLatestPrediction(symbol = 'BTCUSDT') {
    try {
      const predictions = await this.getLatestPredictions(symbol, 1);
      return predictions.length > 0 ? predictions[0] : null;
    } catch (error) {
      console.error(`❌ [AIAnalysisService] 获取${symbol}最新预测失败:`, error);
      throw error;
    }
  }

  // 获取多个币种的最新预测
  async getMultiSymbolPredictions(symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT']) {
    try {
      const results = {};
      
      // 并行请求多个币种的预测
      await Promise.all(
        symbols.map(async (symbol) => {
          try {
            const prediction = await this.getLatestPrediction(symbol);
            if (prediction) {
              // 提取币种代码（去掉USDT后缀）
              const shortSymbol = symbol.replace('USDT', '');
              results[shortSymbol] = prediction;
            }
          } catch (error) {
            console.error(`❌ [AIAnalysisService] 获取${symbol}预测失败:`, error);
          }
        })
      );

      return results;
    } catch (error) {
      console.error('❌ [AIAnalysisService] 获取多币种预测失败:', error);
      throw error;
    }
  }

  // 清除缓存
  clearCache() {
    this.cache = {
      predictions: {},
      lastFetch: null
    };
    console.log('🗑️ [AIAnalysisService] 清除预测缓存');
  }

  // 解析预测分布数据
  parsePredictionDistribution(prediction) {
    if (!prediction || !prediction.prediction_distribution) {
      console.log('❌ 没有预测分布数据或数据为空');
      return {};
    }

    console.log('🔍 解析前的预测分布数据:', prediction.prediction_distribution);
    console.log('🔍 预测分布数据类型:', typeof prediction.prediction_distribution);

    try {
      let distributionData = {};
      
      // 如果是字符串，尝试解析JSON
      if (typeof prediction.prediction_distribution === 'string') {
        console.log('📝 处理字符串格式的预测分布');
        
        // 尝试直接解析为对象
        if (prediction.prediction_distribution.includes('【多空预测】')) {
          console.log('📝 检测到原始AI响应格式，尝试直接提取');
          
          // 从原始AI响应中提取【多空预测】部分
          const predictionSection = prediction.prediction_distribution.split('【多空预测】')[1]?.split('【')[0] || '';
          console.log('📄 提取的多空预测部分:', predictionSection);
          
          // 使用增强的正则表达式提取键值对
          const regex = /(看涨|非常看涨|中性|看跌|非常看跌):\s*(\d+)%?/g;
          let match;
          
          while ((match = regex.exec(predictionSection)) !== null) {
            const key = match[1];
            const value = parseInt(match[2], 10);
            console.log(`📋 提取键值对: ${key} = ${value}`);
            if (!isNaN(value)) {
              distributionData[key] = value;
            }
          }
          
          console.log('📊 从原始响应提取的预测分布:', distributionData);
        } else {
          // 尝试JSON解析
          try {
            distributionData = JSON.parse(prediction.prediction_distribution);
            console.log('✅ JSON解析成功:', distributionData);
          } catch (jsonError) {
            console.log('⚠️ JSON解析失败:', jsonError.message);
            console.log('📝 尝试使用通用正则表达式解析');
            
            // 使用正则表达式从文本中提取键值对 - 更宽松的匹配
            const regex = /(看涨|非常看涨|中性|看跌|非常看跌)[：:]\s*(\d+)%?/g;
            let match;
            
            while ((match = regex.exec(prediction.prediction_distribution)) !== null) {
              const key = match[1];
              // 确保值是数字，并去除百分比符号
              const value = parseInt(match[2], 10);
              console.log(`📋 提取键值对: ${key} = ${value}`);
              if (!isNaN(value)) {
                distributionData[key] = value;
              }
            }
          }
        }
      } else if (typeof prediction.prediction_distribution === 'object') {
        console.log('📝 处理对象格式的预测分布');
        // 如果已经是对象，确保值是数字
        Object.entries(prediction.prediction_distribution).forEach(([key, value]) => {
          console.log(`📋 处理键值对: ${key} = ${value} (${typeof value})`);
          // 处理可能的字符串值（例如："10%"）
          if (typeof value === 'string') {
            const numValue = parseInt(value.replace('%', ''), 10);
            distributionData[key] = isNaN(numValue) ? 0 : numValue;
            console.log(`📊 转换字符串值: ${value} -> ${distributionData[key]}`);
          } else {
            distributionData[key] = value;
          }
        });
      }
      
      console.log('🔍 解析后的预测分布数据:', distributionData);
      
      // 如果没有提取到任何数据，尝试从raw_ai_response中提取
      if (Object.keys(distributionData).length === 0 && prediction.raw_ai_response) {
        console.log('📝 从raw_ai_response中尝试提取预测分布');
        
        const rawResponse = prediction.raw_ai_response;
        if (rawResponse.includes('【多空预测】')) {
          const predictionSection = rawResponse.split('【多空预测】')[1]?.split('【')[0] || '';
          console.log('📄 从原始响应提取的多空预测部分:', predictionSection);
          
          const regex = /(看涨|非常看涨|中性|看跌|非常看跌):\s*(\d+)%?/g;
          let match;
          
          while ((match = regex.exec(predictionSection)) !== null) {
            const key = match[1];
            const value = parseInt(match[2], 10);
            console.log(`📋 从raw_ai_response提取键值对: ${key} = ${value}`);
            if (!isNaN(value)) {
              distributionData[key] = value;
            }
          }
        }
      }
      
      console.log('🔍 最终解析的预测分布数据:', distributionData);
      return distributionData;
    } catch (error) {
      console.error('❌ [AIAnalysisService] 解析预测分布失败:', error);
      return {};
    }
  }

  // 解析市场情绪数据
  parseMarketSentiment(prediction) {
    if (!prediction || !prediction.market_sentiment) {
      console.log('❌ 没有市场情绪数据或数据为空');
      return {};
    }

    console.log('🔍 解析前的市场情绪数据:', prediction.market_sentiment);
    console.log('🔍 市场情绪数据类型:', typeof prediction.market_sentiment);

    try {
      let sentimentData = {};
      
      // 如果是字符串，尝试解析JSON
      if (typeof prediction.market_sentiment === 'string') {
        console.log('📝 处理字符串格式的市场情绪');
        
        // 尝试直接解析为对象
        if (prediction.market_sentiment.includes('【市场情绪】')) {
          console.log('📝 检测到原始AI响应格式，尝试直接提取');
          
          // 从原始AI响应中提取【市场情绪】部分
          const sentimentSection = prediction.market_sentiment.split('【市场情绪】')[1]?.split('【')[0] || '';
          console.log('📄 提取的市场情绪部分:', sentimentSection);
          
          // 使用增强的正则表达式提取键值对
          const regex = /([A-Z]+) 多头占比:\s*(\d+)%?/g;
          let match;
          
          while ((match = regex.exec(sentimentSection)) !== null) {
            const key = `${match[1]}_多头占比`;
            const value = parseInt(match[2], 10);
            console.log(`📋 提取键值对: ${key} = ${value}`);
            if (!isNaN(value)) {
              sentimentData[key] = value;
            }
          }
          
          console.log('📊 从原始响应提取的市场情绪:', sentimentData);
        } else {
          // 尝试JSON解析
          try {
            sentimentData = JSON.parse(prediction.market_sentiment);
            console.log('✅ JSON解析成功:', sentimentData);
          } catch (jsonError) {
            console.log('⚠️ JSON解析失败:', jsonError.message);
            console.log('📝 尝试使用通用正则表达式解析');
            
            // 使用正则表达式从文本中提取键值对 - 更宽松的匹配
            const regex = /([A-Z]+) 多头占比[：:]\s*(\d+)%?/g;
            let match;
            
            while ((match = regex.exec(prediction.market_sentiment)) !== null) {
              const key = `${match[1]}_多头占比`;
              // 确保值是数字，并去除百分比符号
              const value = parseInt(match[2], 10);
              console.log(`📋 提取键值对: ${key} = ${value}`);
              if (!isNaN(value)) {
                sentimentData[key] = value;
              }
            }
          }
        }
      } else if (typeof prediction.market_sentiment === 'object') {
        console.log('📝 处理对象格式的市场情绪');
        // 如果已经是对象，确保值是数字
        Object.entries(prediction.market_sentiment).forEach(([key, value]) => {
          console.log(`📋 处理键值对: ${key} = ${value} (${typeof value})`);
          // 处理可能的字符串值（例如："50%"）
          if (typeof value === 'string') {
            const numValue = parseInt(value.replace('%', ''), 10);
            sentimentData[key] = isNaN(numValue) ? 0 : numValue;
            console.log(`📊 转换字符串值: ${value} -> ${sentimentData[key]}`);
          } else {
            sentimentData[key] = value;
          }
        });
      }
      
      console.log('🔍 解析后的市场情绪数据:', sentimentData);
      
      // 如果没有提取到任何数据，尝试从raw_ai_response中提取
      if (Object.keys(sentimentData).length === 0 && prediction.raw_ai_response) {
        console.log('📝 从raw_ai_response中尝试提取市场情绪');
        
        const rawResponse = prediction.raw_ai_response;
        if (rawResponse.includes('【市场情绪】')) {
          const sentimentSection = rawResponse.split('【市场情绪】')[1]?.split('【')[0] || '';
          console.log('📄 从原始响应提取的市场情绪部分:', sentimentSection);
          
          const regex = /([A-Z]+) 多头占比:\s*(\d+)%?/g;
          let match;
          
          while ((match = regex.exec(sentimentSection)) !== null) {
            const key = `${match[1]}_多头占比`;
            const value = parseInt(match[2], 10);
            console.log(`📋 从raw_ai_response提取键值对: ${key} = ${value}`);
            if (!isNaN(value)) {
              sentimentData[key] = value;
            }
          }
        }
      }
      
      console.log('🔍 最终解析的市场情绪数据:', sentimentData);
      return sentimentData;
    } catch (error) {
      console.error('❌ [AIAnalysisService] 解析市场情绪失败:', error);
      return {};
    }
  }
}

// 创建全局实例
const aiAnalysisService = new AIAnalysisService();

export default aiAnalysisService;
