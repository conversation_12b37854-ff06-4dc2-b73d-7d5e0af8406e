// 多货币多周期数据管理器
import streamCryptoDataService from './StreamDataService.js'

class MultiCryptoDataManager {
  constructor() {
    this.cache = new Map()
    this.loadingPromises = new Map() // 防止重复加载
    this.supportedSymbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'DOGEUSDT', 'ADAUSDT', 'DOTUSDT', 'LINKUSDT', 'LTCUSDT', 'XRPUSDT']
    this.supportedTimeframes = ['1m', '15m', '1h', '4h', '1d', '1mo']
    
    console.log('🏗️ 多货币数据管理器初始化完成')
    console.log(`📊 支持货币: ${this.supportedSymbols.join(', ')}`)
    console.log(`⏰ 支持周期: ${this.supportedTimeframes.join(', ')}`)
  }

  // 🎯 统一数据加载接口
  async loadCryptoData(symbol = 'BTCUSDT', timeframe = '1m', year = '2024') {
    // 验证参数
    if (!this.supportedSymbols.includes(symbol)) {
      console.warn(`⚠️ 不支持的货币: ${symbol}`)
      return []
    }

    if (!this.supportedTimeframes.includes(timeframe)) {
      console.warn(`⚠️ 不支持的时间周期: ${timeframe}`)
      return []
    }

    const cacheKey = `${symbol}_${timeframe}_${year}`
    
    // 检查缓存
    if (this.cache.has(cacheKey)) {
      console.log(`📦 从缓存加载: ${cacheKey}`)
      return this.cache.get(cacheKey)
    }

    // 检查是否正在加载
    if (this.loadingPromises.has(cacheKey)) {
      console.log(`⏳ 等待加载完成: ${cacheKey}`)
      return await this.loadingPromises.get(cacheKey)
    }

    // 开始加载
    console.log(`📥 开始加载数据: ${cacheKey}`)
    const loadingPromise = this.loadDataInternal(symbol, timeframe, year)
    this.loadingPromises.set(cacheKey, loadingPromise)

    try {
      const data = await loadingPromise
      
      // 缓存结果
      if (data && data.length > 0) {
        this.cache.set(cacheKey, data)
        console.log(`✅ 数据加载完成: ${cacheKey} (${data.length} 条记录)`)
      } else {
        console.warn(`⚠️ 数据为空: ${cacheKey}`)
      }
      
      return data
      
    } finally {
      // 清理加载状态
      this.loadingPromises.delete(cacheKey)
    }
  }

  // 内部数据加载逻辑
  async loadDataInternal(symbol, timeframe, year) {
    try {
      // 2025年数据使用智能合并
      if (year === '2025') {
        return await streamCryptoDataService.loadMerged2025Data(symbol, timeframe)
      }
      
      // 其他年份使用标准加载
      return await streamCryptoDataService.loadYearDataWithRetry(symbol, timeframe, year)
      
    } catch (error) {
      console.error(`❌ 数据加载失败: ${symbol}/${timeframe}/${year} - ${error.message}`)
      return []
    }
  }

  // 🎯 批量加载多个货币的数据
  async loadMultipleSymbols(symbols, timeframe = '1m', year = '2024') {
    console.log(`🔄 批量加载数据: ${symbols.join(', ')} / ${timeframe} / ${year}`)
    
    const loadPromises = symbols.map(symbol => 
      this.loadCryptoData(symbol, timeframe, year)
    )
    
    const results = await Promise.allSettled(loadPromises)
    
    const successData = {}
    const failedSymbols = []
    
    results.forEach((result, index) => {
      const symbol = symbols[index]
      if (result.status === 'fulfilled' && result.value.length > 0) {
        successData[symbol] = result.value
      } else {
        failedSymbols.push(symbol)
      }
    })
    
    console.log(`✅ 批量加载完成: ${Object.keys(successData).length}/${symbols.length} 成功`)
    if (failedSymbols.length > 0) {
      console.warn(`⚠️ 加载失败: ${failedSymbols.join(', ')}`)
    }
    
    return { successData, failedSymbols }
  }

  // 🎯 获取货币的所有时间周期数据
  async loadAllTimeframes(symbol, year = '2024') {
    console.log(`🔄 加载 ${symbol} 所有时间周期数据 (${year}年)`)
    
    const timeframes = this.supportedTimeframes
    const results = {}
    
    for (const timeframe of timeframes) {
      try {
        const data = await this.loadCryptoData(symbol, timeframe, year)
        if (data && data.length > 0) {
          results[timeframe] = data
        }
      } catch (error) {
        console.warn(`⚠️ ${symbol}/${timeframe} 加载失败: ${error.message}`)
      }
    }
    
    console.log(`✅ ${symbol} 时间周期加载完成: ${Object.keys(results).join(', ')}`)
    return results
  }

  // 🎯 获取实时数据状态
  async getRealtimeDataStatus() {
    try {
      const response = await fetch('http://localhost:8000/api/crypto/realtime/status/', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      })
      
      const result = await response.json()
      return result
      
    } catch (error) {
      console.warn('⚠️ 无法获取实时数据状态:', error)
      return { success: false, error: error.message }
    }
  }

  // 🎯 触发数据刷新
  async refreshData(symbol, timeframe, year = '2025') {
    const cacheKey = `${symbol}_${timeframe}_${year}`
    
    // 清除缓存
    this.cache.delete(cacheKey)
    streamCryptoDataService.cache.delete(cacheKey)
    
    console.log(`🔄 刷新数据: ${cacheKey}`)
    
    // 重新加载
    return await this.loadCryptoData(symbol, timeframe, year)
  }

  // 🎯 清除所有缓存
  clearAllCache() {
    this.cache.clear()
    streamCryptoDataService.clearCache()
    console.log('🗑️ 所有缓存已清除')
  }

  // 🎯 获取缓存状态
  getCacheStatus() {
    const status = {
      multiManagerCache: this.cache.size,
      streamServiceCache: streamCryptoDataService.cache.size,
      loadingPromises: this.loadingPromises.size,
      cacheKeys: Array.from(this.cache.keys()),
      supportedSymbols: this.supportedSymbols,
      supportedTimeframes: this.supportedTimeframes
    }
    
    console.table([
      { 指标: '多货币管理器缓存', 数量: status.multiManagerCache },
      { 指标: '流数据服务缓存', 数量: status.streamServiceCache },
      { 指标: '正在加载任务', 数量: status.loadingPromises },
      { 指标: '支持货币数', 数量: status.supportedSymbols.length },
      { 指标: '支持周期数', 数量: status.supportedTimeframes.length }
    ])
    
    return status
  }

  // 🎯 智能数据预加载
  async preloadPopularData() {
    console.log('🚀 开始预加载热门数据...')
    
    const popularSymbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT']
    const popularTimeframes = ['1m', '1h', '1d']
    const years = ['2024', '2025']
    
    const loadTasks = []
    
    for (const symbol of popularSymbols) {
      for (const timeframe of popularTimeframes) {
        for (const year of years) {
          loadTasks.push(
            this.loadCryptoData(symbol, timeframe, year)
              .catch(error => console.warn(`⚠️ 预加载失败: ${symbol}/${timeframe}/${year} - ${error.message}`))
          )
        }
      }
    }
    
    await Promise.allSettled(loadTasks)
    console.log('✅ 热门数据预加载完成')
  }
}

// 导出单例
const multiCryptoDataManager = new MultiCryptoDataManager()

// 全局调试接口
if (typeof window !== 'undefined') {
  window.multiCryptoManager = {
    // 加载数据
    load: (symbol, timeframe, year) => multiCryptoDataManager.loadCryptoData(symbol, timeframe, year),
    
    // 批量加载
    loadMultiple: (symbols, timeframe, year) => multiCryptoDataManager.loadMultipleSymbols(symbols, timeframe, year),
    
    // 加载所有周期
    loadAllTimeframes: (symbol, year) => multiCryptoDataManager.loadAllTimeframes(symbol, year),
    
    // 刷新数据
    refresh: (symbol, timeframe, year) => multiCryptoDataManager.refreshData(symbol, timeframe, year),
    
    // 查看状态
    status: () => multiCryptoDataManager.getCacheStatus(),
    
    // 实时状态
    realtimeStatus: () => multiCryptoDataManager.getRealtimeDataStatus(),
    
    // 预加载
    preload: () => multiCryptoDataManager.preloadPopularData(),
    
    // 清除缓存
    clearCache: () => multiCryptoDataManager.clearAllCache(),
    
    // 支持的货币和周期
    symbols: multiCryptoDataManager.supportedSymbols,
    timeframes: multiCryptoDataManager.supportedTimeframes
  }
  
  console.log('🎯 全局调试接口已创建: window.multiCryptoManager')
  console.log('💡 使用示例:')
  console.log('  - window.multiCryptoManager.load("ETHUSDT", "1h", "2025")')
  console.log('  - window.multiCryptoManager.loadMultiple(["BTCUSDT", "ETHUSDT"], "1m", "2025")')
  console.log('  - window.multiCryptoManager.status()')
}

export default multiCryptoDataManager