#!/usr/bin/env python3
"""
自动断点检测和修复系统
"""

import os
import sys
import time
import logging
import subprocess
from datetime import datetime, timedelta
from pathlib import Path

# Django环境设置
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'finance_project.settings')
import django
django.setup()

from ai_analysis.models import PredictionRecord, AutoPredictionTask
from django.utils import timezone

class AutoGapRepair:
    """自动断点修复器"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        
    def _setup_logger(self):
        logger = logging.getLogger('AutoRepair')
        handler = logging.FileHandler('logs/auto_repair.log')
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
        return logger
    
    def detect_critical_gaps(self) -> dict:
        """检测严重断层"""
        critical_gaps = {}
        current_time = timezone.now()
        
        # 检查每个币种的最近预测时间
        for task in AutoPredictionTask.objects.filter(is_active=True):
            symbol = task.symbol
            
            # 获取最近的预测记录
            latest_record = PredictionRecord.objects.filter(
                symbol=symbol,
                prediction_status='completed'
            ).order_by('-created_at').first()
            
            if latest_record:
                gap_minutes = (current_time - latest_record.created_at).total_seconds() / 60
                
                # 如果超过25分钟没有预测，认为是严重断层
                if gap_minutes > 25:
                    critical_gaps[symbol] = {
                        'last_prediction': latest_record.created_at,
                        'gap_minutes': gap_minutes,
                        'severity': 'critical' if gap_minutes > 60 else 'warning'
                    }
            else:
                # 没有任何预测记录
                critical_gaps[symbol] = {
                    'last_prediction': None,
                    'gap_minutes': float('inf'),
                    'severity': 'critical'
                }
        
        return critical_gaps
    
    def repair_prediction_gaps(self, critical_gaps: dict) -> dict:
        """修复预测断层"""
        repair_results = {}
        
        if not critical_gaps:
            self.logger.info("无需修复的断层")
            return repair_results
        
        self.logger.info(f"检测到 {len(critical_gaps)} 个严重断层，开始修复...")
        
        # 1. 清理卡住的任务
        stuck_count = PredictionRecord.objects.filter(
            prediction_status='processing',
            created_at__lt=timezone.now() - timedelta(hours=1)
        ).update(prediction_status='failed')
        
        if stuck_count > 0:
            self.logger.info(f"清理了 {stuck_count} 个卡住的任务")
            repair_results['stuck_cleanup'] = stuck_count
        
        # 2. 重置任务调度时间
        for symbol in critical_gaps.keys():
            try:
                task = AutoPredictionTask.objects.get(symbol=symbol)
                # 立即调度执行
                task.next_run_at = timezone.now()
                task.save()
                
                self.logger.info(f"重置 {symbol} 任务调度")
                repair_results[f'reset_{symbol}'] = True
                
            except AutoPredictionTask.DoesNotExist:
                self.logger.error(f"找不到 {symbol} 的任务配置")
                repair_results[f'reset_{symbol}'] = False
        
        # 3. 强制触发预测任务
        try:
            self.logger.info("触发强制预测任务...")
            result = subprocess.run([
                'python3', 'manage.py', 'cron_predictions', '--force'
            ], 
            cwd='/home/<USER>/qiyuai-web/finance_backend',
            capture_output=True, 
            text=True, 
            timeout=600  # 10分钟超时
            )
            
            if result.returncode == 0:
                self.logger.info("强制预测任务执行成功")
                repair_results['force_prediction'] = True
            else:
                self.logger.error(f"强制预测任务失败: {result.stderr}")
                repair_results['force_prediction'] = False
                
        except subprocess.TimeoutExpired:
            self.logger.error("强制预测任务超时")
            repair_results['force_prediction'] = False
        except Exception as e:
            self.logger.error(f"执行强制预测任务异常: {e}")
            repair_results['force_prediction'] = False
        
        return repair_results
    
    def verify_repair_success(self, original_gaps: dict) -> dict:
        """验证修复是否成功"""
        verification = {}
        
        # 等待几分钟让预测任务完成
        self.logger.info("等待预测任务完成...")
        time.sleep(120)  # 等待2分钟
        
        current_time = timezone.now()
        
        for symbol in original_gaps.keys():
            # 检查是否有新的预测记录
            new_records = PredictionRecord.objects.filter(
                symbol=symbol,
                created_at__gte=current_time - timedelta(minutes=10)
            ).count()
            
            if new_records > 0:
                verification[symbol] = {
                    'status': 'success',
                    'new_records': new_records
                }
                self.logger.info(f"{symbol} 修复成功: 新增 {new_records} 条预测")
            else:
                verification[symbol] = {
                    'status': 'failed',
                    'new_records': 0
                }
                self.logger.warning(f"{symbol} 修复失败: 无新预测记录")
        
        return verification
    
    def emergency_system_restart(self):
        """紧急系统重启"""
        self.logger.warning("执行紧急系统重启...")
        
        try:
            # 重启systemd服务
            subprocess.run(['sudo', 'systemctl', 'restart', 'crypto-collector.service'], check=True)
            self.logger.info("Systemd服务重启成功")
            
            # 清理所有锁文件
            import glob
            for lock_file in glob.glob('/tmp/*.lock'):
                try:
                    os.unlink(lock_file)
                    self.logger.info(f"清理锁文件: {lock_file}")
                except:
                    pass
            
            # 等待服务启动
            time.sleep(30)
            
            return True
            
        except Exception as e:
            self.logger.error(f"紧急重启失败: {e}")
            return False
    
    def run_full_repair(self) -> dict:
        """执行完整修复流程"""
        self.logger.info("🚀 开始自动断点修复...")
        
        repair_report = {
            'start_time': timezone.now(),
            'steps': [],
            'success': False
        }
        
        try:
            # 步骤1: 检测断层
            self.logger.info("步骤1: 检测数据断层")
            critical_gaps = self.detect_critical_gaps()
            
            repair_report['steps'].append({
                'step': 'detect_gaps',
                'result': f"检测到 {len(critical_gaps)} 个断层",
                'details': critical_gaps
            })
            
            if not critical_gaps:
                self.logger.info("无断层，修复完成")
                repair_report['success'] = True
                return repair_report
            
            # 步骤2: 执行修复
            self.logger.info("步骤2: 执行断层修复")
            repair_results = self.repair_prediction_gaps(critical_gaps)
            
            repair_report['steps'].append({
                'step': 'repair_gaps',
                'result': repair_results
            })
            
            # 步骤3: 验证修复
            self.logger.info("步骤3: 验证修复结果")
            verification = self.verify_repair_success(critical_gaps)
            
            repair_report['steps'].append({
                'step': 'verify_repair',
                'result': verification
            })
            
            # 检查修复成功率
            success_count = sum(1 for v in verification.values() if v['status'] == 'success')
            total_count = len(verification)
            success_rate = success_count / total_count if total_count > 0 else 0
            
            if success_rate >= 0.8:  # 80%以上成功
                self.logger.info(f"修复成功率: {success_rate:.1%}")
                repair_report['success'] = True
            else:
                self.logger.warning(f"修复成功率较低: {success_rate:.1%}, 执行紧急重启")
                
                # 步骤4: 紧急重启（如果需要）
                restart_success = self.emergency_system_restart()
                repair_report['steps'].append({
                    'step': 'emergency_restart',
                    'result': restart_success
                })
                
                repair_report['success'] = restart_success
            
        except Exception as e:
            self.logger.error(f"修复过程异常: {e}")
            repair_report['error'] = str(e)
            repair_report['success'] = False
        
        repair_report['end_time'] = timezone.now()
        repair_report['duration_minutes'] = (
            repair_report['end_time'] - repair_report['start_time']
        ).total_seconds() / 60
        
        self.logger.info(f"修复流程完成: {'成功' if repair_report['success'] else '失败'}")
        
        return repair_report


def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='自动断点修复系统')
    parser.add_argument('--check-only', action='store_true', help='仅检测，不修复')
    parser.add_argument('--force-repair', action='store_true', help='强制修复')
    
    args = parser.parse_args()
    
    repairer = AutoGapRepair()
    
    if args.check_only:
        print("🔍 仅检测模式...")
        gaps = repairer.detect_critical_gaps()
        
        if gaps:
            print(f"❌ 检测到 {len(gaps)} 个严重断层:")
            for symbol, info in gaps.items():
                print(f"  {symbol}: {info['gap_minutes']:.1f} 分钟前")
        else:
            print("✅ 无严重断层")
    else:
        print("🔧 自动修复模式...")
        report = repairer.run_full_repair()
        
        print(f"修复结果: {'✅ 成功' if report['success'] else '❌ 失败'}")
        print(f"耗时: {report['duration_minutes']:.1f} 分钟")
        
        if not report['success']:
            print("建议手动检查系统状态")


if __name__ == "__main__":
    main()