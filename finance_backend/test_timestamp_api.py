#!/usr/bin/env python3
"""
测试时间戳API调用
"""
import requests
import json
from datetime import datetime

def test_timestamp_api():
    """测试基于时间戳的API调用"""
    print("🔗 测试时间戳API调用...")
    
    # 测试时间戳: 2025-07-29 00:00:00 UTC
    test_timestamp = 1753718400000
    test_datetime = datetime.fromtimestamp(test_timestamp / 1000)
    
    print(f"📅 测试时间戳: {test_timestamp}")
    print(f"📅 对应时间: {test_datetime}")
    
    # 构建请求数据
    request_data = {
        "messages": [
            {"role": "user", "content": "请基于提供的时间戳数据进行技术分析，给出市场情绪判断和多空预测"}
        ],
        "target_timestamp": test_timestamp,
        "symbol": "BTCUSDT",
        "interval": "1m"
    }
    
    try:
        print("📡 发送API请求...")
        response = requests.post(
            'http://localhost:8000/api/ai/chat/',
            headers={'Content-Type': 'application/json'},
            json=request_data,
            timeout=120
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print(f"✅ API响应成功!")
                print(f"📝 AI分析结果（前500字）:")
                print(content[:500] + "..." if len(content) > 500 else content)
                return True
            else:
                print(f"❌ 响应格式异常: {result}")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"   错误内容: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ API请求超时（120秒）")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    print("🚀 时间戳API测试")
    print("=" * 40)
    
    if test_timestamp_api():
        print("\n🎉 时间戳API测试成功！")
    else:
        print("\n⚠️  时间戳API测试失败")