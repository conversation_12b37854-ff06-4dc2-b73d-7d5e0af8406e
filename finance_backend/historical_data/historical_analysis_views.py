"""
历史同期数据分析API视图
"""

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
from django.core.cache import cache
from datetime import datetime
import logging

from .historical_analysis_service import HistoricalAnalysisService

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_historical_same_period(request):
    """
    获取历史同期数据API
    
    参数:
    - target_date: 目标日期 (必需, 格式: 2024-01-15)
    - symbol: 交易对 (默认: BTCUSDT)
    - days_range: 前后天数范围 (默认: 3)
    
    示例:
    GET /api/historical/same-period?target_date=2024-01-15&symbol=BTCUSDT&days_range=3
    """
    try:
        # 获取参数
        target_date = request.GET.get('target_date')
        symbol = request.GET.get('symbol', 'BTCUSDT').upper()
        days_range = int(request.GET.get('days_range', 3))
        
        # 验证参数
        if not target_date:
            return Response({
                'error': 'target_date参数是必需的',
                'example': '2024-01-15'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 验证日期格式
        try:
            datetime.strptime(target_date, '%Y-%m-%d')
        except ValueError:
            return Response({
                'error': '日期格式错误，请使用 YYYY-MM-DD 格式',
                'example': '2024-01-15'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 验证天数范围
        if days_range < 1 or days_range > 30:
            return Response({
                'error': '天数范围必须在1-30天之间'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 缓存键
        cache_key = f"historical_same_period_{symbol}_{target_date}_{days_range}"
        
        # 尝试从缓存获取
        cached_data = cache.get(cache_key)
        if cached_data:
            logger.info(f"从缓存返回历史同期数据: {target_date}")
            return Response(cached_data)
        
        # 获取数据
        service = HistoricalAnalysisService()
        result = service.get_historical_same_period_data(target_date, symbol, days_range)
        
        if 'error' in result:
            return Response({
                'error': result['error']
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 添加元数据
        result['generated_at'] = timezone.now().isoformat()
        result['cache_ttl'] = 3600  # 1小时缓存
        
        # 缓存数据
        cache.set(cache_key, result, 3600)
        
        logger.info(f"返回历史同期数据: {target_date}, {result['total_years']} 个年份")
        
        return Response(result)
        
    except Exception as e:
        logger.error(f"获取历史同期数据失败: {e}")
        return Response({
            'error': '服务器内部错误'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def calculate_period_indicators(request):
    """
    计算指定时间段技术指标API
    
    参数:
    - target_date: 目标日期 (必需)
    - symbol: 交易对 (默认: BTCUSDT)
    - days_range: 前后天数范围 (默认: 3)
    - indicators: 需要的指标 (默认: rsi,kdj,macd)
    
    示例:
    GET /api/historical/period-indicators?target_date=2024-01-15&indicators=rsi,macd
    """
    try:
        # 获取参数
        target_date = request.GET.get('target_date')
        symbol = request.GET.get('symbol', 'BTCUSDT').upper()
        days_range = int(request.GET.get('days_range', 3))
        indicators_param = request.GET.get('indicators', 'rsi,kdj,macd')
        
        # 解析指标列表
        indicators = [i.strip().lower() for i in indicators_param.split(',')]
        valid_indicators = {'rsi', 'kdj', 'macd', 'stoch'}
        indicators = [i for i in indicators if i in valid_indicators]
        
        if not indicators:
            indicators = ['rsi', 'kdj', 'macd']  # 默认指标
        
        # 验证参数
        if not target_date:
            return Response({
                'error': 'target_date参数是必需的'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 缓存键
        cache_key = f"period_indicators_{symbol}_{target_date}_{days_range}_{'-'.join(indicators)}"
        
        # 尝试从缓存获取
        cached_data = cache.get(cache_key)
        if cached_data:
            return Response(cached_data)
        
        # 计算指标
        service = HistoricalAnalysisService()
        result = service.calculate_period_indicators(
            target_date, symbol, days_range, indicators
        )
        
        if 'error' in result:
            return Response({
                'error': result['error']
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 添加元数据
        result['generated_at'] = timezone.now().isoformat()
        result['requested_indicators'] = indicators
        
        # 缓存数据
        cache.set(cache_key, result, 1800)  # 30分钟缓存
        
        logger.info(f"计算时间段技术指标: {target_date}, 指标: {indicators}")
        
        return Response(result)
        
    except Exception as e:
        logger.error(f"计算时间段技术指标失败: {e}")
        return Response({
            'error': '服务器内部错误'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_comparative_analysis(request):
    """
    获取历史同期对比分析API
    
    结合历史同期数据和技术指标进行综合分析
    
    参数:
    - target_date: 目标日期 (必需)
    - symbol: 交易对 (默认: BTCUSDT)
    - days_range: 前后天数范围 (默认: 3)
    
    示例:
    GET /api/historical/comparative-analysis?target_date=2024-01-15&symbol=BTCUSDT
    """
    try:
        # 获取参数
        target_date = request.GET.get('target_date')
        symbol = request.GET.get('symbol', 'BTCUSDT').upper()
        days_range = int(request.GET.get('days_range', 3))
        
        # 验证参数
        if not target_date:
            return Response({
                'error': 'target_date参数是必需的'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 缓存键
        cache_key = f"comparative_analysis_{symbol}_{target_date}_{days_range}"
        
        # 尝试从缓存获取
        cached_data = cache.get(cache_key)
        if cached_data:
            return Response(cached_data)
        
        # 执行对比分析
        service = HistoricalAnalysisService()
        result = service.get_comparative_analysis(target_date, symbol, days_range)
        
        if 'error' in result:
            return Response({
                'error': result['error']
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 添加元数据
        result['generated_at'] = timezone.now().isoformat()
        result['analysis_type'] = 'comparative_historical'
        
        # 缓存数据 (较长缓存时间，因为历史数据不会变化)
        cache.set(cache_key, result, 7200)  # 2小时缓存
        
        logger.info(f"完成历史同期对比分析: {target_date}")
        
        return Response(result)
        
    except Exception as e:
        logger.error(f"历史同期对比分析失败: {e}")
        return Response({
            'error': '服务器内部错误'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def get_analysis_capabilities(request):
    """
    获取分析功能说明API
    
    返回可用的分析功能和参数说明
    """
    return Response({
        'capabilities': {
            'historical_same_period': {
                'description': '获取历史同期数据（目标时间前后N天的历史各年数据）',
                'endpoint': '/api/historical/same-period',
                'parameters': {
                    'target_date': '目标日期 (YYYY-MM-DD)',
                    'symbol': '交易对符号 (默认: BTCUSDT)',
                    'days_range': '前后天数范围 (1-30, 默认: 3)'
                },
                'example': '/api/historical/same-period?target_date=2024-01-15&symbol=BTCUSDT&days_range=3'
            },
            'period_indicators': {
                'description': '计算指定时间段的技术指标',
                'endpoint': '/api/historical/period-indicators',
                'parameters': {
                    'target_date': '目标日期 (YYYY-MM-DD)',
                    'symbol': '交易对符号 (默认: BTCUSDT)',
                    'days_range': '前后天数范围 (1-30, 默认: 3)',
                    'indicators': '指标列表 (rsi,kdj,macd，默认: 全部)'
                },
                'available_indicators': {
                    'rsi': 'Relative Strength Index (相对强弱指数)',
                    'kdj': 'KDJ随机指标',
                    'macd': 'MACD指数平滑移动平均线'
                },
                'example': '/api/historical/period-indicators?target_date=2024-01-15&indicators=rsi,macd'
            },
            'comparative_analysis': {
                'description': '历史同期综合对比分析',
                'endpoint': '/api/historical/comparative-analysis',
                'parameters': {
                    'target_date': '目标日期 (YYYY-MM-DD)',
                    'symbol': '交易对符号 (默认: BTCUSDT)',
                    'days_range': '前后天数范围 (1-30, 默认: 3)'
                },
                'example': '/api/historical/comparative-analysis?target_date=2024-01-15'
            }
        },
        'supported_symbols': ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'DOGEUSDT', 'XRPUSDT'],
        'data_range': '2017-2024',
        'intervals': ['1m', '15m', '1h', '1d'],
        'generated_at': timezone.now().isoformat()
    })