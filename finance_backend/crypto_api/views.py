from django.shortcuts import render
import json
import logging
import requests
from datetime import datetime, timedelta
import pandas as pd
from django.conf import settings
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
import os

from user_api.models import WechatUser, CreditRecord
from user_api.serializers import WechatUserSerializer

logger = logging.getLogger(__name__)

# 原有Flask后端API地址
FLASK_API_BASE_URL = os.getenv('FLASK_API_BASE_URL', 'http://43.201.31.66:5009')

class CryptoDataView(APIView):
    """加密货币数据视图"""
    permission_classes = []  # 公开API
    
    def get(self, request, symbol):
        """
        获取加密货币K线数据
        
        参数:
        - symbol: 加密货币符号，如BTCUSDT
        - interval: K线间隔，默认为1d
        - limit: 获取数量，默认为180
        """
        interval = request.query_params.get('interval', '1d')
        limit = request.query_params.get('limit', '180')
        
        try:
            # 调用Binance API获取K线数据
            klines_data = self.get_klines_data(symbol, interval, limit)
            
            # 获取实时价格
            ticker_data = self.get_ticker_data(symbol)
            
            return Response({
                "code": 200,
                "message": "获取成功",
                "data": {
                    "klines": klines_data,
                    "ticker": ticker_data
                }
            })
        except Exception as e:
            logger.error(f"获取加密货币数据异常: {e}")
            return Response({
                "code": 500,
                "message": f"获取数据失败: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def get_klines_data(self, symbol, interval='1d', limit=180):
        """从Binance获取K线数据"""
        url = "https://api.binance.com/api/v3/klines"
        params = {
            "symbol": symbol.upper(),
            "interval": interval,
            "limit": limit
        }
        
        response = requests.get(url, params=params)
        response.raise_for_status()
        
        return response.json()
    
    def get_ticker_data(self, symbol):
        """从Binance获取实时价格数据"""
        url = "https://api.binance.com/api/v3/ticker/24hr"
        params = {
            "symbol": symbol.upper()
        }
        
        response = requests.get(url, params=params)
        response.raise_for_status()
        
        return response.json()


class AIAnalysisView(APIView):
    """AI分析视图"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """
        使用DeepSeek AI进行加密货币分析
        
        需要提供:
        - openid: 用户openid
        - messages: 聊天消息
        - cryptoData: 加密货币数据(可选)
        """
        openid = request.data.get('openid')
        messages = request.data.get('messages', [])
        crypto_data = request.data.get('cryptoData')
        
        if not openid:
            return Response({
                "code": 400,
                "message": "缺少openid参数"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if not messages:
            return Response({
                "code": 400,
                "message": "缺少messages参数"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # 获取用户信息
            user = WechatUser.objects.get(openid=openid)
            
            # 检查用户是否有足够的积分
            if user.credits <= 0:
                return Response({
                    "code": 403,
                    "message": "分析次数不足，请获取更多次数"
                }, status=status.HTTP_403_FORBIDDEN)
            
            # 检查用户是否绑定手机号
            if not user.phone:
                return Response({
                    "code": 403,
                    "message": "请先绑定手机号"
                }, status=status.HTTP_403_FORBIDDEN)
            
            # 转发请求到原有Flask后端
            flask_response = self.forward_to_flask_api(messages, crypto_data)
            
            if flask_response.get('error'):
                return Response({
                    "code": 500,
                    "message": f"分析失败: {flask_response.get('error')}"
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            # 扣除用户积分
            user.credits -= 1
            user.save()
            
            # 记录积分使用
            CreditRecord.objects.create(
                user=user,
                type=4,  # 消费使用
                amount=-1,
                description="使用1次AI分析"
            )
            
            # 返回AI分析结果和更新后的用户信息
            user_data = WechatUserSerializer(user).data
            
            return Response({
                "code": 200,
                "message": "分析成功",
                "data": {
                    "analysis": flask_response,
                    "user": user_data
                }
            })
        except WechatUser.DoesNotExist:
            return Response({
                "code": 404,
                "message": "用户不存在"
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"AI分析异常: {e}")
            return Response({
                "code": 500,
                "message": f"分析失败: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def forward_to_flask_api(self, messages, crypto_data=None):
        """转发请求到原有Flask后端"""
        try:
            # 构建请求数据
            request_data = {
                "messages": messages,
                "cryptoData": crypto_data
            }
            
            # 发送请求到Flask后端
            response = requests.post(
                f"{FLASK_API_BASE_URL}/api/chat",
                json=request_data,
                headers={"Content-Type": "application/json"},
                timeout=120  # 增加超时时间，因为AI分析可能需要较长时间
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            # 返回响应数据
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"转发请求到Flask后端异常: {e}")
            return {"error": str(e)}
