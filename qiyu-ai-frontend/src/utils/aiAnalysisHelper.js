/**
 * AI分析辅助工具
 * 提供数据验证、错误恢复和调试功能
 */

class AIAnalysisHelper {
  /**
   * 验证K线数据格式
   */
  static validateKlineData(klineData) {
    if (!Array.isArray(klineData) || klineData.length === 0) {
      return { valid: false, error: 'K线数据为空或格式错误' };
    }

    const sample = klineData[0];
    if (Array.isArray(sample)) {
      // 数组格式 [timestamp, open, high, low, close, volume]
      if (sample.length < 6) {
        return { valid: false, error: 'K线数据数组长度不足' };
      }
    } else if (typeof sample === 'object') {
      // 对象格式
      const requiredFields = ['timestamp', 'open', 'high', 'low', 'close', 'volume'];
      const missingFields = requiredFields.filter(field => !(field in sample));
      if (missingFields.length > 0) {
        return { valid: false, error: `K线数据缺少字段: ${missingFields.join(', ')}` };
      }
    } else {
      return { valid: false, error: 'K线数据格式不正确' };
    }

    return { valid: true };
  }

  /**
   * 验证价格数据格式
   */
  static validateTickerData(tickerData) {
    if (!tickerData || typeof tickerData !== 'object') {
      return { valid: false, error: '价格数据为空或格式错误' };
    }

    const requiredFields = ['symbol', 'lastPrice'];
    const missingFields = requiredFields.filter(field => !(field in tickerData));
    if (missingFields.length > 0) {
      return { valid: false, error: `价格数据缺少字段: ${missingFields.join(', ')}` };
    }

    return { valid: true };
  }

  /**
   * 验证订单簿数据格式
   */
  static validateOrderbookData(orderbookData) {
    if (!orderbookData || typeof orderbookData !== 'object') {
      return { valid: false, error: '订单簿数据为空或格式错误' };
    }

    if (!Array.isArray(orderbookData.bids) || !Array.isArray(orderbookData.asks)) {
      return { valid: false, error: '订单簿数据缺少bids或asks字段' };
    }

    return { valid: true };
  }

  /**
   * 生成数据摘要用于调试
   */
  static generateDataSummary(cryptoData) {
    const summary = {
      timestamp: new Date().toISOString(),
      dataTypes: Object.keys(cryptoData),
      details: {}
    };

    // K线数据摘要
    if (cryptoData.dailyKlines) {
      summary.details.dailyKlines = {
        count: cryptoData.dailyKlines.length,
        timeRange: cryptoData.dailyKlines.length > 0 ? {
          start: new Date(cryptoData.dailyKlines[0].timestamp || cryptoData.dailyKlines[0][0]).toISOString(),
          end: new Date(cryptoData.dailyKlines[cryptoData.dailyKlines.length - 1].timestamp || cryptoData.dailyKlines[cryptoData.dailyKlines.length - 1][0]).toISOString()
        } : null
      };
    }

    if (cryptoData.shortTermKlines) {
      summary.details.shortTermKlines = {
        count: cryptoData.shortTermKlines.length,
        timeRange: cryptoData.shortTermKlines.length > 0 ? {
          start: new Date(cryptoData.shortTermKlines[0].timestamp || cryptoData.shortTermKlines[0][0]).toISOString(),
          end: new Date(cryptoData.shortTermKlines[cryptoData.shortTermKlines.length - 1].timestamp || cryptoData.shortTermKlines[cryptoData.shortTermKlines.length - 1][0]).toISOString()
        } : null
      };
    }

    // 价格数据摘要
    if (cryptoData.ticker) {
      summary.details.ticker = {
        symbol: cryptoData.ticker.symbol,
        price: cryptoData.ticker.lastPrice,
        change: cryptoData.ticker.priceChangePercent
      };
    }

    // 订单簿数据摘要
    if (cryptoData.orderbook) {
      summary.details.orderbook = {
        bidsCount: cryptoData.orderbook.bids?.length || 0,
        asksCount: cryptoData.orderbook.asks?.length || 0,
        spread: cryptoData.orderbook.bids?.[0] && cryptoData.orderbook.asks?.[0] 
          ? (parseFloat(cryptoData.orderbook.asks[0][0]) - parseFloat(cryptoData.orderbook.bids[0][0])).toFixed(2)
          : null
      };
    }

    // BTC辅助数据摘要
    if (cryptoData.btc_auxiliary_data) {
      summary.details.btc_auxiliary_data = {
        hasRealTimePrice: !!cryptoData.btc_auxiliary_data.real_time_price,
        dailyKlinesCount: cryptoData.btc_auxiliary_data.formatted_daily_klines?.length || 0,
        shortTermKlinesCount: cryptoData.btc_auxiliary_data.formatted_short_term_klines?.length || 0
      };
    }

    return summary;
  }

  /**
   * 检查数据完整性
   */
  static checkDataIntegrity(cryptoData, symbol, analysisType) {
    const issues = [];
    const warnings = [];

    // 检查必需数据
    if (!cryptoData.dailyKlines || cryptoData.dailyKlines.length === 0) {
      issues.push('缺少日K线数据');
    } else if (cryptoData.dailyKlines.length < 30) {
      warnings.push(`日K线数据较少: ${cryptoData.dailyKlines.length}条`);
    }

    if (!cryptoData.ticker) {
      issues.push('缺少实时价格数据');
    }

    // 检查分析类型相关数据
    const needsShortTerm = ['four_hour', 'one_day', 'three_day', 'quick'].includes(analysisType);
    if (needsShortTerm && (!cryptoData.shortTermKlines || cryptoData.shortTermKlines.length === 0)) {
      warnings.push('缺少短期K线数据，可能影响分析精度');
    }

    // 检查订单簿数据
    if (!cryptoData.orderbook) {
      warnings.push('缺少订单簿数据，可能影响深度分析');
    }

    // 检查BTC辅助数据（非BTC币种）
    if (symbol !== 'BTC' && !cryptoData.btc_auxiliary_data) {
      warnings.push('缺少BTC参考数据，可能影响市场关联分析');
    }

    return {
      hasIssues: issues.length > 0,
      hasWarnings: warnings.length > 0,
      issues,
      warnings,
      score: Math.max(0, 100 - issues.length * 30 - warnings.length * 10) // 数据完整性评分
    };
  }

  /**
   * 创建降级数据
   */
  static createFallbackData(symbol, analysisType) {
    console.warn('🔄 创建降级数据:', symbol, analysisType);
    
    const now = Date.now();
    const basePrice = symbol === 'BTC' ? 50000 : symbol === 'ETH' ? 3000 : 1;
    
    // 生成模拟K线数据
    const generateMockKlines = (count, interval) => {
      const klines = [];
      let currentPrice = basePrice;
      
      for (let i = count - 1; i >= 0; i--) {
        const timestamp = now - i * interval;
        const volatility = basePrice * 0.02; // 2%波动
        
        const open = currentPrice;
        const change = (Math.random() - 0.5) * volatility;
        const close = Math.max(open + change, basePrice * 0.5);
        const high = Math.max(open, close) + Math.random() * volatility * 0.5;
        const low = Math.min(open, close) - Math.random() * volatility * 0.5;
        const volume = Math.random() * 1000 + 100;
        
        klines.push([timestamp, open, high, low, close, volume]);
        currentPrice = close;
      }
      
      return klines.reverse();
    };

    return {
      dailyKlines: generateMockKlines(90, 24 * 60 * 60 * 1000), // 90天
      shortTermKlines: generateMockKlines(100, 60 * 60 * 1000), // 100小时
      ticker: {
        symbol: `${symbol}USDT`,
        lastPrice: basePrice.toString(),
        priceChangePercent: ((Math.random() - 0.5) * 10).toFixed(2),
        volume: (Math.random() * 10000 + 1000).toString(),
        timestamp: new Date().toISOString()
      },
      orderbook: {
        bids: Array.from({ length: 20 }, (_, i) => [
          (basePrice - i * basePrice * 0.001).toFixed(2),
          (Math.random() * 10 + 1).toFixed(4)
        ]),
        asks: Array.from({ length: 20 }, (_, i) => [
          (basePrice + (i + 1) * basePrice * 0.001).toFixed(2),
          (Math.random() * 10 + 1).toFixed(4)
        ])
      },
      isFallback: true
    };
  }

  /**
   * 格式化错误信息
   */
  static formatError(error, context = {}) {
    let message = error.message || error.toString();
    
    // 网络错误
    if (message.includes('fetch') || message.includes('network')) {
      return {
        type: 'network',
        title: '网络连接失败',
        message: '请检查网络连接或稍后重试',
        suggestion: '检查网络连接状态，或尝试刷新页面'
      };
    }
    
    // API错误
    if (message.includes('HTTP 4')) {
      return {
        type: 'api',
        title: 'API请求失败',
        message: '服务器拒绝了请求',
        suggestion: '请检查请求参数或联系技术支持'
      };
    }
    
    if (message.includes('HTTP 5')) {
      return {
        type: 'server',
        title: '服务器错误',
        message: '服务器暂时无法处理请求',
        suggestion: '请稍后重试，如果问题持续请联系技术支持'
      };
    }
    
    // 数据错误
    if (message.includes('数据') || message.includes('格式')) {
      return {
        type: 'data',
        title: '数据格式错误',
        message: message,
        suggestion: '请尝试重新获取数据或使用其他数据源'
      };
    }
    
    // 通用错误
    return {
      type: 'unknown',
      title: '未知错误',
      message: message,
      suggestion: '请尝试刷新页面或联系技术支持'
    };
  }

  /**
   * 调试信息收集
   */
  static collectDebugInfo(symbol, analysisType, cryptoData, error = null) {
    const debugInfo = {
      timestamp: new Date().toISOString(),
      symbol,
      analysisType,
      userAgent: navigator.userAgent,
      url: window.location.href,
      error: error ? {
        message: error.message,
        stack: error.stack,
        name: error.name
      } : null
    };

    if (cryptoData) {
      debugInfo.dataSummary = this.generateDataSummary(cryptoData);
      debugInfo.dataIntegrity = this.checkDataIntegrity(cryptoData, symbol, analysisType);
    }

    return debugInfo;
  }
}

export default AIAnalysisHelper;