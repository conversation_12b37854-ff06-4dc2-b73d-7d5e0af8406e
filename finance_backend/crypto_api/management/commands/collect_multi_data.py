"""
多货币多周期实时数据收集管理命令
支持同时收集多个交易对的多个时间周期数据

使用方法:
python manage.py collect_multi_data --symbols=BTCUSDT,ETHUSDT --timeframes=1m,15m,1h --daemon
"""

import asyncio
import websockets
import json
import threading
import time
import logging
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.conf import settings
from crypto_api.services.multi_collector import MultiCryptoCollector

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '启动多货币多周期实时数据收集常驻进程'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--symbols', 
            default='BTCUSDT,ETHUSDT,SOLUSDT', 
            help='交易对符号列表，逗号分隔 (默认: BTCUSDT,ETHUSDT,SOLUSDT)'
        )
        parser.add_argument(
            '--timeframes',
            default='1m,15m,1h,4h,1d',
            help='时间周期列表，逗号分隔 (默认: 1m,15m,1h,4h,1d)'
        )
        parser.add_argument(
            '--daemon', 
            action='store_true', 
            help='守护进程模式运行'
        )
        parser.add_argument(
            '--interval',
            default=300,
            type=int,
            help='COS上传间隔（秒，默认300=5分钟）'
        )
        parser.add_argument(
            '--debug',
            action='store_true',
            help='启用调试模式'
        )
    
    def handle(self, *args, **options):
        """启动多货币多周期数据收集器"""
        
        # 解析参数
        symbols = [s.strip().upper() for s in options['symbols'].split(',')]
        timeframes = [tf.strip() for tf in options['timeframes'].split(',')]
        daemon = options['daemon']
        interval = options['interval']
        debug = options['debug']
        
        self.stdout.write('🚀 启动多货币多周期实时数据收集器')
        self.stdout.write(f'📊 交易对: {", ".join(symbols)}')
        self.stdout.write(f'⏰ 时间周期: {", ".join(timeframes)}')
        self.stdout.write(f'📤 上传间隔: {interval}秒')
        self.stdout.write(f'🔧 守护进程: {"是" if daemon else "否"}')
        self.stdout.write(f'🐛 调试模式: {"是" if debug else "否"}')
        
        # 配置日志
        if debug:
            logging.basicConfig(
                level=logging.DEBUG,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                handlers=[
                    logging.FileHandler('logs/multi_collector.log'),
                    logging.StreamHandler()
                ]
            )
        else:
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(levelname)s - %(message)s',
                handlers=[
                    logging.FileHandler('logs/multi_collector.log'),
                    logging.StreamHandler()
                ]
            )
        
        # 创建收集器
        collector = MultiCryptoCollector(
            symbols=symbols,
            timeframes=timeframes,
            upload_interval=interval,
            debug=debug
        )
        
        try:
            # 启动收集器
            collector.start()
            
        except KeyboardInterrupt:
            self.stdout.write('\n🛑 收到停止信号，正在停止收集器...')
            collector.stop()
            self.stdout.write('✅ 数据收集器已停止')
            
        except Exception as e:
            self.stderr.write(f'❌ 收集器启动失败: {e}')
            raise