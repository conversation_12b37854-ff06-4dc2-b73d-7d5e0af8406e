import React, { useState } from 'react';
import { Card, Button, Space } from 'antd';
import IndicatorCalculator from './IndicatorCalculator';
import AIAnalyzer from './AIAnalyzer';

const TestAnalysis = () => {
  const [indicatorResults, setIndicatorResults] = useState(null);

  const handleCalculationComplete = (results) => {
    console.log('🎉 测试：指标计算完成', results);
    setIndicatorResults(results);
  };

  return (
    <div style={{ padding: '20px', background: '#141414', minHeight: '100vh' }}>
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        <Card title="技术指标分析测试" size="small">
          <p style={{ color: '#fff' }}>测试技术指标计算器和AI分析器组件</p>
        </Card>

        <IndicatorCalculator
          symbol="BTCUSDT"
          timeframe="1h"
          years={['2024', '2023']}
          onCalculationComplete={handleCalculationComplete}
        />

        <AIAnalyzer
          indicatorData={indicatorResults}
          symbol="BTCUSDT"
          timeframe="1h"
        />
      </Space>
    </div>
  );
};

export default TestAnalysis;
