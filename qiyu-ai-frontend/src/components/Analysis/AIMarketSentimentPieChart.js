import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';

const AIMarketSentimentPieChart = ({ predictionData = {} }) => {
  const [data, setData] = useState([]);
  const COLORS = ['#52c41a', '#87d068', '#faad14', '#ff7a45', '#ff4d4f'];

  useEffect(() => {
    // 处理预测数据
    if (predictionData && Object.keys(predictionData).length > 0) {
      const chartData = Object.entries(predictionData).map(([key, value]) => {
        // 转换键名
        let name = key;
        if (key === "看涨") name = "看涨";
        if (key === "非常看涨") name = "非常看涨";
        if (key === "中性") name = "中性";
        if (key === "看跌") name = "看跌";
        if (key === "非常看跌") name = "非常看跌";
        
        return {
          name: name,
          value: value
        };
      });
      setData(chartData);
    } else {
      // 默认数据
      setData([
        { name: '看涨', value: 25 },
        { name: '非常看涨', value: 10 },
        { name: '中性', value: 30 },
        { name: '看跌', value: 25 },
        { name: '非常看跌', value: 10 },
      ]);
    }
  }, [predictionData]);

  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * Math.PI / 180);
    const y = cy + radius * Math.sin(-midAngle * Math.PI / 180);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor="middle" 
        dominantBaseline="central"
        fontSize={12}
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  // 自定义图例渲染函数，确保显示百分比值
  const renderCustomLegend = (props) => {
    const { payload } = props;
    
    return (
      <ul style={{ listStyle: 'none', padding: 0, margin: 0, display: 'flex', flexWrap: 'wrap', justifyContent: 'center' }}>
        {payload.map((entry, index) => (
          <li key={`item-${index}`} style={{ display: 'flex', alignItems: 'center', marginRight: 10, marginBottom: 5 }}>
            <div style={{ width: 10, height: 10, backgroundColor: entry.color, marginRight: 5 }} />
            <span style={{ color: '#fff', fontSize: 10 }}>{`${entry.value} ${entry.payload.value}%`}</span>
          </li>
        ))}
      </ul>
    );
  };

  return (
    <ResponsiveContainer width="100%" height={200}>
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={renderCustomizedLabel}
          outerRadius={80}
          fill="#8884d8"
          dataKey="value"
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip formatter={(value) => `${value}%`} />
        <Legend 
          content={renderCustomLegend}
          layout="horizontal" 
          verticalAlign="bottom" 
          align="center"
        />
      </PieChart>
    </ResponsiveContainer>
  );
};

export default AIMarketSentimentPieChart; 