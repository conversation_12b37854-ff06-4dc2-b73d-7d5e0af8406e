// 混合路径的加密货币数据服务 - 临时解决方案
class HybridCryptoDataService {
  constructor() {
    this.cache = new Map()
    
    // 不同时间周期使用不同的路径
    this.cosBasePaths = {
      '1m': 'https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/20250723',
      '1h': 'https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/20250723', 
      '1d': 'https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/20250723',
      '15m': 'https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/20250724',
      '4h': 'https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/20250724',
      '1mo': 'https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/20250724'
    }
    
    this.knownTimeframes = ['1d', '1h', '15m', '4h', '1mo']
    this.knownSymbols = ['BTCUSDT']
    this.knownYears = ['2020', '2021', '2022', '2023', '2024']
  }

  async getAvailableYears(symbol = 'BTCUSDT', timeframe = '1h') {
    const cacheKey = `${symbol}_${timeframe}_years`
    
    if (this.cache.has(cacheKey)) {
      console.log(`📋 从缓存获取可用年份: ${symbol} ${timeframe}`)
      return this.cache.get(cacheKey)
    }

    console.log(`🔍 检查可用年份: ${symbol} ${timeframe}`)
    
    const baseURL = this.cosBasePaths[timeframe]
    if (!baseURL) {
      console.warn(`⚠️ 未知时间周期: ${timeframe}`)
      return []
    }

    const availableYears = []
    const yearChecks = this.knownYears.map(async (year) => {
      const url = `${baseURL}/${symbol}/${timeframe}/${symbol}_${timeframe}_${year}_compressed.json`
      
      try {
        const response = await fetch(url, { method: 'HEAD' })
        if (response.ok) {
          console.log(`✅ 找到数据: ${year}年`)
          return year
        } else {
          console.log(`❌ 未找到数据: ${year}年 (${response.status})`)
          return null
        }
      } catch (error) {
        console.log(`❌ 请求失败: ${year}年 (${error.message})`)
        return null
      }
    })

    const results = await Promise.all(yearChecks)
    const validYears = results.filter(year => year !== null).sort()
    
    console.log(`📊 ${symbol} ${timeframe} 可用年份: ${validYears}`)
    
    this.cache.set(cacheKey, validYears)
    return validYears
  }

  getDataURL(symbol, timeframe, year) {
    const baseURL = this.cosBasePaths[timeframe]
    if (!baseURL) {
      console.warn(`⚠️ 未知时间周期: ${timeframe}`)
      return null
    }
    
    return `${baseURL}/${symbol}/${timeframe}/${symbol}_${timeframe}_${year}_compressed.json`
  }
}

// 创建全局实例
window.hybridCryptoMetadataService = new HybridCryptoDataService()