{"timestamp": "2025-08-01T02:23:12.612089+00:00", "summary": {"total_symbols": 7, "normal_symbols": 0, "warning_symbols": 0, "critical_symbols": 7, "has_stuck_tasks": false, "stuck_count": 0}, "gaps": {"BTCUSDT": {"type": "analysis", "status": "❌ 严重缺失", "severity": "high", "expected_count": 192, "actual_count": 73, "missing_count": 119, "failed_count": 0, "latest_prediction": "2025-08-01 02:03:03.211863+00:00", "latest_gap_minutes": 20.155349750000003, "large_gaps": [{"start": "2025-07-30 16:01:53.506714+00:00", "end": "2025-07-30 16:22:10.226805+00:00", "gap_minutes": 20.27866818333333}, {"start": "2025-07-30 16:22:10.226805+00:00", "end": "2025-07-30 16:48:02.498807+00:00", "gap_minutes": 25.87120003333333}, {"start": "2025-07-30 16:48:02.498807+00:00", "end": "2025-07-30 17:18:02.943405+00:00", "gap_minutes": 30.007409966666668}, {"start": "2025-07-30 17:18:02.943405+00:00", "end": "2025-07-30 17:48:03.069919+00:00", "gap_minutes": 30.002108566666667}, {"start": "2025-07-30 17:48:03.069919+00:00", "end": "2025-07-30 18:18:02.883330+00:00", "gap_minutes": 29.996890183333335}, {"start": "2025-07-30 18:18:02.883330+00:00", "end": "2025-07-30 18:48:03.467158+00:00", "gap_minutes": 30.009730466666667}, {"start": "2025-07-30 18:48:03.467158+00:00", "end": "2025-07-30 19:18:03.002730+00:00", "gap_minutes": 29.992259533333332}, {"start": "2025-07-30 19:18:03.002730+00:00", "end": "2025-07-30 19:48:02.768549+00:00", "gap_minutes": 29.996096983333334}, {"start": "2025-07-30 19:48:02.768549+00:00", "end": "2025-07-30 20:18:03.420485+00:00", "gap_minutes": 30.0108656}, {"start": "2025-07-30 20:18:03.420485+00:00", "end": "2025-07-30 20:48:03.548485+00:00", "gap_minutes": 30.002133333333333}, {"start": "2025-07-30 20:48:03.548485+00:00", "end": "2025-07-30 21:18:02.974238+00:00", "gap_minutes": 29.990429216666666}, {"start": "2025-07-30 21:18:02.974238+00:00", "end": "2025-07-30 21:48:03.040366+00:00", "gap_minutes": 30.001102133333333}, {"start": "2025-07-30 21:48:03.040366+00:00", "end": "2025-07-30 22:18:02.928868+00:00", "gap_minutes": 29.9981417}, {"start": "2025-07-30 22:18:02.928868+00:00", "end": "2025-07-30 22:48:02.914710+00:00", "gap_minutes": 29.999764033333335}, {"start": "2025-07-30 22:48:02.914710+00:00", "end": "2025-07-30 23:18:03.122861+00:00", "gap_minutes": 30.003469183333333}, {"start": "2025-07-30 23:18:03.122861+00:00", "end": "2025-07-30 23:48:02.722419+00:00", "gap_minutes": 29.993325966666664}, {"start": "2025-07-30 23:48:02.722419+00:00", "end": "2025-07-31 00:18:03.637515+00:00", "gap_minutes": 30.0152516}, {"start": "2025-07-31 00:18:03.637515+00:00", "end": "2025-07-31 00:48:03.053741+00:00", "gap_minutes": 29.990270433333333}, {"start": "2025-07-31 00:48:03.053741+00:00", "end": "2025-07-31 01:18:03.342319+00:00", "gap_minutes": 30.00480963333333}, {"start": "2025-07-31 01:18:03.342319+00:00", "end": "2025-07-31 01:48:02.870413+00:00", "gap_minutes": 29.9921349}, {"start": "2025-07-31 01:48:02.870413+00:00", "end": "2025-07-31 02:18:03.395266+00:00", "gap_minutes": 30.00874755}, {"start": "2025-07-31 02:19:55.673575+00:00", "end": "2025-07-31 02:48:02.792427+00:00", "gap_minutes": 28.118647533333334}, {"start": "2025-07-31 02:48:02.792427+00:00", "end": "2025-07-31 03:11:39.060582+00:00", "gap_minutes": 23.60446925}, {"start": "2025-07-31 03:11:39.060582+00:00", "end": "2025-07-31 03:33:02.730325+00:00", "gap_minutes": 21.394495716666665}, {"start": "2025-07-31 03:33:02.730325+00:00", "end": "2025-07-31 04:03:03.270021+00:00", "gap_minutes": 30.008994933333334}, {"start": "2025-07-31 04:03:03.270021+00:00", "end": "2025-07-31 04:33:03.147469+00:00", "gap_minutes": 29.997957466666666}, {"start": "2025-07-31 04:33:03.147469+00:00", "end": "2025-07-31 05:03:03.262594+00:00", "gap_minutes": 30.00191875}, {"start": "2025-07-31 05:03:03.262594+00:00", "end": "2025-07-31 05:33:02.814917+00:00", "gap_minutes": 29.992538716666665}, {"start": "2025-07-31 05:33:02.814917+00:00", "end": "2025-07-31 06:03:03.029861+00:00", "gap_minutes": 30.003582400000003}, {"start": "2025-07-31 06:03:03.029861+00:00", "end": "2025-07-31 06:33:03.345690+00:00", "gap_minutes": 30.005263816666666}, {"start": "2025-07-31 06:33:03.345690+00:00", "end": "2025-07-31 07:03:02.974875+00:00", "gap_minutes": 29.99381975}, {"start": "2025-07-31 07:03:02.974875+00:00", "end": "2025-07-31 07:33:03.374539+00:00", "gap_minutes": 30.006661066666666}, {"start": "2025-07-31 07:33:03.374539+00:00", "end": "2025-07-31 08:03:02.777115+00:00", "gap_minutes": 29.99004293333333}, {"start": "2025-07-31 08:08:06.405932+00:00", "end": "2025-07-31 08:33:03.256686+00:00", "gap_minutes": 24.947512566666667}, {"start": "2025-07-31 08:33:03.256686+00:00", "end": "2025-07-31 09:03:02.789005+00:00", "gap_minutes": 29.992205316666666}, {"start": "2025-07-31 09:03:02.789005+00:00", "end": "2025-07-31 09:33:03.226187+00:00", "gap_minutes": 30.007286366666666}, {"start": "2025-07-31 09:33:03.226187+00:00", "end": "2025-07-31 10:03:02.660025+00:00", "gap_minutes": 29.990563966666667}, {"start": "2025-07-31 10:03:02.660025+00:00", "end": "2025-07-31 10:33:02.752449+00:00", "gap_minutes": 30.0015404}, {"start": "2025-07-31 10:33:02.752449+00:00", "end": "2025-07-31 11:03:02.506525+00:00", "gap_minutes": 29.995901266666667}, {"start": "2025-07-31 11:03:02.506525+00:00", "end": "2025-07-31 11:33:02.718110+00:00", "gap_minutes": 30.003526416666666}, {"start": "2025-07-31 11:33:02.718110+00:00", "end": "2025-07-31 12:03:02.963416+00:00", "gap_minutes": 30.004088433333333}, {"start": "2025-07-31 12:03:02.963416+00:00", "end": "2025-07-31 12:33:02.715251+00:00", "gap_minutes": 29.995863916666668}, {"start": "2025-07-31 12:33:02.715251+00:00", "end": "2025-07-31 13:03:03.138004+00:00", "gap_minutes": 30.007045883333333}, {"start": "2025-07-31 13:03:03.138004+00:00", "end": "2025-07-31 13:33:02.926189+00:00", "gap_minutes": 29.996469750000003}, {"start": "2025-07-31 13:33:02.926189+00:00", "end": "2025-07-31 14:03:02.808625+00:00", "gap_minutes": 29.998040600000003}, {"start": "2025-07-31 14:03:02.808625+00:00", "end": "2025-07-31 14:33:02.926810+00:00", "gap_minutes": 30.00196975}, {"start": "2025-07-31 14:33:02.926810+00:00", "end": "2025-07-31 15:03:03.050853+00:00", "gap_minutes": 30.002067383333333}, {"start": "2025-07-31 15:03:03.050853+00:00", "end": "2025-07-31 15:33:02.882384+00:00", "gap_minutes": 29.997192183333333}, {"start": "2025-07-31 15:33:02.882384+00:00", "end": "2025-07-31 16:03:03.532276+00:00", "gap_minutes": 30.01083153333333}, {"start": "2025-07-31 16:03:03.532276+00:00", "end": "2025-07-31 16:33:02.768338+00:00", "gap_minutes": 29.9872677}, {"start": "2025-07-31 16:33:02.768338+00:00", "end": "2025-07-31 17:03:03.454213+00:00", "gap_minutes": 30.011431249999998}, {"start": "2025-07-31 17:03:03.454213+00:00", "end": "2025-07-31 17:33:03.354263+00:00", "gap_minutes": 29.998334166666666}, {"start": "2025-07-31 17:33:03.354263+00:00", "end": "2025-07-31 18:03:03.387644+00:00", "gap_minutes": 30.00055635}, {"start": "2025-07-31 18:03:03.387644+00:00", "end": "2025-07-31 18:33:02.586216+00:00", "gap_minutes": 29.986642866666667}, {"start": "2025-07-31 18:33:02.586216+00:00", "end": "2025-07-31 19:03:03.280688+00:00", "gap_minutes": 30.01157453333333}, {"start": "2025-07-31 19:03:03.280688+00:00", "end": "2025-07-31 19:33:02.615561+00:00", "gap_minutes": 29.98891455}, {"start": "2025-07-31 19:33:02.615561+00:00", "end": "2025-07-31 20:03:03.088953+00:00", "gap_minutes": 30.00788986666667}, {"start": "2025-07-31 20:03:03.088953+00:00", "end": "2025-07-31 20:33:02.636355+00:00", "gap_minutes": 29.992456699999998}, {"start": "2025-07-31 20:33:02.636355+00:00", "end": "2025-07-31 21:03:03.474133+00:00", "gap_minutes": 30.013962966666668}, {"start": "2025-07-31 21:03:03.474133+00:00", "end": "2025-07-31 21:33:03.036263+00:00", "gap_minutes": 29.992702166666668}, {"start": "2025-07-31 21:33:03.036263+00:00", "end": "2025-07-31 22:03:02.771612+00:00", "gap_minutes": 29.99558915}, {"start": "2025-07-31 22:03:02.771612+00:00", "end": "2025-07-31 22:33:02.521028+00:00", "gap_minutes": 29.9958236}, {"start": "2025-07-31 22:33:02.521028+00:00", "end": "2025-07-31 23:03:02.678644+00:00", "gap_minutes": 30.00262693333333}, {"start": "2025-07-31 23:03:02.678644+00:00", "end": "2025-07-31 23:33:03.362676+00:00", "gap_minutes": 30.011400533333333}, {"start": "2025-07-31 23:33:03.362676+00:00", "end": "2025-08-01 00:03:03.257505+00:00", "gap_minutes": 29.99824715}, {"start": "2025-08-01 00:03:03.257505+00:00", "end": "2025-08-01 00:33:03.294750+00:00", "gap_minutes": 30.00062075}, {"start": "2025-08-01 00:33:03.294750+00:00", "end": "2025-08-01 01:03:02.817142+00:00", "gap_minutes": 29.99203986666667}, {"start": "2025-08-01 01:03:02.817142+00:00", "end": "2025-08-01 01:33:03.063384+00:00", "gap_minutes": 30.00410403333333}, {"start": "2025-08-01 01:33:03.063384+00:00", "end": "2025-08-01 02:03:03.211863+00:00", "gap_minutes": 30.00247465}], "success_rate": 100.0}, "ETHUSDT": {"type": "analysis", "status": "❌ 严重缺失", "severity": "high", "expected_count": 192, "actual_count": 72, "missing_count": 120, "failed_count": 0, "latest_prediction": "2025-08-01 02:03:58.521825+00:00", "latest_gap_minutes": 19.23389896666667, "large_gaps": [{"start": "2025-07-30 16:02:24.417490+00:00", "end": "2025-07-30 16:22:43.826665+00:00", "gap_minutes": 20.32348625}, {"start": "2025-07-30 16:22:43.826665+00:00", "end": "2025-07-30 16:48:29.459111+00:00", "gap_minutes": 25.76054076666667}, {"start": "2025-07-30 16:48:29.459111+00:00", "end": "2025-07-30 17:18:33.388517+00:00", "gap_minutes": 30.065490099999998}, {"start": "2025-07-30 17:18:33.388517+00:00", "end": "2025-07-30 17:48:35.078164+00:00", "gap_minutes": 30.02816078333333}, {"start": "2025-07-30 17:48:35.078164+00:00", "end": "2025-07-30 18:18:31.071300+00:00", "gap_minutes": 29.933218933333333}, {"start": "2025-07-30 18:18:31.071300+00:00", "end": "2025-07-30 18:48:31.380699+00:00", "gap_minutes": 30.00515665}, {"start": "2025-07-30 18:48:31.380699+00:00", "end": "2025-07-30 19:18:33.428481+00:00", "gap_minutes": 30.0341297}, {"start": "2025-07-30 19:18:33.428481+00:00", "end": "2025-07-30 19:48:29.756607+00:00", "gap_minutes": 29.9388021}, {"start": "2025-07-30 19:48:29.756607+00:00", "end": "2025-07-30 20:18:28.984983+00:00", "gap_minutes": 29.9871396}, {"start": "2025-07-30 20:18:28.984983+00:00", "end": "2025-07-30 20:48:28.494764+00:00", "gap_minutes": 29.991829683333332}, {"start": "2025-07-30 20:48:28.494764+00:00", "end": "2025-07-30 21:18:30.067081+00:00", "gap_minutes": 30.026205283333336}, {"start": "2025-07-30 21:18:30.067081+00:00", "end": "2025-07-30 21:48:29.659194+00:00", "gap_minutes": 29.99320188333333}, {"start": "2025-07-30 21:48:29.659194+00:00", "end": "2025-07-30 22:18:27.153020+00:00", "gap_minutes": 29.95823043333333}, {"start": "2025-07-30 22:18:27.153020+00:00", "end": "2025-07-30 22:48:27.570795+00:00", "gap_minutes": 30.006962916666666}, {"start": "2025-07-30 22:48:27.570795+00:00", "end": "2025-07-30 23:18:28.363785+00:00", "gap_minutes": 30.0132165}, {"start": "2025-07-30 23:18:28.363785+00:00", "end": "2025-07-30 23:48:27.127239+00:00", "gap_minutes": 29.9793909}, {"start": "2025-07-30 23:48:27.127239+00:00", "end": "2025-07-31 00:18:30.622394+00:00", "gap_minutes": 30.058252583333335}, {"start": "2025-07-31 00:18:30.622394+00:00", "end": "2025-07-31 00:48:27.586102+00:00", "gap_minutes": 29.949395133333333}, {"start": "2025-07-31 00:48:27.586102+00:00", "end": "2025-07-31 01:18:31.554632+00:00", "gap_minutes": 30.06614216666667}, {"start": "2025-07-31 01:18:31.554632+00:00", "end": "2025-07-31 01:48:28.681792+00:00", "gap_minutes": 29.952119333333332}, {"start": "2025-07-31 01:48:28.681792+00:00", "end": "2025-07-31 02:18:32.159600+00:00", "gap_minutes": 30.057963466666667}, {"start": "2025-07-31 02:20:21.930435+00:00", "end": "2025-07-31 02:48:30.438919+00:00", "gap_minutes": 28.141808066666666}, {"start": "2025-07-31 02:48:30.438919+00:00", "end": "2025-07-31 03:12:15.532543+00:00", "gap_minutes": 23.751560400000002}, {"start": "2025-07-31 03:12:15.532543+00:00", "end": "2025-07-31 03:33:33.238268+00:00", "gap_minutes": 21.295095416666665}, {"start": "2025-07-31 03:33:33.238268+00:00", "end": "2025-07-31 04:03:32.083628+00:00", "gap_minutes": 29.980756}, {"start": "2025-07-31 04:03:32.083628+00:00", "end": "2025-07-31 04:33:28.786232+00:00", "gap_minutes": 29.9450434}, {"start": "2025-07-31 04:33:28.786232+00:00", "end": "2025-07-31 05:03:30.278704+00:00", "gap_minutes": 30.024874533333332}, {"start": "2025-07-31 05:03:30.278704+00:00", "end": "2025-07-31 05:33:32.259356+00:00", "gap_minutes": 30.033010866666665}, {"start": "2025-07-31 05:33:32.259356+00:00", "end": "2025-07-31 06:03:37.313861+00:00", "gap_minutes": 30.08424175}, {"start": "2025-07-31 06:03:37.313861+00:00", "end": "2025-07-31 06:33:41.273162+00:00", "gap_minutes": 30.06598835}, {"start": "2025-07-31 06:33:41.273162+00:00", "end": "2025-07-31 07:03:53.336426+00:00", "gap_minutes": 30.201054399999997}, {"start": "2025-07-31 07:03:53.336426+00:00", "end": "2025-07-31 07:33:39.071462+00:00", "gap_minutes": 29.7622506}, {"start": "2025-07-31 07:33:39.071462+00:00", "end": "2025-07-31 08:03:58.846961+00:00", "gap_minutes": 30.32959165}, {"start": "2025-07-31 08:03:58.846961+00:00", "end": "2025-07-31 08:33:43.415766+00:00", "gap_minutes": 29.742813416666667}, {"start": "2025-07-31 08:33:43.415766+00:00", "end": "2025-07-31 09:03:43.142436+00:00", "gap_minutes": 29.9954445}, {"start": "2025-07-31 09:03:43.142436+00:00", "end": "2025-07-31 09:33:42.478880+00:00", "gap_minutes": 29.988940733333333}, {"start": "2025-07-31 09:33:42.478880+00:00", "end": "2025-07-31 10:03:39.386137+00:00", "gap_minutes": 29.948454283333334}, {"start": "2025-07-31 10:03:39.386137+00:00", "end": "2025-07-31 10:33:45.763154+00:00", "gap_minutes": 30.106283616666666}, {"start": "2025-07-31 10:33:45.763154+00:00", "end": "2025-07-31 11:03:37.381979+00:00", "gap_minutes": 29.86031375}, {"start": "2025-07-31 11:03:37.381979+00:00", "end": "2025-07-31 11:33:36.232615+00:00", "gap_minutes": 29.980843933333333}, {"start": "2025-07-31 11:33:36.232615+00:00", "end": "2025-07-31 12:03:45.678084+00:00", "gap_minutes": 30.157424483333333}, {"start": "2025-07-31 12:03:45.678084+00:00", "end": "2025-07-31 12:33:41.759245+00:00", "gap_minutes": 29.934686016666667}, {"start": "2025-07-31 12:33:41.759245+00:00", "end": "2025-07-31 13:04:03.568957+00:00", "gap_minutes": 30.3634952}, {"start": "2025-07-31 13:04:03.568957+00:00", "end": "2025-07-31 13:33:40.663988+00:00", "gap_minutes": 29.618250516666667}, {"start": "2025-07-31 13:33:40.663988+00:00", "end": "2025-07-31 14:03:56.593681+00:00", "gap_minutes": 30.265494883333336}, {"start": "2025-07-31 14:03:56.593681+00:00", "end": "2025-07-31 14:33:41.469837+00:00", "gap_minutes": 29.747935933333334}, {"start": "2025-07-31 14:33:41.469837+00:00", "end": "2025-07-31 15:03:48.861630+00:00", "gap_minutes": 30.12319655}, {"start": "2025-07-31 15:03:48.861630+00:00", "end": "2025-07-31 15:34:12.316265+00:00", "gap_minutes": 30.390910583333334}, {"start": "2025-07-31 15:34:12.316265+00:00", "end": "2025-07-31 16:04:14.082827+00:00", "gap_minutes": 30.0294427}, {"start": "2025-07-31 16:04:14.082827+00:00", "end": "2025-07-31 16:34:13.595486+00:00", "gap_minutes": 29.99187765}, {"start": "2025-07-31 16:34:13.595486+00:00", "end": "2025-07-31 17:04:11.562579+00:00", "gap_minutes": 29.966118216666665}, {"start": "2025-07-31 17:04:11.562579+00:00", "end": "2025-07-31 17:34:06.519605+00:00", "gap_minutes": 29.915950433333332}, {"start": "2025-07-31 17:34:06.519605+00:00", "end": "2025-07-31 18:04:09.318830+00:00", "gap_minutes": 30.04665375}, {"start": "2025-07-31 18:04:09.318830+00:00", "end": "2025-07-31 18:34:05.265105+00:00", "gap_minutes": 29.932437916666668}, {"start": "2025-07-31 18:34:05.265105+00:00", "end": "2025-07-31 19:04:03.325007+00:00", "gap_minutes": 29.967665033333333}, {"start": "2025-07-31 19:04:03.325007+00:00", "end": "2025-07-31 19:34:01.213189+00:00", "gap_minutes": 29.964803033333332}, {"start": "2025-07-31 19:34:01.213189+00:00", "end": "2025-07-31 20:04:00.572407+00:00", "gap_minutes": 29.9893203}, {"start": "2025-07-31 20:04:00.572407+00:00", "end": "2025-07-31 20:33:53.234288+00:00", "gap_minutes": 29.877698016666667}, {"start": "2025-07-31 20:33:53.234288+00:00", "end": "2025-07-31 21:03:55.568347+00:00", "gap_minutes": 30.038900983333335}, {"start": "2025-07-31 21:03:55.568347+00:00", "end": "2025-07-31 21:33:50.090589+00:00", "gap_minutes": 29.908704033333333}, {"start": "2025-07-31 21:33:50.090589+00:00", "end": "2025-07-31 22:03:59.558729+00:00", "gap_minutes": 30.157802333333333}, {"start": "2025-07-31 22:03:59.558729+00:00", "end": "2025-07-31 22:33:53.348872+00:00", "gap_minutes": 29.89650238333333}, {"start": "2025-07-31 22:33:53.348872+00:00", "end": "2025-07-31 23:03:52.723800+00:00", "gap_minutes": 29.989582133333332}, {"start": "2025-07-31 23:03:52.723800+00:00", "end": "2025-07-31 23:33:52.011944+00:00", "gap_minutes": 29.988135733333333}, {"start": "2025-07-31 23:33:52.011944+00:00", "end": "2025-08-01 00:03:56.090135+00:00", "gap_minutes": 30.06796985}, {"start": "2025-08-01 00:03:56.090135+00:00", "end": "2025-08-01 00:33:49.362545+00:00", "gap_minutes": 29.8878735}, {"start": "2025-08-01 00:33:49.362545+00:00", "end": "2025-08-01 01:03:49.848763+00:00", "gap_minutes": 30.008103633333334}, {"start": "2025-08-01 01:03:49.848763+00:00", "end": "2025-08-01 01:33:54.215211+00:00", "gap_minutes": 30.072774133333333}, {"start": "2025-08-01 01:33:54.215211+00:00", "end": "2025-08-01 02:03:58.521825+00:00", "gap_minutes": 30.0717769}], "success_rate": 100.0}, "BNBUSDT": {"type": "analysis", "status": "❌ 严重缺失", "severity": "high", "expected_count": 192, "actual_count": 73, "missing_count": 119, "failed_count": 0, "latest_prediction": "2025-08-01 02:05:06.291637+00:00", "latest_gap_minutes": 18.1047583, "large_gaps": [{"start": "2025-07-30 16:02:56.876429+00:00", "end": "2025-07-30 16:23:12.687961+00:00", "gap_minutes": 20.263525533333333}, {"start": "2025-07-30 16:23:12.687961+00:00", "end": "2025-07-30 16:48:59.683853+00:00", "gap_minutes": 25.783264866666666}, {"start": "2025-07-30 16:48:59.683853+00:00", "end": "2025-07-30 17:19:02.947670+00:00", "gap_minutes": 30.05439695}, {"start": "2025-07-30 17:19:02.947670+00:00", "end": "2025-07-30 17:49:04.567197+00:00", "gap_minutes": 30.02699211666667}, {"start": "2025-07-30 17:49:04.567197+00:00", "end": "2025-07-30 18:18:59.266001+00:00", "gap_minutes": 29.911646733333335}, {"start": "2025-07-30 18:18:59.266001+00:00", "end": "2025-07-30 18:48:56.551147+00:00", "gap_minutes": 29.954752433333333}, {"start": "2025-07-30 18:48:56.551147+00:00", "end": "2025-07-30 19:18:59.162368+00:00", "gap_minutes": 30.043520349999998}, {"start": "2025-07-30 19:18:59.162368+00:00", "end": "2025-07-30 19:48:56.953108+00:00", "gap_minutes": 29.963179}, {"start": "2025-07-30 19:48:56.953108+00:00", "end": "2025-07-30 20:18:56.756768+00:00", "gap_minutes": 29.99672766666667}, {"start": "2025-07-30 20:18:56.756768+00:00", "end": "2025-07-30 20:48:53.374529+00:00", "gap_minutes": 29.94362935}, {"start": "2025-07-30 20:48:53.374529+00:00", "end": "2025-07-30 21:18:56.113625+00:00", "gap_minutes": 30.0456516}, {"start": "2025-07-30 21:18:56.113625+00:00", "end": "2025-07-30 21:48:55.372182+00:00", "gap_minutes": 29.98764261666667}, {"start": "2025-07-30 21:48:55.372182+00:00", "end": "2025-07-30 22:18:51.708626+00:00", "gap_minutes": 29.938940733333332}, {"start": "2025-07-30 22:18:51.708626+00:00", "end": "2025-07-30 22:48:54.682957+00:00", "gap_minutes": 30.049572183333332}, {"start": "2025-07-30 22:48:54.682957+00:00", "end": "2025-07-30 23:18:55.405761+00:00", "gap_minutes": 30.01204673333333}, {"start": "2025-07-30 23:18:55.405761+00:00", "end": "2025-07-30 23:48:52.004717+00:00", "gap_minutes": 29.943315933333334}, {"start": "2025-07-30 23:48:52.004717+00:00", "end": "2025-07-31 00:18:54.322619+00:00", "gap_minutes": 30.0386317}, {"start": "2025-07-31 00:18:54.322619+00:00", "end": "2025-07-31 00:48:54.165524+00:00", "gap_minutes": 29.99738175}, {"start": "2025-07-31 00:48:54.165524+00:00", "end": "2025-07-31 01:18:58.562931+00:00", "gap_minutes": 30.073290116666666}, {"start": "2025-07-31 01:18:58.562931+00:00", "end": "2025-07-31 01:48:54.516082+00:00", "gap_minutes": 29.932552516666664}, {"start": "2025-07-31 01:48:54.516082+00:00", "end": "2025-07-31 02:19:00.052418+00:00", "gap_minutes": 30.092272266666665}, {"start": "2025-07-31 02:20:47.371375+00:00", "end": "2025-07-31 02:48:56.702831+00:00", "gap_minutes": 28.155524266666664}, {"start": "2025-07-31 02:48:56.702831+00:00", "end": "2025-07-31 03:12:57.613521+00:00", "gap_minutes": 24.015178166666665}, {"start": "2025-07-31 03:12:57.613521+00:00", "end": "2025-07-31 03:34:00.204340+00:00", "gap_minutes": 21.043180316666668}, {"start": "2025-07-31 03:34:00.204340+00:00", "end": "2025-07-31 04:03:58.124587+00:00", "gap_minutes": 29.96533745}, {"start": "2025-07-31 04:03:58.124587+00:00", "end": "2025-07-31 04:33:51.020540+00:00", "gap_minutes": 29.881599216666665}, {"start": "2025-07-31 04:33:51.020540+00:00", "end": "2025-07-31 05:03:59.532644+00:00", "gap_minutes": 30.1418684}, {"start": "2025-07-31 05:03:59.532644+00:00", "end": "2025-07-31 05:34:00.313811+00:00", "gap_minutes": 30.01301945}, {"start": "2025-07-31 05:34:00.313811+00:00", "end": "2025-07-31 06:04:17.086089+00:00", "gap_minutes": 30.279537966666666}, {"start": "2025-07-31 06:04:17.086089+00:00", "end": "2025-07-31 06:34:14.920473+00:00", "gap_minutes": 29.9639064}, {"start": "2025-07-31 06:34:14.920473+00:00", "end": "2025-07-31 07:04:37.951733+00:00", "gap_minutes": 30.383854333333332}, {"start": "2025-07-31 07:04:37.951733+00:00", "end": "2025-07-31 07:34:11.430200+00:00", "gap_minutes": 29.55797445}, {"start": "2025-07-31 07:34:11.430200+00:00", "end": "2025-07-31 08:04:55.533938+00:00", "gap_minutes": 30.7350623}, {"start": "2025-07-31 08:34:18.932241+00:00", "end": "2025-07-31 09:04:27.286466+00:00", "gap_minutes": 30.139237083333335}, {"start": "2025-07-31 09:04:27.286466+00:00", "end": "2025-07-31 09:34:20.563865+00:00", "gap_minutes": 29.88795665}, {"start": "2025-07-31 09:34:20.563865+00:00", "end": "2025-07-31 10:04:11.778317+00:00", "gap_minutes": 29.8535742}, {"start": "2025-07-31 10:04:11.778317+00:00", "end": "2025-07-31 10:34:15.892630+00:00", "gap_minutes": 30.068571883333334}, {"start": "2025-07-31 10:34:15.892630+00:00", "end": "2025-07-31 11:04:09.543510+00:00", "gap_minutes": 29.894181333333332}, {"start": "2025-07-31 11:04:09.543510+00:00", "end": "2025-07-31 11:34:09.188935+00:00", "gap_minutes": 29.994090416666666}, {"start": "2025-07-31 11:34:09.188935+00:00", "end": "2025-07-31 12:04:21.818587+00:00", "gap_minutes": 30.210494200000003}, {"start": "2025-07-31 12:04:21.818587+00:00", "end": "2025-07-31 12:34:21.444554+00:00", "gap_minutes": 29.993766116666666}, {"start": "2025-07-31 12:34:21.444554+00:00", "end": "2025-07-31 13:04:53.350171+00:00", "gap_minutes": 30.53176028333333}, {"start": "2025-07-31 13:04:53.350171+00:00", "end": "2025-07-31 13:34:15.630252+00:00", "gap_minutes": 29.371334683333334}, {"start": "2025-07-31 13:34:15.630252+00:00", "end": "2025-07-31 14:04:46.715567+00:00", "gap_minutes": 30.518088583333334}, {"start": "2025-07-31 14:04:46.715567+00:00", "end": "2025-07-31 14:34:29.098972+00:00", "gap_minutes": 29.706390083333336}, {"start": "2025-07-31 14:34:29.098972+00:00", "end": "2025-07-31 15:04:38.677864+00:00", "gap_minutes": 30.1596482}, {"start": "2025-07-31 15:04:38.677864+00:00", "end": "2025-07-31 15:35:17.363494+00:00", "gap_minutes": 30.6447605}, {"start": "2025-07-31 15:35:17.363494+00:00", "end": "2025-07-31 16:05:36.904343+00:00", "gap_minutes": 30.325680816666665}, {"start": "2025-07-31 16:05:36.904343+00:00", "end": "2025-07-31 16:35:31.396488+00:00", "gap_minutes": 29.908202416666665}, {"start": "2025-07-31 16:35:31.396488+00:00", "end": "2025-07-31 17:05:18.298636+00:00", "gap_minutes": 29.781702466666665}, {"start": "2025-07-31 17:05:18.298636+00:00", "end": "2025-07-31 17:35:10.869962+00:00", "gap_minutes": 29.876188766666665}, {"start": "2025-07-31 17:35:10.869962+00:00", "end": "2025-07-31 18:05:13.828256+00:00", "gap_minutes": 30.0493049}, {"start": "2025-07-31 18:05:13.828256+00:00", "end": "2025-07-31 18:35:04.520614+00:00", "gap_minutes": 29.844872633333335}, {"start": "2025-07-31 18:35:04.520614+00:00", "end": "2025-07-31 19:05:12.127108+00:00", "gap_minutes": 30.1267749}, {"start": "2025-07-31 19:05:12.127108+00:00", "end": "2025-07-31 19:35:01.522634+00:00", "gap_minutes": 29.823258766666665}, {"start": "2025-07-31 19:35:01.522634+00:00", "end": "2025-07-31 20:04:58.991591+00:00", "gap_minutes": 29.95781595}, {"start": "2025-07-31 20:04:58.991591+00:00", "end": "2025-07-31 20:34:49.161239+00:00", "gap_minutes": 29.836160800000002}, {"start": "2025-07-31 20:34:49.161239+00:00", "end": "2025-07-31 21:04:44.432421+00:00", "gap_minutes": 29.921186366666667}, {"start": "2025-07-31 21:04:44.432421+00:00", "end": "2025-07-31 21:34:39.662556+00:00", "gap_minutes": 29.920502250000002}, {"start": "2025-07-31 21:34:39.662556+00:00", "end": "2025-07-31 22:04:49.266626+00:00", "gap_minutes": 30.160067833333336}, {"start": "2025-07-31 22:04:49.266626+00:00", "end": "2025-07-31 22:34:50.829575+00:00", "gap_minutes": 30.02604915}, {"start": "2025-07-31 22:34:50.829575+00:00", "end": "2025-07-31 23:04:36.830824+00:00", "gap_minutes": 29.76668748333333}, {"start": "2025-07-31 23:04:36.830824+00:00", "end": "2025-07-31 23:34:39.671551+00:00", "gap_minutes": 30.04734545}, {"start": "2025-07-31 23:34:39.671551+00:00", "end": "2025-08-01 00:04:54.543817+00:00", "gap_minutes": 30.2478711}, {"start": "2025-08-01 00:04:54.543817+00:00", "end": "2025-08-01 00:34:38.819011+00:00", "gap_minutes": 29.7379199}, {"start": "2025-08-01 00:34:38.819011+00:00", "end": "2025-08-01 01:04:42.413091+00:00", "gap_minutes": 30.059901333333336}, {"start": "2025-08-01 01:04:42.413091+00:00", "end": "2025-08-01 01:34:44.524431+00:00", "gap_minutes": 30.035189}, {"start": "2025-08-01 01:34:44.524431+00:00", "end": "2025-08-01 02:05:06.291637+00:00", "gap_minutes": 30.362786766666666}], "success_rate": 100.0}, "ADAUSDT": {"type": "analysis", "status": "❌ 严重缺失", "severity": "high", "expected_count": 192, "actual_count": 72, "missing_count": 120, "failed_count": 0, "latest_prediction": "2025-08-01 02:06:08.376500+00:00", "latest_gap_minutes": 17.07017505, "large_gaps": [{"start": "2025-07-30 16:03:24.234702+00:00", "end": "2025-07-30 16:23:40.469770+00:00", "gap_minutes": 20.270584466666666}, {"start": "2025-07-30 16:23:40.469770+00:00", "end": "2025-07-30 16:49:41.613538+00:00", "gap_minutes": 26.019062799999997}, {"start": "2025-07-30 16:49:41.613538+00:00", "end": "2025-07-30 17:19:32.174327+00:00", "gap_minutes": 29.842679816666667}, {"start": "2025-07-30 17:19:32.174327+00:00", "end": "2025-07-30 17:49:29.912145+00:00", "gap_minutes": 29.962296966666667}, {"start": "2025-07-30 17:49:29.912145+00:00", "end": "2025-07-30 18:19:24.512866+00:00", "gap_minutes": 29.910012016666666}, {"start": "2025-07-30 18:19:24.512866+00:00", "end": "2025-07-30 18:49:21.431002+00:00", "gap_minutes": 29.9486356}, {"start": "2025-07-30 18:49:21.431002+00:00", "end": "2025-07-30 19:19:24.863269+00:00", "gap_minutes": 30.057204449999997}, {"start": "2025-07-30 19:19:24.863269+00:00", "end": "2025-07-30 19:49:21.324341+00:00", "gap_minutes": 29.941017866666666}, {"start": "2025-07-30 19:49:21.324341+00:00", "end": "2025-07-30 20:19:22.184355+00:00", "gap_minutes": 30.014333566666668}, {"start": "2025-07-30 20:19:22.184355+00:00", "end": "2025-07-30 20:49:18.287039+00:00", "gap_minutes": 29.93504473333333}, {"start": "2025-07-30 20:49:18.287039+00:00", "end": "2025-07-30 21:19:22.366154+00:00", "gap_minutes": 30.06798525}, {"start": "2025-07-30 21:19:22.366154+00:00", "end": "2025-07-30 21:49:17.064478+00:00", "gap_minutes": 29.911638733333334}, {"start": "2025-07-30 21:49:17.064478+00:00", "end": "2025-07-30 22:19:15.059239+00:00", "gap_minutes": 29.96657935}, {"start": "2025-07-30 22:19:15.059239+00:00", "end": "2025-07-30 22:49:19.245550+00:00", "gap_minutes": 30.06977185}, {"start": "2025-07-30 22:49:19.245550+00:00", "end": "2025-07-30 23:19:17.273186+00:00", "gap_minutes": 29.96712726666667}, {"start": "2025-07-30 23:19:17.273186+00:00", "end": "2025-07-30 23:49:14.061525+00:00", "gap_minutes": 29.946472316666664}, {"start": "2025-07-30 23:49:14.061525+00:00", "end": "2025-07-31 00:19:20.618914+00:00", "gap_minutes": 30.109289816666667}, {"start": "2025-07-31 00:19:20.618914+00:00", "end": "2025-07-31 00:49:21.401740+00:00", "gap_minutes": 30.013047099999998}, {"start": "2025-07-31 00:49:21.401740+00:00", "end": "2025-07-31 01:19:27.230085+00:00", "gap_minutes": 30.09713908333333}, {"start": "2025-07-31 01:19:27.230085+00:00", "end": "2025-07-31 01:49:18.239329+00:00", "gap_minutes": 29.85015406666667}, {"start": "2025-07-31 01:49:18.239329+00:00", "end": "2025-07-31 02:19:28.464647+00:00", "gap_minutes": 30.170421966666666}, {"start": "2025-07-31 02:21:11.823237+00:00", "end": "2025-07-31 02:49:23.522228+00:00", "gap_minutes": 28.19498318333333}, {"start": "2025-07-31 02:49:23.522228+00:00", "end": "2025-07-31 03:13:40.199141+00:00", "gap_minutes": 24.27794855}, {"start": "2025-07-31 03:13:40.199141+00:00", "end": "2025-07-31 03:34:29.227805+00:00", "gap_minutes": 20.8171444}, {"start": "2025-07-31 03:34:29.227805+00:00", "end": "2025-07-31 04:04:25.803226+00:00", "gap_minutes": 29.942923683333333}, {"start": "2025-07-31 04:04:25.803226+00:00", "end": "2025-07-31 04:34:15.611167+00:00", "gap_minutes": 29.83013235}, {"start": "2025-07-31 04:34:15.611167+00:00", "end": "2025-07-31 05:04:25.688973+00:00", "gap_minutes": 30.167963433333334}, {"start": "2025-07-31 05:04:25.688973+00:00", "end": "2025-07-31 05:34:32.839167+00:00", "gap_minutes": 30.1191699}, {"start": "2025-07-31 05:34:32.839167+00:00", "end": "2025-07-31 06:05:00.671645+00:00", "gap_minutes": 30.463874633333333}, {"start": "2025-07-31 06:05:00.671645+00:00", "end": "2025-07-31 06:34:56.396263+00:00", "gap_minutes": 29.928743633333333}, {"start": "2025-07-31 06:34:56.396263+00:00", "end": "2025-07-31 07:05:19.629042+00:00", "gap_minutes": 30.38721298333333}, {"start": "2025-07-31 07:05:19.629042+00:00", "end": "2025-07-31 07:34:49.959777+00:00", "gap_minutes": 29.50551225}, {"start": "2025-07-31 07:34:49.959777+00:00", "end": "2025-07-31 08:05:48.278605+00:00", "gap_minutes": 30.971980466666665}, {"start": "2025-07-31 08:05:48.278605+00:00", "end": "2025-07-31 08:34:52.018501+00:00", "gap_minutes": 29.0623316}, {"start": "2025-07-31 08:34:52.018501+00:00", "end": "2025-07-31 09:05:02.829512+00:00", "gap_minutes": 30.180183516666666}, {"start": "2025-07-31 09:05:02.829512+00:00", "end": "2025-07-31 09:34:52.307065+00:00", "gap_minutes": 29.824625883333333}, {"start": "2025-07-31 09:34:52.307065+00:00", "end": "2025-07-31 10:04:45.408241+00:00", "gap_minutes": 29.8850196}, {"start": "2025-07-31 10:04:45.408241+00:00", "end": "2025-07-31 10:34:53.188683+00:00", "gap_minutes": 30.129674033333334}, {"start": "2025-07-31 10:34:53.188683+00:00", "end": "2025-07-31 11:04:37.702258+00:00", "gap_minutes": 29.741892916666664}, {"start": "2025-07-31 11:04:37.702258+00:00", "end": "2025-07-31 11:34:43.385091+00:00", "gap_minutes": 30.094713883333334}, {"start": "2025-07-31 11:34:43.385091+00:00", "end": "2025-07-31 12:05:01.484703+00:00", "gap_minutes": 30.3016602}, {"start": "2025-07-31 12:05:01.484703+00:00", "end": "2025-07-31 12:35:01.582422+00:00", "gap_minutes": 30.00162865}, {"start": "2025-07-31 12:35:01.582422+00:00", "end": "2025-07-31 13:05:41.046603+00:00", "gap_minutes": 30.65773635}, {"start": "2025-07-31 13:05:41.046603+00:00", "end": "2025-07-31 13:34:56.794797+00:00", "gap_minutes": 29.2624699}, {"start": "2025-07-31 13:34:56.794797+00:00", "end": "2025-07-31 14:05:35.567356+00:00", "gap_minutes": 30.646209316666667}, {"start": "2025-07-31 14:05:35.567356+00:00", "end": "2025-07-31 14:35:02.776594+00:00", "gap_minutes": 29.4534873}, {"start": "2025-07-31 14:35:02.776594+00:00", "end": "2025-07-31 15:05:18.626345+00:00", "gap_minutes": 30.264162516666666}, {"start": "2025-07-31 15:05:18.626345+00:00", "end": "2025-07-31 15:36:10.275357+00:00", "gap_minutes": 30.860816866666667}, {"start": "2025-07-31 15:36:10.275357+00:00", "end": "2025-07-31 16:06:50.933690+00:00", "gap_minutes": 30.677638883333334}, {"start": "2025-07-31 16:06:50.933690+00:00", "end": "2025-07-31 16:36:38.712999+00:00", "gap_minutes": 29.796321816666666}, {"start": "2025-07-31 16:36:38.712999+00:00", "end": "2025-07-31 17:06:18.278227+00:00", "gap_minutes": 29.659420466666667}, {"start": "2025-07-31 17:06:18.278227+00:00", "end": "2025-07-31 17:36:18.892481+00:00", "gap_minutes": 30.010237566666667}, {"start": "2025-07-31 17:36:18.892481+00:00", "end": "2025-07-31 18:06:18.310888+00:00", "gap_minutes": 29.99030678333333}, {"start": "2025-07-31 18:06:18.310888+00:00", "end": "2025-07-31 18:36:10.302671+00:00", "gap_minutes": 29.866529716666665}, {"start": "2025-07-31 18:36:10.302671+00:00", "end": "2025-07-31 19:06:17.447009+00:00", "gap_minutes": 30.119072300000003}, {"start": "2025-07-31 19:06:17.447009+00:00", "end": "2025-07-31 19:36:03.426700+00:00", "gap_minutes": 29.766328183333332}, {"start": "2025-07-31 19:36:03.426700+00:00", "end": "2025-07-31 20:05:56.412967+00:00", "gap_minutes": 29.88310445}, {"start": "2025-07-31 20:05:56.412967+00:00", "end": "2025-07-31 20:35:39.759724+00:00", "gap_minutes": 29.72244595}, {"start": "2025-07-31 20:35:39.759724+00:00", "end": "2025-07-31 21:05:30.042265+00:00", "gap_minutes": 29.838042350000002}, {"start": "2025-07-31 21:05:30.042265+00:00", "end": "2025-07-31 21:35:22.762316+00:00", "gap_minutes": 29.878667516666667}, {"start": "2025-07-31 21:35:22.762316+00:00", "end": "2025-07-31 22:05:43.243366+00:00", "gap_minutes": 30.341350833333333}, {"start": "2025-07-31 22:05:43.243366+00:00", "end": "2025-07-31 22:35:40.129310+00:00", "gap_minutes": 29.948099066666668}, {"start": "2025-07-31 22:35:40.129310+00:00", "end": "2025-07-31 23:05:22.757781+00:00", "gap_minutes": 29.710474516666668}, {"start": "2025-07-31 23:05:22.757781+00:00", "end": "2025-07-31 23:35:25.912432+00:00", "gap_minutes": 30.052577516666666}, {"start": "2025-07-31 23:35:25.912432+00:00", "end": "2025-08-01 00:05:44.264415+00:00", "gap_minutes": 30.305866383333335}, {"start": "2025-08-01 00:05:44.264415+00:00", "end": "2025-08-01 00:35:26.269724+00:00", "gap_minutes": 29.70008848333333}, {"start": "2025-08-01 00:35:26.269724+00:00", "end": "2025-08-01 01:05:30.770277+00:00", "gap_minutes": 30.07500921666667}, {"start": "2025-08-01 01:05:30.770277+00:00", "end": "2025-08-01 01:35:31.079832+00:00", "gap_minutes": 30.005159250000002}, {"start": "2025-08-01 01:35:31.079832+00:00", "end": "2025-08-01 02:06:08.376500+00:00", "gap_minutes": 30.62161113333333}], "success_rate": 100.0}, "XRPUSDT": {"type": "analysis", "status": "❌ 严重缺失", "severity": "high", "expected_count": 192, "actual_count": 72, "missing_count": 120, "failed_count": 2, "latest_prediction": "2025-08-01 02:07:44.620163+00:00", "latest_gap_minutes": 15.466268316666666, "large_gaps": [{"start": "2025-07-30 16:03:49.724356+00:00", "end": "2025-07-30 16:24:07.454083+00:00", "gap_minutes": 20.295495449999997}, {"start": "2025-07-30 16:24:07.454083+00:00", "end": "2025-07-30 16:50:04.927598+00:00", "gap_minutes": 25.957891916666664}, {"start": "2025-07-30 16:50:04.927598+00:00", "end": "2025-07-30 17:20:03.195937+00:00", "gap_minutes": 29.971138983333333}, {"start": "2025-07-30 17:20:03.195937+00:00", "end": "2025-07-30 17:50:00.254350+00:00", "gap_minutes": 29.95097355}, {"start": "2025-07-30 17:50:00.254350+00:00", "end": "2025-07-30 18:19:50.712533+00:00", "gap_minutes": 29.840969716666667}, {"start": "2025-07-30 18:19:50.712533+00:00", "end": "2025-07-30 18:49:45.325215+00:00", "gap_minutes": 29.910211366666665}, {"start": "2025-07-30 18:49:45.325215+00:00", "end": "2025-07-30 19:19:52.354034+00:00", "gap_minutes": 30.117146983333335}, {"start": "2025-07-30 19:19:52.354034+00:00", "end": "2025-07-30 19:49:47.747524+00:00", "gap_minutes": 29.923224833333332}, {"start": "2025-07-30 19:49:47.747524+00:00", "end": "2025-07-30 20:19:47.024556+00:00", "gap_minutes": 29.987950533333333}, {"start": "2025-07-30 20:19:47.024556+00:00", "end": "2025-07-30 20:49:43.507639+00:00", "gap_minutes": 29.94138471666667}, {"start": "2025-07-30 20:49:43.507639+00:00", "end": "2025-07-30 21:19:45.593930+00:00", "gap_minutes": 30.03477151666667}, {"start": "2025-07-30 21:19:45.593930+00:00", "end": "2025-07-30 21:49:42.145284+00:00", "gap_minutes": 29.942522566666664}, {"start": "2025-07-30 21:49:42.145284+00:00", "end": "2025-07-30 22:19:39.800569+00:00", "gap_minutes": 29.960921416666668}, {"start": "2025-07-30 22:19:39.800569+00:00", "end": "2025-07-30 22:49:42.203729+00:00", "gap_minutes": 30.040052666666668}, {"start": "2025-07-30 22:49:42.203729+00:00", "end": "2025-07-30 23:19:42.312708+00:00", "gap_minutes": 30.00181631666667}, {"start": "2025-07-30 23:19:42.312708+00:00", "end": "2025-07-30 23:49:35.845879+00:00", "gap_minutes": 29.892219516666668}, {"start": "2025-07-30 23:49:35.845879+00:00", "end": "2025-07-31 00:19:44.323660+00:00", "gap_minutes": 30.14129635}, {"start": "2025-07-31 00:19:44.323660+00:00", "end": "2025-07-31 00:49:46.823872+00:00", "gap_minutes": 30.0416702}, {"start": "2025-07-31 00:49:46.823872+00:00", "end": "2025-07-31 01:19:57.515247+00:00", "gap_minutes": 30.178189583333335}, {"start": "2025-07-31 01:19:57.515247+00:00", "end": "2025-07-31 01:49:46.000606+00:00", "gap_minutes": 29.808089316666667}, {"start": "2025-07-31 01:49:46.000606+00:00", "end": "2025-07-31 02:19:54.450185+00:00", "gap_minutes": 30.14082631666667}, {"start": "2025-07-31 02:21:36.269264+00:00", "end": "2025-07-31 02:49:50.772880+00:00", "gap_minutes": 28.241726933333332}, {"start": "2025-07-31 02:49:50.772880+00:00", "end": "2025-07-31 03:14:21.408737+00:00", "gap_minutes": 24.510597616666665}, {"start": "2025-07-31 03:14:21.408737+00:00", "end": "2025-07-31 03:34:57.524540+00:00", "gap_minutes": 20.60193005}, {"start": "2025-07-31 03:34:57.524540+00:00", "end": "2025-07-31 04:04:52.500795+00:00", "gap_minutes": 29.91627091666667}, {"start": "2025-07-31 04:04:52.500795+00:00", "end": "2025-07-31 04:34:38.210207+00:00", "gap_minutes": 29.76182353333333}, {"start": "2025-07-31 04:34:38.210207+00:00", "end": "2025-07-31 05:04:53.653452+00:00", "gap_minutes": 30.257387416666667}, {"start": "2025-07-31 05:04:53.653452+00:00", "end": "2025-07-31 05:34:59.474206+00:00", "gap_minutes": 30.097012566666667}, {"start": "2025-07-31 05:34:59.474206+00:00", "end": "2025-07-31 06:05:40.453444+00:00", "gap_minutes": 30.682987299999997}, {"start": "2025-07-31 06:05:40.453444+00:00", "end": "2025-07-31 06:35:33.422662+00:00", "gap_minutes": 29.8828203}, {"start": "2025-07-31 06:35:33.422662+00:00", "end": "2025-07-31 07:05:57.889754+00:00", "gap_minutes": 30.407784866666667}, {"start": "2025-07-31 07:05:57.889754+00:00", "end": "2025-07-31 07:35:27.455160+00:00", "gap_minutes": 29.492756766666666}, {"start": "2025-07-31 07:35:27.455160+00:00", "end": "2025-07-31 08:06:50.539906+00:00", "gap_minutes": 31.384745766666665}, {"start": "2025-07-31 08:06:50.539906+00:00", "end": "2025-07-31 08:35:23.448546+00:00", "gap_minutes": 28.548477333333334}, {"start": "2025-07-31 08:35:23.448546+00:00", "end": "2025-07-31 09:05:42.356443+00:00", "gap_minutes": 30.31513161666667}, {"start": "2025-07-31 09:05:42.356443+00:00", "end": "2025-07-31 09:35:36.118922+00:00", "gap_minutes": 29.896041316666665}, {"start": "2025-07-31 09:35:36.118922+00:00", "end": "2025-07-31 10:05:17.900570+00:00", "gap_minutes": 29.696360799999997}, {"start": "2025-07-31 10:05:17.900570+00:00", "end": "2025-07-31 10:35:27.136982+00:00", "gap_minutes": 30.1539402}, {"start": "2025-07-31 10:35:27.136982+00:00", "end": "2025-07-31 11:05:08.999260+00:00", "gap_minutes": 29.697704633333334}, {"start": "2025-07-31 11:05:08.999260+00:00", "end": "2025-07-31 11:35:16.803134+00:00", "gap_minutes": 30.130064566666665}, {"start": "2025-07-31 11:35:16.803134+00:00", "end": "2025-07-31 12:05:37.952484+00:00", "gap_minutes": 30.352489166666665}, {"start": "2025-07-31 12:05:37.952484+00:00", "end": "2025-07-31 12:35:43.755040+00:00", "gap_minutes": 30.096709266666668}, {"start": "2025-07-31 12:35:43.755040+00:00", "end": "2025-07-31 13:06:17.916492+00:00", "gap_minutes": 30.569357533333335}, {"start": "2025-07-31 13:06:17.916492+00:00", "end": "2025-07-31 13:35:26.953792+00:00", "gap_minutes": 29.150621666666666}, {"start": "2025-07-31 13:35:26.953792+00:00", "end": "2025-07-31 14:06:35.018771+00:00", "gap_minutes": 31.134416316666666}, {"start": "2025-07-31 14:06:35.018771+00:00", "end": "2025-07-31 14:35:37.711350+00:00", "gap_minutes": 29.044876316666667}, {"start": "2025-07-31 14:35:37.711350+00:00", "end": "2025-07-31 15:06:15.982872+00:00", "gap_minutes": 30.6378587}, {"start": "2025-07-31 15:06:15.982872+00:00", "end": "2025-07-31 15:37:17.214706+00:00", "gap_minutes": 31.020530566666665}, {"start": "2025-07-31 15:37:17.214706+00:00", "end": "2025-07-31 16:09:37.822615+00:00", "gap_minutes": 32.34346515}, {"start": "2025-07-31 16:09:37.822615+00:00", "end": "2025-07-31 16:37:33.984870+00:00", "gap_minutes": 27.936037583333334}, {"start": "2025-07-31 16:37:33.984870+00:00", "end": "2025-07-31 17:07:36.766223+00:00", "gap_minutes": 30.046355883333334}, {"start": "2025-07-31 17:07:36.766223+00:00", "end": "2025-07-31 17:37:23.942040+00:00", "gap_minutes": 29.786263616666666}, {"start": "2025-07-31 17:37:23.942040+00:00", "end": "2025-07-31 18:07:37.705141+00:00", "gap_minutes": 30.229385016666665}, {"start": "2025-07-31 18:07:37.705141+00:00", "end": "2025-07-31 18:37:02.928475+00:00", "gap_minutes": 29.4203889}, {"start": "2025-07-31 18:37:02.928475+00:00", "end": "2025-07-31 19:07:12.862496+00:00", "gap_minutes": 30.165567016666667}, {"start": "2025-07-31 19:07:12.862496+00:00", "end": "2025-07-31 19:37:07.647493+00:00", "gap_minutes": 29.913083283333332}, {"start": "2025-07-31 19:37:07.647493+00:00", "end": "2025-07-31 20:07:00.331658+00:00", "gap_minutes": 29.878069416666666}, {"start": "2025-07-31 20:07:00.331658+00:00", "end": "2025-07-31 20:36:31.900015+00:00", "gap_minutes": 29.526139283333336}, {"start": "2025-07-31 20:36:31.900015+00:00", "end": "2025-07-31 21:06:19.295669+00:00", "gap_minutes": 29.789927566666666}, {"start": "2025-07-31 21:06:19.295669+00:00", "end": "2025-07-31 21:36:08.862075+00:00", "gap_minutes": 29.826106766666665}, {"start": "2025-07-31 21:36:08.862075+00:00", "end": "2025-07-31 22:06:34.577855+00:00", "gap_minutes": 30.428596333333335}, {"start": "2025-07-31 22:06:34.577855+00:00", "end": "2025-07-31 22:36:30.590400+00:00", "gap_minutes": 29.93354241666667}, {"start": "2025-07-31 22:36:30.590400+00:00", "end": "2025-07-31 23:06:10.504787+00:00", "gap_minutes": 29.665239783333334}, {"start": "2025-07-31 23:06:10.504787+00:00", "end": "2025-07-31 23:36:16.280209+00:00", "gap_minutes": 30.096257033333334}, {"start": "2025-07-31 23:36:16.280209+00:00", "end": "2025-08-01 00:06:31.095435+00:00", "gap_minutes": 30.246920433333333}, {"start": "2025-08-01 00:06:31.095435+00:00", "end": "2025-08-01 00:36:15.583958+00:00", "gap_minutes": 29.741475383333334}, {"start": "2025-08-01 00:36:15.583958+00:00", "end": "2025-08-01 01:06:15.596535+00:00", "gap_minutes": 30.000209616666666}, {"start": "2025-08-01 01:06:15.596535+00:00", "end": "2025-08-01 01:36:26.959149+00:00", "gap_minutes": 30.1893769}, {"start": "2025-08-01 01:36:26.959149+00:00", "end": "2025-08-01 02:07:44.620163+00:00", "gap_minutes": 31.294350233333333}], "success_rate": 97.2}, "SOLUSDT": {"type": "analysis", "status": "❌ 严重缺失", "severity": "high", "expected_count": 192, "actual_count": 70, "missing_count": 122, "failed_count": 0, "latest_prediction": "2025-08-01 02:09:12.762854+00:00", "latest_gap_minutes": 13.997335233333333, "large_gaps": [{"start": "2025-07-30 16:04:18.045276+00:00", "end": "2025-07-30 16:50:33.439947+00:00", "gap_minutes": 46.25657785}, {"start": "2025-07-30 16:50:33.439947+00:00", "end": "2025-07-30 17:20:33.714623+00:00", "gap_minutes": 30.004577933333334}, {"start": "2025-07-30 17:20:33.714623+00:00", "end": "2025-07-30 17:50:27.788372+00:00", "gap_minutes": 29.90122915}, {"start": "2025-07-30 17:50:27.788372+00:00", "end": "2025-07-30 18:20:17.060489+00:00", "gap_minutes": 29.82120195}, {"start": "2025-07-30 18:20:17.060489+00:00", "end": "2025-07-30 18:50:10.830061+00:00", "gap_minutes": 29.896159533333332}, {"start": "2025-07-30 18:50:10.830061+00:00", "end": "2025-07-30 19:20:23.774680+00:00", "gap_minutes": 30.21574365}, {"start": "2025-07-30 19:20:23.774680+00:00", "end": "2025-07-30 19:50:15.332227+00:00", "gap_minutes": 29.85929245}, {"start": "2025-07-30 19:50:15.332227+00:00", "end": "2025-07-30 20:20:13.112618+00:00", "gap_minutes": 29.963006516666667}, {"start": "2025-07-30 20:20:13.112618+00:00", "end": "2025-07-30 20:50:10.266555+00:00", "gap_minutes": 29.952565616666668}, {"start": "2025-07-30 20:50:10.266555+00:00", "end": "2025-07-30 21:20:14.317226+00:00", "gap_minutes": 30.067511183333334}, {"start": "2025-07-30 21:20:14.317226+00:00", "end": "2025-07-30 21:50:08.016558+00:00", "gap_minutes": 29.894988866666665}, {"start": "2025-07-30 21:50:08.016558+00:00", "end": "2025-07-30 22:20:03.000288+00:00", "gap_minutes": 29.9163955}, {"start": "2025-07-30 22:20:03.000288+00:00", "end": "2025-07-30 22:50:06.664001+00:00", "gap_minutes": 30.06106188333333}, {"start": "2025-07-30 22:50:06.664001+00:00", "end": "2025-07-30 23:20:08.815192+00:00", "gap_minutes": 30.035853183333334}, {"start": "2025-07-30 23:20:08.815192+00:00", "end": "2025-07-30 23:50:03.049315+00:00", "gap_minutes": 29.90390205}, {"start": "2025-07-30 23:50:03.049315+00:00", "end": "2025-07-31 00:20:10.191254+00:00", "gap_minutes": 30.11903231666667}, {"start": "2025-07-31 00:20:10.191254+00:00", "end": "2025-07-31 00:50:11.380594+00:00", "gap_minutes": 30.01982233333333}, {"start": "2025-07-31 00:50:11.380594+00:00", "end": "2025-07-31 01:20:26.176435+00:00", "gap_minutes": 30.246597350000002}, {"start": "2025-07-31 01:20:26.176435+00:00", "end": "2025-07-31 01:50:11.023334+00:00", "gap_minutes": 29.747448316666667}, {"start": "2025-07-31 01:50:11.023334+00:00", "end": "2025-07-31 02:20:21.549050+00:00", "gap_minutes": 30.1754286}, {"start": "2025-07-31 02:20:21.549050+00:00", "end": "2025-07-31 02:50:18.763536+00:00", "gap_minutes": 29.95357476666667}, {"start": "2025-07-31 02:50:18.763536+00:00", "end": "2025-07-31 03:15:04.875842+00:00", "gap_minutes": 24.768538433333333}, {"start": "2025-07-31 03:15:04.875842+00:00", "end": "2025-07-31 03:35:28.902513+00:00", "gap_minutes": 20.400444516666667}, {"start": "2025-07-31 03:35:28.902513+00:00", "end": "2025-07-31 04:05:19.375095+00:00", "gap_minutes": 29.8412097}, {"start": "2025-07-31 04:05:19.375095+00:00", "end": "2025-07-31 04:35:02.266620+00:00", "gap_minutes": 29.71485875}, {"start": "2025-07-31 04:35:02.266620+00:00", "end": "2025-07-31 05:05:24.009504+00:00", "gap_minutes": 30.3623814}, {"start": "2025-07-31 05:05:24.009504+00:00", "end": "2025-07-31 05:35:31.137512+00:00", "gap_minutes": 30.11880013333333}, {"start": "2025-07-31 05:35:31.137512+00:00", "end": "2025-07-31 06:06:14.637049+00:00", "gap_minutes": 30.724992283333332}, {"start": "2025-07-31 06:06:14.637049+00:00", "end": "2025-07-31 06:36:06.552676+00:00", "gap_minutes": 29.86526045}, {"start": "2025-07-31 06:36:06.552676+00:00", "end": "2025-07-31 07:06:43.639022+00:00", "gap_minutes": 30.618105766666666}, {"start": "2025-07-31 07:06:43.639022+00:00", "end": "2025-07-31 07:36:00.231364+00:00", "gap_minutes": 29.276539033333332}, {"start": "2025-07-31 07:36:00.231364+00:00", "end": "2025-07-31 08:07:41.466168+00:00", "gap_minutes": 31.687246733333332}, {"start": "2025-07-31 08:07:41.466168+00:00", "end": "2025-07-31 08:35:58.481991+00:00", "gap_minutes": 28.28359705}, {"start": "2025-07-31 08:35:58.481991+00:00", "end": "2025-07-31 09:06:15.733939+00:00", "gap_minutes": 30.28753246666667}, {"start": "2025-07-31 09:06:15.733939+00:00", "end": "2025-07-31 09:36:09.497858+00:00", "gap_minutes": 29.896065316666668}, {"start": "2025-07-31 09:36:09.497858+00:00", "end": "2025-07-31 10:05:54.072165+00:00", "gap_minutes": 29.74290511666667}, {"start": "2025-07-31 10:05:54.072165+00:00", "end": "2025-07-31 10:36:00.567506+00:00", "gap_minutes": 30.108255683333333}, {"start": "2025-07-31 10:36:00.567506+00:00", "end": "2025-07-31 11:05:37.904488+00:00", "gap_minutes": 29.622283033333336}, {"start": "2025-07-31 11:05:37.904488+00:00", "end": "2025-07-31 11:35:50.247517+00:00", "gap_minutes": 30.205717149999998}, {"start": "2025-07-31 11:35:50.247517+00:00", "end": "2025-07-31 12:06:10.925215+00:00", "gap_minutes": 30.3446283}, {"start": "2025-07-31 12:06:10.925215+00:00", "end": "2025-07-31 12:36:22.399592+00:00", "gap_minutes": 30.191239616666667}, {"start": "2025-07-31 12:36:22.399592+00:00", "end": "2025-07-31 13:07:28.158836+00:00", "gap_minutes": 31.095987400000002}, {"start": "2025-07-31 13:07:28.158836+00:00", "end": "2025-07-31 13:36:03.242411+00:00", "gap_minutes": 28.584726250000003}, {"start": "2025-07-31 13:36:03.242411+00:00", "end": "2025-07-31 14:07:57.523141+00:00", "gap_minutes": 31.90467883333333}, {"start": "2025-07-31 14:07:57.523141+00:00", "end": "2025-07-31 14:36:18.631310+00:00", "gap_minutes": 28.35180281666667}, {"start": "2025-07-31 14:36:18.631310+00:00", "end": "2025-07-31 15:07:41.172548+00:00", "gap_minutes": 31.3756873}, {"start": "2025-07-31 15:07:41.172548+00:00", "end": "2025-07-31 15:38:40.538306+00:00", "gap_minutes": 30.989429299999998}, {"start": "2025-07-31 15:38:40.538306+00:00", "end": "2025-07-31 16:11:24.790941+00:00", "gap_minutes": 32.73754391666667}, {"start": "2025-07-31 16:11:24.790941+00:00", "end": "2025-07-31 16:38:38.667399+00:00", "gap_minutes": 27.2312743}, {"start": "2025-07-31 16:38:38.667399+00:00", "end": "2025-07-31 17:08:51.278686+00:00", "gap_minutes": 30.210188116666664}, {"start": "2025-07-31 17:08:51.278686+00:00", "end": "2025-07-31 17:38:28.956369+00:00", "gap_minutes": 29.627961383333332}, {"start": "2025-07-31 17:38:28.956369+00:00", "end": "2025-07-31 18:08:47.516388+00:00", "gap_minutes": 30.30933365}, {"start": "2025-07-31 18:08:47.516388+00:00", "end": "2025-07-31 18:38:04.608909+00:00", "gap_minutes": 29.28487535}, {"start": "2025-07-31 18:38:04.608909+00:00", "end": "2025-07-31 19:08:17.059849+00:00", "gap_minutes": 30.207515666666666}, {"start": "2025-07-31 19:08:17.059849+00:00", "end": "2025-07-31 19:38:10.875355+00:00", "gap_minutes": 29.896925099999997}, {"start": "2025-07-31 19:38:10.875355+00:00", "end": "2025-07-31 20:08:05.584485+00:00", "gap_minutes": 29.91181883333333}, {"start": "2025-07-31 20:08:05.584485+00:00", "end": "2025-07-31 20:37:25.090743+00:00", "gap_minutes": 29.3251043}, {"start": "2025-07-31 20:37:25.090743+00:00", "end": "2025-07-31 21:11:36.274164+00:00", "gap_minutes": 34.18639035}, {"start": "2025-07-31 21:11:36.274164+00:00", "end": "2025-07-31 21:36:58.587359+00:00", "gap_minutes": 25.371886583333332}, {"start": "2025-07-31 21:36:58.587359+00:00", "end": "2025-07-31 22:07:30.729971+00:00", "gap_minutes": 30.5357102}, {"start": "2025-07-31 22:07:30.729971+00:00", "end": "2025-07-31 22:37:26.664969+00:00", "gap_minutes": 29.932249966666667}, {"start": "2025-07-31 22:37:26.664969+00:00", "end": "2025-07-31 23:07:00.811159+00:00", "gap_minutes": 29.569103166666665}, {"start": "2025-07-31 23:07:00.811159+00:00", "end": "2025-07-31 23:37:04.147754+00:00", "gap_minutes": 30.055609916666665}, {"start": "2025-07-31 23:37:04.147754+00:00", "end": "2025-08-01 00:07:42.524557+00:00", "gap_minutes": 30.639613383333334}, {"start": "2025-08-01 00:07:42.524557+00:00", "end": "2025-08-01 00:37:07.552657+00:00", "gap_minutes": 29.417135}, {"start": "2025-08-01 00:37:07.552657+00:00", "end": "2025-08-01 01:07:00.658542+00:00", "gap_minutes": 29.885098083333332}, {"start": "2025-08-01 01:07:00.658542+00:00", "end": "2025-08-01 01:37:22.897665+00:00", "gap_minutes": 30.37065205}, {"start": "2025-08-01 01:37:22.897665+00:00", "end": "2025-08-01 02:09:12.762854+00:00", "gap_minutes": 31.831086483333333}], "success_rate": 100.0}, "DOGEUSDT": {"type": "analysis", "status": "❌ 严重缺失", "severity": "high", "expected_count": 192, "actual_count": 70, "missing_count": 122, "failed_count": 0, "latest_prediction": "2025-08-01 02:10:32.318317+00:00", "latest_gap_minutes": 12.67151705, "large_gaps": [{"start": "2025-07-30 16:04:48.984501+00:00", "end": "2025-07-30 16:50:57.485836+00:00", "gap_minutes": 46.141688916666666}, {"start": "2025-07-30 16:50:57.485836+00:00", "end": "2025-07-30 17:21:00.752422+00:00", "gap_minutes": 30.0544431}, {"start": "2025-07-30 17:21:00.752422+00:00", "end": "2025-07-30 17:50:53.948083+00:00", "gap_minutes": 29.88659435}, {"start": "2025-07-30 17:50:53.948083+00:00", "end": "2025-07-30 18:20:41.378600+00:00", "gap_minutes": 29.790508616666667}, {"start": "2025-07-30 18:20:41.378600+00:00", "end": "2025-07-30 18:50:37.546808+00:00", "gap_minutes": 29.9361368}, {"start": "2025-07-30 18:50:37.546808+00:00", "end": "2025-07-30 19:20:51.339087+00:00", "gap_minutes": 30.229871316666667}, {"start": "2025-07-30 19:20:51.339087+00:00", "end": "2025-07-30 19:50:41.073753+00:00", "gap_minutes": 29.828911100000003}, {"start": "2025-07-30 19:50:41.073753+00:00", "end": "2025-07-30 20:20:37.646245+00:00", "gap_minutes": 29.942874866666667}, {"start": "2025-07-30 20:20:37.646245+00:00", "end": "2025-07-30 20:50:36.020014+00:00", "gap_minutes": 29.97289615}, {"start": "2025-07-30 20:50:36.020014+00:00", "end": "2025-07-30 21:20:39.832702+00:00", "gap_minutes": 30.0635448}, {"start": "2025-07-30 21:20:39.832702+00:00", "end": "2025-07-30 21:50:31.303553+00:00", "gap_minutes": 29.857847516666666}, {"start": "2025-07-30 21:50:31.303553+00:00", "end": "2025-07-30 22:20:27.454590+00:00", "gap_minutes": 29.935850616666666}, {"start": "2025-07-30 22:20:27.454590+00:00", "end": "2025-07-30 22:50:31.591288+00:00", "gap_minutes": 30.068944966666667}, {"start": "2025-07-30 22:50:31.591288+00:00", "end": "2025-07-30 23:20:32.026194+00:00", "gap_minutes": 30.007248433333334}, {"start": "2025-07-30 23:20:32.026194+00:00", "end": "2025-07-30 23:50:29.409131+00:00", "gap_minutes": 29.956382283333333}, {"start": "2025-07-30 23:50:29.409131+00:00", "end": "2025-07-31 00:20:37.157110+00:00", "gap_minutes": 30.12913298333333}, {"start": "2025-07-31 00:20:37.157110+00:00", "end": "2025-07-31 00:50:35.179807+00:00", "gap_minutes": 29.967044950000002}, {"start": "2025-07-31 00:50:35.179807+00:00", "end": "2025-07-31 01:20:53.900577+00:00", "gap_minutes": 30.31201283333333}, {"start": "2025-07-31 01:20:53.900577+00:00", "end": "2025-07-31 01:50:34.766191+00:00", "gap_minutes": 29.681093566666668}, {"start": "2025-07-31 01:50:34.766191+00:00", "end": "2025-07-31 02:20:50.305648+00:00", "gap_minutes": 30.25899095}, {"start": "2025-07-31 02:20:50.305648+00:00", "end": "2025-07-31 02:50:45.995259+00:00", "gap_minutes": 29.928160183333333}, {"start": "2025-07-31 02:50:45.995259+00:00", "end": "2025-07-31 03:15:51.367067+00:00", "gap_minutes": 25.089530133333334}, {"start": "2025-07-31 03:15:51.367067+00:00", "end": "2025-07-31 03:35:53.815625+00:00", "gap_minutes": 20.0408093}, {"start": "2025-07-31 03:35:53.815625+00:00", "end": "2025-07-31 04:05:45.149069+00:00", "gap_minutes": 29.855557400000002}, {"start": "2025-07-31 04:05:45.149069+00:00", "end": "2025-07-31 04:35:25.814943+00:00", "gap_minutes": 29.677764566666667}, {"start": "2025-07-31 04:35:25.814943+00:00", "end": "2025-07-31 05:05:49.495160+00:00", "gap_minutes": 30.394670283333333}, {"start": "2025-07-31 05:05:49.495160+00:00", "end": "2025-07-31 05:36:02.640559+00:00", "gap_minutes": 30.219089983333333}, {"start": "2025-07-31 05:36:02.640559+00:00", "end": "2025-07-31 06:06:54.523459+00:00", "gap_minutes": 30.864715}, {"start": "2025-07-31 06:06:54.523459+00:00", "end": "2025-07-31 06:36:42.320455+00:00", "gap_minutes": 29.7966166}, {"start": "2025-07-31 06:36:42.320455+00:00", "end": "2025-07-31 07:07:26.605009+00:00", "gap_minutes": 30.738075900000002}, {"start": "2025-07-31 07:07:26.605009+00:00", "end": "2025-07-31 07:36:41.035375+00:00", "gap_minutes": 29.2405061}, {"start": "2025-07-31 07:36:41.035375+00:00", "end": "2025-07-31 08:08:40.317101+00:00", "gap_minutes": 31.988028766666666}, {"start": "2025-07-31 08:08:40.317101+00:00", "end": "2025-07-31 08:36:38.824208+00:00", "gap_minutes": 27.97511845}, {"start": "2025-07-31 08:36:38.824208+00:00", "end": "2025-07-31 09:07:12.673501+00:00", "gap_minutes": 30.564154883333334}, {"start": "2025-07-31 09:07:12.673501+00:00", "end": "2025-07-31 09:36:42.279939+00:00", "gap_minutes": 29.493440633333332}, {"start": "2025-07-31 09:36:42.279939+00:00", "end": "2025-07-31 10:06:28.172742+00:00", "gap_minutes": 29.76488005}, {"start": "2025-07-31 10:06:28.172742+00:00", "end": "2025-07-31 10:36:35.276817+00:00", "gap_minutes": 30.118401249999998}, {"start": "2025-07-31 10:36:35.276817+00:00", "end": "2025-07-31 11:06:08.705192+00:00", "gap_minutes": 29.55713958333333}, {"start": "2025-07-31 11:06:08.705192+00:00", "end": "2025-07-31 11:36:18.606365+00:00", "gap_minutes": 30.16501955}, {"start": "2025-07-31 11:36:18.606365+00:00", "end": "2025-07-31 12:07:15.094760+00:00", "gap_minutes": 30.94147325}, {"start": "2025-07-31 12:07:15.094760+00:00", "end": "2025-07-31 12:37:02.572426+00:00", "gap_minutes": 29.791294433333334}, {"start": "2025-07-31 12:37:02.572426+00:00", "end": "2025-07-31 13:08:21.337492+00:00", "gap_minutes": 31.3127511}, {"start": "2025-07-31 13:08:21.337492+00:00", "end": "2025-07-31 13:36:33.735587+00:00", "gap_minutes": 28.206634916666665}, {"start": "2025-07-31 13:36:33.735587+00:00", "end": "2025-07-31 14:08:54.754049+00:00", "gap_minutes": 32.3503077}, {"start": "2025-07-31 14:08:54.754049+00:00", "end": "2025-07-31 14:36:58.161143+00:00", "gap_minutes": 28.0567849}, {"start": "2025-07-31 14:36:58.161143+00:00", "end": "2025-07-31 15:08:34.086245+00:00", "gap_minutes": 31.598751699999998}, {"start": "2025-07-31 15:08:34.086245+00:00", "end": "2025-07-31 15:39:50.764725+00:00", "gap_minutes": 31.27797466666667}, {"start": "2025-07-31 15:39:50.764725+00:00", "end": "2025-07-31 16:13:14.815749+00:00", "gap_minutes": 33.4008504}, {"start": "2025-07-31 16:13:14.815749+00:00", "end": "2025-07-31 16:39:45.363282+00:00", "gap_minutes": 26.50912555}, {"start": "2025-07-31 16:39:45.363282+00:00", "end": "2025-07-31 17:10:20.853425+00:00", "gap_minutes": 30.59150238333333}, {"start": "2025-07-31 17:10:20.853425+00:00", "end": "2025-07-31 17:39:31.444027+00:00", "gap_minutes": 29.176510033333333}, {"start": "2025-07-31 17:39:31.444027+00:00", "end": "2025-07-31 18:10:17.039634+00:00", "gap_minutes": 30.759926783333334}, {"start": "2025-07-31 18:10:17.039634+00:00", "end": "2025-07-31 18:39:00.708041+00:00", "gap_minutes": 28.72780678333333}, {"start": "2025-07-31 18:39:00.708041+00:00", "end": "2025-07-31 19:09:21.876980+00:00", "gap_minutes": 30.35281565}, {"start": "2025-07-31 19:09:21.876980+00:00", "end": "2025-07-31 19:39:09.749841+00:00", "gap_minutes": 29.79788101666667}, {"start": "2025-07-31 19:39:09.749841+00:00", "end": "2025-07-31 20:09:08.943560+00:00", "gap_minutes": 29.986561983333335}, {"start": "2025-07-31 20:09:08.943560+00:00", "end": "2025-07-31 20:38:14.863921+00:00", "gap_minutes": 29.098672683333334}, {"start": "2025-07-31 20:38:14.863921+00:00", "end": "2025-07-31 21:12:35.972596+00:00", "gap_minutes": 34.35181125}, {"start": "2025-07-31 21:12:35.972596+00:00", "end": "2025-07-31 21:37:47.958787+00:00", "gap_minutes": 25.19976985}, {"start": "2025-07-31 21:37:47.958787+00:00", "end": "2025-07-31 22:08:20.653264+00:00", "gap_minutes": 30.54490795}, {"start": "2025-07-31 22:08:20.653264+00:00", "end": "2025-07-31 22:38:13.769583+00:00", "gap_minutes": 29.885271983333332}, {"start": "2025-07-31 22:38:13.769583+00:00", "end": "2025-07-31 23:07:51.206543+00:00", "gap_minutes": 29.623949333333332}, {"start": "2025-07-31 23:07:51.206543+00:00", "end": "2025-07-31 23:37:50.823105+00:00", "gap_minutes": 29.993609366666664}, {"start": "2025-07-31 23:37:50.823105+00:00", "end": "2025-08-01 00:08:54.324817+00:00", "gap_minutes": 31.058361866666665}, {"start": "2025-08-01 00:08:54.324817+00:00", "end": "2025-08-01 00:37:59.905718+00:00", "gap_minutes": 29.093015016666666}, {"start": "2025-08-01 00:37:59.905718+00:00", "end": "2025-08-01 01:07:52.640783+00:00", "gap_minutes": 29.878917750000003}, {"start": "2025-08-01 01:07:52.640783+00:00", "end": "2025-08-01 01:38:13.120934+00:00", "gap_minutes": 30.34133585}, {"start": "2025-08-01 01:38:13.120934+00:00", "end": "2025-08-01 02:10:32.318317+00:00", "gap_minutes": 32.319956383333334}], "success_rate": 100.0}}, "stuck_tasks": {"has_stuck_tasks": false}, "repairs": {}}