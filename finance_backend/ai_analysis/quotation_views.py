import requests
import logging
from django.http import HttpResponse, JsonResponse
from django.views.generic import View
from django.conf import settings
import os

logger = logging.getLogger(__name__)

class QuotationProxyView(View):
    """
    代理转发加密货币行情API请求到币安
    此视图接收所有请求并转发到币安API
    """
    def dispatch(self, request, *args, **kwargs):
        # 获取请求路径
        path = kwargs.get('path', '')
        
        # 币安API密钥
        api_key = os.getenv('BINANCE_API_KEY', 'hMhu5yH0vi8EN1kzd9b4POfkCvcORdtIPOwLi6SPdZZvcLuSKzSmIp82fZWyDnC3')
        
        # 构建目标URL - 处理带有binance前缀的路径
        if path.startswith('binance/'):
            # 如果以binance/开头，去掉这个前缀
            path = path[len('binance/'):]
        
        target_url = f'https://api.binance.com/{path}'
        logger.info(f"代理请求到: {target_url}")
        
        # 准备请求头，明确拒绝压缩内容
        headers = {
            'X-MBX-APIKEY': api_key,
            'Accept-Encoding': 'identity'  # 明确要求不压缩的内容
        }
        
        try:
            # 根据请求方法转发请求
            if request.method == 'GET':
                response = requests.get(
                    target_url, 
                    params=request.GET.dict(),
                    headers=headers,
                    timeout=30
                )
            elif request.method == 'POST':
                response = requests.post(
                    target_url, 
                    data=request.body,
                    headers=headers,
                    timeout=30
                )
            elif request.method == 'PUT':
                response = requests.put(
                    target_url, 
                    data=request.body,
                    headers=headers,
                    timeout=30
                )
            elif request.method == 'DELETE':
                response = requests.delete(
                    target_url, 
                    headers=headers,
                    timeout=30
                )
            else:
                return JsonResponse({'code': -1100, 'msg': 'Method not allowed'}, status=405)
            
            # 创建响应，确保不压缩
            django_response = HttpResponse(
                content=response.content,
                status=response.status_code,
                content_type=response.headers.get('Content-Type', 'application/json')
            )
            
            # 复制响应头，但跳过压缩相关的头
            for header, value in response.headers.items():
                if header.lower() not in ['content-length', 'transfer-encoding', 'connection', 'content-encoding']:
                    django_response[header] = value
            
            # 确保不设置压缩相关的响应头
            if 'Content-Encoding' in django_response:
                del django_response['Content-Encoding']
            
            return django_response
        
        except Exception as e:
            logger.error(f"代理请求失败: {str(e)}")
            return JsonResponse({
                'code': -1,
                'msg': f'Proxy request failed: {str(e)}'
            }, status=500)
