import React, { createContext, useContext, useEffect, useState } from 'react';

// 认证模式开关 - 设置为 'sms' 使用SMS认证, 'mock' 使用模拟数据，'supabase' 使用真实 Supabase
const AUTH_MODE = 'sms';

const AuthContext = createContext({});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initAuth = async () => {
      try {
        if (AUTH_MODE === 'sms') {
          // 使用SMS认证
          const { smsAuthService } = await import('../lib/smsAuth');

          // 检查是否已登录
          if (smsAuthService.isAuthenticated()) {
            const result = await smsAuthService.getUserProfile();
            if (result.data) {
              setUser(result.data);
              setProfile(result.data);
            } else {
              // token无效，清除登录状态
              smsAuthService.logout();
            }
          }
          setLoading(false);
        } else if (AUTH_MODE === 'mock') {
          // 使用模拟认证
          const { mockSupabase } = await import('../lib/mockAuth');

          // 获取初始会话
          const { data: { session } } = await mockSupabase.auth.getSession();
          if (session?.user) {
            setUser(session.user);
            await loadUserProfile(session.user.id);
          }
          setLoading(false);

          // 监听认证状态变化
          const { data: { subscription } } = mockSupabase.auth.onAuthStateChange(
            async (event, session) => {
              console.log('Mock auth state changed:', event, session);

              if (session?.user) {
                setUser(session.user);
                await loadUserProfile(session.user.id);
              } else {
                setUser(null);
                setProfile(null);
              }
              setLoading(false);
            }
          );

          return () => subscription.unsubscribe();
        } else {
          // 使用真实 Supabase
          const { supabase } = await import('../lib/supabase');

          // 获取初始会话
          const { data: { session } } = await supabase.auth.getSession();
          if (session?.user) {
            setUser(session.user);
            await loadUserProfile(session.user.id);
          }
          setLoading(false);

          // 监听认证状态变化
          const { data: { subscription } } = supabase.auth.onAuthStateChange(
            async (event, session) => {
              console.log('Auth state changed:', event, session);

              if (session?.user) {
                setUser(session.user);
                await loadUserProfile(session.user.id);
              } else {
                setUser(null);
                setProfile(null);
              }
              setLoading(false);
            }
          );

          return () => subscription.unsubscribe();
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        setLoading(false);
      }
    };

    initAuth();
  }, []);

  // 加载用户档案
  const loadUserProfile = async (userId) => {
    try {
      const { userService } = await import('../lib/supabase');
      const { data, error } = await userService.getProfile(userId);
      if (error) {
        console.error('Error loading profile:', error);
        return;
      }
      setProfile(data);
    } catch (error) {
      console.error('Error loading profile:', error);
    }
  };

  // 登录
  const signIn = async (phone, password) => {
    if (AUTH_MODE === 'sms') {
      const { smsAuthService } = await import('../lib/smsAuth');
      const result = await smsAuthService.loginWithPassword(phone, password);
      if (result.error) throw new Error(result.error);
      
      // 更新状态
      if (result.data?.user) {
        setUser(result.data.user);
        setProfile(result.data.user);
      }
      return result.data;
    } else {
      const { userService } = await import('../lib/supabase');
      const { data, error } = await userService.signInWithPassword(phone, password);
      if (error) throw error;
      return data;
    }
  };

  // 注册
  const signUp = async (phone, password, username) => {
    if (AUTH_MODE === 'sms') {
      // SMS注册需要验证码，这里先抛出错误提示使用正确的注册流程
      throw new Error('请使用短信验证码注册');
    } else {
      const { userService } = await import('../lib/supabase');
      const { data, error } = await userService.signUpWithPassword(phone, password, username);
      if (error) throw error;
      return data;
    }
  };

  // 使用短信验证码注册
  const signUpWithOTP = async (phone, token, username, password) => {
    if (AUTH_MODE === 'sms') {
      const { smsAuthService } = await import('../lib/smsAuth');
      const result = await smsAuthService.registerWithSMS(phone, token, password, username);
      if (result.error) throw new Error(result.error);
      
      // 更新状态
      if (result.data?.user) {
        setUser(result.data.user);
        setProfile(result.data.user);
      }
      return result.data;
    } else {
      const { userService } = await import('../lib/supabase');
      const { data, error } = await userService.signUpWithOTP(phone, token, username);
      if (error) throw error;
      return data;
    }
  };

  // 发送短信验证码
  const sendOTP = async (phone, purpose = 'register') => {
    if (AUTH_MODE === 'sms') {
      const { smsAuthService } = await import('../lib/smsAuth');
      const result = await smsAuthService.sendSMSCode(phone, purpose);
      if (result.error) throw new Error(result.error);
      return result.data;
    } else {
      const { userService } = await import('../lib/supabase');
      const { data, error } = await userService.sendSignUpOTP(phone);
      if (error) throw error;
      return data;
    }
  };

  // 登出
  const signOut = async () => {
    if (AUTH_MODE === 'sms') {
      const { smsAuthService } = await import('../lib/smsAuth');
      smsAuthService.logout();
      setUser(null);
      setProfile(null);
    } else {
      const { userService } = await import('../lib/supabase');
      const { error } = await userService.signOut();
      if (error) throw error;
      setUser(null);
      setProfile(null);
    }
  };

  // 更新用户档案
  const updateProfile = async (updates) => {
    if (!user) throw new Error('No user logged in');

    const { userService } = await import('../lib/supabase');
    const { data, error } = await userService.updateProfile(user.id, updates);
    if (error) throw error;

    setProfile(data[0]);
    return data[0];
  };

  // 更新余额
  const updateBalance = async (newBalance, transactionType, description) => {
    if (!user) throw new Error('No user logged in');

    const { userService } = await import('../lib/supabase');
    const { data, error } = await userService.updateBalance(
      user.id,
      newBalance,
      transactionType,
      description
    );
    if (error) throw error;

    setProfile(data[0]);
    return data[0];
  };

  // 获取余额变更记录
  const getBalanceTransactions = async (limit = 50) => {
    if (!user) throw new Error('No user logged in');

    const { userService } = await import('../lib/supabase');
    const { data, error } = await userService.getBalanceTransactions(user.id, limit);
    if (error) throw error;

    return data;
  };

  const value = {
    user,
    profile,
    loading,
    signIn,
    signUp,
    signUpWithOTP,
    sendOTP,
    signOut,
    updateProfile,
    updateBalance,
    getBalanceTransactions,
    loadUserProfile
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export { AuthProvider };
export default AuthProvider;
