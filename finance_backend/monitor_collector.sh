#!/bin/bash

# 多收集器监控和自动重启脚本
# 每3小时检查一次，如果上传停止超过2小时则重启

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

LOG_FILE="logs/monitor.log"
COLLECTOR_LOG="logs/multi_collector_restart.log"
PID_FILE="multi_collector.pid"

# 写日志函数
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log_message "🔍 开始监控检查..."

# 检查进程是否运行
check_process() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            log_message "✅ 进程运行正常 (PID: $pid)"
            return 0
        else
            log_message "❌ PID文件存在但进程已死 (PID: $pid)"
            rm -f "$PID_FILE"
            return 1
        fi
    else
        log_message "❌ 未找到PID文件"
        return 1
    fi
}

# 检查最近的定时上传活动
check_recent_upload() {
    if [ ! -f "$COLLECTOR_LOG" ]; then
        log_message "❌ 收集器日志文件不存在"
        return 1
    fi
    
    # 获取当前时间（2小时前）
    local two_hours_ago=$(date -d '2 hours ago' '+%Y-%m-%d %H:%M')
    
    # 检查最近2小时内是否有定时上传活动（非缺口修复）
    local recent_scheduled_uploads=$(grep -E "🔄.*开始定时上传|✅.*定时上传完成" "$COLLECTOR_LOG" | wc -l)
    local last_scheduled_upload=$(grep -E "🔄.*开始定时上传|✅.*定时上传完成" "$COLLECTOR_LOG" | tail -1)
    
    # 检查COS上传（过滤掉缺口修复）
    local recent_cos_uploads=$(grep -E "📤.*COS上传完成" "$COLLECTOR_LOG" | grep -v "缺口修复" | tail -10 | wc -l)
    local last_cos_upload=$(grep -E "📤.*COS上传完成" "$COLLECTOR_LOG" | tail -1)
    
    if [ "$recent_scheduled_uploads" -gt 0 ]; then
        log_message "✅ 发现定时上传活动: $recent_scheduled_uploads 次"
        log_message "📋 最后定时上传: $last_scheduled_upload"
        return 0
    elif [ "$recent_cos_uploads" -gt 5 ]; then
        log_message "✅ 发现COS上传活动: $recent_cos_uploads 次（可能是正常上传）"
        log_message "📋 最后COS上传: $last_cos_upload"
        return 0
    else
        log_message "⚠️ 最近2小时内无定时上传活动"
        log_message "🔍 最后COS上传: $last_cos_upload"
        
        # 检查最后上传时间是否超过2小时
        if echo "$last_cos_upload" | grep -q "$(date -d '2 hours ago' '+%Y-%m-%d %H')"; then
            log_message "❌ 最后上传超过2小时，可能有问题"
            return 1
        else
            return 1
        fi
    fi
}

# 检查数据收集活动
check_data_collection() {
    if [ ! -f "$COLLECTOR_LOG" ]; then
        return 1
    fi
    
    # 检查最近10分钟内是否有K线数据
    local recent_data=$(tail -100 "$COLLECTOR_LOG" | grep -E "📊.*K线数据" | tail -5 | wc -l)
    
    if [ "$recent_data" -gt 0 ]; then
        log_message "✅ 数据收集正常: 最近有 $recent_data 条K线数据"
        return 0
    else
        log_message "❌ 数据收集异常: 最近10分钟无K线数据"
        return 1
    fi
}

# 重启收集器
restart_collector() {
    log_message "🔄 开始重启多收集器..."
    
    # 停止现有进程
    if [ -f "$PID_FILE" ]; then
        local old_pid=$(cat "$PID_FILE")
        log_message "⏹️ 停止现有进程 (PID: $old_pid)"
        kill -TERM "$old_pid" 2>/dev/null || kill -9 "$old_pid" 2>/dev/null
        sleep 5
        rm -f "$PID_FILE"
    fi
    
    # 清理可能残留的进程
    pkill -f "collect_multi_data" 2>/dev/null
    sleep 2
    
    # 启动新进程
    log_message "🚀 启动新的多收集器进程..."
    nohup python3 manage.py collect_multi_data \
        --symbols=BTCUSDT,ETHUSDT,SOLUSDT,DOGEUSDT,XRPUSDT \
        --timeframes=1m,15m,1h,4h,1d \
        --interval=60 \
        --daemon \
        --debug > logs/multi_collector_restart.log 2>&1 &
    
    local new_pid=$!
    echo "$new_pid" > "$PID_FILE"
    log_message "✅ 新进程已启动 (PID: $new_pid)"
    
    # 等待进程启动
    sleep 10
    if ps -p "$new_pid" > /dev/null 2>&1; then
        log_message "✅ 进程启动成功，开始监控..."
    else
        log_message "❌ 进程启动失败！"
        return 1
    fi
}

# 主检查逻辑
main_check() {
    local process_ok=false
    local upload_ok=false
    local data_ok=false
    
    # 检查进程状态
    if check_process; then
        process_ok=true
    fi
    
    # 检查数据收集
    if check_data_collection; then
        data_ok=true
    fi
    
    # 检查上传活动
    if check_recent_upload; then
        upload_ok=true
    fi
    
    # 决策逻辑
    if [ "$process_ok" = true ] && [ "$data_ok" = true ] && [ "$upload_ok" = true ]; then
        log_message "🎉 所有检查通过，系统运行正常"
        return 0
    elif [ "$process_ok" = false ]; then
        log_message "🚨 进程异常，需要重启"
        restart_collector
    elif [ "$data_ok" = false ]; then
        log_message "🚨 数据收集异常，需要重启"
        restart_collector
    elif [ "$upload_ok" = false ]; then
        log_message "🚨 上传功能异常，需要重启"
        restart_collector
    else
        log_message "✅ 系统正常，继续监控"
    fi
}

# 执行检查
main_check

log_message "🏁 监控检查完成\n"