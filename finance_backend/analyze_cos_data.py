#!/usr/bin/env python3
"""
分析COS存储的2025年7月30-31日BTCUSDT 1分钟数据
检查价格异常和时间戳格式问题
"""

import os
import sys
import json
import requests
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# 添加项目路径
sys.path.append('/home/<USER>/qiyuai-web/finance_backend')

def download_cos_data(url, description=""):
    """下载COS数据文件"""
    print(f"📥 下载数据: {description}")
    print(f"🔗 URL: {url}")
    
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        # 清理分块传输标记
        raw_data = response.text
        cleaned_data = clean_chunked_data(raw_data)
        
        # 解析JSON
        json_data = json.loads(cleaned_data)
        
        if 'data' in json_data:
            print(f"✅ 下载成功: {len(json_data['data'])} 条记录")
            return json_data['data']
        else:
            print(f"⚠️ 数据格式异常: 缺少'data'字段")
            return []
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 下载失败: {e}")
        return []
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
        print(f"原始数据前1000字符: {response.text[:1000]}")
        return []

def clean_chunked_data(raw_data):
    """清理分块传输标记"""
    if raw_data.strip().startswith('{'):
        return raw_data.strip()
    
    # 移除分块传输的size headers
    cleaned = raw_data
    # 移除开头的分块大小（十六进制数字 + CRLF）
    cleaned = cleaned.replace('\r\n', '\n')
    lines = cleaned.split('\n')
    
    # 找到第一个 { 和最后一个 }
    json_lines = []
    in_json = False
    for line in lines:
        if line.strip().startswith('{'):
            in_json = True
        if in_json:
            json_lines.append(line)
        if line.strip().endswith('}') and in_json:
            break
    
    return '\n'.join(json_lines)

def analyze_price_data(data, date_str):
    """分析价格数据，查找异常"""
    if not data:
        print(f"❌ {date_str}: 无数据")
        return
    
    print(f"\n📊 分析 {date_str} 数据:")
    print(f"   总记录数: {len(data)}")
    
    # 转换数据格式
    processed_data = []
    timestamp_issues = []
    price_issues = []
    
    for i, item in enumerate(data):
        if isinstance(item, list) and len(item) >= 6:
            timestamp = item[0]
            open_price = float(item[1])
            high_price = float(item[2])
            low_price = float(item[3])
            close_price = float(item[4])
            volume = float(item[5])
            
            # 检查时间戳格式
            if timestamp > 1e15:  # 微秒时间戳
                timestamp_ms = timestamp // 1000
                timestamp_issues.append({
                    'index': i,
                    'original': timestamp,
                    'converted': timestamp_ms,
                    'datetime': datetime.fromtimestamp(timestamp_ms / 1000)
                })
            else:  # 毫秒时间戳
                timestamp_ms = timestamp
            
            # 检查价格异常
            prices = [open_price, high_price, low_price, close_price]
            
            # BTC价格异常检测
            if any(p < 30000 or p > 200000 for p in prices):
                price_issues.append({
                    'index': i,
                    'timestamp': timestamp_ms,
                    'datetime': datetime.fromtimestamp(timestamp_ms / 1000),
                    'open': open_price,
                    'high': high_price,
                    'low': low_price,
                    'close': close_price,
                    'volume': volume
                })
            
            # 检查价格逻辑
            if not (low_price <= high_price and 
                   low_price <= open_price <= high_price and 
                   low_price <= close_price <= high_price):
                price_issues.append({
                    'index': i,
                    'timestamp': timestamp_ms,
                    'datetime': datetime.fromtimestamp(timestamp_ms / 1000),
                    'open': open_price,
                    'high': high_price,
                    'low': low_price,
                    'close': close_price,
                    'volume': volume,
                    'issue': 'price_logic_error'
                })
            
            processed_data.append({
                'timestamp': timestamp_ms,
                'datetime': datetime.fromtimestamp(timestamp_ms / 1000),
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': volume
            })
    
    # 报告时间戳问题
    if timestamp_issues:
        print(f"⚠️  时间戳格式问题: {len(timestamp_issues)} 条记录使用微秒时间戳")
        print(f"   示例: {timestamp_issues[0]['original']} -> {timestamp_issues[0]['converted']}")
        print(f"   时间: {timestamp_issues[0]['datetime']}")
    else:
        print("✅ 时间戳格式正常")
    
    # 报告价格问题
    if price_issues:
        print(f"🚨 发现价格异常: {len(price_issues)} 条记录")
        for issue in price_issues[:10]:  # 只显示前10个
            print(f"   [{issue['index']}] {issue['datetime'].strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"       O:{issue['open']:,.2f} H:{issue['high']:,.2f} L:{issue['low']:,.2f} C:{issue['close']:,.2f}")
            if 'issue' in issue:
                print(f"       问题: {issue['issue']}")
    else:
        print("✅ 价格数据正常")
    
    # 价格统计
    if processed_data:
        df = pd.DataFrame(processed_data)
        print(f"\n📈 价格统计:")
        print(f"   开盘价范围: {df['open'].min():,.2f} - {df['open'].max():,.2f}")
        print(f"   最高价范围: {df['high'].min():,.2f} - {df['high'].max():,.2f}")  
        print(f"   最低价范围: {df['low'].min():,.2f} - {df['low'].max():,.2f}")
        print(f"   收盘价范围: {df['close'].min():,.2f} - {df['close'].max():,.2f}")
        
        # 时间范围
        print(f"   时间范围: {df['datetime'].min()} - {df['datetime'].max()}")
        
        # 查找44900-46000范围的异常数据
        abnormal_range = df[(df['close'] >= 44900) & (df['close'] <= 46000)]
        if len(abnormal_range) > 0:
            print(f"\n🎯 发现44900-46000价格范围异常数据: {len(abnormal_range)} 条")
            for _, row in abnormal_range.head(10).iterrows():
                print(f"   {row['datetime'].strftime('%Y-%m-%d %H:%M:%S')} - 收盘价: {row['close']:,.2f}")
        else:
            print(f"\n✅ 未发现44900-46000价格范围的异常数据")
    
    return processed_data, price_issues, timestamp_issues

def main():
    """主函数"""
    print("🔍 开始分析2025年7月30-31日BTCUSDT 1分钟数据")
    print("=" * 80)
    
    # COS数据路径
    base_url = "https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/20250726/BTCUSDT/1m/daily"
    
    # 分析日期
    dates = [
        ("2025-07-30", "BTCUSDT_1m_07-30_2025_compressed.json"),
        ("2025-07-31", "BTCUSDT_1m_07-31_2025_compressed.json")
    ]
    
    all_results = {}
    
    for date_str, filename in dates:
        url = f"{base_url}/{filename}"
        data = download_cos_data(url, f"{date_str} BTCUSDT 1分钟数据")
        
        if data:
            processed_data, price_issues, timestamp_issues = analyze_price_data(data, date_str)
            all_results[date_str] = {
                'data': processed_data,
                'price_issues': price_issues,
                'timestamp_issues': timestamp_issues
            }
        else:
            print(f"❌ {date_str}: 数据下载失败")
            
        print("-" * 60)
    
    # 总结分析
    print("\n📋 总结分析:")
    total_records = sum(len(result['data']) for result in all_results.values())
    total_price_issues = sum(len(result['price_issues']) for result in all_results.values()) 
    total_timestamp_issues = sum(len(result['timestamp_issues']) for result in all_results.values())
    
    print(f"   总记录数: {total_records}")
    print(f"   价格异常: {total_price_issues}")
    print(f"   时间戳问题: {total_timestamp_issues}")
    
    if total_price_issues > 0:
        print(f"\n🚨 建议:")
        print(f"   1. 检查数据收集过程中的价格验证逻辑")
        print(f"   2. 实施价格范围过滤机制")
        print(f"   3. 验证Binance API数据的完整性")
        
    if total_timestamp_issues > 0:
        print(f"   4. 确保时间戳转换逻辑正确处理微秒格式")
    
    print("\n✅ 分析完成")

if __name__ == "__main__":
    main()