"""
多货币多周期实时数据收集器
支持同时收集多个交易对的多个时间周期数据
"""

import asyncio
import websockets
import json
import threading
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from collections import defaultdict
from .cos_service import COSRealtimeService
from .gap_recovery import GapRecoveryService

logger = logging.getLogger(__name__)

class MultiCryptoCollector:
    """多货币多周期实时数据收集器"""
    
    def __init__(self, symbols=None, timeframes=None, upload_interval=300, debug=False):
        self.symbols = symbols or ['BTCUSDT', 'ETHUSDT', 'SOLUSDT']
        self.timeframes = timeframes or ['1m', '15m', '1h', '4h', '1d']
        self.upload_interval = upload_interval
        self.debug = debug
        
        # 时间周期到分钟数的映射
        self.timeframe_minutes = {
            '1m': 1,
            '15m': 15,
            '1h': 60,
            '4h': 240,
            '1d': 1440
        }
        
        # WebSocket配置 - 使用多个节点提高稳定性
        self.websocket_urls = [
            'wss://stream.binance.com:9443/ws',
            'wss://stream.binance.com:443/ws',
            'wss://stream1.binance.com:9443/ws',
            'wss://stream2.binance.com:9443/ws'
        ]
        self.current_url_index = 0
        
        # 数据缓冲区：symbol -> timeframe -> timestamp -> kline_data
        self.data_buffers = defaultdict(lambda: defaultdict(dict))
        self.buffer_lock = threading.Lock()
        
        # K线聚合缓冲区：symbol -> timeframe -> current_kline
        self.kline_aggregators = defaultdict(lambda: defaultdict(dict))
        
        # 运行状态
        self.is_running = False
        self.websocket_task = None
        self.upload_task = None
        
        # 服务依赖
        self.cos_service = COSRealtimeService(debug=debug)
        self.gap_recovery = GapRecoveryService(debug=debug)
        
        logger.info(f"🏗️ 多货币收集器初始化完成")
        logger.info(f"📊 交易对: {', '.join(self.symbols)}")
        logger.info(f"⏰ 时间周期: {', '.join(self.timeframes)}")
    
    def start(self):
        """启动数据收集"""
        logger.info(f"🚀 启动多货币多周期数据收集...")
        
        # 1. 检查并修复所有交易对的数据缺口
        self.check_and_resume_all_gaps()
        
        # 2. 启动定时上传任务
        self.start_upload_scheduler()
        
        # 3. 启动WebSocket连接
        logger.info("🚀 启动WebSocket实时数据收集...")
        self.start_websocket_connection()
    
    def stop(self):
        """停止数据收集"""
        logger.info("⏹️ 正在停止多货币数据收集器...")
        
        self.is_running = False
        
        # 停止WebSocket任务
        if self.websocket_task:
            self.websocket_task.cancel()
        
        # 最后一次上传所有缓冲区数据
        self.upload_all_buffers()
        
        logger.info("✅ 多货币数据收集器已停止")
    
    def check_and_resume_all_gaps(self):
        """检查并修复所有交易对的数据缺口"""
        logger.info("🔍 开始检查所有交易对数据缺口...")
        
        for symbol in self.symbols:
            try:
                logger.info(f"📊 检查 {symbol} 数据缺口...")
                
                # 获取最后成功上传的时间（基于1分钟数据）
                last_upload_time = self.cos_service.get_last_upload_time(symbol)
                current_time = datetime.now()
                
                if last_upload_time:
                    gap_duration = current_time - last_upload_time
                    
                    # 如果缺口超过5分钟，启动缺口修复
                    if gap_duration.total_seconds() > 300:
                        gap_minutes = int(gap_duration.total_seconds() / 60)
                        logger.warning(f"⚠️ {symbol} 发现数据缺口: {gap_duration} (约{gap_minutes}分钟)")
                        
                        success = self.gap_recovery.fill_gap(
                            symbol=symbol, 
                            start_time=last_upload_time,
                            end_time=current_time
                        )
                        if success:
                            logger.info(f"✅ {symbol} 缺口修复完成")
                        else:
                            logger.error(f"❌ {symbol} 缺口修复失败")
                    else:
                        logger.info(f"✅ {symbol} 数据连续，无需修复")
                else:
                    logger.info(f"ℹ️ {symbol} 首次运行，无历史数据")
                    
            except Exception as e:
                logger.error(f"❌ {symbol} 缺口检查失败: {e}")
    
    def start_upload_scheduler(self):
        """启动定时上传任务"""
        def upload_task():
            consecutive_failures = 0
            max_failures = 3
            
            while self.is_running:
                try:
                    time.sleep(self.upload_interval)
                    if self.is_running:  # 双重检查
                        logger.info(f"🔄 开始定时上传 (间隔: {self.upload_interval}秒)")
                        # 添加缓冲区状态日志
                        total_items = 0
                        for symbol in self.symbols:
                            for timeframe in self.timeframes:
                                items = len(self.data_buffers[symbol][timeframe])
                                total_items += items
                                if items > 0:
                                    logger.info(f"📊 缓冲区 {symbol}/{timeframe}: {items} 条数据")
                        
                        logger.info(f"📊 总缓冲区数据: {total_items} 条")
                        self.upload_all_buffers()
                        consecutive_failures = 0  # 重置失败计数
                        logger.info(f"✅ 定时上传完成")
                        
                except Exception as e:
                    consecutive_failures += 1
                    logger.error(f"❌ 上传调度器异常 ({consecutive_failures}/{max_failures}): {e}")
                    
                    if consecutive_failures >= max_failures:
                        logger.error(f"🚨 上传调度器连续失败 {max_failures} 次，线程退出")
                        break
                    else:
                        logger.info(f"⚠️ 等待 {self.upload_interval} 秒后重试...")
                        time.sleep(self.upload_interval)
        
        self.upload_task = threading.Thread(target=upload_task, daemon=True)
        self.upload_task.start()
        
        logger.info(f"⏰ 定时上传任务已启动 (间隔: {self.upload_interval}秒)")
    
    def start_websocket_connection(self):
        """启动WebSocket连接"""
        self.is_running = True
        
        while self.is_running:
            try:
                # 运行WebSocket连接
                asyncio.run(self.run_websocket())
                
            except KeyboardInterrupt:
                logger.info("🛑 收到停止信号")
                break
                
            except Exception as e:
                logger.error(f"❌ WebSocket异常: {e}")
                if self.is_running:
                    logger.info("🔄 5秒后重新连接...")
                    time.sleep(5)
    
    async def run_websocket(self):
        """运行WebSocket连接 - 多交易对订阅"""
        # 构建所有1分钟数据流名称
        streams = []
        for symbol in self.symbols:
            streams.append(f"{symbol.lower()}@kline_1m")
        
        # 轮换使用不同的WebSocket节点
        uri = f"{self.websocket_urls[self.current_url_index]}/{'/'.join(streams)}"
        self.current_url_index = (self.current_url_index + 1) % len(self.websocket_urls)
        
        try:
            # 连接配置
            connect_kwargs = {
                'uri': uri,
                'ping_interval': 20,
                'ping_timeout': 10,
                'close_timeout': 10,
                'open_timeout': 30,
                'max_size': 2**20,
            }
            
            logger.info(f"🔗 尝试连接WebSocket: {len(streams)}个数据流")
            logger.debug(f"🔗 数据流: {', '.join(streams)}")
            
            async with websockets.connect(**connect_kwargs) as websocket:
                logger.info(f"✅ WebSocket连接成功: {len(streams)}个数据流")
                
                # 发送批量订阅消息
                subscribe_msg = {
                    "method": "SUBSCRIBE",
                    "params": streams,
                    "id": 1
                }
                await websocket.send(json.dumps(subscribe_msg))
                logger.info(f"📡 已订阅 {len(streams)} 个数据流")
                
                async for message in websocket:
                    if not self.is_running:
                        break
                        
                    try:
                        data = json.loads(message)
                        
                        # 跳过订阅确认消息
                        if 'result' in data or 'id' in data:
                            continue
                            
                        self.process_kline_data(data)
                        
                    except json.JSONDecodeError as e:
                        logger.error(f"❌ JSON解析失败: {e}")
                        continue
                        
        except websockets.exceptions.ConnectionClosed:
            logger.warning("⚠️ WebSocket连接关闭")
            
        except asyncio.TimeoutError:
            logger.error("❌ WebSocket连接超时")
            
        except Exception as e:
            logger.error(f"❌ WebSocket运行异常: {e}")
            raise
    
    def process_kline_data(self, data):
        """处理K线数据并聚合多个时间周期"""
        try:
            # 检查数据格式
            if 'k' not in data:
                return
                
            kline = data['k']
            
            # 只处理已确认的K线（isFinal=True）
            if not kline.get('x', False):
                return
            
            # 提取基础数据
            symbol = kline['s']
            timestamp = int(kline['t'])  # 开盘时间（毫秒）
            open_price = float(kline['o'])
            high_price = float(kline['h'])
            low_price = float(kline['l'])
            close_price = float(kline['c'])
            volume = float(kline['v'])
            turnover = float(kline['q'])
            
            # 构建1分钟K线记录
            minute_kline = {
                'timestamp': timestamp,
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': volume,
                'turnover': turnover
            }
            
            # 存储1分钟数据
            with self.buffer_lock:
                self.data_buffers[symbol]['1m'][timestamp] = minute_kline
            
            # 聚合其他时间周期数据
            for timeframe in self.timeframes:
                if timeframe == '1m':
                    continue  # 1分钟数据已处理
                    
                aggregated_kline = self.aggregate_kline(symbol, timeframe, minute_kline)
                if aggregated_kline:
                    with self.buffer_lock:
                        self.data_buffers[symbol][timeframe][aggregated_kline['timestamp']] = aggregated_kline
            
            if self.debug:
                dt = datetime.fromtimestamp(timestamp / 1000)
                logger.debug(f"📊 {symbol} K线数据: {dt.strftime('%H:%M:%S')} - {close_price}")
            
        except Exception as e:
            logger.error(f"❌ K线数据处理失败: {e}")
    
    def aggregate_kline(self, symbol, timeframe, minute_kline):
        """聚合K线数据到指定时间周期"""
        try:
            minutes = self.timeframe_minutes[timeframe]
            timestamp = minute_kline['timestamp']
            
            # 计算聚合周期的开始时间
            dt = datetime.fromtimestamp(timestamp / 1000)
            
            if timeframe == '15m':
                # 15分钟：取15分钟的整数倍
                agg_minute = (dt.minute // 15) * 15
                agg_start = dt.replace(minute=agg_minute, second=0, microsecond=0)
            elif timeframe == '1h':
                # 1小时：取小时的开始
                agg_start = dt.replace(minute=0, second=0, microsecond=0)
            elif timeframe == '4h':
                # 4小时：取4小时的整数倍
                agg_hour = (dt.hour // 4) * 4
                agg_start = dt.replace(hour=agg_hour, minute=0, second=0, microsecond=0)
            elif timeframe == '1d':
                # 1天：取当天的开始
                agg_start = dt.replace(hour=0, minute=0, second=0, microsecond=0)
            else:
                return None
            
            agg_timestamp = int(agg_start.timestamp() * 1000)
            
            # 获取或创建聚合K线
            if agg_timestamp not in self.kline_aggregators[symbol][timeframe]:
                self.kline_aggregators[symbol][timeframe][agg_timestamp] = {
                    'timestamp': agg_timestamp,
                    'open': minute_kline['open'],
                    'high': minute_kline['high'],
                    'low': minute_kline['low'],
                    'close': minute_kline['close'],
                    'volume': minute_kline['volume'],
                    'turnover': minute_kline['turnover'],
                    'count': 1
                }
            else:
                # 更新聚合数据
                agg_kline = self.kline_aggregators[symbol][timeframe][agg_timestamp]
                agg_kline['high'] = max(agg_kline['high'], minute_kline['high'])
                agg_kline['low'] = min(agg_kline['low'], minute_kline['low'])
                agg_kline['close'] = minute_kline['close']  # 最新收盘价
                agg_kline['volume'] += minute_kline['volume']
                agg_kline['turnover'] += minute_kline['turnover']
                agg_kline['count'] += 1
            
            # 检查聚合周期是否完成
            expected_count = minutes
            current_count = self.kline_aggregators[symbol][timeframe][agg_timestamp]['count']
            
            # 如果达到预期数量或时间已过，返回完成的K线
            if current_count >= expected_count or dt.minute % minutes == minutes - 1:
                completed_kline = self.kline_aggregators[symbol][timeframe][agg_timestamp].copy()
                del completed_kline['count']  # 移除计数字段
                return completed_kline
            
            return None
            
        except Exception as e:
            logger.error(f"❌ K线聚合失败 {symbol}/{timeframe}: {e}")
            return None
    
    def upload_all_buffers(self):
        """上传所有缓冲区数据到COS"""
        try:
            with self.buffer_lock:
                for symbol in self.symbols:
                    for timeframe in self.timeframes:
                        buffer_data = self.data_buffers[symbol][timeframe]
                        
                        if not buffer_data:
                            continue
                        
                        # 复制并清空缓冲区
                        buffer_copy = dict(buffer_data)
                        buffer_data.clear()
                        
                        # 按时间戳排序
                        sorted_data = sorted(buffer_copy.values(), key=lambda x: x['timestamp'])
                        
                        if sorted_data:
                            logger.info(f"📤 上传 {symbol}/{timeframe}: {len(sorted_data)} 条记录")
                            
                            # 上传到COS（使用修改后的上传方法）
                            success = self.upload_timeframe_data(symbol, timeframe, sorted_data)
                            
                            if success:
                                first_time = datetime.fromtimestamp(sorted_data[0]['timestamp'] / 1000)
                                last_time = datetime.fromtimestamp(sorted_data[-1]['timestamp'] / 1000)
                                logger.info(f"✅ {symbol}/{timeframe} 上传成功: {first_time.strftime('%H:%M')} ~ {last_time.strftime('%H:%M')}")
                            else:
                                logger.error(f"❌ {symbol}/{timeframe} 上传失败")
                                
                                # 上传失败，恢复数据到缓冲区
                                with self.buffer_lock:
                                    for item in sorted_data:
                                        self.data_buffers[symbol][timeframe][item['timestamp']] = item
            
        except Exception as e:
            logger.error(f"❌ 批量上传异常: {e}")
    
    def upload_timeframe_data(self, symbol, timeframe, data):
        """上传指定时间周期的数据到COS"""
        try:
            # 使用更新后的上传方法，支持多时间周期
            return self.cos_service.upload_realtime_data(symbol, data, timeframe)
            
        except Exception as e:
            logger.error(f"❌ {symbol}/{timeframe} 数据上传失败: {e}")
            return False
    
    def get_status(self) -> Dict:
        """获取收集器状态"""
        status = {
            'symbols': self.symbols,
            'timeframes': self.timeframes,
            'is_running': self.is_running,
            'upload_interval': self.upload_interval,
            'buffer_status': {},
            'last_check': datetime.now().isoformat()
        }
        
        with self.buffer_lock:
            for symbol in self.symbols:
                status['buffer_status'][symbol] = {}
                for timeframe in self.timeframes:
                    buffer_size = len(self.data_buffers[symbol][timeframe])
                    status['buffer_status'][symbol][timeframe] = buffer_size
        
        return status