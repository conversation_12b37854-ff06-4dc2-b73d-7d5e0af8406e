/* 2x2网格K线图表样式 */
.grid-kline-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #141414;
  color: #fff;
  overflow: hidden;
}

/* 全屏模式 */
.grid-kline-chart.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: #000;
}

/* 全局工具栏 */
.global-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #1f1f1f;
  border-bottom: 1px solid #333;
  flex-shrink: 0;
  min-height: 60px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.chart-title {
  color: #fff !important;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.realtime-indicator {
  color: #52c41a;
  font-size: 12px;
  font-weight: 500;
}

.toolbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

/* 2x2网格容器 */
.charts-grid-container {
  height: calc(100vh - 20.5vh - 180px); /* 减去top-charts的20.5vh、导航栏和间距 */
  overflow-y: auto;
  overflow-x: hidden;
  padding: 8px;
  background: #141414;
}

.charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: repeat(2, 1fr); /* 固定2行，每行高度相等 */
  gap: 8px;
  min-height: 100%;
  max-width: 100%;
}

/* 确保网格容器在超过2行时可以滚动 */
@media (max-height: 800px) {
  .charts-grid {
    min-height: calc(100vh - 140px);
  }
}

/* 图表网格项 - 固定高度确保副图完整显示 */
.chart-grid-item {
  height: calc((100vh - 20.5vh - 180px - 16px) / 2); /* (总高度 - padding) / 2 */
  min-height: calc((100vh - 20.5vh - 180px - 16px) / 2);
  max-height: calc((100vh - 20.5vh - 180px - 16px) / 2);
  display: flex;
  flex-direction: column;
}

/* 图表卡片样式 */
.chart-card {
  height: 100%;
  border: 1px solid #333 !important;
  background: #1f1f1f !important;
  border-radius: 8px !important;
}

.chart-card .ant-card-head {
  display: none !important; /* 隐藏Card头部，使用内部工具栏 */
}

.chart-card .ant-card-head-title {
  display: none !important; /* 隐藏Card标题 */
}

.chart-card .ant-card-body {
  background: #1f1f1f !important;
  height: 100% !important; /* 占满整个卡片高度 */
  padding: 0 !important;
}

/* 图表状态指示器 */
.chart-status {
  color: #999;
  font-size: 16px;
}

/* 懒加载占位符 */
.chart-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 24px;
  background: #1a1a1a;
  border-radius: 4px;
}

.lazy-load-hint {
  margin-top: 16px;
  color: #666;
  font-size: 12px;
  text-align: center;
}

/* 加载状态指示器 */
.loading-status {
  position: fixed;
  bottom: 16px;
  right: 16px;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  backdrop-filter: blur(4px);
  border: 1px solid #333;
  z-index: 1000;
}

/* 骨架屏深色主题 */
.chart-placeholder .ant-skeleton-content .ant-skeleton-title,
.chart-placeholder .ant-skeleton-content .ant-skeleton-paragraph > li {
  background: linear-gradient(90deg, #2a2a2a 25%, #333 50%, #2a2a2a 75%) !important;
  background-size: 400% 100% !important;
  animation: ant-skeleton-loading 1.4s ease infinite !important;
}

/* 时间周期选择器深色主题 */
.toolbar-center .ant-radio-group .ant-radio-button-wrapper {
  background: #262626 !important;
  border-color: #434343 !important;
  color: #fff !important;
}

.toolbar-center .ant-radio-group .ant-radio-button-wrapper:hover {
  background: #303030 !important;
  border-color: #595959 !important;
}

.toolbar-center .ant-radio-group .ant-radio-button-wrapper-checked {
  background: #1890ff !important;
  border-color: #1890ff !important;
  color: #fff !important;
}

/* 工具栏按钮深色主题 */
.toolbar-right .ant-btn {
  background: #262626 !important;
  border-color: #434343 !important;
  color: #fff !important;
}

.toolbar-right .ant-btn:hover {
  background: #303030 !important;
  border-color: #595959 !important;
  color: #fff !important;
}

/* 滚动条样式 - 优化滚动体验 */
.charts-grid-container::-webkit-scrollbar {
  width: 6px;
}

.charts-grid-container::-webkit-scrollbar-track {
  background: #1a1a1a;
  border-radius: 3px;
}

.charts-grid-container::-webkit-scrollbar-thumb {
  background: #447A53;
  border-radius: 3px;
  transition: background 0.3s ease;
}

.charts-grid-container::-webkit-scrollbar-thumb:hover {
  background: #26A69A;
}

/* 确保滚动平滑 */
.charts-grid-container {
  scroll-behavior: smooth;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr; /* 小屏幕改为单列 */
  }
  
  .chart-grid-item {
    height: calc((100vh - 20.5vh - 200px - 16px) / 2); /* 中等屏幕减去top-charts高度 */
    min-height: calc((100vh - 20.5vh - 200px - 16px) / 2);
    max-height: calc((100vh - 20.5vh - 200px - 16px) / 2);
  }
}

@media (max-width: 768px) {
  .global-toolbar {
    flex-direction: column;
    gap: 12px;
    padding: 12px;
  }
  
  .toolbar-left,
  .toolbar-center,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }
  
  .chart-grid-item {
    height: calc((100vh - 25vh - 240px - 16px) / 2); /* 移动端减去top-charts高度 */
    min-height: calc((100vh - 25vh - 240px - 16px) / 2);
    max-height: calc((100vh - 25vh - 240px - 16px) / 2);
  }
  
  .charts-grid-container {
    padding: 8px;
  }
  
  .charts-grid {
    gap: 8px;
  }
}