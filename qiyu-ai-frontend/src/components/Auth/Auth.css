/* 认证页面容器 */
.auth-container {
  min-height: 100vh;
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, 
    hsl(158, 60%, 50%) 0%, 
    hsl(158, 60%, 40%) 25%, 
    hsl(180, 60%, 40%) 50%, 
    hsl(200, 60%, 40%) 75%, 
    hsl(220, 60%, 50%) 100%
  );
  overflow: hidden;
}

/* 背景装饰元素 */
.auth-decorations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.decoration {
  position: absolute;
  opacity: 0.9;
  z-index: 15;
}

.decoration-1 {
  top: 8%;
  left: 6%;
  width: 120px;
  height: 120px;
}

.decoration-2 {
  top: 12%;
  right: 8%;
  width: 160px;
  height: 160px;
}

.decoration-3 {
  bottom: 20%;
  left: 3%;
  width: 100px;
  height: 100px;
}

.decoration-4 {
  bottom: 12%;
  right: 5%;
  width: 140px;
  height: 140px;
}

.decoration-5 {
  bottom: 40%;
  right: 20%;
  width: 80px;
  height: 80px;
}

.decoration-f {
  bottom: 8%;
  right: 45%;
  width: 100px;
  height: 100px;
  z-index: 5;
}

/* 表单容器 */
.auth-form-container {
  position: relative;
  z-index: 10;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.auth-form {
  width: 100%;
  max-width: 400px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 32px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 头部区域 */
.auth-header {
  text-align: center;
  margin-bottom: 32px;
}

.auth-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 16px;
}

.auth-logo-image {
  width: 48px;
  height: 48px;
}

.auth-logo-text {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-size: 28px;
  font-weight: 700;
  color: white;
}

.auth-subtitle {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

/* 表单内容 */
.auth-form-content {
  margin-bottom: 24px;
}

.auth-form-content .ant-form-item-label > label {
  color: white;
  font-weight: 500;
  font-size: 14px;
}

.auth-input {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: white !important;
  border-radius: 8px !important;
}

.auth-input::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

.auth-input:focus,
.auth-input:hover {
  background: rgba(255, 255, 255, 0.15) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1) !important;
}

.auth-input .anticon {
  color: rgba(255, 255, 255, 0.7) !important;
}

/* 短信验证码输入组 */
.sms-input-group {
  display: flex;
  gap: 12px;
}

.sms-input {
  flex: 1;
}

.sms-btn {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: white !important;
  border-radius: 8px !important;
  white-space: nowrap;
  min-width: 120px;
}

.sms-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
}

.sms-btn:disabled {
  background: rgba(255, 255, 255, 0.05) !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.4) !important;
}

/* 忘记密码链接 */
.auth-forgot {
  text-align: right;
  margin-bottom: 24px;
}

.auth-forgot-link {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.3s ease;
}

.auth-forgot-link:hover {
  color: white;
  text-decoration: underline;
}

/* 提交按钮 */
.auth-submit-btn {
  background: linear-gradient(90deg, #4ECDC4 0%, #44A08D 100%) !important;
  border: none !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  font-size: 16px !important;
  height: 48px !important;
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.3) !important;
  transition: all 0.3s ease !important;
}

.auth-submit-btn:hover:not(:disabled) {
  background: linear-gradient(90deg, #44A08D 0%, #4ECDC4 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 6px 16px rgba(78, 205, 196, 0.4) !important;
}

/* 切换链接 */
.auth-switch {
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.auth-switch-link {
  background: none;
  border: none;
  color: white;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  text-decoration: none;
  margin-left: 8px;
  transition: color 0.3s ease;
}

.auth-switch-link:hover {
  color: #4ECDC4;
  text-decoration: underline;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .auth-form {
    max-width: 400px;
  }
}

@media (max-width: 767px) {
  .auth-form {
    margin: 20px;
    padding: 28px 20px;
    max-width: none;
  }

  .auth-logo-text {
    font-size: 24px;
  }

  .auth-subtitle {
    font-size: 14px;
  }

  /* 移动端装饰元素缩小但保留 */
  .decoration-1 {
    width: 60px;
    height: 60px;
  }

  .decoration-2 {
    width: 80px;
    height: 80px;
  }

  .decoration-3 {
    width: 50px;
    height: 50px;
  }

  .decoration-4 {
    width: 70px;
    height: 70px;
  }

  .decoration-5 {
    width: 40px;
    height: 40px;
  }

  .decoration-f {
    width: 60px;
    height: 60px;
  }
}

@media (max-width: 480px) {
  .sms-input-group {
    flex-direction: column;
  }
  
  .sms-btn {
    min-width: auto;
  }
}
