"""
🎯 AI分析提示词模板 - 数据转换为AI语言的桥梁

📋 主要功能：
- 将技术指标数据转换为AI可理解的结构化提示词
- 生成专业的加密货币分析指令
- 确保AI输出标准化的多空预测格式

🔄 在数据流程链路中的位置：
_build_prediction_prompt → [AIPromptTemplates] → DeepSeek AI

🏗️ 核心模板：
- build_structured_prediction_prompt(): 结构化多空预测提示词（主要使用）
- build_basic_analysis_prompt(): 基础分析提示词
- build_web_analysis_prompt(): Web专用分析提示词

🎯 关键作用：将复杂的市场数据转换为AI能理解的专业分析指令
"""

import json
from typing import Dict, List, Optional


class AIPromptTemplates:
    """AI提示词模板类"""
    
    @staticmethod
    def build_basic_analysis_prompt(
        real_time_price: Dict,
        formatted_daily_klines: List[Dict],
        formatted_short_term_klines: List[Dict],
        latest_date: str
    ) -> str:
        """构建基础分析提示词"""
        return f"""以下是截至{latest_date}的加密货币价格数据，请在回答中参考这些数据：

【实时价格数据】: {json.dumps(real_time_price, ensure_ascii=False)}

【90天日K线数据】（用于长期趋势分析，共{len(formatted_daily_klines)}条记录）: {json.dumps(formatted_daily_klines, ensure_ascii=False)}

【短期K线数据】（用于短期技术分析，共{len(formatted_short_term_klines)}条记录）: {json.dumps(formatted_short_term_klines[-100:], ensure_ascii=False)}

重要提示：
- 90天日K线数据适用于长期趋势分析、支撑阻力位判断
- 短期K线数据适用于短线交易信号、入场时机判断
- 请根据用户的分析需求选择合适的数据进行分析
- 所有数据均为真实市场数据，请务必基于这些数据进行专业分析

请基于以上数据进行分析，给出持仓建议。"""

    @staticmethod
    def build_historical_comparison_prompt(
        real_time_price: Dict,
        formatted_daily_klines: List[Dict],
        formatted_short_term_klines: List[Dict],
        latest_date: str,
        target_date: str,
        historical_data: Dict
    ) -> str:
        """构建历史同期对比分析提示词"""
        
        historical_analysis_section = f"""

【历史同期对比分析】（目标日期：{target_date}）:
- 分析范围：{historical_data.get('target_date', target_date)}前后3天
- 历史年份数据：{historical_data.get('total_years', 0)}年
- 历史同期数据：{json.dumps(historical_data.get('historical_data', {}), ensure_ascii=False)}
- 目标时间段技术指标：{json.dumps(historical_data.get('target_indicators', {}), ensure_ascii=False)}
- 历史同期技术指标对比：{json.dumps(historical_data.get('historical_indicators', {}), ensure_ascii=False)}

【历史同期分析要求】：
- 请重点关注历史同期的价格走势和技术指标表现
- 对比分析目标时间与历史同期的相似性和差异性
- 基于历史同期数据预测未来走势方向（多/空）
- 给出明确的情绪分析（非常看跌/看跌/其他/看涨/非常看涨）
- 分析历史同期的成功率和失败率模式"""
        
        return f"""以下是截至{latest_date}的加密货币价格数据，请在回答中参考这些数据：

【实时价格数据】: {json.dumps(real_time_price, ensure_ascii=False)}

【90天日K线数据】（用于长期趋势分析，共{len(formatted_daily_klines)}条记录）: {json.dumps(formatted_daily_klines, ensure_ascii=False)}

【短期K线数据】（用于短期技术分析，共{len(formatted_short_term_klines)}条记录）: {json.dumps(formatted_short_term_klines[-100:], ensure_ascii=False)}{historical_analysis_section}

【分析框架要求】：
1. 技术分析层面：
   - 基于90天日K线数据分析长期趋势
   - 基于短期K线数据分析短线信号
   - 结合技术指标（RSI、MACD、KDJ等）判断买卖点

2. 历史对比层面：
   - 对比历史同期的价格走势模式
   - 分析历史同期技术指标的表现
   - 识别历史重复模式和异常情况

3. 预测输出要求：
   - 明确预测方向：多（看涨）/空（看跌）
   - 情绪分析等级：非常看跌/看跌/其他/看涨/非常看涨
   - 置信度评估：0-100%
   - 风险等级：高/中/低

4. 分析结构：
   - 思考过程：详细的分析逻辑
   - 关键发现：重要的技术信号和历史模式
   - 最终结论：综合判断和投资建议

请基于以上数据进行深度分析，给出专业的投资建议。"""

    @staticmethod
    def build_structured_response_template() -> str:
        """构建结构化响应模板说明"""
        return """
请按照以下JSON格式输出分析结果：

{
    "prediction": "多" | "空",
    "sentiment": "非常看跌" | "看跌" | "其他" | "看涨" | "非常看涨",
    "confidence": 0.85,
    "risk_level": "高" | "中" | "低",
    "thinking_process": "详细的分析思考过程...",
    "key_findings": [
        "关键发现1",
        "关键发现2",
        "关键发现3"
    ],
    "historical_insights": {
        "pattern_match": "历史模式匹配度",
        "success_rate": "历史成功率",
        "anomalies": "异常情况说明"
    },
    "technical_signals": {
        "trend": "趋势方向",
        "support_resistance": "支撑阻力位",
        "indicators": "技术指标信号"
    },
    "conclusion": "最终投资建议和结论",
    "timestamp": "分析时间戳"
}
"""

    @staticmethod
    def build_quick_analysis_prompt(symbol: str, analysis_type: str) -> str:
        """构建快速分析提示词"""
        analysis_periods = {
            'quick': '未来24小时',
            'four_hour': '未来4小时',
            'one_day': '未来24小时',
            'three_day': '未来3天',
            'week': '未来一周',
            'long_term': '长期趋势'
        }
        
        period = analysis_periods.get(analysis_type, '未来24小时')
        
        return f"""请对{symbol}进行{analysis_type}分析，预测{period}的价格走势。

分析要求：
1. 基于提供的K线数据和技术指标
2. 给出明确的方向预测（多/空）
3. 评估风险等级和置信度
4. 提供具体的操作建议

请简洁明了地给出分析结果。"""

    @staticmethod
    def build_web_analysis_prompt(symbol: str, crypto_context: str, target_date: str = None) -> str:
        """构建Web版本专用的AI分析提示词 - 专注于三大要素"""
        date_context = f"基于{target_date}的市场数据，" if target_date else "基于当前市场数据，"
        
        return f"""你是一位专业的加密货币分析师。{date_context}请进行深度技术分析。

{crypto_context}

请按以下结构提供分析报告：

## 1. 市场情绪分析
从以下5个等级中选择当前市场情绪：
- **非常看涨** - 市场极度乐观，买盘强劲
- **看涨** - 市场情绪积极，多头占主导
- **其他** - 市场情绪中性，多空平衡
- **看跌** - 市场情绪偏负面，空头力量增强
- **非常看跌** - 市场极度悲观，抛售压力巨大

请明确选择其中一个等级，并说明判断依据。

## 2. BTC和ETH多空预测

### BTC技术分析：
- **MACD指标**：DIF线、DEA线、MACD柱状图数值及趋势
- **KDJ指标**：K值、D值、J值的具体数值及超买/超卖判断
- **RSI指标**：RSI数值、强弱区间判断、背离信号
- **多空判断**：明确看多/看空，给出具体理由
- **关键价位**：重要支撑位、阻力位、短期目标价

### ETH技术分析：
- **MACD指标**：DIF线、DEA线、MACD柱状图数值及趋势
- **KDJ指标**：K值、D值、J值的具体数值及超买/超卖判断
- **RSI指标**：RSI数值、强弱区间判断、背离信号
- **多空判断**：明确看多/看空，给出具体理由
- **关键价位**：重要支撑位、阻力位、短期目标价

## 3. AI分析思考过程
- **数据解读思路**：如何从K线图形中识别趋势和形态
- **指标权重分配**：MACD、KDJ、RSI在当前市况下的重要性排序
- **信号确认逻辑**：多个指标如何相互验证形成交易信号
- **风险因素识别**：当前市场面临的主要风险点
- **结论推导过程**：从技术面分析到最终判断的完整推理链条
- **置信度评估**：对本次分析结果的信心程度（1-10分，10分最高）

注意：请基于提供的真实K线数据和技术指标进行分析，给出准确、专业的判断。"""

    @staticmethod
    def build_ai_kline_analysis_prompt(
        symbol: str,
        target_date: str,
        target_period_data: List[Dict],
        historical_same_period: List[Dict],
        btc_reference_data: Dict,
        technical_indicators: Dict
    ) -> str:
        """
        🎯 构建基于完整K线数据的AI分析提示词 - 让AI直接计算支撑阻力位
        
        📦 输入数据（根据用户要求精简）：
        - target_period_data: 目标日、前一天、后一天的数据
        - historical_same_period: 历年同期数据（前一日、目标日、后一日）
        - btc_reference_data: BTC同期参考数据
        - technical_indicators: 预计算的技术指标（MACD、KDJ、RSI）
        
        🎯 让AI自己分析和计算支撑阻力位，基于精准的历史同期数据
        """
        import json
        
        return f"""你是一位顶级的量化分析师和加密货币专家。已为你准备了精准的历史同期数据，包括目标日和前后天的价格数据，以及历年同期的对比数据，并计算了MACD、KDJ和RSI参数。请你结合历史同期数据进行深度分析，并给出明确的回答是看多还是看空并给出2小时和12小时涨跌概率和目标点位。

### **数据提供**

#### 1. 目标期间数据 (目标日、前一天、后一天)
```json
{json.dumps(target_period_data, ensure_ascii=False)}
```

#### 2. 历史同期数据对比 (历年同期前一日/目标日/后一日)
```json
{json.dumps(historical_same_period, ensure_ascii=False)}
```

#### 3. BTC同期参考数据 (大盘联动分析)
```json
{json.dumps(btc_reference_data, ensure_ascii=False)}
```

#### 4. 技术指标计算结果
- **MACD**: DIF={technical_indicators.get('MACD', {}).get('DIF', 0):.4f}, DEA={technical_indicators.get('MACD', {}).get('DEA', 0):.4f}, MACD={technical_indicators.get('MACD', {}).get('MACD', 0):.4f}
- **KDJ**: K={technical_indicators.get('KDJ', {}).get('K', 50):.2f}, D={technical_indicators.get('KDJ', {}).get('D', 50):.2f}, J={technical_indicators.get('KDJ', {}).get('J', 50):.2f}
- **RSI**: {technical_indicators.get('RSI', {}).get('value', 50):.1f}

### **分析要求**

请你作为专业分析师，**基于历史同期模式**重新计算和验证支撑阻力位，不要依赖任何预设数值。

#### 1. 历史同期深度分析
- 对比历年同期的价格走势特征和规律
- 识别历史同期的成功交易模式和失败案例
- 分析当前时点与历史同期的相似度和差异性
- 评估历史同期数据的统计意义和预测价值

#### 2. 专业支撑阻力位计算
请你基于历史同期数据使用专业方法重新计算：
- **关键支撑位**: 基于历史同期低点、成交量聚集、重要价位
- **关键阻力位**: 基于历史同期高点、前期阻力、心理价位
- **目标点位**: 结合历史同期表现的2小时和12小时价格目标

#### 3. BTC大盘联动分析
- 分析BTC走势对目标币种的影响程度
- 评估大盘情绪对个币表现的联动性
- 结合BTC技术指标判断整体市场方向

#### 4. 概率量化分析
- **2小时涨跌概率**: 看涨X%，看跌Y% (基于历史同期统计)
- **12小时涨跌概率**: 看涨X%，看跌Y% (基于历史同期统计)
- **风险收益比**: 每个时间框架的风险收益评估

### **输出格式要求**

请严格按照以下格式输出：

```
## {symbol.replace('USDT', '')} 历史同期量化分析报告 ({target_date})

### 一、历史同期模式分析
- **历史数据覆盖**: X年数据，共X个同期样本
- **历史同期表现**: 上涨X次，下跌X次，成功率XX%
- **相似度评分**: X/10分 (与历史最相似年份: XXXX年)
- **关键差异**: 当前与历史同期的主要不同点

### 二、支撑阻力位重新计算 (基于历史同期)
- **主要支撑位**: XXX.XX USDT (历史同期支撑成功率: XX%)
- **次要支撑位**: XXX.XX USDT (历史同期支撑成功率: XX%)  
- **主要阻力位**: XXX.XX USDT (历史同期阻力成功率: XX%)
- **次要阻力位**: XXX.XX USDT (历史同期阻力成功率: XX%)

### 三、概率化预测 (基于历史统计)
| 时间框架 | 看涨概率 | 看跌概率 | 目标价位 | 止损位 | 历史依据 |
|---------|---------|---------|---------|-------|------------|
| 2小时   | XX%     | XX%     | XXX.XX  | XXX.XX | 历史同期XX%概率 |
| 12小时  | XX%     | XX%     | XXX.XX  | XXX.XX | 历史同期XX%概率 |

### 四、BTC大盘联动影响
- **BTC当前走势**: {btc_reference_data.get('trend', '中性')} ({btc_reference_data.get('change_percent', 0):+.1f}%)
- **联动影响评估**: 高/中/低影响程度
- **大盘情绪**: 看多/中性/看空

### 五、交易建议
- **多头策略**: 入场价位、目标价位、止损价位 (历史成功率: XX%)
- **空头策略**: 入场价位、目标价位、止损价位 (历史成功率: XX%)
- **风险等级**: 高/中/低

### 六、最终结论
**方向判断**: 看多/看空
**信心指数**: X/10分
**核心逻辑**: 基于X年历史同期数据，当前市况与XXXX年最为相似...
```

**重要**: 所有数值必须基于你对历史同期数据的专业统计分析，请特别重视历史同期的规律性和预测价值。展现出量化分析师的专业水准。
"""

    @staticmethod
    def build_structured_prediction_prompt(
        symbol: str,
        target_date: str,
        current_price: float,
        support_level: float,
        resistance_level: float,
        technical_indicators: Dict,
        prev_day_price: float,
        historical_prices: List[float],
        btc_data: Dict = None
    ) -> str:
        """
        🎯 构建结构化多空预测提示词 - 链路的AI指令生成器
        
        📦 输入数据（来自analysis_result）：
        - technical_indicators: 技术指标字典（MACD、KDJ、RSI）
        - current_price: 当前价格
        - support_level/resistance_level: 支撑阻力位
        - historical_prices: 历史价格数组
        - btc_data: BTC参考数据
        
        🔄 处理流程：
        1. 提取技术指标数值
        2. 格式化价格数据
        3. 构建结构化提示词
        4. 指定AI输出格式（多空预测+市场情绪）
        
        🎯 输出：包含所有分析数据的结构化AI提示词文本
        """
        
        # 🔧 步骤1：格式化技术指标数据
        macd_info = technical_indicators.get('MACD', {})
        kdj_info = technical_indicators.get('KDJ', {})  
        rsi_info = technical_indicators.get('RSI', {})
        
        # 格式化BTC参考数据
        btc_section = ""
        if btc_data and 'price_change' in btc_data:
            btc_change = btc_data['price_change']
            btc_indicators = btc_data.get('technical_indicators', {})
            btc_macd = btc_indicators.get('MACD', {})
            
            btc_section = f"""- BTC 同时段走势：{btc_change.get('trend', '无数据')} {btc_change.get('change_percent', 0):+.1f}%，MACD {'红柱增强' if btc_macd.get('MACD', 0) > 0 else '绿柱增强' if btc_macd.get('MACD', 0) < 0 else '零轴附近'}"""
        
        return f"""你是一位顶级的加密货币分析师。请根据以下为你准备的实时和历史行情数据，为目标币种 {symbol} 生成一份专业的持仓分析与建议。

### **第一部分：详细分析报告**
请严格按照以下Markdown格式生成报告：

## {symbol.replace('USDT','')} 未来24小时持仓分析与建议

#### 一、关键指标计算结果（基于完整数据）
1.  **MACD（日线，参数12/26/9）**：
    -   DIF = {macd_info.get('DIF', 0):.2f}
    -   DEA = {macd_info.get('DEA', 0):.2f}
    -   MACD柱 = {macd_info.get('MACD', 0):.2f}（请基于此数值分析并填写**上涨/下跌动能**）

2.  **RSI（日线，参数14）**：
    -   RSI = {rsi_info.get('value', 50):.0f}
    -   请基于此数值分析并填写**强度状态**（例如：中性、超买、超卖）

3.  **KDJ（日线，参数9/3/3）**：
    -   K = {kdj_info.get('K', 50):.2f}
    -   D = {kdj_info.get('D', 50):.2f}
    -   J = {kdj_info.get('J', 50):.2f}
    -   请基于此数值分析并给出**短期信号**（例如：金叉、死叉、超买信号）

4.  **价格与成交量分析**：
    -   当前价格：{current_price:.2f} USDT
    -   前一日价格：{prev_day_price:.2f} USDT
    -   历史同期价格参考（过去三日）：{', '.join([f'{p:.2f}' for p in historical_prices[:3]])}
    -   关键支撑位：{support_level:.2f} USDT
    -   关键阻力位：{resistance_level:.2f} USDT

---

#### 二、趋势分析与预测
| 时间框架 | 看涨概率 | 看跌概率 | 目标点位 | 关键依据 |
|:---:|:---:|:---:|:---:|:---|
| **2小时** | ...% | ...% | **支撑/阻力位** | ... |
| **12小时**| ...% | ...% | **目标** | ... |
| **24小时**| ...% | ...% | **目标** | ... |

**关键价格区间**：
-   强支撑：...
-   阻力位：...

---

#### 三、持仓建议（当前价 {current_price:.2f}）
1.  **短期操作（0-2小时）**：
    -   ...
2.  **中期策略（12-24小时）**：
    -   ...
3.  **风险控制**：
    -   ...

---

### 结论：**[请填写：谨慎看多/看多/谨慎看空/看空/中性]**
-   **核心逻辑**：...
-   **最佳策略**：...

---
### **第二部分：机器可读摘要**
（请务必按此格式提供，用于系统解析）

【分析总结】
[此处填写150字以内的简洁分析总结]

【多空预测】
看涨: ...%，非常看涨: ...%，中性: ...%，非常看跌: ...%，看跌: ...%

【市场情绪】
BTC 多头占比: ...%，{symbol.replace('USDT', '')} 多头占比: ...%
"""


# 使用示例
if __name__ == "__main__":
    templates = AIPromptTemplates()
    
    # 测试基础分析提示词
    basic_prompt = templates.build_basic_analysis_prompt(
        real_time_price={"symbol": "BTCUSDT", "price": "45000"},
        formatted_daily_klines=[],
        formatted_short_term_klines=[],
        latest_date="2024-01-15 10:30:00"
    )
    
    print("基础分析提示词:")
    print(basic_prompt)
    
    # 测试结构化响应模板
    response_template = templates.build_structured_response_template()
    print("\n结构化响应模板:")
    print(response_template)
