#!/usr/bin/env python3
"""
Django管理命令 - 运行自动AI预测任务
"""
import logging
from django.core.management.base import BaseCommand
from django.utils import timezone
from ai_analysis.auto_prediction_manager import AutoPredictionManager

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = '运行自动AI预测任务 - 为多个币种生成预测分析'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--setup',
            action='store_true',
            help='设置默认的自动预测任务（7个主流币种）'
        )
        
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制运行所有任务，忽略15分钟间隔'
        )
        
        parser.add_argument(
            '--status',
            action='store_true',
            help='查看任务状态概览'
        )
        
        parser.add_argument(
            '--daemon',
            action='store_true',
            help='守护进程模式，每15分钟自动运行一次'
        )
        
        parser.add_argument(
            '--symbols',
            type=str,
            help='指定要设置的币种，用逗号分隔（如：BTCUSDT,ETHUSDT）'
        )
    
    def handle(self, *args, **options):
        manager = AutoPredictionManager()
        
        if options['setup']:
            self.setup_tasks(manager, options.get('symbols'))
            return
        
        if options['status']:
            self.show_status(manager)
            return
        
        if options['daemon']:
            self.run_daemon(manager, options.get('force', False))
            return
        
        # 单次运行
        self.run_once(manager, options.get('force', False))
    
    def setup_tasks(self, manager, symbols_str):
        """设置自动预测任务"""
        self.stdout.write(self.style.SUCCESS('🔧 设置自动预测任务...'))
        
        symbols = None
        if symbols_str:
            symbols = [s.strip().upper() for s in symbols_str.split(',')]
            self.stdout.write(f"指定币种: {', '.join(symbols)}")
        
        result = manager.setup_default_tasks(symbols)
        
        self.stdout.write(self.style.SUCCESS(
            f"✅ 任务设置完成:\n"
            f"  - 总币种数: {result['total_symbols']}\n"
            f"  - 新创建: {result['created']}\n"
            f"  - 已更新: {result['updated']}\n"
            f"  - 币种列表: {', '.join(result['symbols'])}"
        ))
    
    def show_status(self, manager):
        """显示任务状态"""
        status = manager.get_task_status()
        
        self.stdout.write(self.style.SUCCESS('📊 自动预测任务状态:'))
        self.stdout.write(f"  - 总任务数: {status['total_tasks']}")
        self.stdout.write(f"  - 启用任务: {status['active_tasks']}")
        self.stdout.write(f"  - 禁用任务: {status['inactive_tasks']}")
        self.stdout.write(f"  - 近1小时预测: {status['recent_predictions_1h']}")
        self.stdout.write(f"  - 处理状态: {'运行中' if status['processing'] else '空闲'}")
        
        if status['tasks']:
            self.stdout.write('\n📋 任务详情:')
            for task in status['tasks']:
                status_icon = '🟢' if task['is_active'] else '🔴'
                last_run = task['last_run_at'][:19] if task['last_run_at'] else '从未运行'
                next_run = task['next_run_at'][:19] if task['next_run_at'] else '未安排'
                
                self.stdout.write(
                    f"  {status_icon} {task['symbol']} "
                    f"(P{task['priority']}) "
                    f"成功率: {task['success_rate']}% "
                    f"运行: {task['total_runs']}次"
                )
                self.stdout.write(f"      上次: {last_run}, 下次: {next_run}")
    
    def run_once(self, manager, force):
        """单次运行预测任务"""
        self.stdout.write(self.style.SUCCESS(
            f"🚀 开始运行自动预测任务 {'(强制模式)' if force else '(定时模式)'}..."
        ))
        
        result = manager.run_auto_predictions(force_all=force)
        
        if result['status'] == 'completed':
            self.stdout.write(self.style.SUCCESS(
                f"✅ 预测任务完成:\n"
                f"  - 总任务: {result['total_tasks']}\n"
                f"  - 成功: {result['successful']}\n"
                f"  - 失败: {result['failed']}\n"
                f"  - 跳过: {result['skipped']}\n"
                f"  - 耗时: {result['execution_time']}秒"
            ))
            
            # 显示任务详情
            if result.get('task_details'):
                self.stdout.write('\n📋 任务详情:')
                for detail in result['task_details']:
                    status_icon = '✅' if detail['status'] == 'success' else '❌' if detail['status'] == 'failed' else '⏭️'
                    self.stdout.write(
                        f"  {status_icon} {detail['symbol']} "
                        f"({detail['duration']}s) {detail['message']}"
                    )
        elif result['status'] == 'skipped':
            self.stdout.write(self.style.WARNING(f"⏭️ 任务被跳过: {result['reason']}"))
        else:
            self.stdout.write(self.style.ERROR(f"❌ 任务执行失败: {result.get('error', '未知错误')}"))
    
    def run_daemon(self, manager, force):
        """守护进程模式"""
        import time
        
        self.stdout.write(self.style.SUCCESS(
            '🔄 启动守护进程模式 - 每15分钟运行一次自动预测任务'
        ))
        self.stdout.write('按 Ctrl+C 停止')
        
        try:
            while True:
                try:
                    self.stdout.write(f"\n⏰ {timezone.now().strftime('%Y-%m-%d %H:%M:%S')} - 运行预测任务")
                    result = manager.run_auto_predictions(force_all=force)
                    
                    if result['status'] == 'completed':
                        self.stdout.write(
                            f"✅ 完成: {result['successful']}成功, {result['failed']}失败, "
                            f"{result['skipped']}跳过, 耗时{result['execution_time']}秒"
                        )
                    elif result['status'] == 'skipped':
                        self.stdout.write(f"⏭️ {result['reason']}")
                    else:
                        self.stdout.write(self.style.ERROR(f"❌ 失败: {result.get('error')}"))
                    
                    # 等待15分钟
                    self.stdout.write(f"😴 等待15分钟后继续...")
                    time.sleep(15 * 60)  # 15分钟 = 900秒
                    
                except KeyboardInterrupt:
                    raise
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f"❌ 运行出错: {e}"))
                    self.stdout.write("等待1分钟后重试...")
                    time.sleep(60)
                    
        except KeyboardInterrupt:
            self.stdout.write(self.style.SUCCESS('\n👋 守护进程已停止'))
    
    def handle_daemon_interrupt(self, signum, frame):
        """处理守护进程中断信号"""
        self.stdout.write(self.style.SUCCESS('\n📡 收到停止信号，正在安全关闭...'))
        raise KeyboardInterrupt