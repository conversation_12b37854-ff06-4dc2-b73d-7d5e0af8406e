# 优化后的Cron调度 - 避免任务冲突，实现用户需求
# K线数据每5分钟更新，AI预测每15分钟执行

# 1分钟K线数据收集 - 每5分钟执行 (1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56分)
1,6,11,16,21,26,31,36,41,46,51,56 * * * * cd /home/<USER>/qiyuai-web/finance_backend && python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=1m --start=$(date +\%Y-\%m-\%d) --end=$(date +\%Y-\%m-\%d) >> logs/auto_update.log 2>&1

# 15分钟数据更新 - 每15分钟的第2分钟执行 (2, 17, 32, 47分)
2,17,32,47 * * * * cd /home/<USER>/qiyuai-web/finance_backend && python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=15m --start=$(date +\%Y-\%m-\%d) --end=$(date +\%Y-\%m-\%d) >> logs/auto_update.log 2>&1

# 1小时数据更新 - 每小时第7分钟执行
7 * * * * cd /home/<USER>/qiyuai-web/finance_backend && python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=1h --start=$(date +\%Y-\%m-\%d) --end=$(date +\%Y-\%m-\%d) >> logs/auto_update.log 2>&1

# 4小时数据更新 - 每4小时第12分钟执行  
12 */4 * * * cd /home/<USER>/qiyuai-web/finance_backend && python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=4h --start=$(date +\%Y-\%m-\%d) --end=$(date +\%Y-\%m-\%d) >> logs/auto_update.log 2>&1

# 日数据更新 - 每天1:18更新昨日数据
18 1 * * * cd /home/<USER>/qiyuai-web/finance_backend && python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=1d --start=$(date -d "1 day ago" +\%Y-\%m-\%d) --end=$(date -d "1 day ago" +\%Y-\%m-\%d) >> logs/auto_update.log 2>&1

# 当日数据更新 - 每天2:05更新当日数据
5 2 * * * cd /home/<USER>/qiyuai-web/finance_backend && python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=1d --start=$(date +\%Y-\%m-\%d) --end=$(date +\%Y-\%m-\%d) >> logs/auto_update.log 2>&1

# AI预测任务 - 每15分钟第3分钟执行 (3, 18, 33, 48分) - 避开其他数据更新任务
3,18,33,48 * * * * /usr/bin/python3 /home/<USER>/qiyuai-web/finance_backend/manage.py cron_predictions >> /home/<USER>/qiyuai-web/finance_backend/logs/predictions/cron_predictions.log 2>&1

# 实时数据收集进程检查 - 每35分钟检查 (避开其他任务)
35 * * * * if ! pgrep -f "collect_realtime_data.*BTCUSDT" > /dev/null; then cd /home/<USER>/qiyuai-web/finance_backend && nohup python3 manage.py collect_realtime_data --symbol=BTCUSDT --daemon --debug > logs/btc_collector.log 2>&1 & fi

# 日志清理 - 每小时第55分钟执行
55 * * * * cd /home/<USER>/qiyuai-web/finance_backend && tail -n 1000 logs/auto_update.log > logs/auto_update.log.tmp && mv logs/auto_update.log.tmp logs/auto_update.log
