#!/usr/bin/env python3
"""
增强版Cron AI预测任务 - 带高级锁管理和防死锁机制
"""
import os
import sys
import logging
from datetime import datetime
from pathlib import Path
from django.core.management.base import BaseCommand
from django.utils import timezone

# 添加项目路径以导入高级锁管理器
sys.path.append(str(Path(__file__).parent.parent.parent.parent))
from advanced_lock_manager import AdvancedLockManager, LockAlreadyAcquiredError
from ai_analysis.auto_prediction_manager import AutoPredictionManager

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = '增强版AI预测任务 - 高级锁管理和防死锁'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制运行所有任务，忽略15分钟间隔'
        )
        
        parser.add_argument(
            '--lock-timeout',
            type=int,
            default=900,  # 15分钟正常超时
            help='锁超时时间（秒）'
        )
        
        parser.add_argument(
            '--force-timeout',
            type=int,
            default=1800,  # 30分钟强制释放
            help='强制释放锁的时间（秒）'
        )
        
        parser.add_argument(
            '--log-level',
            choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
            default='INFO',
            help='日志级别'
        )
    
    def handle(self, *args, **options):
        # 设置日志级别
        logging.basicConfig(
            level=getattr(logging, options['log_level']),
            format='%(asctime)s [%(levelname)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 初始化高级锁管理器
        lock_manager = AdvancedLockManager()
        
        try:
            # 使用高级锁管理器
            with lock_manager.acquire_lock(
                'ai_predictions',
                timeout=options['lock_timeout'],
                force_after=options['force_timeout']
            ):
                self.run_predictions(options)
                
        except LockAlreadyAcquiredError:
            logger.warning("⚠️ 另一个预测任务正在运行，跳过本次执行")
            self.stdout.write("⚠️ 另一个预测任务正在运行，跳过本次执行")
            
        except TimeoutError as e:
            logger.error(f"💀 预测任务超时: {e}")
            self.stdout.write(f"💀 预测任务超时: {e}")
            sys.exit(1)
            
        except Exception as e:
            logger.error(f"❌ 预测任务异常: {e}")
            self.stdout.write(f"❌ 预测任务异常: {e}")
            sys.exit(1)
    
    def run_predictions(self, options):
        """执行预测任务"""
        start_time = datetime.now()
        
        logger.info("🚀 开始执行AI预测任务")
        self.stdout.write(f"🚀 开始执行AI预测任务 - {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            manager = AutoPredictionManager()
            
            if options['force']:
                success_count, failure_count, skip_count = manager.run_auto_predictions(force=True)
                logger.info("强制执行模式：找到所有启用的任务")
            else:
                success_count, failure_count, skip_count = manager.run_auto_predictions()
            
            # 计算耗时
            duration = (datetime.now() - start_time).total_seconds()
            
            # 输出结果
            result_msg = f"✅ 预测任务完成 - 耗时: {duration:.1f}秒"
            result_detail = f"成功: {success_count}, 失败: {failure_count}, 跳过: {skip_count}"
            
            logger.info(result_msg)
            logger.info(result_detail)
            
            self.stdout.write(result_msg)
            self.stdout.write(f"   {result_detail}")
            
            # 如果有失败，记录详细信息但不退出
            if failure_count > 0:
                logger.warning(f"⚠️ 有 {failure_count} 个预测任务失败")
                
        except Exception as e:
            logger.error(f"执行预测任务时发生异常: {e}", exc_info=True)
            raise


# 保持向后兼容的旧命令名
class CronPredictionsCommand(Command):
    pass