/**
 * 全局调试助手
 * 在浏览器控制台中提供便捷的调试功能
 */

import aiAnalysisService from '../services/AIAnalysisService';
import AIAnalysisHelper from './aiAnalysisHelper';

class GlobalDebugHelper {
  constructor() {
    this.version = '1.0.0';
    this.lastAnalysisData = null;
    this.lastError = null;
    
    console.log('🔧 AI分析调试助手已加载');
    console.log('💡 使用 debugAI.help() 查看可用命令');
  }

  /**
   * 显示帮助信息
   */
  help() {
    console.log(`
🔧 AI分析调试助手 v${this.version}

📋 可用命令:
  debugAI.help()                    - 显示此帮助信息
  debugAI.testAnalysis(symbol)      - 测试AI分析功能
  debugAI.testDataFetch(symbol)     - 测试数据获取
  debugAI.validateData(data)        - 验证数据格式
  debugAI.checkIntegrity(data)      - 检查数据完整性
  debugAI.createFallback(symbol)    - 创建降级数据
  debugAI.getLastData()             - 获取最后一次分析数据
  debugAI.getLastError()            - 获取最后一次错误
  debugAI.exportDebugInfo()         - 导出调试信息
  debugAI.clearCache()              - 清除缓存
  debugAI.getServiceStatus()        - 获取服务状态

🎯 示例:
  debugAI.testAnalysis('BTC')       - 测试BTC分析
  debugAI.testDataFetch('ETH')      - 测试ETH数据获取
    `);
  }

  /**
   * 测试AI分析功能
   */
  async testAnalysis(symbol = 'BTC', analysisType = 'quick') {
    console.log(`🚀 测试AI分析: ${symbol} - ${analysisType}`);
    
    try {
      const result = await aiAnalysisService.analyzeWithAI(symbol, analysisType, {
        onProgress: (message, type) => {
          console.log(`📊 进度: ${type} - ${message.substring(0, 100)}...`);
        },
        enableFallback: true
      });
      
      console.log('✅ 分析完成:', result.length, '字符');
      console.log('📄 结果预览:', result.substring(0, 200) + '...');
      
      return result;
    } catch (error) {
      console.error('❌ 分析失败:', error);
      this.lastError = error;
      return null;
    }
  }

  /**
   * 测试数据获取
   */
  async testDataFetch(symbol = 'BTC', analysisType = 'quick') {
    console.log(`📥 测试数据获取: ${symbol}`);
    
    try {
      // 测试K线数据
      console.log('📊 获取K线数据...');
      const klineData = await aiAnalysisService.fetchKlineData(symbol, analysisType);
      console.log('✅ K线数据:', {
        dailyCount: klineData.dailyKlines?.length || 0,
        shortTermCount: klineData.shortTermKlines?.length || 0
      });

      // 测试价格数据
      console.log('💰 获取价格数据...');
      const tickerData = await aiAnalysisService.fetchTickerData(symbol);
      console.log('✅ 价格数据:', tickerData);

      // 测试订单簿数据
      console.log('📋 获取订单簿数据...');
      const orderbookData = await aiAnalysisService.fetchOrderbookData(symbol, 20);
      console.log('✅ 订单簿数据:', {
        bidsCount: orderbookData?.bids?.length || 0,
        asksCount: orderbookData?.asks?.length || 0
      });

      // 测试BTC辅助数据
      if (symbol !== 'BTC') {
        console.log('🔗 获取BTC辅助数据...');
        const btcData = await aiAnalysisService.fetchBTCAuxiliaryData(analysisType);
        console.log('✅ BTC辅助数据:', btcData ? '已获取' : '获取失败');
      }

      const allData = {
        klineData,
        tickerData,
        orderbookData,
        btcData: symbol !== 'BTC' ? await aiAnalysisService.fetchBTCAuxiliaryData(analysisType) : null
      };

      this.lastAnalysisData = allData;
      console.log('📦 所有数据已保存到 debugAI.lastAnalysisData');
      
      return allData;
    } catch (error) {
      console.error('❌ 数据获取失败:', error);
      this.lastError = error;
      return null;
    }
  }

  /**
   * 验证数据格式
   */
  validateData(data) {
    if (!data) {
      console.warn('⚠️ 没有提供数据');
      return false;
    }

    const results = {};

    if (data.klineData?.dailyKlines) {
      results.dailyKlines = AIAnalysisHelper.validateKlineData(data.klineData.dailyKlines);
    }

    if (data.tickerData) {
      results.tickerData = AIAnalysisHelper.validateTickerData(data.tickerData);
    }

    if (data.orderbookData) {
      results.orderbookData = AIAnalysisHelper.validateOrderbookData(data.orderbookData);
    }

    console.log('🔍 数据验证结果:', results);
    return results;
  }

  /**
   * 检查数据完整性
   */
  checkIntegrity(data, symbol = 'BTC', analysisType = 'quick') {
    if (!data) {
      console.warn('⚠️ 没有提供数据');
      return null;
    }

    const integrity = AIAnalysisHelper.checkDataIntegrity(data, symbol, analysisType);
    console.log('📊 数据完整性检查:', integrity);
    
    if (integrity.hasIssues) {
      console.warn('⚠️ 发现问题:', integrity.issues);
    }
    
    if (integrity.hasWarnings) {
      console.warn('⚠️ 发现警告:', integrity.warnings);
    }

    return integrity;
  }

  /**
   * 创建降级数据
   */
  createFallback(symbol = 'BTC', analysisType = 'quick') {
    console.log(`🔄 创建降级数据: ${symbol}`);
    
    const fallbackData = AIAnalysisHelper.createFallbackData(symbol, analysisType);
    console.log('✅ 降级数据已创建:', {
      dailyKlinesCount: fallbackData.dailyKlines.length,
      shortTermKlinesCount: fallbackData.shortTermKlines.length,
      ticker: fallbackData.ticker.symbol,
      orderbookBids: fallbackData.orderbook.bids.length
    });

    return fallbackData;
  }

  /**
   * 获取最后一次分析数据
   */
  getLastData() {
    if (this.lastAnalysisData) {
      console.log('📦 最后一次分析数据:', this.lastAnalysisData);
      return this.lastAnalysisData;
    } else {
      console.log('📭 没有分析数据');
      return null;
    }
  }

  /**
   * 获取最后一次错误
   */
  getLastError() {
    if (this.lastError) {
      console.log('❌ 最后一次错误:', this.lastError);
      return this.lastError;
    } else {
      console.log('✅ 没有错误记录');
      return null;
    }
  }

  /**
   * 导出调试信息
   */
  exportDebugInfo() {
    const debugInfo = {
      timestamp: new Date().toISOString(),
      version: this.version,
      userAgent: navigator.userAgent,
      url: window.location.href,
      lastAnalysisData: this.lastAnalysisData,
      lastError: this.lastError ? {
        message: this.lastError.message,
        stack: this.lastError.stack,
        name: this.lastError.name
      } : null
    };

    const blob = new Blob([JSON.stringify(debugInfo, null, 2)], { 
      type: 'application/json' 
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ai-debug-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    console.log('📁 调试信息已导出');
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.lastAnalysisData = null;
    this.lastError = null;
    
    // 清除localStorage中的相关缓存
    const keys = Object.keys(localStorage);
    const aiKeys = keys.filter(key => key.includes('ai') || key.includes('analysis'));
    aiKeys.forEach(key => localStorage.removeItem(key));
    
    console.log('🗑️ 缓存已清除');
  }

  /**
   * 获取服务状态
   */
  getServiceStatus() {
    const status = {
      timestamp: new Date().toISOString(),
      aiAnalysisService: {
        baseURL: aiAnalysisService.baseURL,
        aiEndpoint: aiAnalysisService.aiEndpoint,
        quotationEndpoint: aiAnalysisService.quotationEndpoint
      },
      browser: {
        userAgent: navigator.userAgent,
        language: navigator.language,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine
      },
      page: {
        url: window.location.href,
        title: document.title,
        referrer: document.referrer
      }
    };

    console.log('📊 服务状态:', status);
    return status;
  }

  /**
   * 强制检查数据更新
   */
  forceCheckUpdates() {
    console.log('🔄 强制检查数据更新...');
    
    // 触发数据更新事件
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      const updateEvent = new CustomEvent('forceDataUpdate', {
        detail: {
          timestamp: Date.now(),
          source: 'debugHelper'
        }
      });
      window.dispatchEvent(updateEvent);
      console.log('✅ 数据更新事件已触发');
    }
  }

  /**
   * 触发图表刷新
   */
  triggerChartRefresh() {
    console.log('📊 触发图表刷新...');
    
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      const refreshEvent = new CustomEvent('chartRefresh', {
        detail: {
          timestamp: Date.now(),
          source: 'debugHelper'
        }
      });
      window.dispatchEvent(refreshEvent);
      console.log('✅ 图表刷新事件已触发');
    }
  }

  /**
   * 修复数据断层
   */
  async repairGap(symbol = 'BTCUSDT') {
    console.log(`🔧 修复数据断层: ${symbol}`);
    
    try {
      const response = await fetch('http://localhost:8000/api/crypto/repair-gap/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ symbol })
      });
      
      const result = await response.json();
      console.log('✅ 断层修复结果:', result);
      return result;
    } catch (error) {
      console.error('❌ 断层修复失败:', error);
      return null;
    }
  }

  /**
   * 刷新图表
   */
  refreshCharts() {
    console.log('🔄 刷新所有图表...');
    this.triggerChartRefresh();
    this.forceCheckUpdates();
  }
}

// 创建全局实例
const globalDebugHelper = new GlobalDebugHelper();

// 挂载到window对象
if (typeof window !== 'undefined') {
  window.debugAI = globalDebugHelper;
}

export default globalDebugHelper;