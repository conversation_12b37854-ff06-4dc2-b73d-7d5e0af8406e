import React, { useEffect, useRef, useState } from 'react';
import { init, dispose } from 'klinecharts';
import { Button, Space } from 'antd';
import {
  FullscreenOutlined,
  FullscreenExitOutlined
} from '@ant-design/icons';
import klineDataService from '../../services/KlineDataService';
import './KLineChart.css';

const KLineChart = ({
  symbol = 'BTCUSDT',
  interval = '1d',
  isFullscreen = false,
  onFullscreenChange,
  data = null
}) => {
  const chartRef = useRef(null);
  const kLineChart = useRef(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dataStats, setDataStats] = useState(null);

  // 加载真实数据
  const loadRealData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      let data = null;
      
      // 获取当前年数据作为主数据源
      try {
        data = await klineDataService.getCurrentYearData(symbol, interval);
      } catch (err) {
        console.warn('当前年数据不可用，使用2024年数据:', err);
        data = await klineDataService.getYearlyData(symbol, 2024);
      }
      
      if (!data || !data.data || data.data.length === 0) {
        // 如果没有数据，尝试获取概览数据作为备选
        data = await klineDataService.getOverviewData(symbol);
        if (!data || !data.data || data.data.length === 0) {
          throw new Error('暂无数据');
        }
      }
      
      // 转换为K线图格式
      const klineData = klineDataService.convertToKlineFormat(data);
      const stats = klineDataService.getDataStats(data);
      
      setDataStats(stats);
      return klineData;
      
    } catch (err) {
      console.error(`Failed to load data for ${symbol}-${interval}:`, err);
      setError(err.message);
      return [];
    }
  };

  // 初始化图表
  useEffect(() => {
    const chartElement = chartRef.current;
    if (!chartElement) return;

    try {
      setLoading(true);
      setError(null);

      // 图表配置
      const chartOptions = {
        styles: {
          grid: {
            horizontal: {
              color: 'rgba(255, 255, 255, 0.1)'
            },
            vertical: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          },
          candle: {
            priceMark: {
              high: {
                color: '#26a69a'
              },
              low: {
                color: '#ef5350'
              }
            },
            tooltip: {
              showRule: 'follow_cross',
              showType: 'standard'
            }
          },
          xAxis: {
            axisLine: {
              color: 'rgba(255, 255, 255, 0.2)'
            },
            tickText: {
              color: 'rgba(255, 255, 255, 0.6)',
              size: 12
            }
          },
          yAxis: {
            axisLine: {
              color: 'rgba(255, 255, 255, 0.2)'
            },
            tickText: {
              color: 'rgba(255, 255, 255, 0.6)',
              size: 12
            }
          }
        }
      };

      // 创建图表实例
      kLineChart.current = init(chartElement, chartOptions);
      
      // 设置数据加载器
      kLineChart.current.setDataLoader({
        getBars: async ({ type, symbol: requestSymbol, period, timestamp, callback }) => {
          try {
            setLoading(true);
            setError(null);
            
            let data = null;
            const targetSymbol = requestSymbol || symbol;
            const targetInterval = period || interval;
            
            // 根据加载类型决定数据获取策略
            switch (type) {
              case 'init':
                // 初始加载 - 获取当前年份数据
                try {
                  data = await klineDataService.getCurrentYearData(targetSymbol, targetInterval);
                } catch (err) {
                  console.warn('Current year data unavailable, using 2024 data:', err);
                  data = await klineDataService.getYearlyData(targetSymbol, 2024);
                }
                break;
                
              case 'backward':
                // 向前加载历史数据
                if (timestamp) {
                  const year = new Date(timestamp).getFullYear() - 1;
                  data = await klineDataService.getYearlyData(targetSymbol, year);
                } else {
                  data = await klineDataService.getOverviewData(targetSymbol);
                }
                break;
                
              case 'forward':
                // 向后加载新数据 - 通常用于实时更新
                data = await klineDataService.getCurrentYearData(targetSymbol, targetInterval);
                break;
                
              default:
                // 默认加载策略
                data = await loadRealData();
            }
            
            if (!data || !data.data || data.data.length === 0) {
              // 降级到概览数据
              data = await klineDataService.getOverviewData(targetSymbol);
              if (!data || !data.data || data.data.length === 0) {
                throw new Error('No data available');
              }
            }
            
            // 转换为K线格式
            const klineData = klineDataService.convertToKlineFormat(data);
            const stats = klineDataService.getDataStats(data);
            
            setDataStats(stats);
            
            if (klineData && klineData.length > 0) {
              // 仅在初始加载时添加技术指标
              if (type === 'init') {
                addDefaultIndicators();
              }
              callback(klineData);
            } else {
              callback([]);
            }
            
            setLoading(false);
          } catch (err) {
            console.error('数据加载失败:', err);
            setError(err.message);
            callback([]);
            setLoading(false);
          }
        },
        
        // 可选：实时数据订阅（预留接口）
        subscribeBar: ({ symbol: requestSymbol, period, callback }) => {
          console.log('实时数据订阅功能预留', { symbol: requestSymbol, period });
          // 预留实时数据订阅功能
          // 可以通过 WebSocket 或定时轮询实现
          
          // 返回取消订阅函数
          return () => {
            console.log('取消实时数据订阅');
          };
        }
      });

    } catch (error) {
      console.error('图表初始化失败:', error);
      setError(`初始化失败: ${error.message}`);
      setLoading(false);
    }

    // 清理函数
    return () => {
      if (kLineChart.current && chartElement) {
        dispose(chartElement);
        kLineChart.current = null;
      }
    };
  }, [symbol, interval]);

  // 添加默认指标
  const addDefaultIndicators = () => {
    if (!kLineChart.current) return;

    try {
      // 主图指标：移动平均线
      kLineChart.current.createIndicator('MA', false, { id: 'candle_pane' });
      
      // 子图指标：成交量
      kLineChart.current.createIndicator('VOL', false, { id: 'vol_pane' });
      
      // 子图指标：MACD
      kLineChart.current.createIndicator('MACD', false, { id: 'macd_pane' });
      
      console.log('✅ 专业图表指标添加完成');
    } catch (error) {
      console.error('添加指标失败:', error);
    }
  };

  // 更新图表大小
  useEffect(() => {
    if (kLineChart.current) {
      setTimeout(() => {
        try {
          kLineChart.current.resize();
        } catch (error) {
          console.error('调整图表大小失败:', error);
        }
      }, 100);
    }
  }, [isFullscreen]);

  return (
    <div className={`kline-chart ${isFullscreen ? 'fullscreen' : ''}`}>
      {/* 自定义工具栏 */}
      <div className="chart-toolbar">
        <div className="toolbar-left">
          <span className="chart-symbol">{symbol}</span>
          <span className="chart-interval">{interval}</span>
          {dataStats && (
            <span className={`chart-change ${dataStats.price.change >= 0 ? 'positive' : 'negative'}`}>
              {dataStats.price.changePercent}%
            </span>
          )}
        </div>
        <div className="toolbar-right">
          <Space size="small">
            <Button
              size="small"
              icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
              onClick={() => onFullscreenChange && onFullscreenChange(!isFullscreen)}
              className="toolbar-btn"
            >
              {isFullscreen ? '退出全屏' : '全屏'}
            </Button>
          </Space>
        </div>
      </div>

      {/* 图表容器 */}
      <div className="chart-wrapper">
        {loading && (
          <div className="chart-loading">
            <div className="loading-spinner"></div>
            <span>正在加载专业K线图表...</span>
          </div>
        )}
        {error && (
          <div className="chart-error">
            <span>⚠️ {error}</span>
          </div>
        )}
        <div
          ref={chartRef}
          className="chart-container"
          style={{
            width: '100%',
            height: isFullscreen ? 'calc(100vh - 60px)' : '450px',
            display: (loading || error) ? 'none' : 'block'
          }}
        />
      </div>
    </div>
  );
};

export default KLineChart;