#!/usr/bin/env python3
"""
修复所有时间周期数据问题的综合脚本
1. 重启实时1分钟数据收集
2. 更新所有多时间周期数据到当前时间  
3. 生成缺失的1月数据
4. 设置定时更新机制
"""

import os
import sys
import django
import subprocess
import requests
import json
import pandas as pd
from datetime import datetime, timedelta

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'finance_project.settings')
django.setup()

from crypto_api.services.cos_service import COSRealtimeService

def restart_realtime_collector():
    """重启实时数据收集器"""
    print("🔄 重启实时数据收集器...")
    
    # 杀死现有进程
    try:
        subprocess.run(['pkill', '-f', 'collect_realtime_data'], check=False)
        print("✅ 已停止旧的收集进程")
    except:
        pass
    
    # 启动新进程
    cmd = ['nohup', 'python3', 'manage.py', 'collect_realtime_data', 
           '--symbol=BTCUSDT', '--daemon', '--debug']
    
    with open('/dev/null', 'w') as devnull:
        subprocess.Popen(cmd, stdout=devnull, stderr=devnull)
    
    print("✅ 实时收集器已重启")

def update_multitime_data():
    """更新多时间周期数据到当前时间"""
    print("📊 更新多时间周期数据...")
    
    current_date = datetime.now().strftime('%Y-%m-%d')
    timeframes = ['15m', '1h', '4h', '1d']
    symbols = ['BTCUSDT']
    
    for symbol in symbols:
        for timeframe in timeframes:
            try:
                cmd = ['python3', 'manage.py', 'repair_multi_timeframe_data',
                       f'--symbol={symbol}', f'--timeframe={timeframe}',
                       f'--start={current_date}', f'--end={current_date}']
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
                if result.returncode == 0:
                    print(f"✅ {symbol} {timeframe} 更新成功")
                else:
                    print(f"⚠️ {symbol} {timeframe} 更新失败: {result.stderr[:100]}")
            except Exception as e:
                print(f"❌ {symbol} {timeframe} 更新异常: {str(e)[:100]}")

def generate_monthly_data():
    """生成2025年1-6月数据"""
    print("📅 生成月度数据...")
    
    cos_service = COSRealtimeService()
    monthly_data = []
    base_url = 'https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/20250726'
    
    # 收集1-6月数据
    for month in range(1, 7):
        month_str = f'{month:02d}'
        url = f'{base_url}/BTCUSDT/1m/BTCUSDT_1m_{month_str}_2025_compressed.json'
        
        try:
            response = requests.get(url, timeout=30)
            if response.status_code == 200:
                # 解析分块传输的数据
                content = response.text
                # 处理AWS分块传输格式
                if '\\n' in content:
                    lines = content.split('\\n')
                    for line in lines:
                        if line.strip() and line.strip() != '0':
                            try:
                                data = json.loads(line)
                                break
                            except:
                                continue
                else:
                    data = response.json()
                
                if 'data' in data and len(data['data']) > 0:
                    df = pd.DataFrame(data['data'], columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                    
                    # 转换时间戳
                    df['timestamp'] = df['timestamp'].apply(lambda x: x / 1000000 if x > 1e15 else (x / 1000 if x > 1e12 else x))
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
                    df.set_index('timestamp', inplace=True)
                    
                    # 按月聚合
                    monthly = df.resample('M').agg({
                        'open': 'first',
                        'high': 'max',
                        'low': 'min',
                        'close': 'last',
                        'volume': 'sum'
                    }).dropna()
                    
                    if len(monthly) > 0:
                        month_record = monthly.iloc[0]
                        timestamp_ms = int(monthly.index[0].timestamp() * 1000)
                        monthly_data.append([
                            timestamp_ms,
                            float(month_record['open']),
                            float(month_record['high']),
                            float(month_record['low']),
                            float(month_record['close']),
                            float(month_record['volume'])
                        ])
                        print(f"✅ {month}月数据处理成功")
                    else:
                        print(f"⚠️ {month}月无有效数据")
                else:
                    print(f"⚠️ {month}月数据格式异常")
            else:
                print(f"❌ {month}月文件不存在: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {month}月处理失败: {str(e)[:50]}")
    
    # 上传月数据
    if monthly_data:
        monthly_file_data = {
            'symbol': 'BTCUSDT',
            'timeframe': '1mo',
            'date_range': '2025-01-01 to 2025-06-30',
            'total_records': len(monthly_data),
            'data': monthly_data
        }
        
        try:
            cos_path = 'crypto-kline-data-v2/20250726/BTCUSDT/1mo/BTCUSDT_1mo_2025_compressed.json'
            result = cos_service.upload_json_data(cos_path, monthly_file_data)
            if result:
                print(f"✅ 月数据上传成功: {len(monthly_data)} 条记录")
            else:
                print("❌ 月数据上传失败")
        except Exception as e:
            print(f"❌ 上传失败: {str(e)}")
    else:
        print("❌ 无月数据可上传")

def setup_auto_update():
    """设置自动更新定时任务"""
    print("⏰ 设置定时更新...")
    
    cron_script = '''#!/bin/bash
# 每小时更新多时间周期数据
0 * * * * cd /home/<USER>/qiyuai-web/finance_backend && python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=1h --start=$(date +\\%Y-\\%m-\\%d) --end=$(date +\\%Y-\\%m-\\%d) >> logs/auto_update.log 2>&1

# 每4小时更新4小时数据  
0 */4 * * * cd /home/<USER>/qiyuai-web/finance_backend && python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=4h --start=$(date +\\%Y-\\%m-\\%d) --end=$(date +\\%Y-\\%m-\\%d) >> logs/auto_update.log 2>&1

# 每天凌晨更新日数据
5 0 * * * cd /home/<USER>/qiyuai-web/finance_backend && python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=1d --start=$(date -d "1 day ago" +\\%Y-\\%m-\\%d) --end=$(date -d "1 day ago" +\\%Y-\\%m-\\%d) >> logs/auto_update.log 2>&1
'''
    
    with open('/tmp/crypto_cron.txt', 'w') as f:
        f.write(cron_script)
    
    print("✅ 定时任务配置已准备，需要手动执行: crontab /tmp/crypto_cron.txt")

def main():
    print("🚀 开始修复所有时间周期数据问题...")
    print(f"⏰ 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 重启实时收集
    restart_realtime_collector()
    
    # 2. 更新多时间周期数据
    update_multitime_data()
    
    # 3. 生成月数据
    generate_monthly_data()
    
    # 4. 设置定时更新
    setup_auto_update()
    
    print("🎉 所有修复任务完成!")
    print("📋 总结:")
    print("   ✅ 实时1分钟数据收集已重启")
    print("   ✅ 多时间周期数据已更新到当前时间")
    print("   ✅ 2025年1-6月数据已生成")
    print("   ✅ 定时更新机制已配置")
    print("   🔧 请执行: crontab /tmp/crypto_cron.txt 启用定时任务")

if __name__ == '__main__':
    main()