#!/usr/bin/env python3
"""
修复2025-07-29 23:38到2025-07-30 00:43期间的缺失数据
"""

import os
import sys
import django
from datetime import datetime, timedelta

# Django环境设置
sys.path.insert(0, '/home/<USER>/qiyuai-web/finance_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'finance_project.settings')
django.setup()

from crypto_api.services.gap_recovery import GapRecoveryService

def fix_missing_crossday_data():
    """修复跨日期间的缺失数据"""
    
    # 数据缺失的时间范围
    start_time = datetime(2025, 7, 29, 23, 35)  # 提前几分钟确保完整性
    end_time = datetime(2025, 7, 30, 0, 45)     # 延后几分钟确保完整性
    
    # 需要修复的货币对
    symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'DOGEUSDT', 'XRPUSDT']
    
    print(f"🔧 开始修复跨日数据缺失")
    print(f"⏰ 时间范围: {start_time} ~ {end_time}")
    print(f"💰 货币对: {', '.join(symbols)}")
    print("="*60)
    
    # 初始化修复服务
    gap_service = GapRecoveryService(debug=True)
    
    success_count = 0
    total_count = len(symbols)
    
    for symbol in symbols:
        print(f"\n📊 正在修复 {symbol}...")
        
        try:
            # 执行缺口修复
            result = gap_service.fill_gap(
                symbol=symbol,
                start_time=start_time,
                end_time=end_time
            )
            
            if result:
                print(f"✅ {symbol} 修复成功")
                success_count += 1
            else:
                print(f"❌ {symbol} 修复失败")
                
        except Exception as e:
            print(f"❌ {symbol} 修复异常: {e}")
    
    print("\n" + "="*60)
    print(f"📈 修复结果: {success_count}/{total_count} 个货币对修复成功")
    
    if success_count == total_count:
        print("🎉 所有数据修复完成！")
    else:
        print("⚠️ 部分数据修复失败，请检查日志")

if __name__ == "__main__":
    fix_missing_crossday_data()