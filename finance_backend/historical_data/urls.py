from django.urls import path
from . import views
from . import historical_analysis_views

urlpatterns = [
    # 图表数据API
    path('chart/', views.get_chart_data, name='get_chart_data'),
    
    # 技术指标API
    path('indicators/', views.get_technical_indicators, name='get_technical_indicators'),
    
    # 支持的交易对
    path('symbols/', views.get_supported_symbols, name='get_supported_symbols'),
    
    # AI分析API
    path('analyze/', views.analyze_with_ai, name='analyze_with_ai'),
    
    # 图表指标时间序列API
    path('chart-indicators/', views.get_chart_indicators, name='get_chart_indicators'),
    
    # === 历史同期数据分析API ===
    
    # 历史同期数据获取
    path('same-period/', historical_analysis_views.get_historical_same_period, name='get_historical_same_period'),
    
    # 时间段技术指标计算
    path('period-indicators/', historical_analysis_views.calculate_period_indicators, name='calculate_period_indicators'),
    
    # 历史同期对比分析
    path('comparative-analysis/', historical_analysis_views.get_comparative_analysis, name='get_comparative_analysis'),
    
    # 分析功能说明
    path('analysis-capabilities/', historical_analysis_views.get_analysis_capabilities, name='get_analysis_capabilities'),
]