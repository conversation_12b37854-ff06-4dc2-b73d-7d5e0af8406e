#!/usr/bin/env python3
"""
深入检查7月30日12:32-12:40期间的异常数据
"""

import json
import requests
from datetime import datetime

def inspect_anomaly_data():
    """检查异常数据的详细特征"""
    url = "https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/20250726/BTCUSDT/1m/daily/BTCUSDT_1m_07-30_2025_compressed.json"
    
    print("📥 下载7月30日数据...")
    response = requests.get(url, timeout=30)
    raw_data = json.loads(response.text)['data']
    
    print(f"🔍 检查异常时间段数据 (索引750-770)...")
    
    for i in range(750, min(771, len(raw_data))):
        item = raw_data[i]
        
        # 原始时间戳处理
        timestamp = item[0]
        timestamp_type = "微秒" if timestamp > 1e15 else "毫秒"
        
        if timestamp > 1e15:
            timestamp_ms = timestamp // 1000
        else:
            timestamp_ms = timestamp
            
        dt = datetime.fromtimestamp(timestamp_ms / 1000)
        
        # 价格数据
        open_price = float(item[1])
        high_price = float(item[2])
        low_price = float(item[3])
        close_price = float(item[4])
        volume = float(item[5])
        
        # 标记异常数据
        is_anomaly = close_price < 50000
        marker = "🚨" if is_anomaly else "✅"
        
        print(f"{marker} [{i:3d}] {dt.strftime('%H:%M:%S.%f')[:-3]} ({timestamp_type})")
        print(f"     原始时间戳: {timestamp}")
        print(f"     转换时间戳: {timestamp_ms}")
        print(f"     OHLC: {open_price:8.2f} {high_price:8.2f} {low_price:8.2f} {close_price:8.2f}")
        print(f"     成交量: {volume:8.2f}")
        
        # 检查数据是否看起来是模拟/测试数据
        if is_anomaly:
            # 检查价格是否是整数或简单模式
            prices = [open_price, high_price, low_price, close_price]
            is_round = all(p == int(p) for p in prices)
            has_pattern = (high_price - low_price == 200) and (volume == int(volume))
            
            if is_round or has_pattern:
                print(f"     🎯 可能是模拟数据: 整数价格={is_round}, 规律模式={has_pattern}")
        
        print()
    
    # 分析数据来源
    print("📋 异常数据特征分析:")
    anomaly_indices = []
    normal_indices = []
    
    for i in range(750, min(771, len(raw_data))):
        item = raw_data[i]
        close_price = float(item[4])
        
        if close_price < 50000:
            anomaly_indices.append(i)
        else:
            normal_indices.append(i)
    
    print(f"   异常数据索引: {anomaly_indices}")
    print(f"   正常数据索引: {normal_indices}")
    
    if anomaly_indices:
        # 检查异常数据的共同特征
        first_anomaly = raw_data[anomaly_indices[0]]
        print(f"\n🔍 第一条异常数据详情 (索引 {anomaly_indices[0]}):")
        print(f"   原始数组: {first_anomaly}")
        
        # 检查是否所有异常数据都有相似模式
        anomaly_patterns = []
        for idx in anomaly_indices:
            item = raw_data[idx]
            open_p, high_p, low_p, close_p, vol = float(item[1]), float(item[2]), float(item[3]), float(item[4]), float(item[5])
            
            pattern = {
                'price_range': high_p - low_p,
                'volume': vol,
                'is_round_volume': vol == int(vol),
                'is_round_prices': all(p == int(p) for p in [open_p, high_p, low_p, close_p])
            }
            anomaly_patterns.append(pattern)
        
        print(f"\n📊 异常数据模式分析:")
        for i, pattern in enumerate(anomaly_patterns):
            print(f"   [{anomaly_indices[i]}] 价格区间:{pattern['price_range']:.0f}, "
                  f"成交量:{pattern['volume']:.0f}, "
                  f"整数价格:{pattern['is_round_prices']}, "
                  f"整数成交量:{pattern['is_round_volume']}")

if __name__ == "__main__":
    inspect_anomaly_data()