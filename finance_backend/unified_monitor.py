#!/usr/bin/env python3
"""
统一监控和管理脚本 - 整合所有监控功能
"""

import os
import sys
import argparse
from pathlib import Path
from datetime import datetime

# 导入所有监控模块
from improved_process_management import ProcessManager, DatabaseTaskMonitor
from advanced_lock_manager import AdvancedLockManager
from process_health_monitor import ProcessHealthMonitor

class UnifiedMonitor:
    """统一监控管理器"""
    
    def __init__(self, work_dir: str = "/home/<USER>/qiyuai-web/finance_backend"):
        self.work_dir = Path(work_dir)
        self.process_manager = ProcessManager(str(self.work_dir))
        self.lock_manager = AdvancedLockManager()
        self.health_monitor = ProcessHealthMonitor(str(self.work_dir))
        self.db_monitor = DatabaseTaskMonitor()
        
    def full_system_check(self):
        """完整系统检查"""
        print("🔍 执行完整系统检查...")
        print("=" * 50)
        
        # 1. 健康检查
        print("1️⃣ 系统健康检查")
        health_status = self.health_monitor.comprehensive_health_check()
        self._print_health_summary(health_status)
        
        # 2. 锁状态检查
        print("\n2️⃣ 锁状态检查")
        locks = self.lock_manager.list_active_locks()
        if locks:
            for name, info in locks.items():
                if 'error' in info:
                    print(f"  🔒 {name}: ❌ {info['error']}")
                else:
                    age_min = info['age_seconds'] / 60
                    status = "✅ 活跃" if info['is_alive'] else "💀 僵尸"
                    print(f"  🔒 {name}: {status} (PID {info['pid']}, {age_min:.1f}分钟)")
        else:
            print("  🔓 无活跃锁")
        
        # 3. 进程检查
        print("\n3️⃣ 进程检查")
        # 检查数据收集器
        collectors = self.process_manager.find_process_by_pattern("collect.*data")
        print(f"  📊 数据收集器: {len(collectors)} 个")
        for proc in collectors:
            try:
                create_time = datetime.fromtimestamp(proc.create_time())
                print(f"    PID {proc.pid}: 运行于 {create_time}")
            except:
                pass
        
        # 检查Django服务器
        servers = self.health_monitor.check_duplicate_servers()
        print(f"  🌐 Django服务器: {len(servers)} 个")
        for server in servers:
            print(f"    PID {server['pid']}: {server['memory_mb']:.1f}MB")
        
        print("\n" + "=" * 50)
        print("✅ 系统检查完成")
        
        return health_status
    
    def _print_health_summary(self, health_status):
        """打印健康检查摘要"""
        checks = health_status['checks']
        
        # Django服务器状态
        server_count = checks['duplicate_servers']['count']
        server_status = "⚠️ 重复" if server_count > 1 else "✅ 正常"
        print(f"  🌐 Django服务器: {server_status} ({server_count}个)")
        
        # Systemd服务状态
        systemd_active = checks['systemd_service'].get('is_active', False)
        systemd_status = "✅ 运行中" if systemd_active else "❌ 停止"
        print(f"  ⚙️ Systemd服务: {systemd_status}")
        
        # 数据缺失状态
        gap_count = len(checks['data_gaps']['gaps'])
        gap_status = "⚠️ 有缺失" if gap_count > 0 else "✅ 正常"
        print(f"  📈 数据连续性: {gap_status} ({gap_count}个缺失)")
        
        # 系统资源
        cpu = checks['system_resources']['cpu_percent']
        memory = checks['system_resources']['memory_percent']
        disk = checks['system_resources']['disk_percent']
        print(f"  💻 系统资源: CPU {cpu:.1f}%, 内存 {memory:.1f}%, 磁盘 {disk:.1f}%")
        
        # 自动修复
        if 'auto_repairs' in health_status:
            repair_count = len(health_status['auto_repairs'])
            print(f"  🔧 自动修复: {repair_count} 项修复")
    
    def emergency_cleanup(self):
        """紧急清理 - 解决所有已知问题"""
        print("🚨 执行紧急清理...")
        
        # 1. 清理过期锁
        print("1️⃣ 清理过期锁")
        locks = self.lock_manager.list_active_locks()
        for name, info in locks.items():
            if not info.get('is_alive', True) or info.get('age_seconds', 0) > 3600:
                try:
                    lock_file = Path(f"/tmp/{name}.lock")
                    info_file = Path(f"/tmp/{name}.info")
                    lock_file.unlink(missing_ok=True)
                    info_file.unlink(missing_ok=True)
                    print(f"  清理锁: {name}")
                except Exception as e:
                    print(f"  清理失败 {name}: {e}")
        
        # 2. 清理重复进程
        print("2️⃣ 清理重复进程")
        self.health_monitor.resolve_duplicate_servers()
        
        # 3. 清理卡住的任务
        print("3️⃣ 清理数据库任务")
        self.db_monitor.cleanup_stuck_predictions(timeout_hours=1)
        
        # 4. 重启关键服务
        print("4️⃣ 重启Systemd服务")
        try:
            import subprocess
            subprocess.run(['sudo', 'systemctl', 'restart', 'crypto-collector.service'], check=True)
            print("  ✅ 服务重启成功")
        except Exception as e:
            print(f"  ❌ 服务重启失败: {e}")
        
        print("🎯 紧急清理完成")
    
    def status_summary(self):
        """状态摘要"""
        print("📊 系统状态摘要")
        print("=" * 30)
        
        # 快速检查关键指标
        try:
            import psutil
            import subprocess
            
            # 进程状态
            collectors = len(self.process_manager.find_process_by_pattern("collect.*data"))
            servers = len(self.health_monitor.check_duplicate_servers())
            
            # 服务状态
            systemd_result = subprocess.run(
                ['systemctl', 'is-active', 'crypto-collector.service'],
                capture_output=True, text=True
            )
            systemd_active = systemd_result.stdout.strip() == 'active'
            
            # 资源状态
            cpu = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory().percent
            
            print(f"📊 数据收集器: {collectors} 个")
            print(f"🌐 Django服务器: {servers} 个")
            print(f"⚙️ Systemd服务: {'✅' if systemd_active else '❌'}")
            print(f"💻 CPU: {cpu:.1f}% | 内存: {memory:.1f}%")
            
            # 检查最近的任务
            self._check_recent_tasks()
            
        except Exception as e:
            print(f"状态检查失败: {e}")
        
        print("=" * 30)
    
    def _check_recent_tasks(self):
        """检查最近的任务执行情况"""
        try:
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'finance_project.settings')
            import django
            django.setup()
            
            from ai_analysis.models import PredictionRecord
            from django.utils import timezone
            from datetime import timedelta
            
            # 检查最近1小时的预测
            recent_time = timezone.now() - timedelta(hours=1)
            recent_predictions = PredictionRecord.objects.filter(
                created_at__gte=recent_time
            ).count()
            
            print(f"🔮 最近1小时预测: {recent_predictions} 次")
            
        except Exception as e:
            print(f"检查任务失败: {e}")


def main():
    parser = argparse.ArgumentParser(description='统一系统监控工具')
    parser.add_argument('--full-check', action='store_true', help='完整系统检查')
    parser.add_argument('--emergency', action='store_true', help='紧急清理')
    parser.add_argument('--status', action='store_true', help='状态摘要')
    parser.add_argument('--auto', action='store_true', help='自动模式（定期检查和修复）')
    
    args = parser.parse_args()
    
    monitor = UnifiedMonitor()
    
    if args.full_check:
        monitor.full_system_check()
    elif args.emergency:
        monitor.emergency_cleanup()
    elif args.status:
        monitor.status_summary()
    elif args.auto:
        # 自动模式 - 适合cron调用
        health_status = monitor.full_system_check()
        
        # 如果发现严重问题，自动修复
        checks = health_status['checks']
        if (checks['duplicate_servers']['count'] > 1 or 
            len(checks['data_gaps']['gaps']) > 5 or
            not checks['systemd_service'].get('is_active')):
            print("\n🚨 检测到严重问题，执行自动修复...")
            monitor.emergency_cleanup()
    else:
        # 默认显示状态摘要
        monitor.status_summary()


if __name__ == "__main__":
    main()