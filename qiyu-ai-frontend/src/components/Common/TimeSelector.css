/* 时间选择器样式 */
.time-selector {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 4px;
  backdrop-filter: blur(10px);
}

.time-btn {
  min-width: 32px;
  height: 28px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 4px;
  transition: all 0.3s ease;
  border: none;
}

.time-btn.qiyu-button--ghost {
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  border: none;
}

.time-btn.qiyu-button--ghost:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  transform: none;
}

.time-btn.qiyu-button--primary {
  background: #4CAF50;
  color: white;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

.time-btn.qiyu-button--primary:hover {
  background: #45a049;
  color: white;
  transform: none;
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .time-selector {
    gap: 2px;
    padding: 2px;
  }
  
  .time-btn {
    min-width: 28px;
    height: 24px;
    font-size: 11px;
  }
}
