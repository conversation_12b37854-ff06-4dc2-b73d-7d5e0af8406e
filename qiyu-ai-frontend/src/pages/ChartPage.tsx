import React from 'react'
import { ConfigProvider, theme } from 'antd'
import MultiYearKLineChart from '../components/Chart/MultiYearKLineChart'
import './ChartPage.css'

const ChartPage: React.FC = () => {
  const handleBinanceConnect = () => {
    console.log('🚀 Binance实时数据连接成功')
    // 可以在这里添加成功连接的业务逻辑
  }

  return (
    <ConfigProvider
      theme={{
        algorithm: theme.darkAlgorithm,
        token: {
          colorPrimary: '#26A69A',
          colorBgBase: '#0d1117',
          colorTextBase: '#f0f6fc',
          borderRadius: 8,
        },
      }}
    >
      <div className="chart-page">
        <div className="page-header">
          <h1>专业K线图表系统</h1>
          <p>基于KLineCharts v9.x，支持多年数据对比、技术指标分析、实时数据更新</p>
        </div>

        <div className="page-content">
          <MultiYearKLineChart
            symbol="BTCUSDT"
            years={['2024', '2023']}
            enableRealTime={true}
            onBinanceConnect={handleBinanceConnect}
          />
        </div>

        <div className="page-footer">
          <div className="feature-list">
            <div className="feature-item">
              <h4>✨ 核心特性</h4>
              <ul>
                <li>多年份数据对比分析</li>
                <li>深色主题界面设计</li>
                <li>上海时区时间显示</li>
                <li>专业技术指标支持</li>
              </ul>
            </div>
            <div className="feature-item">
              <h4>📊 技术指标</h4>
              <ul>
                <li>BOLL 布林带（主图叠加）</li>
                <li>RSI 相对强弱指标</li>
                <li>KDJ 随机指标</li>
                <li>MACD 指数平滑移动平均（仅最新年份）</li>
              </ul>
            </div>
            <div className="feature-item">
              <h4>🔄 数据更新</h4>
              <ul>
                <li>Binance WebSocket实时数据</li>
                <li>自动重连机制</li>
                <li>历史数据加载</li>
                <li>数据异常处理</li>
              </ul>
            </div>
            <div className="feature-item">
              <h4>🎛️ 交互功能</h4>
              <ul>
                <li>十字光标跟随提示</li>
                <li>指标开关控制</li>
                <li>缩放重置功能</li>
                <li>全屏模式支持</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </ConfigProvider>
  )
}

export default ChartPage