/**
 * SMS认证服务 - 替代Supabase
 * 基于Django后端的腾讯云SMS服务
 */

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000/api/web';

class SMSAuthService {
  constructor() {
    this.baseURL = API_BASE_URL;
    this.token = localStorage.getItem('access_token');
  }

  /**
   * 发送短信验证码
   */
  async sendSMSCode(phone, purpose = 'register') {
    try {
      const response = await fetch(`${this.baseURL}/sms/send-code/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phone: phone,
          purpose: purpose
        })
      });

      const data = await response.json();

      if (response.ok) {
        return { data, error: null };
      } else {
        return { data: null, error: data.error || '发送验证码失败' };
      }
    } catch (error) {
      console.error('发送验证码请求失败:', error);
      return { data: null, error: '网络错误，请稍后重试' };
    }
  }

  /**
   * 短信验证码注册
   */
  async registerWithSMS(phone, smsCode, password, nickname = '', inviteCode = '') {
    try {
      const requestBody = {
        phone: phone,
        sms_code: smsCode,
        password: password,
        nickname: nickname
      };
      
      // 如果有邀请码，添加到请求中
      if (inviteCode) {
        requestBody.invite_code = inviteCode;
      }
      
      const response = await fetch(`${this.baseURL}/sms/register/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      const data = await response.json();

      if (response.ok) {
        // 保存token
        if (data.tokens) {
          localStorage.setItem('access_token', data.tokens.access);
          localStorage.setItem('refresh_token', data.tokens.refresh);
          this.token = data.tokens.access;
        }
        return { data: data, error: null };
      } else {
        return { data: null, error: data.error || '注册失败' };
      }
    } catch (error) {
      console.error('注册请求失败:', error);
      return { data: null, error: '网络错误，请稍后重试' };
    }
  }

  /**
   * 短信验证码登录
   */
  async loginWithSMS(phone, smsCode) {
    try {
      const response = await fetch(`${this.baseURL}/sms/login/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phone: phone,
          sms_code: smsCode
        })
      });

      const data = await response.json();

      if (response.ok) {
        // 保存token
        if (data.tokens) {
          localStorage.setItem('access_token', data.tokens.access);
          localStorage.setItem('refresh_token', data.tokens.refresh);
          this.token = data.tokens.access;
        }
        return { data: data, error: null };
      } else {
        return { data: null, error: data.error || '登录失败' };
      }
    } catch (error) {
      console.error('登录请求失败:', error);
      return { data: null, error: '网络错误，请稍后重试' };
    }
  }

  /**
   * 密码登录
   */
  async loginWithPassword(phone, password) {
    try {
      const response = await fetch(`${this.baseURL}/sms/password-login/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phone: phone,
          password: password
        })
      });

      const data = await response.json();

      if (response.ok) {
        // 保存token
        if (data.tokens) {
          localStorage.setItem('access_token', data.tokens.access);
          localStorage.setItem('refresh_token', data.tokens.refresh);
          this.token = data.tokens.access;
        }
        return { data: data, error: null };
      } else {
        return { data: null, error: data.error || '登录失败' };
      }
    } catch (error) {
      console.error('密码登录请求失败:', error);
      return { data: null, error: '网络错误，请稍后重试' };
    }
  }

  /**
   * 获取用户信息
   */
  async getUserProfile() {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        return { data: null, error: '未登录' };
      }

      const response = await fetch(`${this.baseURL}/sms/profile/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (response.ok) {
        return { data: data.user, error: null };
      } else {
        // token可能过期，清除本地存储
        if (response.status === 401) {
          this.logout();
        }
        return { data: null, error: data.error || '获取用户信息失败' };
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return { data: null, error: '网络错误，请稍后重试' };
    }
  }

  /**
   * 登出
   */
  logout() {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    this.token = null;
  }

  /**
   * 检查是否已登录
   */
  isAuthenticated() {
    return !!localStorage.getItem('access_token');
  }

  /**
   * 获取当前token
   */
  getToken() {
    return localStorage.getItem('access_token');
  }
}

// 导出服务实例
export const smsAuthService = new SMSAuthService();

// 导出兼容Supabase的接口
export const userService = {
  // 发送注册验证码
  async sendSignUpOTP(phone) {
    return await smsAuthService.sendSMSCode(phone, 'register');
  },

  // 发送登录验证码
  async sendSignInOTP(phone) {
    return await smsAuthService.sendSMSCode(phone, 'login');
  },

  // 短信验证码注册
  async signUpWithOTP(phone, token, username, inviteCode = '') {
    // 注意：这里需要密码，但Supabase接口没有密码参数
    // 建议前端传递一个默认密码或者修改注册流程
    const defaultPassword = 'temp123456'; // 临时密码
    return await smsAuthService.registerWithSMS(phone, token, defaultPassword, username, inviteCode);
  },

  // 短信验证码登录
  async verifySignInOTP(phone, token) {
    return await smsAuthService.loginWithSMS(phone, token);
  },

  // 密码登录
  async signInWithPassword(phone, password) {
    return await smsAuthService.loginWithPassword(phone, password);
  },

  // 密码注册
  async signUpWithPassword(phone, password, username) {
    // 先发送验证码，然后用验证码注册
    // 这需要修改前端流程，建议使用两步注册
    const sendResult = await smsAuthService.sendSMSCode(phone, 'register');
    if (sendResult.error) {
      return sendResult;
    }
    
    // 返回需要验证码的提示
    return {
      data: null,
      error: '请先获取短信验证码'
    };
  },

  // 登出
  async signOut() {
    smsAuthService.logout();
    return { error: null };
  },

  // 获取当前用户
  async getCurrentUser() {
    const result = await smsAuthService.getUserProfile();
    return result.data;
  },

  // 获取用户档案
  async getProfile(userId) {
    return await smsAuthService.getUserProfile();
  },

  // 更新用户档案
  async updateProfile(userId, updates) {
    // TODO: 实现更新用户信息的API
    return { data: null, error: '功能开发中' };
  }
};

// 监听认证状态变化的模拟实现
export const onAuthStateChange = (callback) => {
  // 检查当前登录状态
  const checkAuthState = async () => {
    if (smsAuthService.isAuthenticated()) {
      const result = await smsAuthService.getUserProfile();
      if (result.data) {
        callback('SIGNED_IN', result.data);
      } else {
        callback('SIGNED_OUT', null);
      }
    } else {
      callback('SIGNED_OUT', null);
    }
  };

  // 立即检查一次
  checkAuthState();

  // 返回一个取消订阅的函数
  return {
    data: {
      subscription: {
        unsubscribe: () => {
          // 取消订阅逻辑
        }
      }
    }
  };
};