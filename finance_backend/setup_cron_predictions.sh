#!/bin/bash
# AI预测任务Cron配置脚本

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$SCRIPT_DIR"
MANAGE_PY="$PROJECT_DIR/manage.py"
LOG_DIR="$PROJECT_DIR/logs/predictions"
CRON_LOG="$LOG_DIR/cron_predictions.log"

echo "🔧 配置AI预测任务Cron作业..."

# 创建日志目录
mkdir -p "$LOG_DIR"

# 检查manage.py是否存在
if [ ! -f "$MANAGE_PY" ]; then
    echo "❌ 错误: 未找到 $MANAGE_PY"
    exit 1
fi

# 检查Python环境
PYTHON_CMD=$(which python3)
if [ -z "$PYTHON_CMD" ]; then
    echo "❌ 错误: 未找到python3"
    exit 1
fi

echo "✅ Python路径: $PYTHON_CMD"
echo "✅ 项目路径: $PROJECT_DIR"
echo "✅ 日志路径: $CRON_LOG"

# 生成cron作业条目
CRON_ENTRY="*/15 * * * * cd $PROJECT_DIR && $PYTHON_CMD $MANAGE_PY cron_predictions >> $CRON_LOG 2>&1"

echo ""
echo "📋 建议的Cron配置（每15分钟执行一次）:"
echo "----------------------------------------"
echo "$CRON_ENTRY"
echo "----------------------------------------"

# 提供安装选项
read -p "🤔 是否要自动添加到当前用户的crontab? (y/N): " -n 1 -r
echo

if [[ $REPLY =~ ^[Yy]$ ]]; then
    # 备份现有crontab
    if crontab -l > /dev/null 2>&1; then
        crontab -l > "$PROJECT_DIR/crontab_backup_$(date +%Y%m%d_%H%M%S).txt"
        echo "✅ 已备份现有crontab"
    fi
    
    # 检查是否已存在相同任务
    if crontab -l 2>/dev/null | grep -q "cron_predictions"; then
        echo "⚠️  警告: crontab中已存在AI预测任务，请手动检查"
        crontab -l | grep "cron_predictions"
    else
        # 添加新的cron任务
        (crontab -l 2>/dev/null; echo "$CRON_ENTRY") | crontab -
        echo "✅ 已添加AI预测任务到crontab"
    fi
    
    # 显示当前crontab
    echo ""
    echo "📋 当前crontab内容:"
    echo "----------------------------------------"
    crontab -l
    echo "----------------------------------------"
    
else
    echo "🔧 手动配置步骤:"
    echo "1. 运行: crontab -e"
    echo "2. 添加以下行:"
    echo "   $CRON_ENTRY"
    echo "3. 保存退出"
fi

echo ""
echo "📊 其他有用的命令:"
echo "• 查看crontab: crontab -l"
echo "• 编辑crontab: crontab -e"
echo "• 删除crontab: crontab -r"
echo "• 查看日志: tail -f $CRON_LOG"
echo "• 手动测试: cd $PROJECT_DIR && python3 manage.py cron_predictions"

echo ""
echo "✅ Cron配置完成！"