#!/usr/bin/env python3
"""
AI预测分析API - 基于时间戳数据进行AI分析和预测
"""
import json
import logging
import requests
from datetime import datetime
from django.conf import settings
from django.http import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

from historical_data.timestamp_analysis_service import TimestampAnalysisService
from .prompt_templates import AIPromptTemplates

logger = logging.getLogger(__name__)

@method_decorator(csrf_exempt, name='dispatch')
class AIPredictionAPIView(View):
    """AI预测分析API - 生成多空预测和分析总结"""
    
    def options(self, request, *args, **kwargs):
        """处理OPTIONS请求（CORS预检请求）"""
        response = JsonResponse({})
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"
        response["Access-Control-Max-Age"] = "86400"
        return response
    
    def post(self, request, *args, **kwargs):
        """处理POST请求 - AI预测分析"""
        try:
            logger.info(f"收到AI预测请求: {request.method} {request.path}")
            
            # 解析请求数据
            data = json.loads(request.body)
            logger.info(f"请求数据: {data}")
            
            target_timestamp = data.get("target_timestamp")
            symbol = data.get("symbol", "BTCUSDT")
            interval = data.get("interval", "1m")
            
            if not target_timestamp:
                return JsonResponse({
                    "error": "缺少target_timestamp参数",
                    "required_params": ["target_timestamp"],
                    "optional_params": ["symbol", "interval"]
                }, status=400)
            
            logger.info(f"开始AI预测分析: {target_timestamp} {symbol} {interval}")
            
            # 使用时间戳分析服务获取数据
            timestamp_service = TimestampAnalysisService()
            analysis_result = timestamp_service.get_timestamp_analysis_data(
                timestamp=target_timestamp,
                symbol=symbol,
                interval=interval
            )
            
            if 'error' in analysis_result:
                return JsonResponse({
                    "error": f"数据分析失败: {analysis_result['error']}",
                    "analysis_result": analysis_result
                }, status=500)
            
            # 构建AI预测提示词
            ai_prompt = self._build_prediction_prompt(analysis_result)
            
            # 调用真实的DeepSeek AI API
            ai_prediction = self._call_deepseek_api(ai_prompt)
            
            # 构建响应
            response = {
                "success": True,
                "timestamp_analysis": {
                    "target_timestamp": target_timestamp,
                    "target_datetime": analysis_result.get('target_datetime'),
                    "symbol": symbol,
                    "interval": interval
                },
                "market_data": {
                    "current_price": self._extract_current_price(analysis_result),
                    "support_resistance": analysis_result.get('support_resistance', {}),
                    "technical_indicators": analysis_result.get('technical_indicators', {}),
                    "btc_reference": analysis_result.get('btc_reference', {})
                },
                "ai_prompt": ai_prompt,
                "ai_prediction": ai_prediction,
                "analysis_timestamp": datetime.now().isoformat()
            }
            
            logger.info("✅ AI预测分析完成")
            return JsonResponse(response)
            
        except Exception as e:
            logger.error(f"AI预测请求处理失败: {e}", exc_info=True)
            return JsonResponse({"error": str(e)}, status=500)
    
    def _build_prediction_prompt(self, analysis_result: dict) -> str:
        """构建AI预测提示词"""
        try:
            symbol = analysis_result.get('symbol', 'BTCUSDT')
            target_datetime = analysis_result.get('target_datetime', '')
            target_date = target_datetime.split('T')[0] if 'T' in target_datetime else target_datetime
            
            # 提取价格数据
            current_price = self._extract_current_price(analysis_result)
            prev_day_price = self._extract_prev_day_price(analysis_result)
            
            # 提取支撑阻力位
            support_resistance = analysis_result.get('support_resistance', {})
            support_level = support_resistance.get('support', current_price * 0.95)
            resistance_level = support_resistance.get('resistance', current_price * 1.05)
            
            # 提取技术指标
            technical_indicators = analysis_result.get('technical_indicators', {})
            
            # 提取历史价格数据
            historical_prices = self._extract_historical_prices(analysis_result)
            
            # 提取BTC数据
            btc_data = analysis_result.get('btc_reference', {})
            
            # 使用提示词模板
            prompt = AIPromptTemplates.build_structured_prediction_prompt(
                symbol=symbol,
                target_date=target_date,
                current_price=current_price,
                support_level=support_level,
                resistance_level=resistance_level,
                technical_indicators=technical_indicators,
                prev_day_price=prev_day_price,
                historical_prices=historical_prices,
                btc_data=btc_data
            )
            
            return prompt
            
        except Exception as e:
            logger.error(f"构建AI提示词失败: {e}")
            return f"提示词构建失败: {str(e)}"
    
    def _call_deepseek_api(self, prompt: str) -> dict:
        """调用DeepSeek API进行分析"""
        try:
            api_url = "https://api.deepseek.com/chat/completions"
            api_key = getattr(settings, 'DEEPSEEK_API_KEY', None)
            
            if not api_key:
                logger.error("DeepSeek API密钥未配置")
                return self._fallback_analysis()
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}"
            }
            
            payload = {
                "model": "deepseek-chat",  
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "max_tokens": 1000,
                "temperature": 0.3,
                "stream": False
            }
            
            logger.info("调用DeepSeek API进行预测分析")
            response = requests.post(api_url, headers=headers, json=payload, timeout=720)  # 12分钟超时
            response.raise_for_status()
            
            api_response = response.json()
            
            if 'choices' in api_response and len(api_response['choices']) > 0:
                ai_content = api_response['choices'][0]['message']['content']
                logger.info("✅ DeepSeek AI分析完成")
                
                # 解析AI响应为结构化格式
                return self._parse_ai_response(ai_content)
            else:
                logger.warning("DeepSeek API响应格式异常")
                return self._fallback_analysis()
                
        except requests.exceptions.RequestException as e:
            logger.error(f"DeepSeek API请求失败: {e}")
            return self._fallback_analysis()
        except Exception as e:
            logger.error(f"调用DeepSeek API时发生错误: {e}")
            return self._fallback_analysis()
    
    def _parse_ai_response(self, ai_content: str) -> dict:
        """解析AI响应为结构化格式"""
        try:
            # 尝试从AI响应中提取结构化信息
            lines = ai_content.split('\n')
            
            analysis_summary = ""
            prediction_data = {}
            sentiment_data = {}
            
            # 简单解析逻辑
            current_section = None
            for line in lines:
                line = line.strip()
                if '【分析总结】' in line:
                    current_section = 'summary'
                    continue
                elif '【多空预测】' in line:
                    current_section = 'prediction'
                    continue
                elif '【市场情绪】' in line:
                    current_section = 'sentiment'
                    continue
                
                if current_section == 'summary' and line:
                    analysis_summary += line + " "
                elif current_section == 'prediction' and '：' in line:
                    # 解析预测数据：看涨: 35%，非常看涨: 15%...
                    parts = line.split('，')
                    for part in parts:
                        if '：' in part or ':' in part:
                            key_value = part.split('：' if '：' in part else ':')
                            if len(key_value) == 2:
                                key = key_value[0].strip()
                                value = key_value[1].strip().replace('%', '')
                                try:
                                    prediction_data[key] = int(value)
                                except ValueError:
                                    pass
                elif current_section == 'sentiment' and ('占比' in line or '%' in line):
                    # 解析情绪数据
                    if '：' in line or ':' in line:
                        key_value = line.split('：' if '：' in line else ':')
                        if len(key_value) == 2:
                            key = key_value[0].strip()
                            value = key_value[1].strip().replace('%', '')
                            try:
                                sentiment_data[key] = int(value)
                            except ValueError:
                                pass
            
            # 如果解析失败，使用默认值
            if not prediction_data:
                prediction_data = {
                    "看涨": 25, "非常看涨": 10, "中性": 30, 
                    "看跌": 25, "非常看跌": 10
                }
            
            if not sentiment_data:
                sentiment_data = {"BTC_多头占比": 50, "目标币种_多头占比": 50}
            
            return {
                "analysis_summary": analysis_summary.strip() or "AI分析正在处理中...",
                "prediction_distribution": prediction_data,
                "market_sentiment": sentiment_data,
                "sentiment_description": "AI智能分析",
                "confidence_level": "高",
                "raw_ai_response": ai_content
            }
            
        except Exception as e:
            logger.error(f"解析AI响应失败: {e}")
            return {
                "analysis_summary": ai_content[:200] + "..." if len(ai_content) > 200 else ai_content,
                "prediction_distribution": {
                    "看涨": 25, "非常看涨": 10, "中性": 30, 
                    "看跌": 25, "非常看跌": 10
                },
                "market_sentiment": {"BTC_多头占比": 50, "目标币种_多头占比": 50},
                "raw_ai_response": ai_content,
                "parsing_error": str(e)
            }
    
    def _fallback_analysis(self) -> dict:
        """备用分析方法（当AI API不可用时）"""
        return {
            "analysis_summary": "AI服务暂时不可用，建议结合技术指标进行综合判断。",
            "prediction_distribution": {
                "看涨": 25, "非常看涨": 10, "中性": 30, 
                "看跌": 25, "非常看跌": 10
            },
            "market_sentiment": {"BTC_多头占比": 50, "目标币种_多头占比": 50},
            "sentiment_description": "系统维护中",
            "confidence_level": "低",
            "fallback": True
        }
    
    def _extract_historical_prices(self, analysis_result: dict) -> list:
        """提取真实历史价格数据"""
        try:
            historical_prices = []
            
            # 从当前数据提取价格
            current_data = analysis_result.get('current_data', {})
            if '目标日' in current_data and 'price_range' in current_data['目标日']:
                historical_prices.append(float(current_data['目标日']['price_range']['close']))
            if '前一天' in current_data and 'price_range' in current_data['前一天']:
                historical_prices.append(float(current_data['前一天']['price_range']['close']))
            
            # 从历史年份数据提取价格（最多3个历史价格点）
            historical_years_data = analysis_result.get('historical_years_data', {})
            for year, year_data in list(historical_years_data.items())[:3]:
                if year_data and 'period_data' in year_data:
                    period_data = year_data['period_data']
                    for day_name in ['目标日', '前一日', '后一日']:
                        if (day_name in period_data and 
                            'price_range' in period_data[day_name] and
                            'error' not in period_data[day_name]):
                            price = float(period_data[day_name]['price_range']['close'])
                            historical_prices.append(price)
                            break  # 只取每年一个价格点
            
            # 如果没有足够的历史数据，使用当前价格作为基准
            if len(historical_prices) < 3:
                current_price = self._extract_current_price(analysis_result)
                while len(historical_prices) < 3:
                    historical_prices.append(current_price)
            
            return historical_prices[:5]  # 最多返回5个历史价格
            
        except Exception as e:
            logger.error(f"提取历史价格失败: {e}")
            # 返回默认价格
            current_price = self._extract_current_price(analysis_result)
            return [current_price, current_price, current_price]
    
    def _extract_current_price(self, analysis_result: dict) -> float:
        """提取当前价格"""
        try:
            # 优先从支撑阻力位数据获取
            support_resistance = analysis_result.get('support_resistance', {})
            if 'current_price' in support_resistance:
                return float(support_resistance['current_price'])
            
            # 从技术指标获取
            technical_indicators = analysis_result.get('technical_indicators', {})
            if 'current_price' in technical_indicators:
                return float(technical_indicators['current_price'])
            
            # 从当前数据获取
            current_data = analysis_result.get('current_data', {})
            for day_name in ['目标日', '前一天']:
                if day_name in current_data and 'price_range' in current_data[day_name]:
                    price_range = current_data[day_name]['price_range']
                    if 'close' in price_range:
                        return float(price_range['close'])
            
            return 0.0
        except Exception:
            return 0.0
    
    def _extract_prev_day_price(self, analysis_result: dict) -> float:
        """提取前一天价格"""
        try:
            current_data = analysis_result.get('current_data', {})
            if '前一天' in current_data and 'price_range' in current_data['前一天']:
                return float(current_data['前一天']['price_range']['close'])
            return self._extract_current_price(analysis_result)
        except Exception:
            return 0.0