import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Select, Button, Spin, Alert, Typography, Divider } from 'antd';
import { RobotOutlined, BarChartOutlined, DatabaseOutlined } from '@ant-design/icons';
import EnhancedAIAnalyzer from '../components/Analysis/EnhancedAIAnalyzer';
import MultiYearK<PERSON>ineChart from '../components/Chart/MultiYearKLineChart';

const { Option } = Select;
const { Title, Text, Paragraph } = Typography;

const AIAnalysisPage = () => {
  const [selectedSymbol, setSelectedSymbol] = useState('BTCUSDT');
  const [analysisResult, setAnalysisResult] = useState(null);
  const [dataPackageInfo, setDataPackageInfo] = useState(null);
  const [loading, setLoading] = useState(false);

  // 可用的币种列表
  const availableSymbols = [
    { value: 'BTCUSDT', label: 'Bitcoin (BTC)', name: '比特币' },
    { value: 'ETHUSDT', label: 'Ethereum (ETH)', name: '以太坊' },
    { value: 'SOLUSDT', label: '<PERSON><PERSON> (SOL)', name: '索拉纳' },
    { value: 'XRPUSDT', label: 'Ripple (XRP)', name: '瑞波币' },
    { value: 'DOGEUSDT', label: 'Dogecoin (DOGE)', name: '狗狗币' },
    { value: 'LTCUSDT', label: 'Litecoin (LTC)', name: '莱特币' },
    { value: 'TRUMPUSDT', label: 'Trump (TRUMP)', name: '特朗普币' }
  ];

  // 加载数据包信息
  useEffect(() => {
    loadDataPackageInfo();
  }, []);

  const loadDataPackageInfo = async () => {
    try {
      // 这里应该从后端API获取数据包信息
      // 暂时使用模拟数据
      const mockInfo = {
        total_packages: 21,
        symbols: 7,
        analysis_types: ['quick_analysis', 'detailed_analysis', 'deep_analysis'],
        total_size_mb: 1.2,
        last_updated: '2025-07-26T11:00:59.918443'
      };
      setDataPackageInfo(mockInfo);
    } catch (error) {
      console.error('加载数据包信息失败:', error);
    }
  };

  const handleAnalysisComplete = (result) => {
    setAnalysisResult(result);
  };

  const handleSymbolChange = (value) => {
    setSelectedSymbol(value);
    setAnalysisResult(null); // 清除之前的分析结果
  };

  const getCurrentSymbolInfo = () => {
    return availableSymbols.find(s => s.value === selectedSymbol);
  };

  return (
    <div style={{ padding: '24px', background: '#0d1117', minHeight: '100vh' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px', textAlign: 'center' }}>
        <Title level={2} style={{ color: '#f0f6fc', marginBottom: '8px' }}>
          <RobotOutlined style={{ marginRight: '12px', color: '#26A69A' }} />
          AI智能分析系统
        </Title>
        <Paragraph style={{ color: '#8b949e', fontSize: '16px' }}>
          基于深度学习的加密货币市场分析，提供专业的投资建议和风险评估
        </Paragraph>
      </div>

      {/* 数据包状态信息 */}
      {dataPackageInfo && (
        <Card 
          size="small" 
          style={{ 
            marginBottom: '24px',
            background: 'rgba(38, 166, 154, 0.05)',
            border: '1px solid rgba(38, 166, 154, 0.2)'
          }}
        >
          <Row gutter={16} align="middle">
            <Col span={4}>
              <div style={{ textAlign: 'center' }}>
                <DatabaseOutlined style={{ fontSize: '24px', color: '#26A69A' }} />
                <div style={{ color: '#f0f6fc', fontWeight: 'bold', marginTop: '4px' }}>
                  数据就绪
                </div>
              </div>
            </Col>
            <Col span={20}>
              <Row gutter={24}>
                <Col span={6}>
                  <Text strong style={{ color: '#f0f6fc' }}>数据包总数：</Text>
                  <Text style={{ color: '#26A69A' }}>{dataPackageInfo.total_packages}</Text>
                </Col>
                <Col span={6}>
                  <Text strong style={{ color: '#f0f6fc' }}>支持币种：</Text>
                  <Text style={{ color: '#26A69A' }}>{dataPackageInfo.symbols} 个</Text>
                </Col>
                <Col span={6}>
                  <Text strong style={{ color: '#f0f6fc' }}>数据大小：</Text>
                  <Text style={{ color: '#26A69A' }}>{dataPackageInfo.total_size_mb} MB</Text>
                </Col>
                <Col span={6}>
                  <Text strong style={{ color: '#f0f6fc' }}>更新时间：</Text>
                  <Text style={{ color: '#26A69A' }}>
                    {new Date(dataPackageInfo.last_updated).toLocaleString()}
                  </Text>
                </Col>
              </Row>
            </Col>
          </Row>
        </Card>
      )}

      <Row gutter={24}>
        {/* 左侧：币种选择和图表 */}
        <Col span={14}>
          <Card 
            title={
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <BarChartOutlined style={{ color: '#26A69A' }} />
                  <span>市场数据</span>
                </div>
                <Select
                  value={selectedSymbol}
                  onChange={handleSymbolChange}
                  style={{ width: '200px' }}
                  size="small"
                >
                  {availableSymbols.map(symbol => (
                    <Option key={symbol.value} value={symbol.value}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <span>{symbol.label}</span>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {symbol.name}
                        </Text>
                      </div>
                    </Option>
                  ))}
                </Select>
              </div>
            }
            style={{ marginBottom: '24px' }}
          >
            {/* K线图表 */}
            <div style={{ height: '400px' }}>
              <MultiYearKLineChart
                symbol={selectedSymbol.replace('USDT', '')}
                years={['2025']}
                enableRealTime={true}
                height={380}
              />
            </div>
          </Card>

          {/* 分析结果显示 */}
          {analysisResult && (
            <Card 
              title="AI分析报告"
              style={{ 
                background: 'rgba(38, 166, 154, 0.03)',
                border: '1px solid rgba(38, 166, 154, 0.15)'
              }}
            >
              <div style={{ 
                maxHeight: '300px', 
                overflowY: 'auto',
                padding: '16px',
                background: 'rgba(0, 0, 0, 0.2)',
                borderRadius: '8px',
                border: '1px solid rgba(255, 255, 255, 0.1)'
              }}>
                <Paragraph style={{ 
                  whiteSpace: 'pre-wrap', 
                  margin: 0,
                  color: '#f0f6fc',
                  lineHeight: '1.8',
                  fontSize: '14px'
                }}>
                  {analysisResult}
                </Paragraph>
              </div>
            </Card>
          )}
        </Col>

        {/* 右侧：AI分析控制面板 */}
        <Col span={10}>
          <EnhancedAIAnalyzer
            symbol={selectedSymbol.replace('USDT', '')}
            onAnalysisComplete={handleAnalysisComplete}
          />

          {/* 分析说明 */}
          <Card 
            title="分析说明" 
            size="small"
            style={{ 
              marginTop: '16px',
              background: 'rgba(255, 255, 255, 0.02)',
              border: '1px solid rgba(255, 255, 255, 0.08)'
            }}
          >
            <div style={{ color: '#8b949e' }}>
              <div style={{ marginBottom: '12px' }}>
                <Text strong style={{ color: '#f0f6fc' }}>数据来源：</Text>
                <ul style={{ margin: '4px 0', paddingLeft: '20px' }}>
                  <li>90天历史K线数据</li>
                  <li>实时价格和成交量</li>
                  <li>技术指标计算结果</li>
                  <li>市场深度数据</li>
                </ul>
              </div>
              
              <div style={{ marginBottom: '12px' }}>
                <Text strong style={{ color: '#f0f6fc' }}>分析维度：</Text>
                <ul style={{ margin: '4px 0', paddingLeft: '20px' }}>
                  <li>趋势分析（MA、BOLL）</li>
                  <li>动量指标（RSI、KDJ）</li>
                  <li>成交量分析</li>
                  <li>支撑阻力位识别</li>
                </ul>
              </div>

              <div>
                <Text strong style={{ color: '#f0f6fc' }}>风险提示：</Text>
                <Paragraph style={{ 
                  margin: '4px 0', 
                  fontSize: '12px',
                  color: '#f85149',
                  background: 'rgba(248, 81, 73, 0.1)',
                  padding: '8px',
                  borderRadius: '4px',
                  border: '1px solid rgba(248, 81, 73, 0.2)'
                }}>
                  AI分析仅供参考，不构成投资建议。加密货币投资存在高风险，请谨慎决策。
                </Paragraph>
              </div>
            </div>
          </Card>

          {/* 数据包信息 */}
          <Card 
            title="数据包信息" 
            size="small"
            style={{ 
              marginTop: '16px',
              background: 'rgba(255, 255, 255, 0.02)',
              border: '1px solid rgba(255, 255, 255, 0.08)'
            }}
          >
            <div style={{ color: '#8b949e', fontSize: '12px' }}>
              <div style={{ marginBottom: '8px' }}>
                <Text strong style={{ color: '#f0f6fc' }}>当前币种：</Text>
                <Text style={{ color: '#26A69A' }}>
                  {getCurrentSymbolInfo()?.name} ({getCurrentSymbolInfo()?.value})
                </Text>
              </div>
              
              <div style={{ marginBottom: '8px' }}>
                <Text strong style={{ color: '#f0f6fc' }}>可用分析类型：</Text>
                <div style={{ marginTop: '4px' }}>
                  <Text style={{ color: '#26A69A' }}>• 快速分析（90天日K）</Text><br/>
                  <Text style={{ color: '#26A69A' }}>• 详细分析（多时间周期）</Text><br/>
                  <Text style={{ color: '#26A69A' }}>• 深度分析（全量数据）</Text>
                </div>
              </div>

              <div>
                <Text strong style={{ color: '#f0f6fc' }}>数据特点：</Text>
                <div style={{ marginTop: '4px' }}>
                  <Text style={{ color: '#8b949e' }}>• 实时更新</Text><br/>
                  <Text style={{ color: '#8b949e' }}>• 高精度计算</Text><br/>
                  <Text style={{ color: '#8b949e' }}>• 多维度分析</Text>
                </div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default AIAnalysisPage;
