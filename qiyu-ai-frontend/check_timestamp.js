#!/usr/bin/env node
/**
 * 检查2025年1分钟数据的时间戳格式
 */

const fetch = require('node-fetch')

async function checkTimestamp() {
  try {
    console.log('🔍 检查2025年1分钟数据时间戳格式...')
    
    const url = 'https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/20250726/BTCUSDT/1m/BTCUSDT_1m_01_2025_compressed.json'
    
    const response = await fetch(url)
    const rawData = await response.text()
    
    // 清理分块传输标记
    let cleaned = rawData
      .replace(/^[0-9a-fA-F]+\r?\n/, '')
      .replace(/\r?\n[0-9a-fA-F]+\r?\n/g, '')
      .replace(/\r?\n0\r?\n\r?\n?$/, '')
      .trim()
    
    const firstBrace = cleaned.indexOf('{')
    const lastBrace = cleaned.lastIndexOf('}')
    if (firstBrace >= 0 && lastBrace >= 0) {
      cleaned = cleaned.substring(firstBrace, lastBrace + 1)
    }
    
    const jsonData = JSON.parse(cleaned)
    const data = jsonData.data || jsonData
    
    if (Array.isArray(data) && data.length > 0) {
      console.log('📊 前3条数据的原始时间戳:')
      for (let i = 0; i < Math.min(3, data.length); i++) {
        const record = data[i]
        const timestamp = Array.isArray(record) ? record[0] : record.timestamp
        
        console.log(`  记录 ${i + 1}:`)
        console.log(`    原始时间戳: ${timestamp}`)
        console.log(`    时间戳长度: ${timestamp.toString().length} 位`)
        
        // 尝试不同的时间戳解析
        const asMillis = new Date(timestamp)
        const asMicros = new Date(timestamp / 1000)
        
        console.log(`    作为毫秒: ${asMillis.toISOString()} (${asMillis.getFullYear()})`)
        console.log(`    作为微秒: ${asMicros.toISOString()} (${asMicros.getFullYear()})`)
        console.log('')
      }
      
      // 分析时间戳特征
      const firstTimestamp = Array.isArray(data[0]) ? data[0][0] : data[0].timestamp
      const timestampStr = firstTimestamp.toString()
      
      console.log('🎯 时间戳分析:')
      console.log(`  时间戳位数: ${timestampStr.length}`)
      
      if (timestampStr.length === 16) {
        console.log('  判断: 这是微秒时间戳 (需要除以1000转换为毫秒)')
      } else if (timestampStr.length === 13) {
        console.log('  判断: 这是毫秒时间戳 (无需转换)')
      } else {
        console.log('  判断: 未知格式的时间戳')
      }
      
    } else {
      console.log('❌ 无法解析数据格式')
    }
    
  } catch (error) {
    console.error('❌ 检查时间戳失败:', error.message)
  }
}

checkTimestamp()