{"name": "qiyu-ai-frontend", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^5.3.7", "@ant-design/plots": "^1.2.5", "@antv/g2": "^4.2.10", "@antv/vendor": "^1.0.11", "@supabase/supabase-js": "^2.52.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "antd": "^5.26.4", "atropos": "^2.0.2", "chroma-js": "^3.1.2", "klinecharts": "^9.8.12", "react": "^19.1.0", "react-dom": "^19.1.0", "react-scripts": "5.0.1", "recharts": "^3.1.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app"], "overrides": [{"files": ["**/__tests__/**/*", "**/*.{spec,test}.*"], "env": {"jest": true}}]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}