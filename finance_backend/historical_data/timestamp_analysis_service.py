"""
📊 时间戳分析服务 - AI数据流程链路的数据汇聚中心

🎯 核心功能：
- 从COS存储获取历史K线数据（2017-2025年）
- 计算技术指标（MACD、KDJ、RSI）
- 计算支撑阻力位
- 获取BTC参考数据
- 组装完整的analysis_result数据包

🔄 在数据流程链路中的位置：
ai_prediction_api.py → [TimestampAnalysisService] → 多个数据源

📦 数据处理链路：
1. 从COS获取历史数据 → self._get_klines_by_date()
2. 计算技术指标 → TechnicalIndicatorService
3. 计算支撑阻力位 → self._calculate_support_resistance() 
4. 获取BTC参考数据 → 非BTC币种时获取
5. 组装analysis_result → 返回给AI API

🎯 输出数据结构：analysis_result包含所有AI分析所需的市场数据
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
import requests
import json

from .services import HistoricalDataService, COSDataService
from .technical_indicators_service import TechnicalIndicatorService

logger = logging.getLogger(__name__)


class TimestampAnalysisService:
    """
    📊 时间戳分析服务 - AI数据流程的核心数据处理器
    
    🔧 主要组件：
    - cos_service: COS数据服务，从云存储获取历史K线数据
    - indicator_service: 技术指标服务，计算MACD、KDJ、RSI等
    """
    
    def __init__(self):
        self.cos_service = COSDataService()
        self.indicator_service = TechnicalIndicatorService()
    
    def get_timestamp_analysis_data(self, 
                                   timestamp: int, 
                                   symbol: str = 'BTCUSDT',
                                   interval: str = '1m') -> Dict:
        """
        🎯 核心方法：获取时间戳分析数据 - 链路的数据汇聚点
        
        📥 输入参数：
        - timestamp: 目标分析时间戳
        - symbol: 交易对（如BTCUSDT、ETHUSDT）
        - interval: 时间间隔（1m、15m、1h、4h、1d）
        
        🔄 执行流程：
        1. 🗂️ 从COS获取历史同期数据（2017-2024年）
        2. 📊 获取目标年份的当日和前日数据
        3. 🧮 计算技术指标（MACD、KDJ、RSI）
        4. 📈 计算支撑阻力位
        5. 🔗 获取BTC参考数据（非BTC币种）
        6. 📦 组装完整的analysis_result数据包
        
        📤 返回数据：包含技术指标、历史数据、支撑阻力位等的完整数据包
        """
        try:
            # 确保时间戳是整数
            timestamp = int(timestamp)
            # 转换时间戳为日期时间
            target_datetime = datetime.fromtimestamp(timestamp / 1000)
            target_date = target_datetime.date()
            
            logger.info(f"开始获取时间戳分析数据: {timestamp} ({target_datetime}) {symbol} {interval}")
            
            # 获取目标年份和日期信息
            target_year = target_date.year
            target_month = target_date.month
            target_day = target_date.day
            current_year = datetime.now().year
            
            # 判断是否为历史年份分析
            is_historical_analysis = target_year < current_year
            
            # 1. 获取历史同期3日数据
            historical_years_data = {}
            if is_historical_analysis:
                # 历史年份分析：获取更早的历史数据作为参考
                for year in range(2017, target_year):
                    try:
                        year_data = self._get_historical_period_data(
                            symbol, year, target_month, target_day, interval
                        )
                        if year_data:
                            historical_years_data[str(year)] = year_data
                            logger.info(f"✅ 获取到 {year} 年同期数据")
                        else:
                            logger.warning(f"❌ 未获取到 {year} 年同期数据")
                    except Exception as e:
                        logger.error(f"获取 {year} 年数据失败: {e}")
            else:
                # 当前年份分析：动态获取历史数据作为参考
                # 🎯 智能年份范围计算：获取所有可用的历史数据用于AI分析
                historical_start_year = 2017 
                historical_end_year = target_year  # 不包括目标年份

                logger.info(f"📅 动态历史年份范围: {historical_start_year}-{historical_end_year-1}")

                for year in range(historical_start_year, historical_end_year):
                    try:
                        year_data = self._get_historical_period_data(
                            symbol, year, target_month, target_day, interval
                        )
                        if year_data:
                            historical_years_data[str(year)] = year_data
                            logger.info(f"✅ 获取到 {year} 年同期数据")
                        else:
                            logger.warning(f"❌ 未获取到 {year} 年同期数据")
                    except Exception as e:
                        logger.error(f"获取 {year} 年数据失败: {e}")
            
            # 2. 获取目标年份的前一日和当日数据
            prev_date = target_date - timedelta(days=1)
            current_dates_to_fetch = [prev_date, target_date]
            current_date_names = ['前一天', '目标日']
            
            logger.info(f"分析模式: {'历史年份分析' if is_historical_analysis else '当前年份分析'}")
            logger.info(f"目标年份: {target_year}, 历史数据年份: {list(historical_years_data.keys())}")
            
            # 获取目标年份的当前数据
            current_daily_data = {}
            all_current_klines = []
            
            for i, date in enumerate(current_dates_to_fetch):
                try:
                    klines = self._get_klines_by_date(symbol, date, interval)
                    if not klines.empty:
                        current_daily_data[current_date_names[i]] = {
                            'date': date.strftime('%Y-%m-%d'),
                            'data_points': len(klines),
                            'klines': klines.to_dict('records'),
                            'price_range': {
                                'open': float(klines['open'].iloc[0]),
                                'close': float(klines['close'].iloc[-1]),
                                'high': float(klines['high'].max()),
                                'low': float(klines['low'].min()),
                                'volume': float(klines['volume'].sum())
                            }
                        }
                        all_current_klines.append(klines)
                        logger.info(f"✅ 获取到 {date} 的当前数据: {len(klines)} 条")
                    else:
                        logger.warning(f"❌ 未获取到 {date} 的数据")
                        current_daily_data[current_date_names[i]] = {
                            'date': date.strftime('%Y-%m-%d'),
                            'data_points': 0,
                            'error': '无数据'
                        }
                except Exception as e:
                    logger.error(f"获取 {date} 数据失败: {e}")
                    current_daily_data[current_date_names[i]] = {
                        'date': date.strftime('%Y-%m-%d'),
                        'error': str(e)
                    }
            
            # 🔴 分离数据：只有主分析区间数据用于技术指标计算
            # 历史同期数据仅用于AI模式分析，不参与指标计算
            
            # 主分析区间数据（用于指标计算）
            main_analysis_klines = pd.DataFrame()
            if all_current_klines:
                main_analysis_klines = pd.concat(all_current_klines, ignore_index=True)
                main_analysis_klines = main_analysis_klines.sort_values('open_time').reset_index(drop=True)
            
            # 历史同期数据（仅用于AI分析，不计算指标）
            historical_klines_for_ai = []
            for year, year_data in historical_years_data.items():
                if 'klines' in year_data and year_data['klines']:
                    historical_klines_for_ai.extend(year_data['klines'])
            
            # 🧮 步骤3：计算技术指标 - 仅基于主分析区间数据（2880条1分钟K线）
            technical_indicators = {}
            if not main_analysis_klines.empty and len(main_analysis_klines) >= 26:
                try:
                    logger.info(f"🧮 开始计算技术指标，主分析区间数据量: {len(main_analysis_klines)}条")
                    
                    # 准备数据格式（仅使用主分析区间数据）
                    kline_records = main_analysis_klines.to_dict('records')
                    df = self.indicator_service.prepare_dataframe(kline_records)
                    
                    if not df.empty:
                        # 🔗 调用技术指标服务 - 链路关键步骤
                        all_indicators = self.indicator_service.calculate_all_indicators(df)
                        
                        # 📊 组装技术指标数据 - 这些数据将传递给AI
                        technical_indicators = {
                            # MACD指标：趋势分析核心
                            'MACD': {
                                'DIF': round(all_indicators.get('macd_line', 0.0), 4),      # 快线
                                'DEA': round(all_indicators.get('macd_signal', 0.0), 4),   # 慢线
                                'MACD': round(all_indicators.get('macd_histogram', 0.0), 4), # 柱状图
                                'trend': '金叉' if all_indicators.get('macd_line', 0) > all_indicators.get('macd_signal', 0) else '死叉'
                            },
                            # KDJ指标：超买超卖分析
                            'KDJ': {
                                'K': round(all_indicators.get('stoch_k', 50.0), 2),
                                'D': round(all_indicators.get('stoch_d', 50.0), 2),
                                'J': round(all_indicators.get('stoch_j', 50.0), 2),
                                'state': '超买' if all_indicators.get('stoch_k', 50) > 80 else '超卖' if all_indicators.get('stoch_k', 50) < 20 else '正常'
                            },
                            # RSI指标：相对强弱分析
                            'RSI': {
                                'value': round(all_indicators.get('rsi', 50.0), 2),
                                'state': '超买' if all_indicators.get('rsi', 50) > 70 else '超卖' if all_indicators.get('rsi', 50) < 30 else '正常'
                            },
                            'current_price': all_indicators.get('current_price', 0),
                            'calculation_period': f'{len(df)}条数据',
                            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        }
                        logger.info("✅ 技术指标计算完成")
                except Exception as e:
                    logger.error(f"技术指标计算失败: {e}")
                    technical_indicators = {'error': f'计算失败: {str(e)}'}
            else:
                technical_indicators = {'error': f'主分析区间数据不足，仅有 {len(main_analysis_klines)} 条数据，需要至少26条'}
            
            # 支撑阻力位计算也应该基于主分析区间数据  
            support_resistance = self._calculate_support_resistance(main_analysis_klines)
            
            vertical_analysis = self._perform_comprehensive_analysis(
                current_daily_data, historical_years_data
            )
            
            btc_reference_data = {}
            if symbol != 'BTCUSDT':
                try:
                    logger.info("获取BTC同期数据作为大盘参考")
                    btc_current_data = {}
                    btc_prev_date = target_date - timedelta(days=1)
                    btc_dates_to_fetch = [btc_prev_date, target_date]
                    btc_date_names = ['前一天', '目标日']
                    
                    for i, date in enumerate(btc_dates_to_fetch):
                        try:
                            btc_klines = self._get_klines_by_date('BTCUSDT', date, interval)
                            if not btc_klines.empty:
                                btc_current_data[btc_date_names[i]] = {
                                    'date': date.strftime('%Y-%m-%d'),
                                    'data_points': len(btc_klines),
                                    'price_range': {
                                        'open': float(btc_klines['open'].iloc[0]),
                                        'close': float(btc_klines['close'].iloc[-1]),
                                        'high': float(btc_klines['high'].max()),
                                        'low': float(btc_klines['low'].min()),
                                        'volume': float(btc_klines['volume'].sum())
                                    }
                                }
                        except Exception as e:
                            logger.error(f"获取BTC {date} 数据失败: {e}")
                    
                    if btc_current_data:
                        btc_reference_data = {
                            'current_data': btc_current_data,
                            'price_change': self._calculate_price_change(btc_current_data),
                            'symbol': 'BTCUSDT'
                        }
                        logger.info("✅ BTC参考数据获取成功")
                    else:
                        logger.warning("❌ BTC参考数据获取失败")
                        
                except Exception as e:
                    logger.error(f"获取BTC参考数据失败: {e}")
            
            # 📦 步骤6：组装analysis_result数据包 - 传递给AI的完整数据
            logger.info("📦 组装analysis_result数据包")
            return {
                # 基础信息
                'target_timestamp': timestamp,
                'target_datetime': target_datetime.isoformat(),
                'symbol': symbol,
                'interval': interval,
                
                # 🎯 AI分析核心数据
                'technical_indicators': technical_indicators,     # ← 技术指标（MACD、KDJ、RSI）
                'support_resistance': support_resistance,         # ← 支撑阻力位
                'current_data': current_daily_data,              # ← 当前价格数据
                'historical_years_data': historical_years_data,  # ← 历史同期数据
                'btc_reference': btc_reference_data,             # ← BTC参考数据
                
                # 纵向分析结果
                'vertical_analysis': vertical_analysis,
                
                # 统计信息
                'total_main_analysis_points': len(main_analysis_klines),  # 主分析区间数据点
                'total_historical_points': len(historical_klines_for_ai),  # 历史同期数据点  
                'historical_years_count': len(historical_years_data),
                'analysis_timestamp': datetime.now().isoformat(),
                'prediction_context': {
                    'purpose': '基于历史同期数据和当前趋势预测未来15分钟',
                    'data_range': f'{min(historical_years_data.keys()) if historical_years_data else "N/A"}-{target_year}',
                    'prediction_target': '15分钟内价格走势预测'
                }
            }
            
        except Exception as e:
            logger.error(f"时间戳分析失败: {e}")
            return {'error': str(e)}
    
    def _get_klines_by_date(self, symbol: str, date: datetime.date, interval: str) -> pd.DataFrame:
        """根据日期获取K线数据"""
        try:
            year = date.year
            month = date.month
            day = date.day
            
            # 根据年份使用不同的URL模式
            if year <= 2024:
                # Pre-2025数据路径
                url = f"https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/finance-1324685443/finance-1324685443/crypto-kline-data-v2/20250724/{symbol}/{interval}/{symbol}_{interval}_{year}_compressed.json"
            elif year == 2025:
                if month <= 6:
                    # 2025年1-6月使用月度文件
                    url = f"https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/20250726/{symbol}/{interval}/{symbol}_{interval}_{month:02d}_{year}_compressed.json"
                else:
                    # 2025年7月+使用每日文件
                    url = f"https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/20250726/{symbol}/{interval}/daily/{symbol}_{interval}_{month:02d}-{day:02d}_{year}_compressed.json"
            else:
                # 2026年及以后使用扩展格式
                year_suffix = str(year)[-2:]  # 取年份后两位：26, 27, 28...
                url = f"https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/202{year_suffix}0726/{symbol}/{interval}/daily/{symbol}_{interval}_{month:02d}-{day:02d}_{year}_compressed.json"

            logger.info(f"正在获取数据: {url}")
            
            # 获取数据
            response = requests.get(url, timeout=60)
            response.raise_for_status()
            
            # 处理分块传输编码
            content_text = response.text
            
            # 处理不同的文件格式
            lines = content_text.strip().split('\n')
            json_line = None
            
            # 检查是否是历史数据格式（第一行是数字，第二行是JSON）
            if len(lines) >= 2 and lines[0].strip().isdigit():
                # 历史数据格式：跳过第一行，使用第二行
                json_line = lines[1].strip()
                logger.info(f"检测到历史数据格式，跳过第一行: {lines[0].strip()}")
            elif content_text.startswith('{'):
                # 标准JSON格式：处理分块传输
                if '\n' in content_text:
                    for line in lines:
                        if line.strip().startswith('{') and line.strip().endswith('}'):
                            json_line = line.strip()
                            break
                else:
                    json_line = content_text.strip()
            else:
                logger.error(f"无法识别的文件格式")
                return pd.DataFrame()
            
            if not json_line:
                logger.error("未找到有效的JSON数据")
                return pd.DataFrame()
            
            # 解析JSON数据
            try:
                data = json.loads(json_line)
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {e}")
                logger.error(f"尝试解析的内容: {json_line[:200]}...")
                return pd.DataFrame()
            
            # 检查数据格式并提取K线数据
            if isinstance(data, dict) and 'data' in data:
                # 历史数据格式：包含metadata和data
                kline_data = data['data']
                logger.info(f"历史数据格式，元数据: {data.get('metadata', {})}")
            elif isinstance(data, dict) and 'metadata' in data:
                # 另一种可能的历史数据格式
                kline_data = data.get('data', [])
                logger.info(f"历史数据格式，元数据: {data.get('metadata', {})}")
            elif isinstance(data, list):
                # 标准格式：直接是K线数组
                kline_data = data
            else:
                logger.warning(f"未知数据格式: {type(data)}, keys: {data.keys() if isinstance(data, dict) else 'N/A'}")
                return pd.DataFrame()
            
            if not kline_data:
                return pd.DataFrame()
            
            # 转换为DataFrame
            df = pd.DataFrame(kline_data)
            
            if df.empty:
                return pd.DataFrame()
            
            # 检查数据格式并标准化列名
            if 'timestamp' in df.columns:
                # 历史数据格式：对象格式，直接重命名列
                column_mapping = {
                    'timestamp': 'open_time',
                    'open': 'open',
                    'high': 'high', 
                    'low': 'low',
                    'close': 'close',
                    'volume': 'volume'
                }
                # 只重命名存在的列
                df = df.rename(columns={k: v for k, v in column_mapping.items() if k in df.columns})
                df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
                logger.info(f"历史数据格式，列名: {list(df.columns)}")
            elif len(df.columns) >= 6:
                # Binance数组格式：[timestamp, open, high, low, close, volume, ...]
                df.columns = ['open_time', 'open', 'high', 'low', 'close', 'volume'] + [f'col_{i}' for i in range(6, len(df.columns))]
                df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
                logger.info(f"Binance数组格式，列数: {len(df.columns)}")
            else:
                logger.error(f"数据格式不支持，列数: {len(df.columns)}, 列名: {list(df.columns)}")
                return pd.DataFrame()
            
            # 过滤指定日期的数据
            target_date = pd.to_datetime(date).date()
            df = df[df['open_time'].dt.date == target_date]
            
            # 确保数据类型正确
            numeric_cols = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_cols:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 去除无效数据
            df = df.dropna().reset_index(drop=True)
            
            return df
            
        except requests.exceptions.RequestException as e:
            logger.error(f"HTTP请求失败 {symbol} {date}: {e}")
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"获取K线数据失败 {symbol} {date}: {e}")
            return pd.DataFrame()
    
    def _perform_vertical_analysis(self, daily_data: Dict) -> Dict:
        """进行纵向对比分析"""
        try:
            analysis = {
                'price_trend': '无数据',
                'volume_trend': '无数据',
                'volatility_analysis': '无数据',
                'trading_pattern': '无数据'
            }
            
            # 提取有效数据
            valid_days = []
            for day_name, day_data in daily_data.items():
                if 'price_range' in day_data:
                    valid_days.append((day_name, day_data))
            
            if len(valid_days) < 2:
                return analysis
            
            # 价格趋势分析
            prices = []
            volumes = []
            volatilities = []
            
            for day_name, day_data in valid_days:
                price_range = day_data['price_range']
                prices.append(price_range['close'])
                volumes.append(price_range['volume'])
                volatility = (price_range['high'] - price_range['low']) / price_range['open'] * 100
                volatilities.append(volatility)
            
            # 价格趋势判断
            if len(prices) >= 2:
                if prices[-1] > prices[0]:
                    price_change = (prices[-1] - prices[0]) / prices[0] * 100
                    analysis['price_trend'] = f'上涨趋势，涨幅 {price_change:.2f}%'
                elif prices[-1] < prices[0]:
                    price_change = (prices[0] - prices[-1]) / prices[0] * 100
                    analysis['price_trend'] = f'下跌趋势，跌幅 {price_change:.2f}%'
                else:
                    analysis['price_trend'] = '横盘整理'
            
            # 成交量趋势
            if len(volumes) >= 2:
                volume_change = (volumes[-1] - volumes[0]) / volumes[0] * 100
                if volume_change > 20:
                    analysis['volume_trend'] = f'成交量放大 {volume_change:.1f}%'
                elif volume_change < -20:
                    analysis['volume_trend'] = f'成交量萎缩 {volume_change:.1f}%'
                else:
                    analysis['volume_trend'] = '成交量平稳'
            
            # 波动率分析
            if volatilities:
                avg_volatility = np.mean(volatilities)
                if avg_volatility > 5:
                    analysis['volatility_analysis'] = f'高波动率 {avg_volatility:.2f}%'
                elif avg_volatility > 2:
                    analysis['volatility_analysis'] = f'中等波动率 {avg_volatility:.2f}%'
                else:
                    analysis['volatility_analysis'] = f'低波动率 {avg_volatility:.2f}%'
            
            # 交易模式识别
            if len(valid_days) >= 3:
                analysis['trading_pattern'] = self._identify_trading_pattern(valid_days)
            
            return analysis
            
        except Exception as e:
            logger.error(f"纵向分析失败: {e}")
            return {'error': str(e)}
    
    def _identify_trading_pattern(self, valid_days: List) -> str:
        """识别交易模式"""
        try:
            day_names = [day[0] for day in valid_days]
            day_data = [day[1] for day in valid_days]
            
            # 提取收盘价
            closes = [data['price_range']['close'] for data in day_data]
            
            if len(closes) == 3:
                # 三日模式识别
                if closes[1] > closes[0] and closes[2] > closes[1]:
                    return '连续上涨模式'
                elif closes[1] < closes[0] and closes[2] < closes[1]:
                    return '连续下跌模式'
                elif closes[1] > closes[0] and closes[2] < closes[1]:
                    return 'V型反转模式'
                elif closes[1] < closes[0] and closes[2] > closes[1]:
                    return '倒V型反转模式'
                else:
                    return '震荡整理模式'
            
            return '无明确模式'
            
        except Exception as e:
            logger.error(f"模式识别失败: {e}")
            return '模式识别失败'
    
    def _perform_comprehensive_analysis(self, current_data: Dict, historical_data: Dict) -> Dict:
        """综合分析：基于当前数据和历史同期数据进行对比"""
        try:
            analysis = {
                'current_trend': '无数据',
                'historical_comparison': '无历史数据',
                'seasonality_pattern': '无季节性规律',
                'prediction_confidence': 'low',
                'market_sentiment': '中性',
                'volatility_trend': '无数据'
            }
            
            # 分析当前趋势
            current_analysis = self._perform_vertical_analysis(current_data)
            analysis['current_trend'] = current_analysis.get('price_trend', '无数据')
            analysis['volatility_trend'] = current_analysis.get('volatility_analysis', '无数据')
            
            # 分析历史同期模式
            if historical_data:
                historical_patterns = []
                historical_prices = []
                
                for year, year_data in historical_data.items():
                    if 'period_data' in year_data:
                        period_data = year_data['period_data']
                        # 检查该年份数据的完整性
                        valid_days = [(day, info) for day, info in period_data.items() if 'error' not in info]
                        if len(valid_days) >= 2:
                            pattern = self._identify_trading_pattern(valid_days)
                            historical_patterns.append(f"{year}年: {pattern}")
                            
                            # 收集价格数据用于季节性分析
                            for day_name, day_info in valid_days:
                                if 'price_range' in day_info:
                                    historical_prices.append(day_info['price_range']['close'])
                
                if historical_patterns:
                    analysis['historical_comparison'] = '; '.join(historical_patterns[:3])  # 显示最多3年
                    
                    # 计算历史同期平均表现
                    if historical_prices:
                        avg_historical_price = np.mean(historical_prices)
                        current_price = self._extract_current_price(current_data)
                        if current_price:
                            price_deviation = (current_price - avg_historical_price) / avg_historical_price * 100
                            if abs(price_deviation) > 5:
                                analysis['seasonality_pattern'] = f'当前价格相比历史同期{"偏高" if price_deviation > 0 else "偏低"} {abs(price_deviation):.1f}%'
                                analysis['prediction_confidence'] = 'medium' if abs(price_deviation) < 15 else 'high'
                            else:
                                analysis['seasonality_pattern'] = '价格符合历史同期正常区间'
                                analysis['prediction_confidence'] = 'low'
                
                # 基于历史数据和当前趋势评估市场情绪
                if len(historical_data) >= 3:  # 有足够历史数据
                    bullish_patterns = sum(1 for p in historical_patterns if any(word in p for word in ['上涨', 'V型反转']))
                    bearish_patterns = sum(1 for p in historical_patterns if any(word in p for word in ['下跌', '倒V']))
                    
                    if bullish_patterns > bearish_patterns:
                        analysis['market_sentiment'] = '历史同期偏多'
                    elif bearish_patterns > bullish_patterns:
                        analysis['market_sentiment'] = '历史同期偏空' 
                    else:
                        analysis['market_sentiment'] = '历史同期中性'
            
            return analysis
            
        except Exception as e:
            logger.error(f"综合分析失败: {e}")
            return {'error': str(e)}
    
    def _extract_current_price(self, current_data: Dict) -> float:
        """从当前数据中提取最新价格"""
        try:
            for day_name in ['目标日', '前一天']:
                if day_name in current_data and 'price_range' in current_data[day_name]:
                    return current_data[day_name]['price_range']['close']
            return None
        except Exception:
            return None
    
    def _calculate_price_change(self, current_data: Dict) -> Dict:
        """计算价格变化百分比"""
        try:
            result = {'change_percent': 0, 'trend': '无数据'}
            
            if '前一天' in current_data and '目标日' in current_data:
                prev_data = current_data['前一天']
                target_data = current_data['目标日']
                
                if ('price_range' in prev_data and 'price_range' in target_data and
                    'error' not in prev_data and 'error' not in target_data):
                    
                    prev_price = prev_data['price_range']['close']
                    target_price = target_data['price_range']['close']
                    
                    change_percent = ((target_price - prev_price) / prev_price) * 100
                    result = {
                        'change_percent': round(change_percent, 2),
                        'trend': '上升' if change_percent > 0 else '下降' if change_percent < 0 else '持平',
                        'prev_price': prev_price,
                        'current_price': target_price
                    }
            
            return result
        except Exception as e:
            logger.error(f"计算价格变化失败: {e}")
            return {'change_percent': 0, 'trend': '计算失败'}
    
    def _calculate_support_resistance(self, combined_klines: pd.DataFrame) -> Dict:
        """计算支撑位和阻力位"""
        try:
            if combined_klines.empty or len(combined_klines) < 20:
                return {'support': 0, 'resistance': 0, 'method': '数据不足'}
            
            recent_data = combined_klines.tail(50) 
            
            highs = recent_data['high'].values
            lows = recent_data['low'].values
            closes = recent_data['close'].values
            
            current_price = closes[-1]
            
            recent_lows = np.sort(lows)[:10]  
            support_level = np.mean(recent_lows)
            
            recent_highs = np.sort(highs)[-10:] 
            resistance_level = np.mean(recent_highs)
            
            if support_level >= current_price:
                support_level = current_price * 0.95  
            if resistance_level <= current_price:
                resistance_level = current_price * 1.05  
                
            return {
                'support': round(support_level, 2),
                'resistance': round(resistance_level, 2),
                'current_price': round(current_price, 2),
                'method': '基于最近50个数据点的高低点分析'
            }
            
        except Exception as e:
            logger.error(f"计算支撑阻力位失败: {e}")
            return {'support': 0, 'resistance': 0, 'method': '计算失败'}
    
    def _get_historical_period_data(self, symbol: str, year: int, month: int, day: int, interval: str) -> Dict:
        try:
            target_date = datetime(year, month, day).date()
            prev_date = target_date - timedelta(days=1)
            next_date = target_date + timedelta(days=1)
            
            dates_to_fetch = [prev_date, target_date, next_date]
            date_names = ['前一日', '目标日', '后一日']
            
            period_data = {}
            all_period_klines = []
            
            for i, date in enumerate(dates_to_fetch):
                try:
                    klines = self._get_klines_by_date(symbol, date, interval)
                    if not klines.empty:
                        period_data[date_names[i]] = {
                            'date': date.strftime('%Y-%m-%d'),
                            'data_points': len(klines),
                            'price_range': {
                                'open': float(klines['open'].iloc[0]),
                                'close': float(klines['close'].iloc[-1]),
                                'high': float(klines['high'].max()),
                                'low': float(klines['low'].min()),
                                'volume': float(klines['volume'].sum())
                            }
                        }
                        all_period_klines.append(klines)
                        logger.debug(f"✅ 获取到 {year}-{date} 的数据: {len(klines)} 条")
                    else:
                        period_data[date_names[i]] = {
                            'date': date.strftime('%Y-%m-%d'),
                            'error': '无数据'
                        }
                except Exception as e:
                    logger.error(f"获取 {year}-{date} 数据失败: {e}")
                    period_data[date_names[i]] = {
                        'date': date.strftime('%Y-%m-%d'),
                        'error': str(e)
                    }
            
            # 合并该年份的K线数据
            combined_klines = pd.DataFrame()
            if all_period_klines:
                combined_klines = pd.concat(all_period_klines, ignore_index=True)
                combined_klines = combined_klines.sort_values('open_time').reset_index(drop=True)
            
            return {
                'year': year,
                'period_data': period_data,
                'klines': combined_klines.to_dict('records') if not combined_klines.empty else [],
                'total_data_points': len(combined_klines),
                'summary': {
                    'data_availability': sum(1 for day_data in period_data.values() if 'error' not in day_data),
                    'total_days': len(period_data)
                }
            }
            
        except Exception as e:
            logger.error(f"获取 {year} 年历史同期数据失败: {e}")
            return None


# 使用示例
if __name__ == "__main__":
    service = TimestampAnalysisService()
    
    # 测试时间戳分析
    test_timestamp = 1753718400000  # 2025-07-29 00:00:00
    result = service.get_timestamp_analysis_data(
        timestamp=test_timestamp,
        symbol='BTCUSDT',
        interval='1m'
    )
    
    print("时间戳分析结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))