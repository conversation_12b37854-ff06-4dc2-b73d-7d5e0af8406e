/**
 * AI服务初始化器组件
 * 在应用启动时自动初始化和启动AI分析服务
 */

import { useEffect, useState } from 'react';
import { notification } from 'antd';
import aiServiceManager from '../../services/AIServiceManager';

const AIServiceInitializer = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [initError, setInitError] = useState(null);

  useEffect(() => {
    let mounted = true;

    const initializeAIService = async () => {
      try {
        console.log('🚀 开始初始化AI服务...');
        
        // 配置AI服务（可以根据环境变量或用户设置调整）
        aiServiceManager.configure({
          symbols: ['BTC', 'ETH', 'SOL', 'XRP', 'DOGE'], // 默认分析币种
          autoStart: true, // 自动启动
          startupDelay: 3000 // 3秒延迟启动
        });

        // 请求通知权限（可选）
        await aiServiceManager.requestNotificationPermission();

        // 初始化AI服务管理器
        await aiServiceManager.initialize();

        if (mounted) {
          setIsInitialized(true);
          
          // 显示启动成功通知
          notification.success({
            message: 'AI分析服务已启动',
            description: '系统将每15分钟自动分析市场趋势，为您提供投资建议',
            duration: 4,
            placement: 'topRight'
          });
        }

      } catch (error) {
        console.error('❌ AI服务初始化失败:', error);
        
        if (mounted) {
          setInitError(error);
          
          // 显示错误通知
          notification.error({
            message: 'AI服务启动失败',
            description: '请检查网络连接和后端服务状态',
            duration: 6,
            placement: 'topRight'
          });
        }
      }
    };

    // 延迟初始化，确保应用完全加载
    const initTimer = setTimeout(initializeAIService, 1000);

    return () => {
      mounted = false;
      clearTimeout(initTimer);
    };
  }, []);

  // 监听AI服务状态变化
  useEffect(() => {
    if (!isInitialized) return;

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // 页面重新可见时，检查服务状态
        const status = aiServiceManager.getStatus();
        if (!status.isRunning && status.autoStartEnabled) {
          console.log('🔄 页面重新可见，重启AI服务...');
          aiServiceManager.restart();
        }
      }
    };

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isInitialized]);

  // 在开发环境下显示初始化状态
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const logStatus = () => {
        const status = aiServiceManager.getStatus();
        console.log('🤖 AI服务状态:', status);
      };

      // 每30秒记录一次状态（仅开发环境）
      const statusTimer = setInterval(logStatus, 30000);

      return () => clearInterval(statusTimer);
    }
  }, [isInitialized]);

  // 渲染子组件（应用的其余部分）
  return children;
};

export default AIServiceInitializer;
