"""
Django settings for finance_project project.

Generated by 'django-admin startproject' using Django 5.2.3.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

import os
# import pymysql
from pathlib import Path
from dotenv import load_dotenv

# 使用PyMySQL代替MySQLdb (暂时注释掉，使用SQLite)
# pymysql.install_as_MySQLdb()

# 加载环境变量
load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv('SECRET_KEY', 'django-insecure-default-key-change-in-production')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.getenv('DEBUG', 'True') == 'True'

ALLOWED_HOSTS = ['**************', 'www.jinseqiyu.com', 'jinseqiyu.com', 'localhost', '127.0.0.1']

# CSRF protection for your domain
CSRF_TRUSTED_ORIGINS = ['https://www.jinseqiyu.com', 'https://jinseqiyu.com']


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    # 第三方应用
    'rest_framework',
    'corsheaders',
    'storages',
    # 自定义应用
    'user_api',
    'crypto_api',
    'ai_analysis',
    'historical_data',
    'web_api',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',  # CORS中间件
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'finance_project.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'finance_project.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': os.path.join(BASE_DIR, 'db.sqlite3'),
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = 'static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'static')

# Media files
MEDIA_URL = 'media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# CORS配置
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",  # React开发端口
    "https://localhost:3000",
    "http://localhost:8080",
    "https://localhost:8080",
    "http://localhost:8888",  # 前端开发端口
    "https://localhost:8888",
]

# 微信小程序配置
WECHAT_CONFIG = {
    'APP_ID': os.getenv('WECHAT_APP_ID', ''),
    'APP_SECRET': os.getenv('WECHAT_APP_SECRET', ''),

}

# DeepSeek AI配置
DEEPSEEK_API_KEY = os.getenv('DEEPSEEK_API_KEY', '***********************************')
DEEPSEEK_CONFIG = {
    'API_KEY': DEEPSEEK_API_KEY,
    'API_URL': 'https://api.deepseek.com/v1/chat/completions',
}

# 腾讯云SMS配置
TENCENT_SMS_CONFIG = {
    'SECRET_ID': os.getenv('TENCENT_SECRET_ID', ''),
    'SECRET_KEY': os.getenv('TENCENT_SECRET_KEY', ''),
    'SDK_APP_ID': os.getenv('TENCENT_SMS_SDK_APP_ID', '1401016175'),
    'SIGN_ID': os.getenv('TENCENT_SMS_SIGN_ID', '660929'),
    'TEMPLATE_ID': os.getenv('TENCENT_SMS_TEMPLATE_ID', '2483395'),
    'REGION': os.getenv('TENCENT_SMS_REGION', 'ap-guangzhou'),
}

# 腾讯云COS存储配置
DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
AWS_STORAGE_BUCKET_NAME = os.getenv('COS_BUCKET_NAME', '')
AWS_S3_ENDPOINT_URL = 'https://cos.ap-guangzhou.myqcloud.com'
AWS_ACCESS_KEY_ID = os.getenv('COS_SECRET_ID', '')
AWS_SECRET_ACCESS_KEY = os.getenv('COS_SECRET_KEY', '')
AWS_S3_REGION_NAME = os.getenv('COS_REGION', 'ap-guangzhou')
AWS_S3_FILE_OVERWRITE = False
AWS_DEFAULT_ACL = 'public-read'
AWS_S3_VERIFY = True
AWS_QUERYSTRING_AUTH = False
AWS_S3_ADDRESSING_STYLE = "virtual"
# COS上传优化配置
AWS_S3_MAX_MEMORY_SIZE = 1024 * 1024 * 5  # 5MB，小文件直接内存处理
AWS_S3_FILE_UPLOAD_MAX_MEMORY_SIZE = 1024 * 1024 * 5
AWS_S3_CONNECT_TIMEOUT = 10  # 连接超时10秒
AWS_S3_READ_TIMEOUT = 20     # 读取超时20秒
AWS_S3_CUSTOM_DOMAIN = "finance-1324685443.cos.ap-guangzhou.myqcloud.com"

# REST Framework配置
REST_FRAMEWORK = {
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.BasicAuthentication',
    ],
}

# JWT配置
from datetime import timedelta
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=30),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=60),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
    'UPDATE_LAST_LOGIN': True,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': None,
    'AUDIENCE': None,
    'ISSUER': None,
    'JWK_URL': None,
    'LEEWAY': 0,
    'AUTH_HEADER_TYPES': ('Bearer',),
    'AUTH_HEADER_NAME': 'HTTP_AUTHORIZATION',
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',
    'USER_AUTHENTICATION_RULE': 'rest_framework_simplejwt.authentication.default_user_authentication_rule',
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'TOKEN_TYPE_CLAIM': 'token_type',
    'TOKEN_USER_CLASS': 'rest_framework_simplejwt.models.TokenUser',
    'JTI_CLAIM': 'jti',
    'SLIDING_TOKEN_REFRESH_EXP_CLAIM': 'refresh_exp',
    'SLIDING_TOKEN_LIFETIME': timedelta(days=30),
    'SLIDING_TOKEN_REFRESH_LIFETIME': timedelta(days=60),
}

# 腾讯云SMS配置
TENCENT_SMS_CONFIG = {
    'SECRET_ID': os.getenv('TENCENT_SECRET_ID', 'AKID7RbcUBi3HNHF9vbK7yjOvELK9oJdkhUc'),
    'SECRET_KEY': os.getenv('TENCENT_SECRET_KEY', '7mGYeZKsGSRCk7GE4ykPvFNJE2d0DGF3'),
    'REGION': os.getenv('TENCENT_SMS_REGION', 'ap-guangzhou'),
    'SDK_APP_ID': os.getenv('TENCENT_SMS_SDK_APP_ID', '1401016175'),
    'SIGN_ID': os.getenv('TENCENT_SMS_SIGN_ID', '660929'),
    'TEMPLATE_ID': os.getenv('TENCENT_SMS_TEMPLATE_ID', '2483395'),
}