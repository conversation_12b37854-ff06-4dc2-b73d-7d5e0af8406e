/* 统计卡片样式 */
.stats-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  height: 100%;
}

.stats-card:hover {
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* 卡片头部 */
.stats-card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-icon {
  font-size: 18px;
  color: #4CAF50;
}

.card-title {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 卡片内容 */
.stats-card-content {
  height: calc(100% - 60px);
}

/* 预测卡片样式 */
.prediction-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.prediction-legend {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.legend-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  flex: 1;
}

.legend-value {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 600;
}

/* 市场情绪卡片样式 */
.market-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.market-header {
  margin-bottom: 16px;
}

.update-time {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

.sentiment-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
}

.sentiment-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.sentiment-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coin-name {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  font-weight: 600;
}

.sentiment-value {
  color: #26a69a;
  font-size: 14px;
  font-weight: 600;
}

/* 进度条样式覆盖 */
.sentiment-item .ant-progress-bg {
  background: #26a69a !important;
}

.sentiment-item .ant-progress-inner {
  background: rgba(255, 255, 255, 0.1) !important;
}

/* 分析卡片样式 */
.analysis-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.locked-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 20px;
}

.lock-icon {
  font-size: 48px;
  opacity: 0.6;
}

.lock-message {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.lock-message p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin: 0;
}

.action-text {
  color: #4CAF50;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: color 0.3s ease;
}

.action-text:hover {
  color: #66bb6a;
}

.analysis-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  line-height: 1.6;
}

/* 不同类型卡片的特殊样式 */
.stats-card--prediction {
  min-height: 280px;
}

.stats-card--market {
  min-height: 200px;
}

.stats-card--analysis {
  min-height: 200px;
  cursor: pointer;
}

.stats-card--analysis:hover {
  border-color: #4CAF50;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-card {
    padding: 12px;
    border-radius: 8px;
  }
  
  .stats-card-header {
    margin-bottom: 12px;
    padding-bottom: 8px;
  }
  
  .card-title {
    font-size: 14px;
  }
  
  .legend-item {
    padding: 6px 0;
    gap: 6px;
  }
  
  .legend-label,
  .legend-value {
    font-size: 12px;
  }
  
  .coin-name {
    font-size: 14px;
  }
  
  .sentiment-value {
    font-size: 12px;
  }
  
  .lock-icon {
    font-size: 36px;
  }
  
  .locked-content {
    padding: 16px;
    gap: 12px;
  }
}
