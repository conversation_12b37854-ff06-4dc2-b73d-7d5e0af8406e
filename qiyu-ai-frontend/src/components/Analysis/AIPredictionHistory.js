import React from 'react';
import { List, Typography, Tag, Divider } from 'antd';
import { RiseOutlined, FallOutlined, MinusOutlined } from '@ant-design/icons';

const { Text } = Typography;

const AIPredictionHistory = ({ predictions = [] }) => {
  // 解析JSON字符串
  const parseJSON = (jsonStr) => {
    if (!jsonStr) return {};
    if (typeof jsonStr === 'object') return jsonStr;
    
    try {
      return JSON.parse(jsonStr);
    } catch (e) {
      console.error('解析JSON失败:', e);
      return {};
    }
  };

  // 获取情绪图标和颜色
  const getSentimentDisplay = (prediction) => {
    let predictionDistribution = {};
    
    try {
      predictionDistribution = parseJSON(prediction.prediction_distribution);
    } catch (e) {
      predictionDistribution = {
        "看涨": 25, "非常看涨": 10, "中性": 30, "看跌": 25, "非常看跌": 10
      };
    }
    
    const bullishTotal = (predictionDistribution['看涨'] || 0) + (predictionDistribution['非常看涨'] || 0);
    const bearishTotal = (predictionDistribution['看跌'] || 0) + (predictionDistribution['非常看跌'] || 0);
    const neutral = predictionDistribution['中性'] || 0;
    
    if (bullishTotal > bearishTotal && bullishTotal > neutral) {
      return { icon: <RiseOutlined />, color: '#52c41a', text: '看多' };
    } else if (bearishTotal > bullishTotal && bearishTotal > neutral) {
      return { icon: <FallOutlined />, color: '#ff4d4f', text: '看空' };
    } else {
      return { icon: <MinusOutlined />, color: '#faad14', text: '中性' };
    }
  };

  // 获取置信度标签颜色
  const getConfidenceLevelColor = (level) => {
    switch (level) {
      case '高': return 'green';
      case '中': return 'blue';
      case '低': return 'orange';
      default: return 'default';
    }
  };

  return (
    <List
      itemLayout="vertical"
      dataSource={predictions}
      renderItem={(item) => {
        const sentimentDisplay = getSentimentDisplay(item);
        const predictionTime = new Date(item.prediction_datetime).toLocaleString('zh-CN', {
          month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric'
        });
        
        return (
          <List.Item style={{ padding: '12px', background: 'rgba(255, 255, 255, 0.03)', borderRadius: '8px', marginBottom: '8px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '8px' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <Text strong style={{ fontSize: '16px' }}>{item.symbol}</Text>
                <div style={{ display: 'flex', alignItems: 'center', gap: '4px', color: sentimentDisplay.color }}>
                  {sentimentDisplay.icon}
                  <Text style={{ color: sentimentDisplay.color, fontWeight: 'bold' }}>{sentimentDisplay.text}</Text>
                </div>
              </div>
              
              <div>
                <Tag color={getConfidenceLevelColor(item.confidence_level)}>置信度: {item.confidence_level}</Tag>
                <Text type="secondary" style={{ fontSize: '12px', marginLeft: '8px' }}>{predictionTime}</Text>
              </div>
            </div>
            
            <div style={{ fontSize: '14px', marginBottom: '8px' }}>{item.analysis_summary}</div>
            
            <Divider style={{ margin: '8px 0' }} />
            
            <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px', color: '#999' }}>
              <div>数据点: {item.data_points_count || '未知'}</div>
              <div>历史年份: {item.historical_years_count || '未知'}</div>
            </div>
          </List.Item>
        );
      }}
    />
  );
};

export default AIPredictionHistory; 