/**
 * 动态图表生成器
 * 根据实际历史数据生成对应的图表配置
 */

export const getAvailableYears = (symbol) => {
  const yearMap = {
    'BTCUSDT': [2017, 2018, 2019, 2020, 2021, 2022, 2023, 2024],
    'ETHUSDT': [2017, 2018, 2019, 2020, 2021, 2022, 2023, 2024], 
    'LTCUSDT': [2017, 2018, 2019, 2020, 2021, 2022, 2023, 2024],
    'XRPUSDT': [2018, 2019, 2020, 2021, 2022, 2023, 2024],
    'DOGEUSDT': [2019, 2020, 2021, 2022, 2023, 2024],
    'SOLUSDT': [2020, 2021, 2022, 2023, 2024],
    'TRUMPUSDT': [] // 暂无数据
  };
  
  return yearMap[symbol] || [];
};

export const generateChartConfigs = (symbol) => {
  const availableYears = getAvailableYears(symbol);
  const configs = [];

  console.log(`📊 为${symbol}生成图表配置，可用年份:`, availableYears);

  // 2*2 布局：最新的在左上角，然后往右排列历年的
  // 第一个位置：当年数据（左上角）
  configs.push({
    id: 'chart-current',
    symbol,
    year: '2025',
    showMACD: true,
    enableRealTime: false,
    title: '当年数据',
    position: 0 // 左上角
  });

  // 获取最新的3年历史数据，按时间倒序排列（最新的先）
  const sortedYears = [...availableYears].reverse(); // 创建副本避免修改原数组
  const latestThreeYears = sortedYears.slice(0, 3); // 取前3年填满2*2布局

  // 第二、三、四个位置：历年数据（从左到右，从上到下）
  latestThreeYears.forEach((year, index) => {
    configs.push({
      id: `chart-${year}`,
      symbol,
      year: year.toString(),
      showMACD: false,
      enableRealTime: false,
      title: `${year}年`,
      position: index + 1 // 位置1、2、3
    });
  });

  console.log(`✅ 生成${configs.length}个图表配置 (2*2布局):`, configs.map(c => `${c.title}(位置${c.position})`));
  return configs;
};

export const getSymbolDisplayName = (symbol) => {
  const nameMap = {
    'BTCUSDT': 'BTC',
    'ETHUSDT': 'ETH',
    'LTCUSDT': 'LTC',
    'XRPUSDT': 'XRP',
    'DOGEUSDT': 'DOGE',
    'SOLUSDT': 'SOL',
    'TRUMPUSDT': 'TRUMP'
  };
  
  return nameMap[symbol] || symbol;
};