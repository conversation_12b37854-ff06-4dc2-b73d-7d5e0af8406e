/* CSS 变量定义 */
:root {
  --nav-gradient: linear-gradient(360deg, #016A86 0%, #447A53 50.48%);
  --footer-gradient: linear-gradient(360deg, #016A86 0%, #447A53 50.48%);
  --button-gradient: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
}

/* 导航栏样式 */
.qiyu-nav {
  background: var(--nav-gradient);
  border: 1px solid #555555;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  position: sticky;
  top: 0;
  z-index: 1000;
  height: 70px;
  transition: background 0.5s ease;
}

.nav-container {
  width: 100%;
  margin: 0 auto;
  padding: 0 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
  position: relative;
}

/* Logo 区域 */
.nav-logo {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px;
  position: absolute;
  width: 114px;
  height: 70px;
  left: 32px;
  top: 0px;
  cursor: pointer;
}

.logo-image {
  width: 53px;
  height: 38px;
  object-fit: contain;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.logo-title {
  width: 53px;
  height: 20px;
  font-family: 'eryaxindahei', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 20px;
  display: flex;
  align-items: center;
  color: #FFFFFF;
  flex: none;
  order: 0;
  flex-grow: 0;
  margin-left: 10px;
}

/* 币种选择区域 */
.nav-symbols {
  position: absolute;
  width: 914.11px;
  height: 36px;
  left: 252.19px;
  top: calc(50% - 18px);
  display: flex;
  align-items: center;
  gap: 16px;
  justify-content: flex-start;
}

.symbol-btn {
  width: auto;
  min-width: 83px;
  height: 36px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  border-radius: 6px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #F8F9FA;
  position: relative;
  overflow: hidden;
}

.symbol-btn.qiyu-button--ghost {
  background: transparent;
  color: #F8F9FA;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.symbol-btn.qiyu-button--ghost:hover {
  background: transparent;
  color: #F8F9FA;
  border: 1px solid rgba(255, 255, 255, 0.5);
  transform: scale(1.05);
}

.symbol-btn.qiyu-button--ghost.pi-coin {
  background: transparent;
  color: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: not-allowed;
  opacity: 0.6;
}

.symbol-btn.qiyu-button--ghost.pi-coin:hover {
  background: transparent;
  color: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transform: none;
}

.symbol-btn.qiyu-button--primary {
  background: linear-gradient(135deg, rgba(0, 201, 255, 0.3) 0%, rgba(146, 254, 157, 0.3) 100%);
  color: #F8F9FA;
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 6px;
}

.symbol-btn.qiyu-button--primary:hover {
  background: linear-gradient(135deg, rgba(0, 201, 255, 0.3) 0%, rgba(146, 254, 157, 0.3) 100%);
  color: #F8F9FA;
  border: 1px solid rgba(255, 255, 255, 0.7);
  transform: scale(1.05);
}

.symbol-shine-effect {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  transition: left 0.6s ease;
  pointer-events: none;
  opacity: 0;
}

.symbol-btn:hover .symbol-shine-effect {
  left: 100%;
  opacity: 1;
}

/* 用户区域 */
.nav-user {
  position: absolute;
  width: 194px;
  height: 32px;
  right: 24px;
  top: calc(50% - 16px);
  display: flex;
  align-items: center;
}

.notification-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.user-avatar-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 4px 8px;
  border-radius: 6px;
}

.user-avatar-wrapper:hover {
  background: rgba(255, 255, 255, 0.1);
}

.user-avatar {
  background: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  flex-shrink: 0;
}

.user-name {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.user-dropdown-icon {
  color: white;
  font-size: 12px;
  transition: transform 0.3s ease;
}

.user-avatar-wrapper:hover .user-dropdown-icon {
  transform: rotate(180deg);
}

/* 登录注册按钮 */
.auth-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.auth-link {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 500;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 4px 8px;
  border-radius: 4px;
}

.auth-link:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.auth-divider {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  margin: 0 4px;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .nav-symbols {
    width: auto;
    left: 18%;
    right: 18%;
    gap: 12px;
  }

  .symbol-btn {
    min-width: 75px;
    font-size: 13px;
  }
}

@media (max-width: 1200px) {
  .qiyu-nav {
    height: 65px; /* 约93% */
  }

  .nav-container {
    height: 65px;
  }

  .nav-logo {
    height: 65px;
  }

  .nav-symbols {
    width: auto;
    left: 16%;
    right: 20%;
    gap: 10px;
  }

  .symbol-btn {
    min-width: 70px;
    font-size: 12px;
  }
}

@media (max-width: 1024px) {
  .qiyu-nav {
    height: 60px; /* 约86% */
  }

  .nav-container {
    height: 60px;
    padding: 0 24px;
  }

  .nav-logo {
    height: 60px;
    left: 24px;
  }

  .nav-symbols {
    left: 14%;
    right: 22%;
    gap: 8px;
  }

  .symbol-btn {
    min-width: 65px;
    height: 32px;
    font-size: 12px;
  }

  .nav-user {
    right: 20px;
  }
}

@media (max-width: 768px) {
  .qiyu-nav {
    height: 55px; /* 约79% */
  }

  .nav-container {
    padding: 0 16px;
    position: relative;
    height: 55px;
  }

  .nav-logo {
    position: static;
    width: auto;
    height: auto;
  }

  .nav-symbols {
    position: static;
    width: auto;
    flex: 1;
    justify-content: center;
    gap: 6px;
    margin: 0 12px;
  }

  .symbol-btn {
    min-width: 55px;
    height: 28px;
    font-size: 11px;
  }

  .logo-title {
    display: none;
  }

  .logo-image {
    width: 32px;
    height: 32px;
  }

  .nav-user {
    position: static;
    width: auto;
  }

  .user-name {
    display: none;
  }

  .auth-link {
    font-size: 12px;
    padding: 3px 6px;
  }

  .auth-divider {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .qiyu-nav {
    height: 50px; /* 约71% */
  }

  .nav-container {
    justify-content: space-between;
    height: 50px;
    padding: 0 12px;
  }

  .nav-symbols {
    display: none;
  }

  .nav-logo {
    position: static;
    height: auto;
  }

  .logo-image {
    width: 28px;
    height: 28px;
  }

  .nav-user {
    position: static;
  }

  .user-avatar {
    width: 28px;
    height: 28px;
  }
}
