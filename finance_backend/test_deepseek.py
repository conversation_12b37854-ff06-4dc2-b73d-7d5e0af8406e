#!/usr/bin/env python3
"""
测试DeepSeek API连接
"""
import os
import sys
import django
import requests
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'finance_project.settings')
django.setup()

from django.conf import settings

def test_deepseek_api():
    """测试DeepSeek API连接"""
    print("🤖 测试DeepSeek API连接...")
    
    api_key = settings.DEEPSEEK_CONFIG['API_KEY']
    api_url = settings.DEEPSEEK_CONFIG['API_URL']
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # 简单的测试消息
    payload = {
        "model": "deepseek-reasoner",
        "messages": [
            {"role": "user", "content": "Hello, please respond with 'API connection successful'"}
        ],
        "temperature": 0.1,
        "max_tokens": 50
    }
    
    try:
        print(f"📡 发送请求到: {api_url}")
        print(f"🔑 API Key前缀: {api_key[:10]}...")
        
        response = requests.post(api_url, headers=headers, json=payload, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print(f"✅ API响应成功: {content}")
                return True
            else:
                print(f"❌ 响应格式异常: {result}")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"   错误内容: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ API请求超时（30秒）")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    print("🚀 DeepSeek API连接测试")
    print("=" * 40)
    
    if test_deepseek_api():
        print("\n🎉 DeepSeek API连接正常！")
    else:
        print("\n⚠️  DeepSeek API连接失败")
        sys.exit(1)