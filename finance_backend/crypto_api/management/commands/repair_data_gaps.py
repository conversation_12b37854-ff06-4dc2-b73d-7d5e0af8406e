"""
数据缺口修复命令
用于修复指定时间段的缺失数据
"""

import asyncio
import logging
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand, CommandError
from crypto_api.services.gap_recovery import GapRecoveryService

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '修复指定时间段的数据缺口'

    def add_arguments(self, parser):
        parser.add_argument(
            '--symbol',
            type=str,
            default='BTCUSDT',
            help='交易对符号 (默认: BTCUSDT)'
        )
        parser.add_argument(
            '--start',
            type=str,
            required=True,
            help='开始时间 (格式: 2025-07-30T00:00:00 或 2025-07-30 00:00:00)'
        )
        parser.add_argument(
            '--end',
            type=str,
            required=True,
            help='结束时间 (格式: 2025-07-30T11:24:59 或 2025-07-30 11:24:59)'
        )
        parser.add_argument(
            '--debug',
            action='store_true',
            help='启用调试模式'
        )

    def handle(self, *args, **options):
        symbol = options['symbol'].upper()
        start_str = options['start']
        end_str = options['end']
        debug = options['debug']

        # 解析时间格式
        try:
            # 支持两种格式: 2025-07-30T00:00:00 或 2025-07-30 00:00:00
            if 'T' in start_str:
                start_time = datetime.fromisoformat(start_str)
            else:
                start_time = datetime.strptime(start_str, '%Y-%m-%d %H:%M:%S')
                
            if 'T' in end_str:
                end_time = datetime.fromisoformat(end_str)
            else:
                end_time = datetime.strptime(end_str, '%Y-%m-%d %H:%M:%S')
                
        except ValueError as e:
            raise CommandError(f"时间格式错误: {e}")

        if start_time >= end_time:
            raise CommandError("开始时间必须早于结束时间")

        if end_time > datetime.now():
            self.stdout.write(
                self.style.WARNING(f"⚠️ 结束时间超过当前时间，将调整到: {datetime.now()}")
            )
            end_time = datetime.now()

        self.stdout.write(f"🔧 开始修复数据缺口...")
        self.stdout.write(f"📊 交易对: {symbol}")
        self.stdout.write(f"⏰ 时间范围: {start_time} ~ {end_time}")
        
        duration = end_time - start_time
        duration_minutes = int(duration.total_seconds() / 60)
        self.stdout.write(f"⏱️ 持续时间: {duration_minutes} 分钟")

        try:
            # 创建缺口修复服务
            gap_service = GapRecoveryService(debug=debug)
            
            # 执行修复
            success = gap_service.fill_gap(
                symbol=symbol,
                start_time=start_time,
                end_time=end_time
            )
            
            if success:
                self.stdout.write(
                    self.style.SUCCESS(f"✅ 数据缺口修复成功: {symbol} ({duration_minutes}分钟)")
                )
            else:
                raise CommandError(f"❌ 数据缺口修复失败: {symbol}")
                
        except Exception as e:
            logger.error(f"数据修复异常: {e}")
            raise CommandError(f"❌ 修复过程异常: {e}")