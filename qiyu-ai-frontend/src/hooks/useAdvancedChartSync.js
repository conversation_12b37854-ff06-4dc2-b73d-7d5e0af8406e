import { useRef, useCallback } from 'react';

/**
 * 高级K线图多图联动Hook
 * 支持十字线同步、缩放同步、滚动同步
 */
export const useAdvancedChartSync = () => {
  const chartsRef = useRef(new Map());
  const syncEnabledRef = useRef(true);
  const syncingRef = useRef(false);

  // 注册图表
  const registerChart = useCallback((chartId, chartInstance) => {
    if (!chartId || !chartInstance) return;

    chartsRef.current.set(chartId, chartInstance);
    
    // 绑定事件监听器
    bindChartEvents(chartId, chartInstance);
    
    console.log(`📊 图表 ${chartId} 已注册，当前图表数量: ${chartsRef.current.size}`);
  }, []);

  // 注销图表
  const unregisterChart = useCallback((chartId) => {
    if (chartsRef.current.has(chartId)) {
      const chart = chartsRef.current.get(chartId);
      unbindChartEvents(chart);
      chartsRef.current.delete(chartId);
      console.log(`📊 图表 ${chartId} 已注销，当前图表数量: ${chartsRef.current.size}`);
    }
  }, []);

  // 绑定图表事件
  const bindChartEvents = (chartId, chart) => {
    if (!chart || !chart.subscribeAction) return;

    // 十字线移动事件
    chart.subscribeAction('onCrosshairChange', (data) => {
      if (!syncEnabledRef.current || syncingRef.current) return;
      
      syncingRef.current = true;
      
      // 同步到其他图表
      chartsRef.current.forEach((otherChart, otherChartId) => {
        if (otherChartId !== chartId && otherChart.setCrosshair) {
          try {
            otherChart.setCrosshair(data);
          } catch (error) {
            console.warn(`同步十字线到图表 ${otherChartId} 失败:`, error);
          }
        }
      });
      
      setTimeout(() => {
        syncingRef.current = false;
      }, 50);
    });

    // 缩放事件
    chart.subscribeAction('onZoom', (data) => {
      if (!syncEnabledRef.current || syncingRef.current) return;
      
      syncingRef.current = true;
      
      // 同步缩放到其他图表
      chartsRef.current.forEach((otherChart, otherChartId) => {
        if (otherChartId !== chartId && otherChart.zoomAtCoordinate) {
          try {
            otherChart.zoomAtCoordinate(data.scale, data.coordinate.x, data.coordinate);
          } catch (error) {
            console.warn(`同步缩放到图表 ${otherChartId} 失败:`, error);
          }
        }
      });
      
      setTimeout(() => {
        syncingRef.current = false;
      }, 50);
    });

    // 滚动事件
    chart.subscribeAction('onScroll', (data) => {
      if (!syncEnabledRef.current || syncingRef.current) return;
      
      syncingRef.current = true;
      
      // 同步滚动到其他图表
      chartsRef.current.forEach((otherChart, otherChartId) => {
        if (otherChartId !== chartId && otherChart.scrollToRealTime) {
          try {
            if (data.isForward) {
              otherChart.scrollByDistance(data.distance);
            } else {
              otherChart.scrollByDistance(-data.distance);
            }
          } catch (error) {
            console.warn(`同步滚动到图表 ${otherChartId} 失败:`, error);
          }
        }
      });
      
      setTimeout(() => {
        syncingRef.current = false;
      }, 50);
    });
  };

  // 解绑图表事件
  const unbindChartEvents = (chart) => {
    if (!chart || !chart.unsubscribeAction) return;

    try {
      chart.unsubscribeAction('onCrosshairChange');
      chart.unsubscribeAction('onZoom');
      chart.unsubscribeAction('onScroll');
    } catch (error) {
      console.warn('解绑图表事件失败:', error);
    }
  };

  // 设置同步状态
  const setSyncEnabled = useCallback((enabled) => {
    syncEnabledRef.current = enabled;
    console.log(`🔗 图表联动已${enabled ? '启用' : '禁用'}`);
  }, []);

  // 重置所有图表缩放
  const resetAllChartsZoom = useCallback(() => {
    chartsRef.current.forEach((chart, chartId) => {
      try {
        if (chart.zoomAtCoordinate) {
          chart.zoomAtCoordinate(0.5, 1, { x: 0, y: 0 });
        }
      } catch (error) {
        console.warn(`重置图表 ${chartId} 缩放失败:`, error);
      }
    });
    console.log('🔄 已重置所有图表缩放');
  }, []);

  // 同步所有图表到实时数据
  const syncAllChartsToRealTime = useCallback(() => {
    chartsRef.current.forEach((chart, chartId) => {
      try {
        if (chart.scrollToRealTime) {
          chart.scrollToRealTime();
        }
      } catch (error) {
        console.warn(`同步图表 ${chartId} 到实时数据失败:`, error);
      }
    });
    console.log('⏱️ 已同步所有图表到实时数据');
  }, []);

  // 获取已注册图表数量
  const getRegisteredChartsCount = useCallback(() => {
    return chartsRef.current.size;
  }, []);

  // 获取所有图表实例
  const getAllCharts = useCallback(() => {
    return Array.from(chartsRef.current.values());
  }, []);

  // 为所有图表设置相同的时间周期
  const setAllChartsInterval = useCallback((interval) => {
    chartsRef.current.forEach((chart, chartId) => {
      try {
        // 这里需要根据具体的图表组件实现来调用相应的方法
        // 由于KLineCharts没有直接的setInterval方法，我们需要通过重新加载数据来实现
        if (chart.updateData) {
          chart.updateData(interval);
        }
      } catch (error) {
        console.warn(`设置图表 ${chartId} 时间周期失败:`, error);
      }
    });
    console.log(`⏰ 已设置所有图表时间周期为: ${interval}`);
  }, []);

  // 为所有图表添加/移除指标
  const toggleIndicatorForAllCharts = useCallback((indicatorName, enabled) => {
    chartsRef.current.forEach((chart, chartId) => {
      try {
        if (enabled) {
          if (chart.createIndicator) {
            chart.createIndicator(indicatorName);
          }
        } else {
          if (chart.removeIndicator) {
            chart.removeIndicator(indicatorName);
          }
        }
      } catch (error) {
        console.warn(`切换图表 ${chartId} 指标 ${indicatorName} 失败:`, error);
      }
    });
    console.log(`📈 已${enabled ? '添加' : '移除'}所有图表的 ${indicatorName} 指标`);
  }, []);

  return {
    registerChart,
    unregisterChart,
    setSyncEnabled,
    resetAllChartsZoom,
    syncAllChartsToRealTime,
    setAllChartsInterval,
    toggleIndicatorForAllCharts,
    getRegisteredChartsCount,
    getAllCharts
  };
};