@import 'atropos/css';

/* 个人中心页面样式 */
.profile-container {
  width: 100%;
  max-width: none;
  margin: 0;
  padding: 32px;
  background: #0f0f0f;
  height: calc(100vh - 70px - 75px); /* nav 70px + footer 75px */
  overflow-y: auto;
}

.profile-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 70px - 75px);
  color: #ffffff;
  font-size: 16px;
}

/* Layout Content 样式 */
.ant-layout-content {
  background: #0f0f0f !important;
  flex: 1;
  min-height: 0; /* 防止flex子元素溢出 */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin: 0 !important;
  padding: 0 !important;
}

/* Nav 和 Footer 样式确保不溢出 */
.qiyu-nav {
  flex-shrink: 0 !important;
}

.qiyu-footer {
  flex-shrink: 0 !important;
  margin-top: 0 !important;
  height: 75px !important;
  overflow: hidden !important;
}

.qiyu-footer .footer-bg {
  height: 75px !important;
  top: 0px !important;
}

/* 调整footer内部元素位置 */
.qiyu-footer .footer-logo {
  top: 0px !important;
}

.qiyu-footer .footer-copyright {
  top: 31px !important;
}

.qiyu-footer .footer-company {
  top: 31px !important;
}

.qiyu-footer .footer-contact-text {
  top: 11px !important;
}

.qiyu-footer .footer-wechat {
  top: 26px !important;
}

/* Layout 整体样式 */
.ant-layout {
  height: 100vh !important;
  max-height: 100vh !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 三列布局：左侧（信息+邀请+套餐），右侧（积分明细） */
.profile-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  height: 100%;
}

.profile-left {
  display: grid;
  grid-template-rows: auto auto 1fr;
  gap: 24px;
  height: 100%;
}

.profile-right {
  display: grid;
  grid-template-rows: 1fr;
  gap: 24px;
  height: 100%;
}

/* 用户信息卡片 */
.user-info-card {
  background: #1a1a1a !important;
  border: 1px solid #555555 !important;
  height: 180px;
  display: flex;
  flex-direction: column;
}

.user-info-card .ant-card-head {
  background: #1a1a1a;
  border-bottom: 1px solid #555555;
  flex-shrink: 0;
}

.user-info-card .ant-card-head-title {
  color: #ffffff;
}

.user-info-card .ant-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 20px !important;
}

.user-info-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex: 1;
  overflow-y: auto;
}

.user-info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 0;
  min-height: 32px;
}

.user-info-item .label {
  color: #b3b3b3;
  font-size: 13px;
  white-space: nowrap;
}

.user-info-item .value {
  color: #ffffff;
  font-size: 13px;
  flex: 1;
  text-align: right;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.modify-btn {
  color: #447A53 !important;
  padding: 0 !important;
  font-size: 11px !important;
  white-space: nowrap;
  flex-shrink: 0;
}

/* 邀请链接卡片 */
.invite-card {
  background: #1a1a1a !important;
  border: 1px solid #555555 !important;
  height: 200px;
  display: flex;
  flex-direction: column;
}

.invite-card .ant-card-head {
  background: #1a1a1a;
  border-bottom: 1px solid #555555;
  flex-shrink: 0;
}

.invite-card .ant-card-head-title {
  color: #ffffff;
}

.invite-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.invite-help-icon {
  color: #888888;
  cursor: pointer;
  transition: color 0.3s ease;
}

.invite-help-icon:hover {
  color: #447A53;
}

.invite-card .ant-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 20px !important;
}

.invite-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  overflow-y: auto;
}

.invite-url {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #2a2a2a;
  border-radius: 8px;
  border: 1px solid #555555;
  min-height: 40px;
}

.invite-url .ant-typography {
  flex: 1;
  margin: 0 !important;
  color: #ffffff !important;
  background: transparent !important;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.invite-url .ant-typography code {
  background: transparent !important;
  color: #ffffff !important;
  font-size: 12px !important;
}

.agent-stats {
  padding: 16px;
  background: #2a2a2a;
  border-radius: 8px;
  border: 1px solid #555555;
}

.agent-stats .ant-descriptions-item-label {
  color: #b3b3b3 !important;
}

.agent-stats .ant-descriptions-item-content {
  color: #ffffff !important;
}

/* 积分卡片 */
.credits-card {
  background: #1a1a1a !important;
  border: 1px solid #555555 !important;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.credits-card .ant-card-head {
  background: #1a1a1a;
  border-bottom: 1px solid #555555;
  flex-shrink: 0;
}

.credits-card .ant-card-head-title {
  color: #ffffff;
}

.credits-card-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.credits-balance {
  color: #447A53 !important;
  font-size: 18px !important;
  font-weight: bold !important;
}

.credits-card .ant-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 24px !important;
}

.credits-header {
  margin-bottom: 32px;
}

.credits-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.credits-amount {
  color: #447A53 !important;
  margin: 0 !important;
  font-size: 36px !important;
  font-weight: bold;
}

.credits-label {
  color: #888888 !important;
  font-size: 16px !important;
}

/* 3D 套餐卡片网格 - 2*2 横版布局 */
.package-grid {
  display: grid !important;
  grid-template-columns: repeat(2, 1fr) !important;
  grid-template-rows: repeat(2, 1fr) !important;
  gap: 16px !important;
  flex: 1;
  min-height: 200px;
  height: 100%;
}

.package-grid .package-atropos:nth-child(3) {
  grid-column: 1 !important;
  grid-row: 2 !important;
}

.package-atropos {
  width: 100%;
  height: 100%;
  min-height: 80px;
  border-radius: 16px;
}

.package-card-3d {
  width: 100%;
  height: 100%;
  border-radius: 16px;
  padding: 20px;
  color: white;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
  box-sizing: border-box;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* 基础套餐 - 浅绿色 */
.package-card-basic {
  background: linear-gradient(135deg, rgba(68, 122, 83, 0.6) 0%, rgba(68, 122, 83, 0.8) 100%);
  border: 1px solid rgba(68, 122, 83, 0.3);
}

/* 标准套餐 - 中绿色 */
.package-card-standard {
  background: linear-gradient(135deg, rgba(68, 122, 83, 0.8) 0%, rgba(68, 122, 83, 0.9) 100%);
  border: 1px solid rgba(68, 122, 83, 0.5);
}

/* 高级套餐 - 深绿色 */
.package-card-premium {
  background: linear-gradient(135deg, rgba(68, 122, 83, 0.9) 0%, rgba(68, 122, 83, 1) 100%);
  border: 1px solid rgba(68, 122, 83, 0.7);
}

/* 背景光晕效果 */
.package-background-glow {
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  pointer-events: none;
}

/* 图标背景 - 横版布局调整 */
.package-icon-bg {
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  opacity: 0.15;
  pointer-events: none;
  z-index: 0;
  font-size: 36px;
}

/* 套餐内容 - 横版布局 */
.package-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  position: relative;
  z-index: 1;
  text-align: left;
}

.package-price {
  font-size: 24px;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  margin-bottom: 4px;
  line-height: 1;
}

.package-credits {
  font-size: 16px;
  opacity: 0.9;
  margin-bottom: 8px;
}

.package-description {
  font-size: 12px;
  opacity: 0.8;
  line-height: 1.3;
  text-align: left;
  font-style: italic;
  max-height: 2.6em;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 套餐按钮 - 横版布局 */
.package-button {
  position: relative;
  z-index: 1;
  background: rgba(255, 255, 255, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: white !important;
  height: 40px !important;
  width: 80px !important;
  font-weight: 500 !important;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease !important;
  flex-shrink: 0;
  margin-left: 16px;
}

.package-button:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  transform: translateY(-2px);
}

/* 光泽效果 */
.shine-effect {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.3s ease;
  pointer-events: none;
}

.package-card-3d:hover .shine-effect {
  left: 100%;
}

.package-decoration {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 24px;
  opacity: 0;
  pointer-events: none;
  z-index: 10;
  animation: none;
}

.package-card-3d:hover .package-decoration {
  animation: decorationFall 1.5s ease-out forwards;
}

@keyframes decorationFall {
  0% {
    top: -20px;
    opacity: 0;
    transform: translateX(-50%) rotate(0deg) scale(0.5);
  }
  20% {
    opacity: 1;
    transform: translateX(-50%) rotate(180deg) scale(1);
  }
  80% {
    opacity: 1;
    transform: translateX(-50%) rotate(540deg) scale(1);
  }
  100% {
    top: calc(100% + 20px);
    opacity: 0;
    transform: translateX(-50%) rotate(720deg) scale(0.5);
  }
}

/* 积分明细卡片 */
.transactions-card {
  background: #1a1a1a !important;
  border: 1px solid #555555 !important;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.transactions-card .ant-card-head {
  background: #1a1a1a;
  border-bottom: 1px solid #555555;
  flex-shrink: 0;
}

.transactions-card .ant-card-head-title {
  color: #ffffff;
}

.transactions-card .ant-card-body {
  padding: 20px !important;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 简化的表格样式 */
.transactions-card .ant-table-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.transactions-card .ant-table {
  background: transparent !important;
  flex: 1;
}

.transactions-card .ant-spin-nested-loading {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.transactions-card .ant-spin-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.transactions-card .ant-table-thead > tr > th {
  background: #2a2a2a !important;
  color: #ffffff !important;
  border-bottom: 1px solid #555555 !important;
}

.transactions-card .ant-table-tbody > tr > td {
  background: transparent !important;
  color: #b3b3b3 !important;
  border-bottom: 1px solid #333333 !important;
}

.transactions-card .ant-table-tbody > tr:hover > td {
  background: #2a2a2a !important;
}

.transactions-card .positive {
  color: #00C853;
  font-weight: 500;
}

.transactions-card .negative {
  color: #FF1744;
  font-weight: 500;
}

/* 分页样式 */
.transactions-card .ant-pagination {
  margin-top: auto;
  padding-top: 16px;
  text-align: right;
  justify-content: flex-end;
  flex-shrink: 0;
}

.transactions-card .ant-pagination .ant-pagination-item {
  background: #2a2a2a;
  border-color: #555555;
}

.transactions-card .ant-pagination .ant-pagination-item a {
  color: #ffffff;
}

.transactions-card .ant-pagination .ant-pagination-item-active {
  background: #447A53;
  border-color: #447A53;
}

.transactions-card .ant-pagination .ant-pagination-prev,
.transactions-card .ant-pagination .ant-pagination-next {
  color: #ffffff;
}

/* 邀请Modal样式 - 与Profile页面风格一致 */
.invite-modal .ant-modal-content {
  background: #1a1a1a !important;
  border: 1px solid #555555 !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6) !important;
}

.invite-modal .ant-modal-header {
  background: #1a1a1a !important;
  border-bottom: 1px solid #555555 !important;
  border-radius: 12px 12px 0 0 !important;
  padding: 20px 24px !important;
}

.invite-modal .ant-modal-title {
  color: #ffffff !important;
  font-size: 18px !important;
  font-weight: 600 !important;
}

.invite-modal .ant-modal-body {
  background: #1a1a1a !important;
  color: #ffffff !important;
  padding: 24px !important;
}

.invite-modal .ant-modal-close {
  color: #888888 !important;
  top: 16px !important;
  right: 16px !important;
}

.invite-modal .ant-modal-close:hover {
  color: #ffffff !important;
  background: rgba(255, 255, 255, 0.1) !important;
}

.invite-modal .ant-modal-mask {
  background: rgba(0, 0, 0, 0.7) !important;
}

.invite-modal .input-group {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.invite-modal .input-group .ant-input {
  flex: 1;
  background: #2a2a2a !important;
  border: 1px solid #555555 !important;
  color: #ffffff !important;
  border-radius: 6px !important;
  padding: 8px 12px !important;
}

.invite-modal .input-group .ant-input:focus {
  border-color: #447A53 !important;
  box-shadow: 0 0 0 2px rgba(68, 122, 83, 0.2) !important;
}

.invite-modal .input-group .ant-btn {
  border-radius: 6px !important;
  height: auto !important;
  padding: 8px 16px !important;
  background: #447A53 !important;
  border-color: #447A53 !important;
}

.invite-modal .input-group .ant-btn:hover {
  background: #5a8f66 !important;
  border-color: #5a8f66 !important;
}

.invite-modal .mb-3 {
  margin-bottom: 20px;
}

.invite-modal .d-block {
  display: block;
}

.invite-modal .mb-2 {
  margin-bottom: 12px;
}

.invite-modal .ant-card {
  background: #2a2a2a !important;
  border: 1px solid #555555 !important;
  border-radius: 8px !important;
}

.invite-modal .ant-card .ant-card-body {
  background: #2a2a2a !important;
  color: #ffffff !important;
}

.invite-modal .ant-card .ant-card-head {
  background: #2a2a2a !important;
  border-bottom: 1px solid #555555 !important;
}

.invite-modal .ant-card .ant-card-head-title {
  color: #ffffff !important;
}

.invite-modal .ant-typography {
  color: #b3b3b3 !important;
}

.invite-modal .ant-typography.ant-typography-secondary {
  color: #888888 !important;
}

/* 响应式设计 */
/* 桌面端 - 确保2*2布局 */
@media (min-width: 769px) {
  .package-grid {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    grid-template-rows: repeat(2, 1fr) !important;
    gap: 16px !important;
  }

  .package-grid .package-atropos:nth-child(3) {
    grid-column: 1 !important;
    grid-row: 2 !important;
  }
}

@media (max-width: 768px) {
  .profile-container {
    padding: 16px;
    height: calc(100vh - 55px - 55px); /* 768px断点：nav 55px, footer 55px */
  }

  .qiyu-footer {
    height: 55px !important;
  }

  .qiyu-footer .footer-bg {
    height: 55px !important;
    top: 0px !important;
  }

  .profile-layout {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto auto;
    gap: 20px;
  }

  .profile-left {
    grid-template-rows: auto auto auto;
  }

  .profile-right {
    grid-template-rows: auto;
  }

  .user-info-card,
  .invite-card {
    height: auto;
    min-height: 140px;
  }

  .credits-card,
  .transactions-card {
    height: 350px;
  }

  .invite-url {
    flex-direction: column;
    align-items: stretch;
  }

  .package-grid {
    gap: 12px !important;
    grid-template-columns: 1fr !important;
    grid-template-rows: repeat(3, auto) !important;
  }

  .package-grid .package-atropos:nth-child(3) {
    grid-column: 1 !important;
    grid-row: 3 !important;
  }

  .package-atropos {
    height: 80px;
    min-height: 80px;
  }

  .package-card-3d {
    padding: 12px;
  }

  .package-price {
    font-size: 18px;
  }

  .package-credits {
    font-size: 12px;
  }

  .package-description {
    font-size: 10px;
  }

  .package-icon-bg {
    font-size: 24px;
    right: 12px;
  }

  .package-button {
    width: 60px !important;
    height: 32px !important;
    margin-left: 8px;
  }

  .credits-amount {
    font-size: 28px !important;
  }
}

@media (max-width: 480px) {
  .profile-container {
    padding: 12px;
    height: calc(100vh - 50px - 45px); /* 480px断点：nav 50px, footer 45px */
  }

  .qiyu-footer {
    height: 45px !important;
  }

  .qiyu-footer .footer-bg {
    height: 45px !important;
    top: 0px !important;
  }

  .user-info-card,
  .invite-card {
    height: auto;
    min-height: 110px;
  }

  .credits-card,
  .transactions-card {
    height: 300px;
  }

  .package-atropos {
    height: 70px;
    min-height: 70px;
  }

  .package-card-3d {
    padding: 8px;
  }

  .package-price {
    font-size: 16px;
  }

  .package-credits {
    font-size: 11px;
  }

  .package-description {
    font-size: 9px;
  }

  .package-icon-bg {
    font-size: 20px;
    right: 8px;
  }

  .package-button {
    width: 50px !important;
    height: 28px !important;
    margin-left: 6px;
    font-size: 12px !important;
  }
}

/* 按钮样式覆盖 */
.profile-container .ant-btn-primary {
  background: #447A53;
  border-color: #447A53;
}

.profile-container .ant-btn-primary:hover {
  background: #5a9267;
  border-color: #5a9267;
}

.profile-container .ant-btn-link {
  color: #447A53;
}

.profile-container .ant-btn-link:hover {
  color: #5a9267;
}