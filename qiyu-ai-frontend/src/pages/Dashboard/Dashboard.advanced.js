import React, { useState, useEffect, useCallback } from 'react';
import { Layout, Row, Col, Card, Button, Space } from 'antd';
import {
  LineChartOutlined,
  ReloadOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined
} from '@ant-design/icons';
import Atropos from 'atropos/react';
import Nav from '../../components/Layout/Nav';
import Footer from '../../components/Layout/Footer';
import GridKLineChart from '../../components/Chart/GridKLineChart';

import AIPredictionModule from '../../components/Dashboard/AIPredictionModule';
import cryptoMetadataService from '../../services/CryptoMetadataService';
import cryptoDataService from '../../services/StreamDataService';

import { generateNaturalGradient } from '../../utils/gradientGenerator';
import './Dashboard.advanced.css';
import 'atropos/css';

const { Content } = Layout;

const Dashboard = () => {
  const [selectedSymbol, setSelectedSymbol] = useState('BTCUSDT');
  const [availableYears, setAvailableYears] = useState([]); // 动态获取的年份
  const [loadingYears, setLoadingYears] = useState(false); // 加载年份的状态
  const [currentTimeframe, setCurrentTimeframe] = useState('1h'); // 当前时间周期
  const [supportedTimeframes, setSupportedTimeframes] = useState(['1m', '15m', '1h', '4h', '1d', '1mo']); // 当前支持的时间框架
  const [isFullscreen, setIsFullscreen] = useState(false); // 全屏状态
  const [isRefreshing, setIsRefreshing] = useState(false); // 刷新状态





  // 根据可用年份计算支持的时间框架
  const calculateSupportedTimeframes = useCallback((years) => {
    const timeframeSet = new Set();
    
    years.forEach(year => {
      const yearTimeframes = cryptoMetadataService.getTimeframesForYear(year);
      yearTimeframes.forEach(tf => timeframeSet.add(tf));
    });
    
    // 按正确顺序排序
    const orderedTimeframes = ['1m', '1h', '4h', '1d', '1mo'];
    return orderedTimeframes.filter(tf => timeframeSet.has(tf));
  }, []);

  // 动态获取可用年份数据
  const loadAvailableYears = useCallback(async () => {
    setLoadingYears(true);
    try {
      console.log(`🔍 获取${selectedSymbol} ${currentTimeframe}的可用年份...`);
      const yearsList = await cryptoMetadataService.getAvailableYears(selectedSymbol, currentTimeframe);
      console.log(`📅 获取到可用年份:`, yearsList);
      setAvailableYears(yearsList);
      
      // 更新支持的时间框架
      const supported = calculateSupportedTimeframes(yearsList);
      setSupportedTimeframes(supported);
      console.log(`⏰ 支持的时间框架:`, supported);
      
      // 检查当前时间框架是否还被支持，如果不支持则切换到第一个支持的时间框架
      if (supported.length > 0 && !supported.includes(currentTimeframe)) {
        const newTimeframe = supported[0];
        console.log(`🔄 当前时间框架${currentTimeframe}不被支持，切换到${newTimeframe}`);
        setCurrentTimeframe(newTimeframe);
      }
      
    } catch (error) {
      console.error('❗ 获取年份数据失败:', error);
      // 失败时使用默认值
      setAvailableYears(['2024', '2023']);
      setSupportedTimeframes(['1m', '1h', '4h', '1d', '1mo']);
    } finally {
      setLoadingYears(false);
    }
  }, [selectedSymbol, currentTimeframe, calculateSupportedTimeframes]);

  // 加载年份数据
  useEffect(() => {
    loadAvailableYears();
  }, [loadAvailableYears]);

  // 简化的初始化逻辑 - 数据已在useState中初始化



  // 不再需要获取年份数据，现在使用固定的时间视图

  // 应用渐变样式
  useEffect(() => {
    const hslGradient = generateNaturalGradient(
      '#016A86',
      '#447A53',
      8,
      'hsl',
      '360deg'
    );

    document.documentElement.style.setProperty('--nav-gradient', hslGradient);
    document.documentElement.style.setProperty('--footer-gradient', hslGradient);
    
    const buttonGradient = hslGradient.replace('360deg', '135deg');
    document.documentElement.style.setProperty('--button-gradient', buttonGradient);
  }, []);

  // 时间周期切换 - 与GridKLineChart保持一致的逻辑
  const handleTimeframeChange = (timeframe) => {
    console.log(`\n🕐 [Dashboard.advanced时间周期] 用户切换时间周期: ${currentTimeframe} -> ${timeframe}`);
    console.log(`🎯 [Dashboard.advanced时间周期] 当前货币: ${selectedSymbol}`);
    setCurrentTimeframe(timeframe);

    // 清除缓存和已加载状态，强制重新加载 - 与GridKLineChart保持一致
    console.log(`🗑️ [Dashboard.advanced时间周期] 清除缓存，准备重新加载数据`);
    cryptoDataService.clearCache();
    cryptoMetadataService.clearCache();
    console.log(`🔄 [Dashboard.advanced时间周期] 时间周期切换完成，等待图表重新渲染\n`);
  };

  // 币种切换
  const handleSymbolChange = (symbol) => {
    setSelectedSymbol(symbol);
    console.log(`📊 币种切换: ${symbol}`);
  };

  // 智能生成预测符号列表，避免重复
  const getPredictionSymbols = () => {
    const mainSymbol = selectedSymbol.replace('USDT', '');
    const defaultSymbols = ['BTC', 'ETH', 'SOL', 'XRP'];
    
    // 如果选中的币种不在默认列表中，则添加到首位
    if (!defaultSymbols.includes(mainSymbol)) {
      return [mainSymbol, ...defaultSymbols].slice(0, 4); // 最多显示4个
    }
    
    // 如果选中的币种在默认列表中，将其移到首位
    const filteredSymbols = defaultSymbols.filter(s => s !== mainSymbol);
    return [mainSymbol, ...filteredSymbols].slice(0, 4);
  };

  // 统一刷新功能 - 简洁的物理按钮效果
  const handleRefresh = async () => {
    setIsRefreshing(true);
    console.log(`🔄 [Dashboard刷新] 开始刷新所有数据...`);

    try {
      // 清除所有缓存
      cryptoDataService.clearCache();
      cryptoMetadataService.clearCache();

      // 重新加载年份数据
      await loadAvailableYears();

      console.log(`✅ [Dashboard刷新] 刷新完成`);
    } catch (error) {
      console.error(`❌ [Dashboard刷新] 刷新失败:`, error);
    } finally {
      // 延迟一点时间显示刷新效果
      setTimeout(() => {
        setIsRefreshing(false);
      }, 1000);
    }
  };



  // 图表容器全屏切换功能
  const handleFullscreen = () => {
    const newFullscreenState = !isFullscreen;
    setIsFullscreen(newFullscreenState);
    console.log(`📺 [图表全屏] ${newFullscreenState ? '进入' : '退出'}图表容器全屏模式`);

    // 延迟触发刷新，等待DOM更新完成
    setTimeout(() => {
      console.log(`🔄 [图表全屏] 触发图表尺寸刷新`);
      // 触发窗口resize事件，让图表重新计算尺寸
      window.dispatchEvent(new Event('resize'));
    }, 200);
  };

  // 监听ESC键退出全屏
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
        console.log(`⌨️ [图表全屏] ESC键退出全屏模式`);

        // 延迟触发刷新
        setTimeout(() => {
          console.log(`🔄 [图表全屏] ESC退出后触发图表尺寸刷新`);
          window.dispatchEvent(new Event('resize'));
        }, 200);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isFullscreen]);



  return (
    <div>
    <Layout className="dashboard-layout">
      {/* 导航栏 */}
      <Nav
        onSymbolChange={handleSymbolChange}
        selectedSymbol={selectedSymbol}
      />

      <Content className="dashboard-content">
        <div className="content-wrapper">

          {/* 主内容区域：左侧图表2/3 + 右侧历史预测1/3 */}
          <Row
            gutter={8}
            className="main-content-row"
            style={{
              width: '100%',
              margin: 0
            }}
          >
            {/* 左侧图表区域 2/3 */}
            <Col
              xs={16}
              sm={16}
              md={16}
              lg={16}
              xl={16}
              xxl={16}
              className="charts-section"

            >
              {/* 上部分：饼图和多空预测 */}
              <div className="top-charts-section">
                {/* 饼图 */}
                <div className="pie-chart-container">
                  <div className="chart-title">
                    <span style={{ display: 'flex', alignItems: 'center', gap: '8px', justifyContent: 'center' }}>
                      <img src="/logo_textless.png" alt="logo" style={{ width: '16px', height: '16px' }} />
                      {selectedSymbol} 市场情绪
                    </span>
                  </div>
                  <div className="pie-chart-placeholder">
                    <div style={{ textAlign: 'center', color: '#999', width: '100%' }}>
                      <LineChartOutlined style={{ fontSize: '32px', marginBottom: '8px' }} />
                      <div style={{ fontSize: '12px' }}>饼图组件开发中</div>
                    </div>
                  </div>
                </div>

                {/* 多空预测 */}
                <div className="prediction-container">
                  <div className="chart-title">
                    <span style={{ display: 'flex', alignItems: 'center', gap: '8px', justifyContent: 'center' }}>
                      <img src="/logo_textless.png" alt="logo" style={{ width: '16px', height: '16px' }} />
                      AI多空预测
                    </span>
                  </div>
                  <div className="prediction-content">
                    <div style={{ textAlign: 'center', color: '#999' }}>
                      <LineChartOutlined style={{ fontSize: '32px', marginBottom: '8px' }} />
                      <div style={{ fontSize: '12px' }}>多空预测开发中</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 下部分：K线图 */}
              <div className="kline-chart-section">
                <Card 
                  title={
                    <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <img src="/logo_textless.png" alt="logo" style={{ width: '20px', height: '20px' }} />
                      {selectedSymbol} K线图表 - {currentTimeframe}
                      {loadingYears && <span style={{color: '#999', fontSize: '12px'}}> (加载中...)</span>}
                      {!loadingYears && availableYears.length > 0 && (
                        <span style={{color: '#999', fontSize: '12px'}}> ({availableYears.length}年数据)</span>
                      )}
                    </span>
                  }
                  className={`kline-card ${isFullscreen ? 'kline-card-fullscreen' : ''}`}
                  extra={
                    <Space size="middle">
                      {/* 时间周期选择器 */}
                      <Space>
                        {supportedTimeframes.map((timeframe) => (
                          <Button
                            key={timeframe}
                            size="small"
                            type={currentTimeframe === timeframe ? 'primary' : 'default'}
                            onClick={() => handleTimeframeChange(timeframe)}
                            style={{
                              backgroundColor: currentTimeframe === timeframe ? '#447A53' : 'transparent',
                              borderColor: currentTimeframe === timeframe ? '#447A53' : '#555',
                              color: currentTimeframe === timeframe ? '#fff' : '#999'
                            }}
                          >
                            {timeframe}
                          </Button>
                        ))}
                      </Space>

                      {/* 3D刷新按钮 */}
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <Atropos
                          className="refresh-atropos"
                          activeOffset={20}
                          shadowScale={1.05}
                          rotateXMax={10}
                          rotateYMax={10}
                        >
                          <div className="refresh-button-3d">
                            <div className="refresh-background-glow" data-atropos-offset="-2"></div>
                            <Button
                              size="small"
                              icon={<ReloadOutlined />}
                              loading={isRefreshing}
                              onClick={handleRefresh}
                              className="refresh-button"
                              data-atropos-offset="3"
                              style={{
                                backgroundColor: '#447A53',
                                borderColor: '#447A53',
                                color: '#fff'
                              }}
                            >
                              刷新
                            </Button>
                            <div className="refresh-shine-effect" data-atropos-offset="3"></div>
                          </div>
                        </Atropos>
                      </div>

                      {/* 全屏按钮 */}
                      <Button
                        size="small"
                        icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
                        onClick={handleFullscreen}
                        style={{
                          backgroundColor: 'transparent',
                          borderColor: '#555',
                          color: '#999'
                        }}
                      >
                        {isFullscreen ? '退出全屏' : '全屏'}
                      </Button>
                    </Space>
                  }
                >
                  {/* 使用GridKLineChart组件显示所有年份数据 */}
                  <div style={{
                    height: '100%',
                    background: '#141414'
                  }}>
                    {loadingYears ? (
                      <div style={{ 
                        display: 'flex', 
                        alignItems: 'center', 
                        justifyContent: 'center', 
                        height: '100%',
                        color: '#999'
                      }}>
                        正在加载年份数据...
                      </div>
                    ) : availableYears.length === 0 ? (
                      <div style={{ 
                        display: 'flex', 
                        alignItems: 'center', 
                        justifyContent: 'center', 
                        height: '100%',
                        color: '#999'
                      }}>
                        暂无可用数据
                      </div>
                    ) : (
                      <GridKLineChart
                        symbol={selectedSymbol}
                        years={availableYears} // 传入动态获取的年份
                        timeframe={currentTimeframe} // 传递当前时间周期
                        enableRealTime={['1m', '15m', '1h', '4h', '1d'].includes(currentTimeframe)}
                        onBinanceConnect={() => {
                          console.log('🔌 Binance实时数据连接成功');
                        }}
                      />
                    )}
                  </div>
                </Card>
              </div>
            </Col>

            {/* 右侧预测区域 1/3 - 统一容器，内部垂直滚动 */}
            <Col
              xs={8}
              sm={8}
              md={8}
              lg={8}
              xl={8}
              xxl={8}
              className="predictions-section"
            >
              {/* 上半部分：AI预测分析 */}
              <div className="predictions-top-section">
                <div className="chart-title" style={{ position: 'relative' }}>
                  <span style={{ display: 'flex', alignItems: 'center', gap: '8px', justifyContent: 'center' }}>
                    <img src="/logo_textless.png" alt="logo" style={{ width: '16px', height: '16px' }} />
                    AI预测分析
                  </span>

                  {/* AI分析刷新按钮 - 右上角 */}
                  <div style={{
                    position: 'absolute',
                    top: '2px',
                    right: '8px',
                    display: 'flex',
                    alignItems: 'center'
                  }}>
                    <Atropos
                      className="refresh-atropos"
                      activeOffset={15}
                      shadowScale={1.03}
                      rotateXMax={8}
                      rotateYMax={8}
                    >
                      <div className="ai-refresh-button-3d">
                        <div className="refresh-background-glow" data-atropos-offset="-2"></div>
                        <Button
                          size="small"
                          icon={<ReloadOutlined />}
                          loading={isRefreshing}
                          onClick={() => {
                            // 触发指标计算器重新计算
                            console.log('🤖 触发AI分析刷新...');
                            // 这个按钮现在主要用于触发下方的指标计算器
                            // 实际的计算逻辑在IndicatorCalculator组件中
                          }}
                          className="ai-refresh-button"
                          data-atropos-offset="3"
                          style={{
                            backgroundColor: '#447A53',
                            borderColor: '#447A53',
                            color: '#fff',
                            fontSize: '10px',
                            height: '24px',
                            width: '24px',
                            padding: '0',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                        />
                        <div className="refresh-shine-effect" data-atropos-offset="3"></div>
                      </div>
                    </Atropos>
                  </div>
                </div>
                <div className="ai-analysis-content">
                  {/* AI预测模块 */}
                  <AIPredictionModule
                    symbols={getPredictionSymbols()}
                  />
                </div>
              </div>

              {/* 余额按钮 - 复刻刷新按钮的物理立体效果 */}
              <div className="balance-button-container">
                <Button
                  className="balance-button-3d"
                  icon={<span style={{ fontSize: '14px' }}>💰</span>}
                >
                  最新预测 ✕ 3
                </Button>
              </div>

              {/* 下半部分：AI分析历史 */}
              <div className="predictions-bottom-section">
                <div style={{
                  background: 'rgba(255, 255, 255, 0.03)',
                  border: '1px solid rgba(255, 255, 255, 0.08)',
                  borderRadius: '12px',
                  padding: '16px',
                  color: '#fff'
                }}>
                  <h3>AI分析历史</h3>
                  <p style={{ color: '#999' }}>查看过去24小时的AI分析记录和趋势变化</p>
                  <p style={{ color: '#666', fontSize: '12px' }}>功能开发中，将显示历史分析结果图表</p>
                </div>
              </div>
            </Col>
          </Row>


        </div>
      </Content>
    </Layout>



    {/* 页脚移到下面，不占用第一屏高度 */}
    <div style={{ marginTop: '20px' }}>
      <Footer />
    </div>
  </div>
  );
};

export default Dashboard;