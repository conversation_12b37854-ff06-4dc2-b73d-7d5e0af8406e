from rest_framework import serializers
from .models import WechatUser, CreditRecord

class WechatUserSerializer(serializers.ModelSerializer):
    """微信用户序列化器"""
    class Meta:
        model = WechatUser
        fields = ['id', 'openid', 'nickname', 'avatar_url', 'phone', 'credits', 'created_at', 'updated_at']
        read_only_fields = ['id', 'openid', 'credits', 'created_at', 'updated_at']

class CreditRecordSerializer(serializers.ModelSerializer):
    """积分记录序列化器"""
    type_display = serializers.CharField(source='get_type_display', read_only=True)
    
    class Meta:
        model = CreditRecord
        fields = ['id', 'user', 'type', 'type_display', 'amount', 'description', 'created_at']
        read_only_fields = ['id', 'created_at']

class WechatLoginSerializer(serializers.Serializer):
    """微信登录序列化器"""
    code = serializers.CharField(required=True, help_text="微信登录code")
    nickname = serializers.CharField(required=False, allow_blank=True, help_text="用户昵称")
    avatar_url = serializers.URLField(required=False, allow_blank=True, help_text="用户头像URL")

class WechatRegisterSerializer(serializers.Serializer):
    """微信用户注册序列化器"""
    code = serializers.CharField(required=True, help_text="微信登录code")
    nickname = serializers.CharField(required=False, allow_blank=True, help_text="用户昵称")
    avatar_url = serializers.URLField(required=False, allow_blank=True, help_text="用户头像URL")

class PhoneBindSerializer(serializers.Serializer):
    """手机号绑定序列化器"""
    code = serializers.CharField(required=True, help_text="微信登录code")
    encrypted_data = serializers.CharField(required=True, help_text="加密数据")
    iv = serializers.CharField(required=True, help_text="加密算法的初始向量") 