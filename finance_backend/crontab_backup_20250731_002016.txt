# 多时间周期数据自动更新任务
# 每15分钟更新15分钟数据
*/15 * * * * cd /home/<USER>/qiyuai-web/finance_backend && python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=15m --start=$(date +\%Y-\%m-\%d) --end=$(date +\%Y-\%m-\%d) >> logs/auto_update.log 2>&1

# 每小时5分更新1小时数据
5 * * * * cd /home/<USER>/qiyuai-web/finance_backend && python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=1h --start=$(date +\%Y-\%m-\%d) --end=$(date +\%Y-\%m-\%d) >> logs/auto_update.log 2>&1

# 每4小时10分更新4小时数据  
10 */4 * * * cd /home/<USER>/qiyuai-web/finance_backend && python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=4h --start=$(date +\%Y-\%m-\%d) --end=$(date +\%Y-\%m-\%d) >> logs/auto_update.log 2>&1

# 每天1:15更新昨日的日数据
15 1 * * * cd /home/<USER>/qiyuai-web/finance_backend && python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=1d --start=$(date -d "1 day ago" +\%Y-\%m-\%d) --end=$(date -d "1 day ago" +\%Y-\%m-\%d) >> logs/auto_update.log 2>&1

# 每天2:00更新当日的1日数据（用于当天实时更新）
0 2 * * * cd /home/<USER>/qiyuai-web/finance_backend && python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=1d --start=$(date +\%Y-\%m-\%d) --end=$(date +\%Y-\%m-\%d) >> logs/auto_update.log 2>&1

# 每30分钟检查实时数据收集进程状态
*/30 * * * * if ! pgrep -f "collect_realtime_data.*BTCUSDT" > /dev/null; then cd /home/<USER>/qiyuai-web/finance_backend && nohup python3 manage.py collect_realtime_data --symbol=BTCUSDT --daemon --debug > logs/btc_collector.log 2>&1 & fi

# 每小时清理日志文件（保持最新1000行）
0 * * * * cd /home/<USER>/qiyuai-web/finance_backend && tail -n 1000 logs/auto_update.log > logs/auto_update.log.tmp && mv logs/auto_update.log.tmp logs/auto_update.log

# AI预测任务 - 每15分钟执行
*/15 * * * * /usr/bin/python3 /home/<USER>/qiyuai-web/finance_backend/manage.py cron_predictions >> /home/<USER>/qiyuai-web/finance_backend/logs/predictions/cron_predictions.log 2>&1
