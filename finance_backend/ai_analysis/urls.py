from django.urls import path
from . import views
from . import ai_prediction_api
from . import prediction_history_api

urlpatterns = [
    path('', views.index, name='index'),
    path('predict/', ai_prediction_api.AIPredictionAPIView.as_view(), name='ai_prediction'),
    path('predictions/', prediction_history_api.get_prediction_history, name='get_prediction_history'),
    path('prediction/latest/', prediction_history_api.get_latest_prediction, name='get_latest_prediction'),
    path('predictions/history/', prediction_history_api.PredictionHistoryAPIView.as_view(), name='prediction_history'),
    path('predictions/stats/', prediction_history_api.PredictionStatsAPIView.as_view(), name='prediction_stats'),
    path('tasks/status/', prediction_history_api.AutoTaskStatusAPIView.as_view(), name='auto_task_status'),
] 