from django.db import models
from django.utils import timezone

class WechatUser(models.Model):
    """微信用户模型"""
    openid = models.CharField(max_length=100, unique=True, verbose_name="微信OpenID")
    unionid = models.CharField(max_length=100, null=True, blank=True, verbose_name="微信UnionID")
    nickname = models.CharField(max_length=100, null=True, blank=True, verbose_name="昵称")
    avatar_url = models.URLField(max_length=500, null=True, blank=True, verbose_name="头像URL")
    phone = models.CharField(max_length=20, null=True, blank=True, verbose_name="手机号")
    credits = models.IntegerField(default=3, verbose_name="剩余分析次数")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    created_at = models.DateTimeField(default=timezone.now, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "微信用户"
        verbose_name_plural = verbose_name
        db_table = "wechat_user"
    
    def __str__(self):
        return self.nickname or self.openid
    
    # Authentication interface for Django compatibility
    @property
    def is_authenticated(self):
        return True
    
    @property
    def is_anonymous(self):
        return False

class CreditRecord(models.Model):
    """积分记录模型"""
    TYPE_CHOICES = (
        (1, "初始赠送"),
        (2, "每日签到"),
        (3, "观看广告"),
        (4, "消费使用"),
        (5, "充值购买"),
    )
    
    user = models.ForeignKey(WechatUser, on_delete=models.CASCADE, related_name="credit_records", verbose_name="用户")
    type = models.SmallIntegerField(choices=TYPE_CHOICES, verbose_name="类型")
    amount = models.IntegerField(verbose_name="数量")
    description = models.CharField(max_length=200, null=True, blank=True, verbose_name="描述")
    created_at = models.DateTimeField(default=timezone.now, verbose_name="创建时间")
    
    class Meta:
        verbose_name = "积分记录"
        verbose_name_plural = verbose_name
        db_table = "credit_record"
        ordering = ["-created_at"]
    
    def __str__(self):
        return f"{self.user.nickname or self.user.openid} - {self.get_type_display()} - {self.amount}"
