
> qiyu-ai-frontend@0.1.0 start
> react-scripts start

(node:8656) [DEP_WEBPACK_DEV_SERVER_ON_AFTER_SETUP_MIDDLEWARE] DeprecationWarning: 'onAfterSetupMiddleware' option is deprecated. Please use the 'setupMiddlewares' option.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:8656) [DEP_WEBPACK_DEV_SERVER_ON_BEFORE_SETUP_MIDDLEWARE] DeprecationWarning: 'onBeforeSetupMiddleware' option is deprecated. Please use the 'setupMiddlewares' option.
Starting the development server...

Compiled successfully!

You can now view qiyu-ai-frontend in the browser.

  Local:            http://localhost:3000
  On Your Network:  http://************:3000

Note that the development build is not optimized.
To create a production build, use npm run build.

webpack compiled successfully
Compiling...
Compiled successfully!
webpack compiled successfully
Compiling...
Compiled successfully!
webpack compiled successfully
Compiling...
Compiled successfully!
webpack compiled successfully
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 168:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 459:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 510:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 168:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 459:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 510:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

webpack compiled with 1 warning
The build failed because the process exited too early. This probably means the system ran out of memory or someone called `kill -9` on the process.
