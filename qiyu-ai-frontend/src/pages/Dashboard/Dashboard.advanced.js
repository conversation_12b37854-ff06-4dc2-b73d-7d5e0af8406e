import React, { useState, useEffect, useCallback } from 'react';
import { Layout, Row, Col, Card, Button, Space } from 'antd';
import {
  LineChartOutlined,
  ReloadOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined
} from '@ant-design/icons';
import Atropos from 'atropos/react';
import Nav from '../../components/Layout/Nav';
import Footer from '../../components/Layout/Footer';
import GridKLineChart from '../../components/Chart/GridKLineChart';

import PredictionPieChart from '../../components/AI/PredictionPieChart';
import SentimentChart from '../../components/AI/SentimentChart';
import PredictionHistory from '../../components/AI/PredictionHistory';
import LatestPrediction from '../../components/AI/LatestPrediction';
import AIPredictionService from '../../services/AIPredictionService';
import cryptoMetadataService from '../../services/CryptoMetadataService';
import cryptoDataService from '../../services/StreamDataService';

import { generateNaturalGradient } from '../../utils/gradientGenerator';
import './Dashboard.advanced.css';
import 'atropos/css';

const { Content } = Layout;

const Dashboard = () => {
  const [selectedSymbol, setSelectedSymbol] = useState('BTCUSDT');
  const [availableYears, setAvailableYears] = useState([]); // 动态获取的年份
  const [loadingYears, setLoadingYears] = useState(false); // 加载年份的状态
  const [currentTimeframe, setCurrentTimeframe] = useState('1h'); // 当前时间周期
  const [supportedTimeframes, setSupportedTimeframes] = useState(['1m', '15m', '1h', '4h', '1d', '1mo']); // 当前支持的时间框架
  const [isFullscreen, setIsFullscreen] = useState(false); // 全屏状态
  const [isRefreshing, setIsRefreshing] = useState(false); // 刷新状态
  const [predictionData, setPredictionData] = useState({}); // AI预测数据
  const [predictionHistory, setPredictionHistory] = useState([]); // AI预测历史
  const [loadingPredictions, setLoadingPredictions] = useState(false); // 加载预测数据的状态

  // 根据可用年份计算支持的时间框架
  const calculateSupportedTimeframes = useCallback((years) => {
    const timeframeSet = new Set();
    
    years.forEach(year => {
      const yearTimeframes = cryptoMetadataService.getTimeframesForYear(year);
      yearTimeframes.forEach(tf => timeframeSet.add(tf));
    });
    
    // 按正确顺序排序
    const orderedTimeframes = ['1m', '1h', '4h', '1d', '1mo'];
    return orderedTimeframes.filter(tf => timeframeSet.has(tf));
  }, []);

  // 动态获取可用年份数据
  const loadAvailableYears = useCallback(async () => {
    setLoadingYears(true);
    try {
      console.log(`🔍 获取${selectedSymbol} ${currentTimeframe}的可用年份...`);
      const yearsList = await cryptoMetadataService.getAvailableYears(selectedSymbol, currentTimeframe);
      console.log(`📅 获取到可用年份:`, yearsList);
      setAvailableYears(yearsList);
      
      // 更新支持的时间框架
      const supported = calculateSupportedTimeframes(yearsList);
      setSupportedTimeframes(supported);
      console.log(`⏰ 支持的时间框架:`, supported);
      
      // 检查当前时间框架是否还被支持，如果不支持则切换到第一个支持的时间框架
      if (supported.length > 0 && !supported.includes(currentTimeframe)) {
        const newTimeframe = supported[0];
        console.log(`🔄 当前时间框架${currentTimeframe}不被支持，切换到${newTimeframe}`);
        setCurrentTimeframe(newTimeframe);
      }
      
    } catch (error) {
      console.error('❗ 获取年份数据失败:', error);
      // 失败时使用默认值
      setAvailableYears(['2024', '2023']);
      setSupportedTimeframes(['1m', '1h', '4h', '1d', '1mo']);
    } finally {
      setLoadingYears(false);
    }
  }, [selectedSymbol, currentTimeframe, calculateSupportedTimeframes]);

  // 加载年份数据
  useEffect(() => {
    loadAvailableYears();
  }, [loadAvailableYears]);

  // 加载AI预测数据
  const loadPredictionData = useCallback(async () => {
    console.log('🚀 [Dashboard] loadPredictionData函数被调用！');
    setLoadingPredictions(true);
    try {
      console.log(`🤖 [Dashboard] 获取${selectedSymbol}的AI预测数据...`);
      
      // 获取最新预测
      const latestResponse = await AIPredictionService.getLatestPrediction(selectedSymbol);
      
      if (latestResponse.success && latestResponse.data) {
        const latestPrediction = latestResponse.data;
        
        // 修复：确保prediction_distribution是对象类型
        let distribution = latestPrediction.prediction_distribution;
        if (typeof distribution === 'string') {
          try {
            // 尝试将单引号替换为双引号以修复无效的JSON
            distribution = JSON.parse(distribution.replace(/'/g, '"'));
          } catch (e) {
            console.error('解析prediction_distribution失败:', e);
            distribution = {}; // 解析失败时设为空对象
          }
        }

        console.log('🔍 [Dashboard] 原始prediction_distribution:', latestPrediction.prediction_distribution);
        console.log('🔍 [Dashboard] 处理后的distribution类型:', typeof distribution);
        
        setPredictionData({
          prediction: latestPrediction,
          distribution: distribution || {},
          sentiment: latestPrediction.market_sentiment || {}
        });
        
        console.log('🔍 [Dashboard] 设置的distribution:', distribution || {});
        
        console.log(`✅ [Dashboard] 获取到${selectedSymbol}的AI预测数据:`, latestPrediction);
      } else {
        console.log(`⚠️ [Dashboard] 未找到${selectedSymbol}的AI预测数据`);
        setPredictionData({});
      }
      
      // 获取预测历史
      const historyResponse = await AIPredictionService.getLatestPredictions(selectedSymbol, 10);
      if (historyResponse.success && historyResponse.data.length > 0) {
        setPredictionHistory(historyResponse.data);
        console.log(`📊 [Dashboard] 获取到${historyResponse.data.length}条预测历史`);
      } else {
        setPredictionHistory([]);
      }
      
    } catch (error) {
      console.error('❌ [Dashboard] 获取AI预测数据失败:', error);
      setPredictionData({});
      setPredictionHistory([]);
    } finally {
      setLoadingPredictions(false);
    }
  }, [selectedSymbol]);

  // 加载AI预测数据
  useEffect(() => {
    console.log('🚀 [Dashboard] useEffect触发，准备加载AI预测数据');
    loadPredictionData();
  }, [loadPredictionData]);

  // 应用渐变样式
  useEffect(() => {
    const hslGradient = generateNaturalGradient(
      '#016A86',
      '#447A53',
      8,
      'hsl',
      '360deg'
    );

    document.documentElement.style.setProperty('--nav-gradient', hslGradient);
    document.documentElement.style.setProperty('--footer-gradient', hslGradient);
    
    const buttonGradient = hslGradient.replace('360deg', '135deg');
    document.documentElement.style.setProperty('--button-gradient', buttonGradient);
  }, []);

  // 时间周期切换 - 与GridKLineChart保持一致的逻辑
  const handleTimeframeChange = (timeframe) => {
    console.log(`\n🕐 [Dashboard.advanced时间周期] 用户切换时间周期: ${currentTimeframe} -> ${timeframe}`);
    console.log(`🎯 [Dashboard.advanced时间周期] 当前货币: ${selectedSymbol}`);
    setCurrentTimeframe(timeframe);

    // 清除缓存和已加载状态，强制重新加载 - 与GridKLineChart保持一致
    console.log(`🗑️ [Dashboard.advanced时间周期] 清除缓存，准备重新加载数据`);
    cryptoDataService.clearCache();
    cryptoMetadataService.clearCache();
    console.log(`🔄 [Dashboard.advanced时间周期] 时间周期切换完成，等待图表重新渲染\n`);
  };

  // 币种切换
  const handleSymbolChange = (symbol) => {
    setSelectedSymbol(symbol);
    console.log(`📊 币种切换: ${symbol}`);
    
    // 清除缓存，重新加载AI预测数据
    // Note: AIPredictionService doesn't have a clearCache method
  };


  // 统一刷新功能 - 简洁的物理按钮效果
  const handleRefresh = async () => {
    setIsRefreshing(true);
    console.log(`🔄 [Dashboard刷新] 开始刷新所有数据...`);

    try {
      // 清除所有缓存
      cryptoDataService.clearCache();
      cryptoMetadataService.clearCache();
      // Note: AIPredictionService doesn't have a clearCache method

      // 重新加载年份数据
      await loadAvailableYears();
      
      // 重新加载AI预测数据
      await loadPredictionData();

      console.log(`✅ [Dashboard刷新] 刷新完成`);
    } catch (error) {
      console.error(`❌ [Dashboard刷新] 刷新失败:`, error);
    } finally {
      // 延迟一点时间显示刷新效果
      setTimeout(() => {
        setIsRefreshing(false);
      }, 1000);
    }
  };

  // 图表容器全屏切换功能
  const handleFullscreen = () => {
    const newFullscreenState = !isFullscreen;
    setIsFullscreen(newFullscreenState);
    console.log(`📺 [图表全屏] ${newFullscreenState ? '进入' : '退出'}图表容器全屏模式`);

    // 延迟触发刷新，等待DOM更新完成
    setTimeout(() => {
      console.log(`🔄 [图表全屏] 触发图表尺寸刷新`);
      // 触发窗口resize事件，让图表重新计算尺寸
      window.dispatchEvent(new Event('resize'));
    }, 200);
  };

  // 监听ESC键退出全屏
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
        console.log(`⌨️ [图表全屏] ESC键退出全屏模式`);

        // 延迟触发刷新
        setTimeout(() => {
          console.log(`🔄 [图表全屏] ESC退出后触发图表尺寸刷新`);
          window.dispatchEvent(new Event('resize'));
        }, 200);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isFullscreen]);

  return (
    <div>
    <Layout className="dashboard-layout">
      {/* 导航栏 */}
      <Nav
        onSymbolChange={handleSymbolChange}
        selectedSymbol={selectedSymbol}
      />

      <Content className="dashboard-content">
        <div className="content-wrapper">

          {/* 主内容区域：左侧图表2/3 + 右侧历史预测1/3 */}
          <Row
            gutter={8}
            className="main-content-row"
            style={{
              width: '100%',
              margin: 0
            }}
          >
            {/* 左侧图表区域 2/3 */}
            <Col
              xs={16}
              sm={16}
              md={16}
              lg={16}
              xl={16}
              xxl={16}
              className="charts-section"

            >
              {/* 上部分：饼图和多空预测 */}
              <div className="top-charts-section">
                {/* 饼图 */}
                <div className="pie-chart-container">
                  <div className="chart-title">
                    <span style={{ display: 'flex', alignItems: 'center', gap: '8px', justifyContent: 'center' }}>
                      <img src="/logo_textless.png" alt="logo" style={{ width: '16px', height: '16px' }} />
                      AI情绪预测
                    </span>
                  </div>
                  {loadingPredictions ? (
                    <div className="pie-chart-placeholder">
                      <div style={{ textAlign: 'center', color: '#999', width: '100%' }}>
                        <LineChartOutlined style={{ fontSize: '32px', marginBottom: '8px' }} />
                        <div style={{ fontSize: '12px' }}>加载中...</div>
                      </div>
                    </div>
                  ) : Object.keys(predictionData.distribution || {}).length > 0 ? (
                    <PredictionPieChart predictionData={predictionData.distribution} title={`${selectedSymbol} 市场情绪`} />
                  ) : (
                    <div className="pie-chart-placeholder">
                      <div style={{ textAlign: 'center', color: '#999', width: '100%' }}>
                        <LineChartOutlined style={{ fontSize: '32px', marginBottom: '8px' }} />
                        <div style={{ fontSize: '12px' }}>暂无预测数据</div>
                      </div>
                    </div>
                  )}
                </div>

                {/* 多空预测 */}
                <div className="prediction-container" style={{
                  display: 'flex',
                  flexDirection: 'column',
                  height: '100%',
                  overflow: 'hidden'
                }}>
                  <div className="chart-title" style={{ flexShrink: 0 }}>
                    <span style={{ display: 'flex', alignItems: 'center', gap: '8px', justifyContent: 'center' }}>
                      <img src="/logo_textless.png" alt="logo" style={{ width: '16px', height: '16px' }} />
                      AI多空预测
                    </span>
                  </div>
                  <div style={{
                    flex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    padding: '4px',
                    minHeight: 0,
                    overflow: 'hidden'
                  }}>
                    {loadingPredictions ? (
                      <div style={{ textAlign: 'center', color: '#999' }}>
                        <LineChartOutlined style={{ fontSize: '24px', marginBottom: '4px' }} />
                        <div style={{ fontSize: '10px' }}>加载中...</div>
                      </div>
                    ) : Object.keys(predictionData.sentiment || {}).length > 0 ? (
                      <SentimentChart sentimentData={predictionData.sentiment} title="AI多空预测" />
                    ) : (
                      <div style={{ textAlign: 'center', color: '#999' }}>
                        <LineChartOutlined style={{ fontSize: '24px', marginBottom: '4px' }} />
                        <div style={{ fontSize: '10px' }}>暂无情绪数据</div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* 下部分：K线图 */}
              <div className="kline-chart-section">
                <Card 
                  title={
                    <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <img src="/logo_textless.png" alt="logo" style={{ width: '20px', height: '20px' }} />
                      {selectedSymbol} K线图表 - {currentTimeframe}
                      {loadingYears && <span style={{color: '#999', fontSize: '12px'}}> (加载中...)</span>}
                      {!loadingYears && availableYears.length > 0 && (
                        <span style={{color: '#999', fontSize: '12px'}}> ({availableYears.length}年数据)</span>
                      )}
                    </span>
                  }
                  className={`kline-card ${isFullscreen ? 'kline-card-fullscreen' : ''}`}
                  extra={
                    <Space size="middle">
                      {/* 时间周期选择器 */}
                      <Space>
                        {supportedTimeframes.map((timeframe) => (
                          <Button
                            key={timeframe}
                            size="small"
                            type={currentTimeframe === timeframe ? 'primary' : 'default'}
                            onClick={() => handleTimeframeChange(timeframe)}
                            style={{
                              backgroundColor: currentTimeframe === timeframe ? '#447A53' : 'transparent',
                              borderColor: currentTimeframe === timeframe ? '#447A53' : '#555',
                              color: currentTimeframe === timeframe ? '#fff' : '#999'
                            }}
                          >
                            {timeframe}
                          </Button>
                        ))}
                      </Space>

                      {/* 3D刷新按钮 */}
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <Atropos
                          className="refresh-atropos"
                          activeOffset={20}
                          shadowScale={1.05}
                          rotateXMax={10}
                          rotateYMax={10}
                        >
                          <div className="refresh-button-3d">
                            <div className="refresh-background-glow" data-atropos-offset="-2"></div>
                            <Button
                              size="small"
                              icon={<ReloadOutlined />}
                              loading={isRefreshing}
                              onClick={handleRefresh}
                              className="refresh-button"
                              data-atropos-offset="3"
                              style={{
                                backgroundColor: '#447A53',
                                borderColor: '#447A53',
                                color: '#fff'
                              }}
                            >
                              刷新
                            </Button>
                            <div className="refresh-shine-effect" data-atropos-offset="3"></div>
                          </div>
                        </Atropos>
                      </div>

                      {/* 全屏按钮 */}
                      <Button
                        size="small"
                        icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
                        onClick={handleFullscreen}
                        style={{
                          backgroundColor: 'transparent',
                          borderColor: '#555',
                          color: '#999'
                        }}
                      >
                        {isFullscreen ? '退出全屏' : '全屏'}
                      </Button>
                    </Space>
                  }
                >
                  {/* 使用GridKLineChart组件显示所有年份数据 */}
                  <div style={{
                    height: '100%',
                    background: '#141414'
                  }}>
                    {loadingYears ? (
                      <div style={{ 
                        display: 'flex', 
                        alignItems: 'center', 
                        justifyContent: 'center', 
                        height: '100%',
                        color: '#999'
                      }}>
                        正在加载年份数据...
                      </div>
                    ) : availableYears.length === 0 ? (
                      <div style={{ 
                        display: 'flex', 
                        alignItems: 'center', 
                        justifyContent: 'center', 
                        height: '100%',
                        color: '#999'
                      }}>
                        暂无可用数据
                      </div>
                    ) : (
                      <GridKLineChart
                        symbol={selectedSymbol}
                        years={availableYears} // 传入动态获取的年份
                        timeframe={currentTimeframe} // 传递当前时间周期
                        enableRealTime={['1m', '15m', '1h', '4h', '1d'].includes(currentTimeframe)}
                        onBinanceConnect={() => {
                          console.log('🔌 Binance实时数据连接成功');
                        }}
                      />
                    )}
                  </div>
                </Card>
              </div>
            </Col>

            {/* 右侧预测区域 1/3 - 统一容器，内部垂直滚动 */}
            <Col
              xs={8}
              sm={8}
              md={8}
              lg={8}
              xl={8}
              xxl={8}
              className="predictions-section"
              style={{
                height: 'calc(100vh - 140px)',
                display: 'flex',
                flexDirection: 'column'
              }}
            >
              {/* 上半部分：AI预测分析 */}
              <div className="predictions-top-section" style={{
                height: '18.5vh',
                overflow: 'auto',
                flexShrink: 0
              }}>
                <div className="chart-title" style={{ position: 'relative' }}>
                  <span style={{ display: 'flex', alignItems: 'center', gap: '8px', justifyContent: 'center' }}>
                    <img src="/logo_textless.png" alt="logo" style={{ width: '16px', height: '16px' }} />
                    AI预测分析
                  </span>

                  {/* AI分析刷新按钮 - 右上角 */}
                  <div style={{
                    position: 'absolute',
                    top: '2px',
                    right: '8px',
                    display: 'flex',
                    alignItems: 'center'
                  }}>
                    <Atropos
                      className="refresh-atropos"
                      activeOffset={15}
                      shadowScale={1.03}
                      rotateXMax={8}
                      rotateYMax={8}
                    >
                      <div className="ai-refresh-button-3d">
                        <div className="refresh-background-glow" data-atropos-offset="-2"></div>
                        <Button
                          size="small"
                          icon={<ReloadOutlined />}
                          loading={loadingPredictions}
                          onClick={loadPredictionData}
                          className="ai-refresh-button"
                          data-atropos-offset="3"
                          style={{
                            backgroundColor: '#447A53',
                            borderColor: '#447A53',
                            color: '#fff',
                            fontSize: '10px',
                            height: '24px',
                            width: '24px',
                            padding: '0',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                        />
                        <div className="refresh-shine-effect" data-atropos-offset="3"></div>
                      </div>
                    </Atropos>
                  </div>
                </div>

                {/* 最新预测内容 */}
                <div style={{ 
                  marginTop: '-1px',
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  minHeight: 0
                }}>
                  {loadingPredictions ? (
                    <div style={{
                      background: 'rgba(255, 255, 255, 0.03)',
                      border: '1px solid rgba(255, 255, 255, 0.08)',
                      borderRadius: '12px',
                      padding: '16px',
                      color: '#fff',
                      textAlign: 'center'
                    }}>
                      <LineChartOutlined style={{ fontSize: '32px', marginBottom: '8px' }} />
                      <div style={{ fontSize: '12px', color: '#999' }}>加载中...</div>
                    </div>
                  ) : predictionHistory.length > 0 ? (
                    <LatestPrediction prediction={predictionHistory[0]} symbol={selectedSymbol} />
                  ) : (
                    <div style={{
                      background: 'rgba(255, 255, 255, 0.03)',
                      border: '1px solid rgba(255, 255, 255, 0.08)',
                      borderRadius: '12px',
                      padding: '16px',
                      color: '#fff',
                      textAlign: 'center'
                    }}>
                      <LineChartOutlined style={{ fontSize: '32px', marginBottom: '8px' }} />
                      <div style={{ fontSize: '12px', color: '#999' }}>暂无最新预测</div>
                    </div>
                  )}
                </div>
              </div>

              {/* 余额按钮 - 复刻刷新按钮的物理立体效果 */}
              {/* <div className="balance-button-container">
                <Button
                  className="balance-button-3d"
                  icon={<span style={{ fontSize: '14px' }}>💰</span>}
                >
                  最新预测 ✕ {predictionHistory.length || 0}
                </Button>
              </div> */}

              {/* 下半部分：AI分析历史 */}
              <div className="predictions-bottom-section" style={{
                flex: 1,
                minHeight: 0,
                display: 'flex',
                flexDirection: 'column'
              }}>
                {loadingPredictions ? (
                  <div style={{
                    border: '1px solid rgba(255, 255, 255, 0.08)',
                    borderRadius: '12px',
                    padding: '16px',
                    textAlign: 'center'
                  }}>
                    <LineChartOutlined style={{ fontSize: '32px', marginBottom: '8px' }} />
                    <div style={{ fontSize: '12px', color: '#fffff' }}>加载中...</div>
                  </div>
                ) : predictionHistory.length > 1 ? (
                  <PredictionHistory predictions={predictionHistory.slice(1)} symbol={selectedSymbol} title="AI预测分析历史" />
                ) : (
                  <div style={{
                    border: '1px solid rgba(255, 255, 255, 0.08)',
                    borderRadius: '12px',
                    padding: '16px',
                    color: '#fffff'
                  }}>
                    <p style={{ color: '#fffff' }}>暂无历史分析记录</p>
                  </div>
                )}
              </div>
            </Col>
          </Row>


        </div>
      </Content>
    </Layout>


    {/* 页脚移到下面，不占用第一屏高度 */}
    <div style={{ marginTop: '20px' }}>
      <Footer />
    </div>
  </div>
  );
};

export default Dashboard;