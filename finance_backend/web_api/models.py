"""
Web API 用户模型
"""

from django.db import models
from django.contrib.auth.hashers import make_password, check_password
from django.utils import timezone
import uuid
import secrets
import string


class WebUser(models.Model):
    """Web用户模型（基于手机号的认证系统）"""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    phone = models.CharField(max_length=20, unique=True, verbose_name="手机号")
    password = models.CharField(max_length=128, verbose_name="密码", null=True, blank=True)
    nickname = models.CharField(max_length=100, verbose_name="昵称", default="")
    avatar = models.URLField(max_length=500, verbose_name="头像", null=True, blank=True)
    email = models.EmailField(verbose_name="邮箱", null=True, blank=True)
    credits = models.IntegerField(default=3, verbose_name="剩余积分")
    invite_code = models.Char<PERSON>ield(max_length=12, unique=True, verbose_name="邀请码", null=True, blank=True)
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    phone_verified = models.BooleanField(default=False, verbose_name="手机号已验证")
    last_login = models.DateTimeField(verbose_name="最后登录时间", null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "Web用户"
        verbose_name_plural = verbose_name
        db_table = "web_user"
        ordering = ["-created_at"]
    
    def __str__(self):
        return f"{self.nickname or self.phone} ({self.phone})"
    
    def set_password(self, raw_password):
        """设置密码（加密存储）"""
        self.password = make_password(raw_password)
    
    def check_password(self, raw_password):
        """验证密码"""
        if not self.password:
            return False
        return check_password(raw_password, self.password)
    
    def update_last_login(self):
        """更新最后登录时间"""
        self.last_login = timezone.now()
        self.save(update_fields=['last_login'])
    
    def consume_credits(self, amount=1):
        """消耗积分"""
        if self.credits >= amount:
            self.credits -= amount
            self.save(update_fields=['credits'])
            return True
        return False
    
    def add_credits(self, amount):
        """增加积分"""
        self.credits += amount
        self.save(update_fields=['credits'])
    
    @staticmethod
    def generate_invite_code():
        """生成邀请码"""
        chars = string.ascii_uppercase + string.digits
        while True:
            code = ''.join(secrets.choice(chars) for _ in range(9))
            if not WebUser.objects.filter(invite_code=code).exists():
                return code
    
    def save(self, *args, **kwargs):
        """保存前生成邀请码"""
        if not self.invite_code:
            self.invite_code = self.generate_invite_code()
        if not self.nickname:
            self.nickname = f'用户{self.phone[-4:]}' if self.phone else ''
        super().save(*args, **kwargs)
    
    # Django authentication interface compatibility
    @property
    def is_authenticated(self):
        return True
    
    @property
    def is_anonymous(self):
        return False


class SMSRecord(models.Model):
    """短信发送记录"""
    
    phone = models.CharField(max_length=20, verbose_name="手机号")
    code = models.CharField(max_length=10, verbose_name="验证码")
    purpose = models.CharField(max_length=50, verbose_name="用途", choices=[
        ('register', '注册'),
        ('login', '登录'),
        ('reset_password', '重置密码'),
        ('verify_phone', '验证手机号'),
    ])
    is_used = models.BooleanField(default=False, verbose_name="是否已使用")
    sent_at = models.DateTimeField(default=timezone.now, verbose_name="发送时间")
    used_at = models.DateTimeField(null=True, blank=True, verbose_name="使用时间")
    expires_at = models.DateTimeField(verbose_name="过期时间")
    
    class Meta:
        verbose_name = "短信记录"
        verbose_name_plural = verbose_name
        db_table = "sms_record"
        ordering = ["-sent_at"]
    
    def __str__(self):
        return f"{self.phone} - {self.get_purpose_display()} - {self.code}"
    
    def is_expired(self):
        """检查是否已过期"""
        return timezone.now() > self.expires_at
    
    def mark_as_used(self):
        """标记为已使用"""
        self.is_used = True
        self.used_at = timezone.now()
        self.save(update_fields=['is_used', 'used_at'])


class AgentRelation(models.Model):
    """代理关系表"""
    
    user = models.OneToOneField(WebUser, on_delete=models.CASCADE, related_name='agent_relation', verbose_name="用户")
    parent_agent = models.ForeignKey(WebUser, on_delete=models.SET_NULL, null=True, blank=True, related_name='direct_invitees', verbose_name="直接邀请人")
    grandparent_agent = models.ForeignKey(WebUser, on_delete=models.SET_NULL, null=True, blank=True, related_name='indirect_invitees', verbose_name="二级邀请人")
    invite_code_used = models.CharField(max_length=12, blank=True, verbose_name="使用的邀请码")
    level = models.IntegerField(default=1, verbose_name="代理层级")  # 1=直接下级, 2=二级下级
    created_at = models.DateTimeField(default=timezone.now, verbose_name="建立时间")
    
    class Meta:
        verbose_name = "代理关系"
        verbose_name_plural = verbose_name
        db_table = "agent_relation"
    
    def __str__(self):
        return f"{self.user.phone} -> {self.parent_agent.phone if self.parent_agent else '无'}"


class CommissionRecord(models.Model):
    """返利记录表"""
    
    COMMISSION_TYPES = (
        ('level1', '一级返利'),  # 1/3
        ('level2', '二级返利'),  # 1/5
    )
    
    agent = models.ForeignKey(WebUser, on_delete=models.CASCADE, related_name='commission_records', verbose_name="代理人")
    invitee = models.ForeignKey(WebUser, on_delete=models.CASCADE, related_name='generated_commissions', verbose_name="被邀请人")
    order_user = models.ForeignKey(WebUser, on_delete=models.CASCADE, related_name='order_commissions', verbose_name="订单用户")
    commission_type = models.CharField(max_length=10, choices=COMMISSION_TYPES, verbose_name="返利类型")
    original_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="原始金额")
    commission_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="返利金额")
    commission_rate = models.DecimalField(max_digits=5, decimal_places=4, verbose_name="返利比例")
    order_id = models.CharField(max_length=100, blank=True, verbose_name="订单ID")
    is_paid = models.BooleanField(default=False, verbose_name="是否已发放")
    paid_at = models.DateTimeField(null=True, blank=True, verbose_name="发放时间")
    created_at = models.DateTimeField(default=timezone.now, verbose_name="创建时间")
    
    class Meta:
        verbose_name = "返利记录"
        verbose_name_plural = verbose_name
        db_table = "commission_record"
        ordering = ["-created_at"]
    
    def __str__(self):
        return f"{self.agent.phone} <- {self.invitee.phone} ({self.commission_amount})"


class CreditTransaction(models.Model):
    """积分交易记录"""
    
    TRANSACTION_TYPES = (
        ('initial', '初始赠送'),
        ('consume', '消费扣除'),
        ('purchase', '购买充值'),
        ('bonus', '奖励赠送'),
        ('refund', '退款返还'),
        ('commission', '代理返利'),
    )
    
    user = models.ForeignKey(WebUser, on_delete=models.CASCADE, related_name='credit_transactions', verbose_name="用户")
    type = models.CharField(max_length=20, choices=TRANSACTION_TYPES, verbose_name="交易类型")
    amount = models.IntegerField(verbose_name="积分数量")  # 正数为增加，负数为减少
    balance_before = models.IntegerField(verbose_name="交易前余额")
    balance_after = models.IntegerField(verbose_name="交易后余额")
    description = models.CharField(max_length=200, verbose_name="描述", null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now, verbose_name="创建时间")
    
    class Meta:
        verbose_name = "积分交易"
        verbose_name_plural = verbose_name
        db_table = "credit_transaction"
        ordering = ["-created_at"]
    
    def __str__(self):
        return f"{self.user.phone} - {self.get_type_display()} - {self.amount}"


class PackageConfig(models.Model):
    """套餐配置表"""

    name = models.CharField(max_length=50, verbose_name="套餐名称")
    package_key = models.CharField(max_length=20, unique=True, verbose_name="套餐标识")  # small, medium, large
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="价格(USD)")
    credits = models.IntegerField(verbose_name="积分数量")
    description = models.CharField(max_length=200, verbose_name="描述", null=True, blank=True)
    icon = models.CharField(max_length=50, verbose_name="图标", default="star")  # star, crown, rocket
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    sort_order = models.IntegerField(default=0, verbose_name="排序")
    created_at = models.DateTimeField(default=timezone.now, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "套餐配置"
        verbose_name_plural = verbose_name
        db_table = "package_config"
        ordering = ["sort_order", "price"]

    def __str__(self):
        return f"{self.name} - {self.price}USD/{self.credits}次"

    @property
    def price_per_credit(self):
        """每次价格"""
        if self.credits > 0:
            return round(float(self.price) / self.credits, 2)
        return 0


class CommissionConfig(models.Model):
    """返利配置表"""

    COMMISSION_TYPES = (
        ('level1', '一级返利'),
        ('level2', '二级返利'),
    )

    commission_type = models.CharField(max_length=10, choices=COMMISSION_TYPES, unique=True, verbose_name="返利类型")
    commission_rate = models.DecimalField(max_digits=5, decimal_places=4, verbose_name="返利比例")  # 0.3333 = 1/3, 0.2000 = 1/5
    description = models.CharField(max_length=200, verbose_name="描述", null=True, blank=True)
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    created_at = models.DateTimeField(default=timezone.now, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "返利配置"
        verbose_name_plural = verbose_name
        db_table = "commission_config"

    def __str__(self):
        return f"{self.get_commission_type_display()} - {self.commission_rate}"

    @classmethod
    def get_commission_rate(cls, commission_type):
        """获取返利比例"""
        try:
            config = cls.objects.get(commission_type=commission_type, is_active=True)
            return config.commission_rate
        except cls.DoesNotExist:
            # 默认返利比例
            default_rates = {
                'level1': 0.3333,  # 1/3
                'level2': 0.2000,  # 1/5
            }
            return default_rates.get(commission_type, 0)