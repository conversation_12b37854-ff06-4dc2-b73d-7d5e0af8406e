# This file may be used to create an environment using:
# $ conda create --name <env> --file <this file>
# platform: osx-arm64
# created-by: conda 25.3.0
annotated-types=0.7.0=pypi_0
anyio=4.9.0=pypi_0
asgiref=3.9.1=pypi_0
boto3=1.34.14=pypi_0
botocore=1.34.162=pypi_0
bzip2=1.0.8=h3422bc3_4
ca-certificates=2022.9.24=h4653dfc_0
certifi=2025.7.14=pypi_0
charset-normalizer=3.4.2=pypi_0
cos-python-sdk-v5=1.9.25=pypi_0
crcmod=1.7=pypi_0
dateparser=1.1.8=pypi_0
deprecation=2.1.0=pypi_0
django=4.2.7=pypi_0
django-cors-headers=4.3.0=pypi_0
django-storages=1.14.2=pypi_0
djangorestframework=3.14.0=pypi_0
djangorestframework-simplejwt=5.3.0=pypi_0
exceptiongroup=1.3.0=pypi_0
gotrue=2.12.3=pypi_0
gunicorn=21.2.0=pypi_0
h11=0.16.0=pypi_0
h2=4.2.0=pypi_0
hpack=4.1.0=pypi_0
httpcore=1.0.9=pypi_0
httpx=0.28.1=pypi_0
hyperframe=6.1.0=pypi_0
idna=3.10=pypi_0
jmespath=1.0.1=pypi_0
libffi=3.4.2=h3422bc3_5
libsqlite=3.40.0=h76d750c_0
libzlib=1.2.13=h03a7124_4
ncurses=6.3=h07bb92c_1
numpy=1.24.4=pypi_0
openssl=3.0.7=h03a7124_0
packaging=25.0=pypi_0
pandas=2.3.1=pypi_0
pandas-ta=0.3.14b0=pypi_0
pip=22.3.1=pyhd8ed1ab_0
postgrest=1.1.1=pypi_0
pycryptodome=3.19.0=pypi_0
pydantic=2.11.7=pypi_0
pydantic-core=2.33.2=pypi_0
pyjwt=2.10.1=pypi_0
pymysql=1.1.0=pypi_0
python=3.10.6=hae75cb6_0_cpython
python-dateutil=2.9.0.post0=pypi_0
python-dotenv=1.0.0=pypi_0
pytz=2025.2=pypi_0
readline=8.1.2=h46ed386_0
realtime=2.6.0=pypi_0
regex=2024.11.6=pypi_0
requests=2.31.0=pypi_0
s3transfer=0.10.4=pypi_0
setuptools=65.5.1=pyhd8ed1ab_0
six=1.17.0=pypi_0
sniffio=1.3.1=pypi_0
sqlparse=0.5.3=pypi_0
storage3=0.12.0=pypi_0
strenum=0.4.15=pypi_0
supabase=2.17.0=pypi_0
supafunc=0.10.1=pypi_0
tk=8.6.12=he1e0b03_0
typing-extensions=4.14.1=pypi_0
typing-inspection=0.4.1=pypi_0
tzdata=2025.2=pypi_0
tzlocal=5.3.1=pypi_0
urllib3=2.5.0=pypi_0
websockets=15.0.1=pypi_0
wheel=0.38.4=pyhd8ed1ab_0
xmltodict=0.14.2=pypi_0
xz=5.2.6=h57fd34a_0
