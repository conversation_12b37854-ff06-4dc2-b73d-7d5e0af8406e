#!/usr/bin/env python3
"""
AI分析预测模型 - 存储历史预测记录
"""
from django.db import models
from django.utils import timezone
import json


class PredictionRecord(models.Model):
    """AI预测历史记录模型"""
    
    # 基本信息
    symbol = models.CharField(max_length=20, verbose_name="交易对符号", db_index=True)
    interval = models.CharField(max_length=10, default="1m", verbose_name="时间间隔")
    prediction_timestamp = models.BigIntegerField(verbose_name="预测目标时间戳", db_index=True)
    prediction_datetime = models.DateTimeField(verbose_name="预测目标时间", db_index=True)
    created_at = models.DateTimeField(default=timezone.now, verbose_name="创建时间", db_index=True)
    
    # 市场数据
    current_price = models.DecimalField(max_digits=20, decimal_places=8, verbose_name="当前价格")
    support_level = models.DecimalField(max_digits=20, decimal_places=8, null=True, verbose_name="支撑位")
    resistance_level = models.DecimalField(max_digits=20, decimal_places=8, null=True, verbose_name="阻力位")
    
    # 技术指标（JSON存储）
    technical_indicators = models.JSONField(default=dict, verbose_name="技术指标数据")
    btc_reference = models.JSONField(default=dict, verbose_name="BTC参考数据")
    
    # AI预测结果
    analysis_summary = models.TextField(verbose_name="AI分析总结")
    prediction_distribution = models.JSONField(default=dict, verbose_name="多空预测分布")
    market_sentiment = models.JSONField(default=dict, verbose_name="市场情绪数据")
    confidence_level = models.CharField(max_length=10, default="中", verbose_name="置信度")
    
    # 预测状态
    prediction_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', '待处理'),
            ('processing', '处理中'),
            ('completed', '已完成'),
            ('failed', '失败'),
        ],
        default='pending',
        verbose_name="预测状态",
        db_index=True
    )
    
    # 元数据
    data_points_count = models.IntegerField(default=0, verbose_name="数据点数量")
    historical_years_count = models.IntegerField(default=0, verbose_name="历史年份数量") 
    processing_time_seconds = models.FloatField(null=True, verbose_name="处理耗时(秒)")
    error_message = models.TextField(blank=True, verbose_name="错误信息")
    
    # AI原始响应（用于调试）
    raw_ai_response = models.TextField(blank=True, verbose_name="AI原始响应")
    
    class Meta:
        db_table = 'ai_prediction_records'
        verbose_name = 'AI预测记录'
        verbose_name_plural = 'AI预测记录'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['symbol', '-created_at']),
            models.Index(fields=['prediction_datetime', 'symbol']),
            models.Index(fields=['prediction_status', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.symbol} - {self.prediction_datetime.strftime('%Y-%m-%d %H:%M')} - {self.prediction_status}"
    
    @property
    def is_bullish(self):
        """判断是否看涨倾向"""
        distribution = self.prediction_distribution
        bullish = distribution.get('看涨', 0) + distribution.get('非常看涨', 0)
        bearish = distribution.get('看跌', 0) + distribution.get('非常看跌', 0)
        return bullish > bearish
    
    @property
    def sentiment_score(self):
        """计算情绪得分 (-100到100)"""
        distribution = self.prediction_distribution
        score = (
            distribution.get('非常看涨', 0) * 2 +
            distribution.get('看涨', 0) * 1 +
            distribution.get('中性', 0) * 0 +
            distribution.get('看跌', 0) * (-1) +
            distribution.get('非常看跌', 0) * (-2)
        )
        return score
    
    def to_api_dict(self):
        """转换为API响应格式"""
        return {
            'id': self.id,
            'symbol': self.symbol,
            'prediction_timestamp': self.prediction_timestamp,
            'prediction_datetime': self.prediction_datetime.isoformat(),
            'created_at': self.created_at.isoformat(),
            'current_price': float(self.current_price),
            'support_resistance': {
                'support': float(self.support_level) if self.support_level else None,
                'resistance': float(self.resistance_level) if self.resistance_level else None,
            },
            'technical_indicators': self.technical_indicators,
            'btc_reference': self.btc_reference,
            'analysis_summary': self.analysis_summary,
            'prediction_distribution': self.prediction_distribution,
            'market_sentiment': self.market_sentiment,
            'confidence_level': self.confidence_level,
            'sentiment_score': self.sentiment_score,
            'is_bullish': self.is_bullish,
            'prediction_status': self.prediction_status,
            'processing_time_seconds': self.processing_time_seconds,
        }


class AutoPredictionTask(models.Model):
    """自动预测任务配置"""
    
    symbol = models.CharField(max_length=20, unique=True, verbose_name="交易对符号")
    interval = models.CharField(max_length=10, default="1m", verbose_name="时间间隔")
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    priority = models.IntegerField(default=1, verbose_name="优先级(1-10)", help_text="数字越小优先级越高")
    
    # 任务统计
    last_run_at = models.DateTimeField(null=True, blank=True, verbose_name="上次运行时间")
    next_run_at = models.DateTimeField(null=True, blank=True, verbose_name="下次运行时间")
    total_runs = models.IntegerField(default=0, verbose_name="总运行次数")
    success_runs = models.IntegerField(default=0, verbose_name="成功次数")
    failed_runs = models.IntegerField(default=0, verbose_name="失败次数")
    
    # 配置
    created_at = models.DateTimeField(default=timezone.now, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        db_table = 'ai_auto_prediction_tasks'
        verbose_name = '自动预测任务'
        verbose_name_plural = '自动预测任务'
        ordering = ['priority', 'symbol']
    
    def __str__(self):
        return f"{self.symbol} - {'启用' if self.is_active else '禁用'} - 优先级{self.priority}"
    
    @property
    def success_rate(self):
        """成功率"""
        if self.total_runs == 0:
            return 0
        return round((self.success_runs / self.total_runs) * 100, 1)
