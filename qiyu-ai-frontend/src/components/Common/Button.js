import React from 'react';
import { Button as AntButton } from 'antd';
import './Button.css';

const Button = ({ 
  children, 
  variant = 'primary', 
  size = 'medium',
  icon,
  loading = false,
  disabled = false,
  onClick,
  className = '',
  ...props 
}) => {
  const getButtonClass = () => {
    const baseClass = 'qiyu-button';
    const variantClass = `qiyu-button--${variant}`;
    const sizeClass = `qiyu-button--${size}`;
    
    return `${baseClass} ${variantClass} ${sizeClass} ${className}`.trim();
  };

  return (
    <AntButton
      className={getButtonClass()}
      icon={icon}
      loading={loading}
      disabled={disabled}
      onClick={onClick}
      size={size === 'small' ? 'small' : size === 'large' ? 'large' : 'middle'}
      {...props}
    >
      {children}
    </AntButton>
  );
};

export default Button;
