#!/usr/bin/env python3
"""
数据完整性检查脚本
检查COS中1分钟K线数据的时间连续性，识别缺失时段并生成修复建议
"""

import os
import sys
import django
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Set
from collections import defaultdict

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'finance_project.settings')
django.setup()

from crypto_api.services.cos_service import COSRealtimeService

class DataIntegrityChecker:
    """数据完整性检查器"""
    
    def __init__(self, debug=False):
        self.debug = debug
        self.cos_service = COSRealtimeService(debug=debug)
        
        # 支持的交易对和时间周期
        self.symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'DOGEUSDT', 'XRPUSDT']
        self.timeframes = ['1m']  # 重点检查1分钟数据
        
        # 检查结果
        self.integrity_report = {}
        
        print("🔍 数据完整性检查器初始化完成")
    
    def check_date_range(self, start_date: str, end_date: str = None) -> Dict:
        """
        检查指定日期范围的数据完整性
        
        Args:
            start_date: 开始日期 'YYYY-MM-DD'
            end_date: 结束日期 'YYYY-MM-DD'，默认为今天
        """
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d')
        
        print(f"📅 检查日期范围: {start_date} ~ {end_date}")
        
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        # 按日期遍历检查
        current_date = start_dt
        overall_report = {
            'check_period': {'start': start_date, 'end': end_date},
            'symbols': {},
            'summary': {
                'total_days': 0,
                'total_expected_minutes': 0,
                'total_actual_minutes': 0,
                'overall_coverage': 0.0,
                'critical_gaps': []
            }
        }
        
        while current_date <= end_dt:
            date_str = current_date.strftime('%Y-%m-%d')
            print(f"\n📊 检查日期: {date_str}")
            
            # 检查每个交易对
            for symbol in self.symbols:
                if symbol not in overall_report['symbols']:
                    overall_report['symbols'][symbol] = {
                        'daily_reports': {},
                        'total_coverage': 0.0,
                        'missing_periods': [],
                        'statistics': {
                            'total_expected': 0,
                            'total_actual': 0,
                            'missing_count': 0
                        }
                    }
                
                # 检查当日数据
                daily_report = self.check_daily_data(symbol, date_str)
                overall_report['symbols'][symbol]['daily_reports'][date_str] = daily_report
                
                # 累计统计
                stats = overall_report['symbols'][symbol]['statistics']
                stats['total_expected'] += daily_report['expected_minutes']
                stats['total_actual'] += daily_report['actual_minutes']
                stats['missing_count'] += len(daily_report['missing_periods'])
                
                # 收集缺失时段
                for period in daily_report['missing_periods']:
                    overall_report['symbols'][symbol]['missing_periods'].append({
                        'date': date_str,
                        'start_time': period['start_time'],
                        'end_time': period['end_time'],
                        'duration_minutes': period['duration_minutes']
                    })
            
            overall_report['summary']['total_days'] += 1
            current_date += timedelta(days=1)
        
        # 计算总体统计
        self.calculate_overall_statistics(overall_report)
        
        return overall_report
    
    def check_daily_data(self, symbol: str, date_str: str) -> Dict:
        """检查单日数据完整性"""
        print(f"  🔍 检查 {symbol} {date_str}")
        
        # 生成COS文件路径
        cos_key = self.cos_service.generate_cos_path(symbol, date_str, '1m')
        
        report = {
            'symbol': symbol,
            'date': date_str,
            'cos_key': cos_key,
            'file_exists': False,
            'expected_minutes': 1440,  # 一天1440分钟
            'actual_minutes': 0,
            'coverage_percent': 0.0,
            'missing_periods': [],
            'data_statistics': {},
            'timestamp_issues': []
        }
        
        try:
            # 检查文件是否存在
            if not self.cos_service.check_file_exists(cos_key):
                print(f"    ❌ 文件不存在: {cos_key}")
                report['missing_periods'] = [{
                    'start_time': f"{date_str} 00:00:00",
                    'end_time': f"{date_str} 23:59:00", 
                    'duration_minutes': 1440,
                    'reason': 'file_not_exists'
                }]
                return report
            
            report['file_exists'] = True
            
            # 下载并解析文件
            file_obj = self.cos_service.cos_client.get_object(
                Bucket=self.cos_service.bucket_name,
                Key=cos_key
            )
            file_content = file_obj['Body'].read().decode('utf-8')
            cleaned_content = self.cos_service.clean_chunked_data(file_content)
            data = json.loads(cleaned_content)
            
            klines = data.get('data', [])
            if not klines:
                print(f"    ❌ 文件无数据: {cos_key}")
                report['missing_periods'] = [{
                    'start_time': f"{date_str} 00:00:00",
                    'end_time': f"{date_str} 23:59:00",
                    'duration_minutes': 1440,
                    'reason': 'empty_data'
                }]
                return report
            
            # 解析时间戳并检查完整性
            minutes_set = self.extract_minute_timestamps(klines, date_str)
            report['actual_minutes'] = len(minutes_set)
            report['coverage_percent'] = (len(minutes_set) / 1440) * 100
            
            # 查找缺失时段
            report['missing_periods'] = self.find_missing_periods(minutes_set, date_str)
            
            # 数据统计
            report['data_statistics'] = {
                'total_klines': len(klines),
                'first_timestamp': klines[0][0] if klines else None,
                'last_timestamp': klines[-1][0] if klines else None,
                'timestamp_format': self.detect_timestamp_format(klines[0][0] if klines else None)
            }
            
            # 时间戳异常检查
            report['timestamp_issues'] = self.check_timestamp_issues(klines, date_str)
            
            if self.debug:
                print(f"    ✅ 数据覆盖率: {report['coverage_percent']:.1f}% ({report['actual_minutes']}/1440分钟)")
                if report['missing_periods']:
                    print(f"    ⚠️ 发现 {len(report['missing_periods'])} 个缺失时段")
            
        except Exception as e:
            print(f"    ❌ 检查异常: {e}")
            report['error'] = str(e)
            report['missing_periods'] = [{
                'start_time': f"{date_str} 00:00:00",
                'end_time': f"{date_str} 23:59:00",
                'duration_minutes': 1440,
                'reason': f'check_error: {str(e)}'
            }]
        
        return report
    
    def extract_minute_timestamps(self, klines: List, date_str: str) -> Set[str]:
        """提取分钟级时间戳集合"""
        minutes_set = set()
        
        for kline in klines:
            try:
                timestamp = kline[0]
                
                # 处理时间戳格式（微秒/毫秒）
                if timestamp > 1e15:  # 微秒时间戳
                    timestamp_seconds = timestamp / 1000000
                else:  # 毫秒时间戳
                    timestamp_seconds = timestamp / 1000
                
                # 转换为分钟字符串
                dt = datetime.fromtimestamp(timestamp_seconds)
                minute_str = dt.strftime('%H:%M')
                minutes_set.add(minute_str)
                
            except Exception as e:
                if self.debug:
                    print(f"      ⚠️ 时间戳解析异常: {timestamp} - {e}")
                continue
        
        return minutes_set
    
    def find_missing_periods(self, actual_minutes: Set[str], date_str: str) -> List[Dict]:
        """查找缺失的时间段"""
        missing_periods = []
        
        # 生成完整的分钟列表 (00:00 ~ 23:59)
        expected_minutes = set()
        for hour in range(24):
            for minute in range(60):
                expected_minutes.add(f"{hour:02d}:{minute:02d}")
        
        # 找出缺失的分钟
        missing_minutes = sorted(expected_minutes - actual_minutes)
        
        if not missing_minutes:
            return missing_periods
        
        # 将连续缺失的分钟合并为时间段
        current_start = missing_minutes[0]
        current_end = missing_minutes[0]
        
        for i in range(1, len(missing_minutes)):
            prev_time = datetime.strptime(missing_minutes[i-1], '%H:%M')
            curr_time = datetime.strptime(missing_minutes[i], '%H:%M')
            
            # 检查是否连续（考虑跨日情况）
            if (curr_time - prev_time).total_seconds() <= 60:
                current_end = missing_minutes[i]
            else:
                # 结束当前时段，开始新时段
                missing_periods.append({
                    'start_time': f"{date_str} {current_start}:00",
                    'end_time': f"{date_str} {current_end}:59",
                    'duration_minutes': self.calculate_duration(current_start, current_end),
                    'reason': 'data_missing'
                })
                current_start = missing_minutes[i]
                current_end = missing_minutes[i]
        
        # 添加最后一个时段
        missing_periods.append({
            'start_time': f"{date_str} {current_start}:00",
            'end_time': f"{date_str} {current_end}:59", 
            'duration_minutes': self.calculate_duration(current_start, current_end),
            'reason': 'data_missing'
        })
        
        return missing_periods
    
    def calculate_duration(self, start_time: str, end_time: str) -> int:
        """计算时间段持续分钟数"""
        start_dt = datetime.strptime(start_time, '%H:%M')
        end_dt = datetime.strptime(end_time, '%H:%M')
        return int((end_dt - start_dt).total_seconds() / 60) + 1
    
    def detect_timestamp_format(self, timestamp) -> str:
        """检测时间戳格式"""
        if timestamp is None:
            return 'unknown'
        
        if timestamp > 1e15:
            return 'microseconds'
        elif timestamp > 1e12:
            return 'milliseconds'
        elif timestamp > 1e9:
            return 'seconds'
        else:
            return 'unknown'
    
    def check_timestamp_issues(self, klines: List, date_str: str) -> List[Dict]:
        """检查时间戳异常"""
        issues = []
        
        if not klines:
            return issues
        
        date_dt = datetime.strptime(date_str, '%Y-%m-%d')
        next_date_dt = date_dt + timedelta(days=1)
        
        for i, kline in enumerate(klines):
            try:
                timestamp = kline[0]
                
                # 处理时间戳格式
                if timestamp > 1e15:
                    timestamp_seconds = timestamp / 1000000
                else:
                    timestamp_seconds = timestamp / 1000
                
                dt = datetime.fromtimestamp(timestamp_seconds)
                
                # 检查是否在正确的日期范围内
                if dt.date() != date_dt.date():
                    issues.append({
                        'index': i,
                        'timestamp': timestamp,
                        'converted_time': dt.isoformat(),
                        'issue': 'wrong_date',
                        'expected_date': date_str
                    })
                
                # 检查时间戳是否合理（不能是未来时间）
                if dt > datetime.now() + timedelta(hours=1):
                    issues.append({
                        'index': i,
                        'timestamp': timestamp,
                        'converted_time': dt.isoformat(),
                        'issue': 'future_timestamp'
                    })
                
            except Exception as e:
                issues.append({
                    'index': i,
                    'timestamp': timestamp,
                    'issue': 'conversion_error',
                    'error': str(e)
                })
        
        return issues
    
    def calculate_overall_statistics(self, report: Dict) -> None:
        """计算总体统计信息"""
        summary = report['summary']
        total_expected = 0
        total_actual = 0
        critical_gaps = []
        
        for symbol, symbol_data in report['symbols'].items():
            stats = symbol_data['statistics']
            total_expected += stats['total_expected']
            total_actual += stats['total_actual']
            
            # 计算该交易对的覆盖率
            if stats['total_expected'] > 0:
                symbol_data['total_coverage'] = (stats['total_actual'] / stats['total_expected']) * 100
            
            # 收集重要缺失时段（超过60分钟的）
            for period in symbol_data['missing_periods']:
                if period['duration_minutes'] >= 60:
                    critical_gaps.append({
                        'symbol': symbol,
                        'date': period['date'],
                        'start_time': period['start_time'],
                        'end_time': period['end_time'],
                        'duration_minutes': period['duration_minutes']
                    })
        
        summary['total_expected_minutes'] = total_expected
        summary['total_actual_minutes'] = total_actual
        summary['overall_coverage'] = (total_actual / total_expected * 100) if total_expected > 0 else 0
        summary['critical_gaps'] = sorted(critical_gaps, key=lambda x: x['duration_minutes'], reverse=True)
    
    def generate_report(self, integrity_data: Dict, output_file: str = None) -> str:
        """生成完整性检查报告"""
        report_lines = []
        
        # 报告头部
        report_lines.append("=" * 80)
        report_lines.append("🔍 数据完整性检查报告")
        report_lines.append("=" * 80)
        report_lines.append(f"📅 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"📊 检查期间: {integrity_data['check_period']['start']} ~ {integrity_data['check_period']['end']}")
        report_lines.append(f"⏱️  检查天数: {integrity_data['summary']['total_days']} 天")
        report_lines.append("")
        
        # 总体统计
        summary = integrity_data['summary']
        report_lines.append("📈 总体统计")
        report_lines.append("-" * 40)
        report_lines.append(f"期望分钟数: {summary['total_expected_minutes']:,}")
        report_lines.append(f"实际分钟数: {summary['total_actual_minutes']:,}")
        report_lines.append(f"总体覆盖率: {summary['overall_coverage']:.2f}%")
        report_lines.append(f"重要缺失: {len(summary['critical_gaps'])} 个时段")
        report_lines.append("")
        
        # 按交易对统计
        report_lines.append("📊 分交易对统计")
        report_lines.append("-" * 40)
        for symbol, data in integrity_data['symbols'].items():
            stats = data['statistics']
            coverage = data['total_coverage']
            missing_count = len(data['missing_periods'])
            
            report_lines.append(f"{symbol}:")
            report_lines.append(f"  覆盖率: {coverage:.2f}%")
            report_lines.append(f"  缺失时段: {missing_count} 个")
            report_lines.append(f"  实际/期望: {stats['total_actual']}/{stats['total_expected']}")
        report_lines.append("")
        
        # 重要缺失时段
        if summary['critical_gaps']:
            report_lines.append("🚨 重要缺失时段 (>60分钟)")
            report_lines.append("-" * 40)
            for gap in summary['critical_gaps'][:10]:  # 显示前10个
                report_lines.append(f"{gap['symbol']} {gap['date']}")
                report_lines.append(f"  时间: {gap['start_time']} ~ {gap['end_time']}")
                report_lines.append(f"  持续: {gap['duration_minutes']} 分钟")
                report_lines.append("")
        
        # 修复建议
        report_lines.append("🔧 修复建议")
        report_lines.append("-" * 40)
        repair_commands = self.generate_repair_commands(integrity_data)
        for cmd in repair_commands[:5]:  # 显示前5个修复命令
            report_lines.append(cmd)
        report_lines.append("")
        
        report_content = "\n".join(report_lines)
        
        # 保存到文件
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            print(f"📄 报告已保存: {output_file}")
        
        return report_content
    
    def generate_repair_commands(self, integrity_data: Dict) -> List[str]:
        """生成修复命令建议"""
        commands = []
        
        for symbol, data in integrity_data['symbols'].items():
            for period in data['missing_periods']:
                if period['duration_minutes'] >= 30:  # 只修复超过30分钟的缺失
                    start_time = period['start_time'].replace(' ', 'T')
                    end_time = period['end_time'].replace(' ', 'T')
                    
                    cmd = f"python3 manage.py repair_data_gaps --symbol={symbol} --start='{start_time}' --end='{end_time}'"
                    commands.append(cmd)
        
        return commands

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='数据完整性检查工具')
    parser.add_argument('--start-date', type=str, required=True, help='开始日期 YYYY-MM-DD')
    parser.add_argument('--end-date', type=str, help='结束日期 YYYY-MM-DD (默认今天)')
    parser.add_argument('--output', type=str, help='输出报告文件路径')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    
    args = parser.parse_args()
    
    # 创建检查器
    checker = DataIntegrityChecker(debug=args.debug)
    
    # 执行检查
    print("🚀 开始数据完整性检查...")
    integrity_data = checker.check_date_range(args.start_date, args.end_date)
    
    # 生成报告
    output_file = args.output or f"logs/integrity_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    report_content = checker.generate_report(integrity_data, output_file)
    
    # 显示摘要
    summary = integrity_data['summary']
    print(f"\n📊 检查完成!")
    print(f"总体覆盖率: {summary['overall_coverage']:.2f}%")
    print(f"重要缺失: {len(summary['critical_gaps'])} 个时段")
    print(f"报告文件: {output_file}")
    
    # 返回状态码
    if summary['overall_coverage'] < 95.0:
        print("⚠️ 数据完整性低于95%，建议检查和修复")
        sys.exit(1)
    else:
        print("✅ 数据完整性良好")
        sys.exit(0)

if __name__ == '__main__':
    main()