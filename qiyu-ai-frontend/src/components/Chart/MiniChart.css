/* 小图表样式 */
.mini-chart {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.mini-chart:hover {
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* 图表头部 */
.mini-chart-header {
  padding: 8px 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 30px;
}

.chart-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0; /* 允许文本截断 */
}

.chart-year {
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  font-weight: 500;
}

.chart-stats {
  color: #4CAF50;
  font-size: 11px;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60px;
}

.chart-stats.negative {
  color: #ef5350;
}

.chart-stats.positive {
  color: #4CAF50;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  z-index: 20;
}

.chart-indicator-btn {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.chart-indicator-btn:hover {
  color: #4CAF50;
}

.chart-settings {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.chart-settings:hover {
  color: rgba(255, 255, 255, 0.9);
}

/* 图表容器 */
.mini-chart-container {
  position: relative;
  width: 100%;
  height: calc(100% - 30px);
  min-height: 0; /* 移除最小高度限制，让容器自适应 */
}

.chart-canvas {
  width: 100%;
  height: 100%;
  min-height: 0; /* 移除最小高度限制，让图表完全自适应 */
  background: transparent;
}

/* 加载状态 */
.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  z-index: 10;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  gap: 8px;
}

/* 错误状态 */
.chart-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(239, 83, 80, 0.1);
  backdrop-filter: blur(5px);
  z-index: 10;
  color: #ef5350;
  font-size: 12px;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 图表网格线覆盖 */
.mini-chart .klinecharts-grid-line {
  stroke: rgba(255, 255, 255, 0.1) !important;
}

/* 图表坐标轴覆盖 */
.mini-chart .klinecharts-axis-line {
  stroke: rgba(255, 255, 255, 0.2) !important;
}

.mini-chart .klinecharts-axis-tick-text {
  fill: rgba(255, 255, 255, 0.6) !important;
  font-size: 10px !important;
}

/* 蜡烛图样式覆盖 */
.mini-chart .klinecharts-candle-up {
  fill: #26a69a !important;
  stroke: #26a69a !important;
}

.mini-chart .klinecharts-candle-down {
  fill: #ef5350 !important;
  stroke: #ef5350 !important;
}

/* 成交量样式覆盖 */
.mini-chart .klinecharts-volume-up {
  fill: rgba(38, 166, 154, 0.6) !important;
}

.mini-chart .klinecharts-volume-down {
  fill: rgba(239, 83, 80, 0.6) !important;
}

/* 指标控制面板 */
.indicator-panel {
  position: absolute;
  top: 30px;
  right: 0;
  background: rgba(26, 31, 43, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px;
  min-width: 200px;
  z-index: 100;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.indicator-group {
  margin-bottom: 12px;
}

.indicator-group:last-child {
  margin-bottom: 0;
}

.indicator-group-title {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 11px;
  font-weight: 500;
  margin-bottom: 6px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.indicator-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.indicator-btn {
  padding: 4px 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 35px;
  text-align: center;
}

.indicator-btn:hover {
  border-color: rgba(76, 175, 80, 0.5);
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.indicator-btn.active {
  border-color: #4CAF50;
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mini-chart {
    border-radius: 8px;
  }
  
  .mini-chart-header {
    padding: 6px 10px;
  }
  
  .chart-year {
    font-size: 11px;
  }
  
  .chart-settings, .chart-indicator-btn {
    font-size: 11px;
  }
  
  .indicator-panel {
    right: -10px;
    min-width: 180px;
    padding: 10px;
  }
  
  .indicator-btn {
    font-size: 9px;
    padding: 3px 6px;
    min-width: 30px;
  }
}
