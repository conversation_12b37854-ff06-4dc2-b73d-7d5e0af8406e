#!/bin/bash
# 应用优化后的Cron调度脚本

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$SCRIPT_DIR"
OPTIMIZED_CRONTAB="$PROJECT_DIR/optimized_crontab.txt"

echo "🔧 应用优化后的Cron调度..."
echo "📍 项目目录: $PROJECT_DIR"
echo "📋 优化调度文件: $OPTIMIZED_CRONTAB"

# 检查优化调度文件是否存在
if [ ! -f "$OPTIMIZED_CRONTAB" ]; then
    echo "❌ 错误: 未找到优化调度文件 $OPTIMIZED_CRONTAB"
    exit 1
fi

# 备份当前crontab
BACKUP_FILE="$PROJECT_DIR/crontab_backup_$(date +%Y%m%d_%H%M%S).txt"
if crontab -l > /dev/null 2>&1; then
    crontab -l > "$BACKUP_FILE"
    echo "✅ 已备份当前crontab到: $BACKUP_FILE"
else
    echo "ℹ️ 当前无crontab设置，跳过备份"
fi

# 显示新的调度内容
echo ""
echo "📋 新的Cron调度内容:"
echo "----------------------------------------"
cat "$OPTIMIZED_CRONTAB"
echo "----------------------------------------"

# 确认是否应用
read -p "🤔 是否要应用这个优化后的调度? (y/N): " -n 1 -r
echo

if [[ $REPLY =~ ^[Yy]$ ]]; then
    # 应用新的crontab
    crontab "$OPTIMIZED_CRONTAB"
    echo "✅ 已应用优化后的Cron调度"
    
    # 显示当前crontab
    echo ""
    echo "📋 当前生效的crontab:"
    echo "----------------------------------------"
    crontab -l
    echo "----------------------------------------"
    
    echo ""
    echo "🎯 调度优化要点:"
    echo "• K线数据(1m): 每5分钟更新 (1,6,11,16,21,26,31,36,41,46,51,56分)"
    echo "• AI预测任务: 每15分钟执行 (3,18,33,48分) - 15分钟间隔"
    echo "• 其他数据更新: 分散在不同时间点避免冲突"
    echo "• 系统资源: 通过错峰执行避免同时运行过多任务"
    
else
    echo "🚫 取消应用，保持当前调度不变"
fi

echo ""
echo "📊 其他有用的命令:"
echo "• 查看crontab: crontab -l"
echo "• 编辑crontab: crontab -e"
echo "• 查看预测日志: tail -f logs/predictions/cron_predictions.log"
echo "• 查看数据更新日志: tail -f logs/auto_update.log"
echo "• 手动测试AI预测: python3 manage.py cron_predictions --force"

echo ""
echo "✅ Cron优化脚本完成！"