/**
 * 模拟数据生成器
 * 用于生成K线数据，解决数据加载问题
 */

class MockDataGenerator {
  /**
   * 生成模拟K线数据
   * @param {string} symbol - 交易对
   * @param {number} year - 年份  
   * @param {string} interval - 时间间隔
   * @param {number} count - 数据点数量
   */
  static generateKlineData(symbol, year = 2024, interval = '1d', count = 365) {
    const data = [];
    const startPrice = this._getBasePrice(symbol);
    let currentPrice = startPrice;
    
    // 计算开始时间
    const startTime = new Date(year, 0, 1).getTime();
    const intervalMs = this._getIntervalMs(interval);
    
    for (let i = 0; i < count; i++) {
      const timestamp = startTime + i * intervalMs;
      
      // 生成价格变动 (模拟真实市场波动)
      const volatility = this._getVolatility(symbol);
      const trend = this._getTrendFactor(i, count);
      const randomFactor = (Math.random() - 0.5) * volatility;
      
      // 计算OHLC
      const open = currentPrice;
      const changePercent = (trend + randomFactor) / 100;
      const close = open * (1 + changePercent);
      
      // 生成高低价 (确保逻辑正确)
      const range = Math.abs(close - open) * (1 + Math.random());
      const high = Math.max(open, close) + range * Math.random() * 0.5;
      const low = Math.min(open, close) - range * Math.random() * 0.5;
      
      // 生成成交量 (基于价格变动)
      const baseVolume = this._getBaseVolume(symbol);
      const volumeMultiplier = 1 + Math.abs(changePercent) * 2 + Math.random() * 0.5;
      const volume = baseVolume * volumeMultiplier;
      
      data.push({
        timestamp,
        open: parseFloat(open.toFixed(4)),
        high: parseFloat(high.toFixed(4)),
        low: parseFloat(low.toFixed(4)),
        close: parseFloat(close.toFixed(4)),
        volume: parseInt(volume)
      });
      
      currentPrice = close;
    }
    
    return {
      symbol,
      interval,
      year,
      data,
      metadata: {
        count: data.length,
        period: `${year}-01-01 to ${year}-12-31`,
        generated: new Date().toISOString(),
        priceRange: {
          min: Math.min(...data.map(d => d.low)),
          max: Math.max(...data.map(d => d.high))
        }
      }
    };
  }
  
  /**
   * 生成当前年份的实时数据
   * @param {string} symbol - 交易对
   */
  static generateCurrentYearData(symbol) {
    const now = new Date();
    const year = now.getFullYear();
    const dayOfYear = Math.floor((now - new Date(year, 0, 0)) / (1000 * 60 * 60 * 24));
    
    return this.generateKlineData(symbol, year, '1d', dayOfYear);
  }
  
  /**
   * 生成概览数据 (多年数据采样)
   * @param {string} symbol - 交易对
   */
  static generateOverviewData(symbol) {
    const years = [2017, 2018, 2019, 2020, 2021, 2022, 2023, 2024];
    const data = [];
    let currentPrice = this._getBasePrice(symbol) * 0.1; // 历史低价开始
    
    years.forEach((year, yearIndex) => {
      // 每年取12个点 (月度数据)
      for (let month = 0; month < 12; month++) {
        const timestamp = new Date(year, month, 15).getTime(); // 每月15号
        
        // 模拟长期趋势
        const yearProgress = yearIndex / years.length;
        const monthProgress = month / 12;
        const longTermTrend = this._getLongTermTrend(symbol, yearProgress);
        const seasonalFactor = Math.sin(monthProgress * 2 * Math.PI) * 0.1;
        const randomFactor = (Math.random() - 0.5) * 0.2;
        
        const changePercent = longTermTrend + seasonalFactor + randomFactor;
        
        const open = currentPrice;
        const close = open * (1 + changePercent);
        const range = Math.abs(close - open) * (1 + Math.random());
        const high = Math.max(open, close) + range * 0.3;
        const low = Math.min(open, close) - range * 0.3;
        const volume = this._getBaseVolume(symbol) * (1 + Math.random());
        
        data.push({
          timestamp,
          open: parseFloat(open.toFixed(4)),
          high: parseFloat(high.toFixed(4)),
          low: parseFloat(low.toFixed(4)),
          close: parseFloat(close.toFixed(4)),
          volume: parseInt(volume)
        });
        
        currentPrice = close;
      }
    });
    
    return {
      symbol,
      interval: '1M',
      period: '8-year-overview',
      data,
      metadata: {
        count: data.length,
        years: years,
        generated: new Date().toISOString()
      }
    };
  }
  
  // 私有辅助方法
  static _getBasePrice(symbol) {
    const basePrices = {
      'BTCUSDT': 45000,
      'ETHUSDT': 2500,
      'LTCUSDT': 85,
      'XRPUSDT': 0.6,
      'DOGEUSDT': 0.08,
      'SOLUSDT': 95,
      'TRUMPUSDT': 35,
      'PIUSDT': 0.03
    };
    return basePrices[symbol] || 100;
  }
  
  static _getVolatility(symbol) {
    const volatilities = {
      'BTCUSDT': 3.5,
      'ETHUSDT': 4.2,
      'LTCUSDT': 5.1,
      'XRPUSDT': 6.8,
      'DOGEUSDT': 8.5,
      'SOLUSDT': 7.2,
      'TRUMPUSDT': 12.0,
      'PIUSDT': 15.0
    };
    return volatilities[symbol] || 5.0;
  }
  
  static _getBaseVolume(symbol) {
    const baseVolumes = {
      'BTCUSDT': 25000,
      'ETHUSDT': 45000,
      'LTCUSDT': 15000,
      'XRPUSDT': 180000,
      'DOGEUSDT': 350000,
      'SOLUSDT': 35000,
      'TRUMPUSDT': 85000,
      'PIUSDT': 120000
    };
    return baseVolumes[symbol] || 50000;
  }
  
  static _getTrendFactor(index, total) {
    // 模拟周期性趋势
    const progress = index / total;
    const cycleFactor = Math.sin(progress * 4 * Math.PI) * 0.3;
    const overallTrend = (Math.random() - 0.5) * 0.1;
    return cycleFactor + overallTrend;
  }
  
  static _getLongTermTrend(symbol, progress) {
    // 不同币种的长期趋势模拟
    const trendPatterns = {
      'BTCUSDT': Math.pow(progress, 0.7) * 0.5, // 指数增长放缓
      'ETHUSDT': Math.pow(progress, 0.8) * 0.4,
      'DOGEUSDT': progress * 0.2 + Math.sin(progress * 3 * Math.PI) * 0.3,
      'default': progress * 0.1
    };
    
    return trendPatterns[symbol] || trendPatterns['default'];
  }
  
  static _getIntervalMs(interval) {
    const intervals = {
      '1m': 60 * 1000,
      '5m': 5 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '1d': 24 * 60 * 60 * 1000,
      '1M': 30 * 24 * 60 * 60 * 1000
    };
    return intervals[interval] || intervals['1d'];
  }
}

export default MockDataGenerator;