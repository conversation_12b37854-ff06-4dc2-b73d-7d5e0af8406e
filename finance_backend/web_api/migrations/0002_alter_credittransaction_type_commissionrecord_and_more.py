# Generated by Django 4.2.7 on 2025-07-20 17:48

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('web_api', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='credittransaction',
            name='type',
            field=models.CharField(choices=[('initial', '初始赠送'), ('consume', '消费扣除'), ('purchase', '购买充值'), ('bonus', '奖励赠送'), ('refund', '退款返还'), ('commission', '代理返利')], max_length=20, verbose_name='交易类型'),
        ),
        migrations.CreateModel(
            name='CommissionRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('commission_type', models.CharField(choices=[('level1', '一级返利'), ('level2', '二级返利')], max_length=10, verbose_name='返利类型')),
                ('original_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='原始金额')),
                ('commission_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='返利金额')),
                ('commission_rate', models.DecimalField(decimal_places=4, max_digits=5, verbose_name='返利比例')),
                ('order_id', models.CharField(blank=True, max_length=100, verbose_name='订单ID')),
                ('is_paid', models.BooleanField(default=False, verbose_name='是否已发放')),
                ('paid_at', models.DateTimeField(blank=True, null=True, verbose_name='发放时间')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('agent', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='commission_records', to='web_api.webuser', verbose_name='代理人')),
                ('invitee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='generated_commissions', to='web_api.webuser', verbose_name='被邀请人')),
                ('order_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_commissions', to='web_api.webuser', verbose_name='订单用户')),
            ],
            options={
                'verbose_name': '返利记录',
                'verbose_name_plural': '返利记录',
                'db_table': 'commission_record',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AgentRelation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invite_code_used', models.CharField(blank=True, max_length=8, verbose_name='使用的邀请码')),
                ('level', models.IntegerField(default=1, verbose_name='代理层级')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='建立时间')),
                ('grandparent_agent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='indirect_invitees', to='web_api.webuser', verbose_name='二级邀请人')),
                ('parent_agent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='direct_invitees', to='web_api.webuser', verbose_name='直接邀请人')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='agent_relation', to='web_api.webuser', verbose_name='用户')),
            ],
            options={
                'verbose_name': '代理关系',
                'verbose_name_plural': '代理关系',
                'db_table': 'agent_relation',
            },
        ),
    ]
