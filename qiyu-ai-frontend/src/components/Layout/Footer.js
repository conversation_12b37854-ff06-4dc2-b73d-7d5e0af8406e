import React from 'react';
import { WechatOutlined } from '@ant-design/icons';
import './Footer.css';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const handleContactClick = (type) => {
    switch (type) {
      case 'wechat':
        console.log('打开微信联系方式');
        break;
      case 'email':
        window.location.href = 'mailto:<EMAIL>';
        break;
      default:
        break;
    }
  };

  return (
    <footer className="qiyu-footer">
      {/* 背景容器 */}
      <div className="footer-bg">
        {/* Logo 区域 */}
        <div className="footer-logo">
          <img
            src="/logo_with_text.png"
            alt="旗鱼AI"
            className="footer-logo-image"
          />
        </div>

        {/* 版权信息 */}
        <div className="footer-copyright">
          <span>Copyright© {currentYear} 旗鱼AI 版权所有</span>
        </div>

        {/* 公司链接 */}
        <div className="footer-company">
          <button className="footer-link" onClick={() => console.log('隐私政策')}>
            隐私政策
          </button>
          <button className="footer-link" onClick={() => console.log('用户协议')}>
            用户协议
          </button>
        </div>

        {/* 联系方式文字 */}
        <div className="footer-contact-text">
          需要帮助？欢迎随时联系我们：<EMAIL>
        </div>

        {/* 微信图标 */}
        <div className="footer-wechat">
          <div
            className="wechat-icon"
            onClick={() => handleContactClick('wechat')}
            title="微信联系"
          >
            <WechatOutlined />
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
