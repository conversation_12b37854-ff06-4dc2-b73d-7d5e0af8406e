{"messages": [{"role": "user", "content": "请分析BTC的技术指标，给出市场情绪判断和多空预测"}], "cryptoData": {"ticker": {"symbol": "BTCUSDT", "lastPrice": "95000", "priceChangePercent": "2.5", "volume": "50000", "timestamp": "1753878265297"}, "shortTermKlines": [[1735689600000, 94000, 94200, 93800, 93750, 1000], [1735689660000, 94100, 94300, 93900, 93900, 1010], [1735689720000, 94200, 94400, 94000, 94050, 1020], [1735689780000, 94300, 94500, 94100, 94200, 1030], [1735689840000, 94400, 94600, 94200, 94350, 1040], [1735689900000, 94500, 94700, 94300, 94500, 1050], [1735689960000, 94600, 94800, 94400, 94650, 1060], [1735690020000, 94700, 94900, 94500, 94800, 1070], [1735690080000, 94800, 95000, 94600, 94950, 1080], [1735690140000, 94900, 95100, 94700, 95100, 1090], [1735690200000, 95000, 95200, 94800, 94750, 1100], [1735690260000, 95100, 95300, 94900, 94900, 1110], [1735690320000, 95200, 95400, 95000, 95050, 1120], [1735690380000, 95300, 95500, 95100, 95200, 1130], [1735690440000, 95400, 95600, 95200, 95350, 1140], [1735690500000, 95500, 95700, 95300, 95500, 1150], [1735690560000, 95600, 95800, 95400, 95650, 1160], [1735690620000, 95700, 95900, 95500, 95800, 1170], [1735690680000, 95800, 96000, 95600, 95950, 1180], [1735690740000, 95900, 96100, 95700, 96100, 1190], [1735690800000, 94000, 94200, 93800, 93750, 1200], [1735690860000, 94100, 94300, 93900, 93900, 1210], [1735690920000, 94200, 94400, 94000, 94050, 1220], [1735690980000, 94300, 94500, 94100, 94200, 1230], [1735691040000, 94400, 94600, 94200, 94350, 1240], [1735691100000, 94500, 94700, 94300, 94500, 1250], [1735691160000, 94600, 94800, 94400, 94650, 1260], [1735691220000, 94700, 94900, 94500, 94800, 1270], [1735691280000, 94800, 95000, 94600, 94950, 1280], [1735691340000, 94900, 95100, 94700, 95100, 1290], [1735691400000, 95000, 95200, 94800, 94750, 1300], [1735691460000, 95100, 95300, 94900, 94900, 1310], [1735691520000, 95200, 95400, 95000, 95050, 1320], [1735691580000, 95300, 95500, 95100, 95200, 1330], [1735691640000, 95400, 95600, 95200, 95350, 1340], [1735691700000, 95500, 95700, 95300, 95500, 1350], [1735691760000, 95600, 95800, 95400, 95650, 1360], [1735691820000, 95700, 95900, 95500, 95800, 1370], [1735691880000, 95800, 96000, 95600, 95950, 1380], [1735691940000, 95900, 96100, 95700, 96100, 1390], [1735692000000, 94000, 94200, 93800, 93750, 1400], [1735692060000, 94100, 94300, 93900, 93900, 1410], [1735692120000, 94200, 94400, 94000, 94050, 1420], [1735692180000, 94300, 94500, 94100, 94200, 1430], [1735692240000, 94400, 94600, 94200, 94350, 1440], [1735692300000, 94500, 94700, 94300, 94500, 1450], [1735692360000, 94600, 94800, 94400, 94650, 1460], [1735692420000, 94700, 94900, 94500, 94800, 1470], [1735692480000, 94800, 95000, 94600, 94950, 1480], [1735692540000, 94900, 95100, 94700, 95100, 1490], [1735692600000, 95000, 95200, 94800, 94750, 1500], [1735692660000, 95100, 95300, 94900, 94900, 1510], [1735692720000, 95200, 95400, 95000, 95050, 1520], [1735692780000, 95300, 95500, 95100, 95200, 1530], [1735692840000, 95400, 95600, 95200, 95350, 1540], [1735692900000, 95500, 95700, 95300, 95500, 1550], [1735692960000, 95600, 95800, 95400, 95650, 1560], [1735693020000, 95700, 95900, 95500, 95800, 1570], [1735693080000, 95800, 96000, 95600, 95950, 1580], [1735693140000, 95900, 96100, 95700, 96100, 1590], [1735693200000, 94000, 94200, 93800, 93750, 1600], [1735693260000, 94100, 94300, 93900, 93900, 1610], [1735693320000, 94200, 94400, 94000, 94050, 1620], [1735693380000, 94300, 94500, 94100, 94200, 1630], [1735693440000, 94400, 94600, 94200, 94350, 1640], [1735693500000, 94500, 94700, 94300, 94500, 1650], [1735693560000, 94600, 94800, 94400, 94650, 1660], [1735693620000, 94700, 94900, 94500, 94800, 1670], [1735693680000, 94800, 95000, 94600, 94950, 1680], [1735693740000, 94900, 95100, 94700, 95100, 1690], [1735693800000, 95000, 95200, 94800, 94750, 1700], [1735693860000, 95100, 95300, 94900, 94900, 1710], [1735693920000, 95200, 95400, 95000, 95050, 1720], [1735693980000, 95300, 95500, 95100, 95200, 1730], [1735694040000, 95400, 95600, 95200, 95350, 1740], [1735694100000, 95500, 95700, 95300, 95500, 1750], [1735694160000, 95600, 95800, 95400, 95650, 1760], [1735694220000, 95700, 95900, 95500, 95800, 1770], [1735694280000, 95800, 96000, 95600, 95950, 1780], [1735694340000, 95900, 96100, 95700, 96100, 1790], [1735694400000, 94000, 94200, 93800, 93750, 1800], [1735694460000, 94100, 94300, 93900, 93900, 1810], [1735694520000, 94200, 94400, 94000, 94050, 1820], [1735694580000, 94300, 94500, 94100, 94200, 1830], [1735694640000, 94400, 94600, 94200, 94350, 1840], [1735694700000, 94500, 94700, 94300, 94500, 1850], [1735694760000, 94600, 94800, 94400, 94650, 1860], [1735694820000, 94700, 94900, 94500, 94800, 1870], [1735694880000, 94800, 95000, 94600, 94950, 1880], [1735694940000, 94900, 95100, 94700, 95100, 1890], [1735695000000, 95000, 95200, 94800, 94750, 1900], [1735695060000, 95100, 95300, 94900, 94900, 1910], [1735695120000, 95200, 95400, 95000, 95050, 1920], [1735695180000, 95300, 95500, 95100, 95200, 1930], [1735695240000, 95400, 95600, 95200, 95350, 1940], [1735695300000, 95500, 95700, 95300, 95500, 1950], [1735695360000, 95600, 95800, 95400, 95650, 1960], [1735695420000, 95700, 95900, 95500, 95800, 1970], [1735695480000, 95800, 96000, 95600, 95950, 1980], [1735695540000, 95900, 96100, 95700, 96100, 1990]], "dailyKlines": [[1735689600000, 91500, 92500, 90500, 90900, 50000], [1735776000000, 92000, 93000, 91000, 91600, 51000], [1735862400000, 92500, 93500, 91500, 92300, 52000], [1735948800000, 93000, 94000, 92000, 93000, 53000], [1736035200000, 93500, 94500, 92500, 93700, 54000], [1736121600000, 94000, 95000, 93000, 94400, 55000], [1736208000000, 94500, 95500, 93500, 95100, 56000], [1736294400000, 95000, 96000, 94000, 94400, 57000], [1736380800000, 95500, 96500, 94500, 95100, 58000], [1736467200000, 96000, 97000, 95000, 95800, 59000], [1736553600000, 96500, 97500, 95500, 96500, 60000], [1736640000000, 97000, 98000, 96000, 97200, 61000], [1736726400000, 97500, 98500, 96500, 97900, 62000], [1736812800000, 98000, 99000, 97000, 98600, 63000], [1736899200000, 98500, 99500, 97500, 97900, 64000], [1736985600000, 91500, 92500, 90500, 91100, 65000], [1737072000000, 92000, 93000, 91000, 91800, 66000], [1737158400000, 92500, 93500, 91500, 92500, 67000], [1737244800000, 93000, 94000, 92000, 93200, 68000], [1737331200000, 93500, 94500, 92500, 93900, 69000], [1737417600000, 94000, 95000, 93000, 94600, 70000], [1737504000000, 94500, 95500, 93500, 93900, 71000], [1737590400000, 95000, 96000, 94000, 94600, 72000], [1737676800000, 95500, 96500, 94500, 95300, 73000], [1737763200000, 96000, 97000, 95000, 96000, 74000], [1737849600000, 96500, 97500, 95500, 96700, 75000], [1737936000000, 97000, 98000, 96000, 97400, 76000], [1738022400000, 97500, 98500, 96500, 98100, 77000], [1738108800000, 98000, 99000, 97000, 97400, 78000], [1738195200000, 98500, 99500, 97500, 98100, 79000]]}}