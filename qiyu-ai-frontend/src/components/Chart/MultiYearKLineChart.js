import React, { useState, useEffect, useCallback } from 'react'
import { Tabs, Card, Space, Button, Radio } from 'antd'
import { ReloadOutlined, FullscreenOutlined, ClockCircleOutlined } from '@ant-design/icons'
import EnhancedKLineChart from './EnhancedKLineChart'
import cryptoDataService from '../../services/CryptoDataService'
import './MultiYearKLineChart.css'

const { TabPane } = Tabs

const MultiYearKLineChart = ({
  symbol = 'BTCUSDT',
  years = ['2024', '2023'],
  enableRealTime = false,
  onBinanceConnect,
  useGridLayout = true // 新增网格布局选项
}) => {
  const [activeYear, setActiveYear] = useState(years[0])
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [realTimeConnected, setRealTimeConnected] = useState(false)
  const [currentTimeframe, setCurrentTimeframe] = useState('1m') // 当前时间周期

  // 使用useCallback优化onDataUpdate回调，避免重复渲染
  const handleDataUpdate = useCallback((year) => {
    return (data) => {
      console.log(`${year}年数据更新:`, data.length)
    }
  }, []) // 空依赖数组，确保函数引用稳定

  // 实时更新连接（模拟Binance WebSocket）
  const connectRealTime = async () => {
    if (!enableRealTime) return

    try {
      console.log('🔌 连接Binance实时数据...')
      // 这里可以集成真实的Binance WebSocket
      // const ws = new WebSocket('wss://stream.binance.com:9443/ws/btcusdt@kline_1m')
      
      // 模拟连接成功
      setTimeout(() => {
        setRealTimeConnected(true)
        console.log('✅ Binance实时数据连接成功')
        if (onBinanceConnect) {
          onBinanceConnect()
        }
      }, 1000)
      
    } catch (error) {
      console.error('❌ Binance连接失败:', error)
    }
  }

  // 组件挂载时连接实时数据
  useEffect(() => {
    if (enableRealTime) {
      // 延迟连接，让图表先渲染完成
      const timer = setTimeout(() => {
        connectRealTime()
      }, 2000)
      
      return () => clearTimeout(timer)
    }
  }, [enableRealTime])

  // 全屏切换
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  // 时间周期切换
  const handleTimeframeChange = (e) => {
    const newTimeframe = e.target.value
    console.log(`🕐 切换时间周期: ${currentTimeframe} -> ${newTimeframe}`)
    setCurrentTimeframe(newTimeframe)
    
    // 清除数据缓存，强制重新加载
    cryptoDataService.clearCache()
  }

  // 刷新数据
  const refreshData = () => {
    cryptoDataService.clearCache()
    window.location.reload()
  }

  return (
    <div className={`multi-year-kline-chart ${isFullscreen ? 'fullscreen' : ''}`}>
      {/* 全局工具栏 */}
      <div className="global-toolbar">
        <div className="toolbar-left">
          <h3 className="chart-title">{symbol} K线图表</h3>
          {realTimeConnected && (
            <span className="realtime-indicator">
              🟢 实时数据已连接
            </span>
          )}
        </div>

        {/* 时间周期选择器 */}
        <div className="toolbar-center">
          <Space align="center">
            <ClockCircleOutlined style={{ color: '#999' }} />
            <span style={{ color: '#999', fontSize: '12px' }}>时间周期:</span>
            <Radio.Group 
              value={currentTimeframe} 
              onChange={handleTimeframeChange}
              size="small"
              buttonStyle="solid"
            >
              <Radio.Button value="1m">1分钟</Radio.Button>
              <Radio.Button value="15m">15分钟</Radio.Button>
              <Radio.Button value="1h">1小时</Radio.Button>
              <Radio.Button value="4h">4小时</Radio.Button>
              <Radio.Button value="1d">1天</Radio.Button>
              <Radio.Button value="1mo">1月</Radio.Button>
            </Radio.Group>
          </Space>
        </div>

        <div className="toolbar-right">
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={refreshData}
              type="default"
              size="small"
            >
              刷新
            </Button>
            <Button 
              icon={<FullscreenOutlined />} 
              onClick={toggleFullscreen}
              type="default"
              size="small"
            >
              {isFullscreen ? '退出全屏' : '全屏'}
            </Button>
          </Space>
        </div>
      </div>

      {/* 图表容器 - 支持网格布局 */}
      <div className="charts-container">
        {useGridLayout && years.length <= 2 ? (
          // 2x2网格布局
          <div className="charts-grid-layout">
            {years.map((year) => (
              <Card 
                key={year}
                className="grid-chart-card"
                title={`${year}年`}
                styles={{ body: { padding: 0 } }}
              >
                <EnhancedKLineChart
                  symbol={symbol}
                  year={year}
                  timeframe={currentTimeframe}
                  width="100%"
                  height={isFullscreen ? 'calc(50vh - 80px)' : '400px'}
                  showMACD={year === new Date().getFullYear().toString()}
                  enableRealTime={enableRealTime && year === new Date().getFullYear().toString()}
                  onDataUpdate={handleDataUpdate(year)}
                />
              </Card>
            ))}
          </div>
        ) : (
          // 标签页布局
          <Tabs
            activeKey={activeYear}
            onChange={setActiveYear}
            type="card"
            size="small"
            className="year-tabs"
          >
            {years.map((year) => (
              <TabPane tab={`${year}年`} key={year}>
                <Card 
                  className="chart-card"
                  styles={{ body: { padding: 0 } }}
                >
                  <EnhancedKLineChart
                    symbol={symbol}
                    year={year}
                    timeframe={currentTimeframe}
                    width="100%"
                    height={isFullscreen ? 'calc(70vh - 120px)' : '300px'}
                    showMACD={year === new Date().getFullYear().toString()}
                    enableRealTime={enableRealTime && year === new Date().getFullYear().toString()}
                    onDataUpdate={handleDataUpdate(year)}
                  />
                </Card>
              </TabPane>
            ))}
          </Tabs>
        )}
      </div>
    </div>
  )
}

export default MultiYearKLineChart
