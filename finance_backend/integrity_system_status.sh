#!/bin/bash

# 数据完整性监控系统状态总览脚本
# 显示当前完整性监控系统的运行状态和统计信息

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "=================================================================================="
echo "🔍 数据完整性监控系统 - 状态总览"
echo "=================================================================================="
echo "📅 检查时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""

# 1. 检查定时任务状态
echo "⏰ 定时任务状态"
echo "----------------------------------------"
CRON_INTEGRITY=$(crontab -l 2>/dev/null | grep -c "integrity")
if [ $CRON_INTEGRITY -gt 0 ]; then
    echo "✅ 数据完整性检查定时任务已配置 ($CRON_INTEGRITY 个任务)"
    crontab -l 2>/dev/null | grep "integrity" | sed 's/^/  /'
else
    echo "❌ 未配置数据完整性检查定时任务"
    echo "💡 运行: ./setup_integrity_cron.sh 进行配置"
fi
echo ""

# 2. 检查收集器进程状态
echo "🔄 数据收集器状态"
echo "----------------------------------------"
COLLECTOR_PID=$(cat multi_collector.pid 2>/dev/null)
if [ -n "$COLLECTOR_PID" ] && ps -p "$COLLECTOR_PID" > /dev/null 2>&1; then
    echo "✅ 多收集器进程运行正常 (PID: $COLLECTOR_PID)"
    COLLECTOR_CMD=$(ps -p "$COLLECTOR_PID" -o cmd --no-headers)
    echo "  命令: $COLLECTOR_CMD"
else
    echo "❌ 多收集器进程异常"
    echo "💡 检查: ps aux | grep collect_multi_data"
fi

BTC_COLLECTOR=$(ps aux | grep "collect_realtime_data.*BTCUSDT" | grep -v grep | wc -l)
if [ $BTC_COLLECTOR -gt 0 ]; then
    echo "✅ BTCUSDT单收集器运行正常"
else
    echo "⚠️ BTCUSDT单收集器未运行"
fi
echo ""

# 3. 检查最新完整性报告
echo "📊 最新完整性检查结果"
echo "----------------------------------------"
LATEST_REPORT=$(find logs/integrity_reports/ -name "*.txt" -type f -exec ls -t {} + 2>/dev/null | head -1)

if [ -n "$LATEST_REPORT" ]; then
    REPORT_TIME=$(stat -c %y "$LATEST_REPORT" | cut -d'.' -f1)
    echo "📄 最新报告: $(basename "$LATEST_REPORT")"
    echo "🕐 生成时间: $REPORT_TIME"
    
    # 提取关键统计信息
    if [ -f "$LATEST_REPORT" ]; then
        COVERAGE=$(grep "总体覆盖率" "$LATEST_REPORT" | grep -o '[0-9.]*%' | head -1)
        CRITICAL_GAPS=$(grep "重要缺失" "$LATEST_REPORT" | grep -o '[0-9]*' | head -1)
        CHECK_PERIOD=$(grep "检查期间" "$LATEST_REPORT" | cut -d':' -f2 | xargs)
        
        echo "📈 覆盖率: ${COVERAGE:-未知}"
        echo "🚨 重要缺失: ${CRITICAL_GAPS:-0} 个时段"
        echo "📅 检查期间: $CHECK_PERIOD"
        
        # 根据覆盖率给出建议
        if [ -n "$COVERAGE" ]; then
            COVERAGE_NUM=$(echo "$COVERAGE" | sed 's/%//')
            if (( $(echo "$COVERAGE_NUM < 80" | bc -l) )); then
                echo "⚠️ 警告: 数据完整性不足，需要立即修复"
            elif (( $(echo "$COVERAGE_NUM < 95" | bc -l) )); then
                echo "💡 建议: 数据完整性良好，但仍有改进空间"
            else
                echo "🎉 优秀: 数据完整性良好"
            fi
        fi
    fi
else
    echo "❌ 未找到完整性检查报告"
    echo "💡 运行: ./quick_integrity_check.sh 生成报告"
fi
echo ""

# 4. 检查日志文件状态
echo "📝 日志文件状态"
echo "----------------------------------------"
declare -A log_files=(
    ["快速检查日志"]="logs/integrity_quick.log"
    ["收集器监控日志"]="logs/monitor.log"
    ["多收集器日志"]="logs/multi_collector_new.log"
    ["自动修复日志"]="logs/auto_repair.log"
)

for desc in "${!log_files[@]}"; do
    log_file="${log_files[$desc]}"
    if [ -f "$log_file" ]; then
        file_size=$(du -h "$log_file" | cut -f1)
        last_modified=$(stat -c %y "$log_file" | cut -d'.' -f1)
        echo "✅ $desc: $file_size (最后更新: $last_modified)"
    else
        echo "❌ $desc: 文件不存在"
    fi
done
echo ""

# 5. 磁盘空间检查
echo "💾 存储空间状态"
echo "----------------------------------------"
DISK_INFO=$(df -h . | tail -1)
DISK_USAGE=$(echo "$DISK_INFO" | awk '{print $5}' | sed 's/%//')
AVAILABLE=$(echo "$DISK_INFO" | awk '{print $4}')

echo "📊 当前目录磁盘使用率: ${DISK_USAGE}%"
echo "💿 可用空间: $AVAILABLE"

if [ "$DISK_USAGE" -gt 90 ]; then
    echo "⚠️ 警告: 磁盘使用率过高，建议清理日志文件"
elif [ "$DISK_USAGE" -gt 80 ]; then
    echo "💡 提醒: 磁盘使用率较高，注意监控"
else
    echo "✅ 磁盘空间充足"
fi
echo ""

# 6. 快捷操作建议
echo "🛠️ 快捷操作命令"
echo "----------------------------------------"
echo "检查完整性:     ./quick_integrity_check.sh"
echo "详细检查:       python3 check_data_integrity.py --start-date=\$(date -d '3 days ago' '+%Y-%m-%d')"
echo "自动修复:       ./auto_repair_gaps.sh"
echo "监控日志:       tail -f logs/integrity_quick.log"
echo "收集器状态:     ./monitor_collector.sh"
echo "配置定时任务:   ./setup_integrity_cron.sh"
echo ""

# 7. 系统健康评分
echo "🏥 系统健康评分"
echo "----------------------------------------"
HEALTH_SCORE=100

# 定时任务检查 (20分)
if [ $CRON_INTEGRITY -eq 0 ]; then
    HEALTH_SCORE=$((HEALTH_SCORE - 20))
fi

# 收集器进程检查 (30分)
if [ -z "$COLLECTOR_PID" ] || ! ps -p "$COLLECTOR_PID" > /dev/null 2>&1; then
    HEALTH_SCORE=$((HEALTH_SCORE - 30))
fi

# 数据完整性检查 (40分)
if [ -n "$COVERAGE" ]; then
    COVERAGE_NUM=$(echo "$COVERAGE" | sed 's/%//')
    if (( $(echo "$COVERAGE_NUM < 60" | bc -l) )); then
        HEALTH_SCORE=$((HEALTH_SCORE - 40))
    elif (( $(echo "$COVERAGE_NUM < 80" | bc -l) )); then
        HEALTH_SCORE=$((HEALTH_SCORE - 25))
    elif (( $(echo "$COVERAGE_NUM < 95" | bc -l) )); then
        HEALTH_SCORE=$((HEALTH_SCORE - 10))
    fi
else
    HEALTH_SCORE=$((HEALTH_SCORE - 20))
fi

# 磁盘空间检查 (10分)
if [ "$DISK_USAGE" -gt 90 ]; then
    HEALTH_SCORE=$((HEALTH_SCORE - 10))
elif [ "$DISK_USAGE" -gt 80 ]; then
    HEALTH_SCORE=$((HEALTH_SCORE - 5))
fi

echo "📊 系统健康评分: $HEALTH_SCORE/100"

if [ $HEALTH_SCORE -ge 90 ]; then
    echo "🎉 系统状态优秀！"
elif [ $HEALTH_SCORE -ge 70 ]; then
    echo "👍 系统状态良好"
elif [ $HEALTH_SCORE -ge 50 ]; then
    echo "⚠️ 系统状态一般，需要关注"
else
    echo "🚨 系统状态较差，需要立即修复！"
fi

echo ""
echo "================================================================================"