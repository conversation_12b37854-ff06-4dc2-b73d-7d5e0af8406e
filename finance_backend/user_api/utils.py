import json
import base64
import requests
import logging
from Crypto.Cipher import AES
from django.conf import settings

logger = logging.getLogger(__name__)

class WechatAPI:
    """微信API工具类"""
    
    @staticmethod
    def code2session(code):
        """
        使用code获取微信openid和session_key
        
        参考文档：https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/login/auth.code2Session.html
        """
        app_id = settings.WECHAT_CONFIG.get('APP_ID')
        app_secret = settings.WECHAT_CONFIG.get('APP_SECRET')
        
        if not app_id or not app_secret:
            logger.error("微信小程序配置缺失")
            return None
        
        url = f"https://api.weixin.qq.com/sns/jscode2session?appid={app_id}&secret={app_secret}&js_code={code}&grant_type=authorization_code"
        
        try:
            response = requests.get(url)
            result = response.json()
            
            if 'errcode' in result and result['errcode'] != 0:
                logger.error(f"微信code2session请求失败: {result}")
                return None
            
            return {
                'openid': result.get('openid'),
                'session_key': result.get('session_key'),
                'unionid': result.get('unionid')
            }
        except Exception as e:
            logger.error(f"微信code2session请求异常: {e}")
            return None
    
    @staticmethod
    def decrypt_phone_number(session_key, encrypted_data, iv):
        """
        解密微信获取的手机号数据
        
        参考文档：https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/getPhoneNumber.html
        """
        try:
            # Base64解码
            session_key = base64.b64decode(session_key)
            encrypted_data = base64.b64decode(encrypted_data)
            iv = base64.b64decode(iv)
            
            # 使用AES-128-CBC解密
            cipher = AES.new(session_key, AES.MODE_CBC, iv)
            decrypted = cipher.decrypt(encrypted_data)
            
            # 去除填充
            pad = decrypted[-1]
            if isinstance(pad, int):
                pad_size = pad
            else:
                pad_size = ord(pad)
            
            decrypted = decrypted[:-pad_size]
            
            # 解析JSON
            decrypted_data = json.loads(decrypted)
            
            # 返回手机号
            return {
                'phone_number': decrypted_data.get('phoneNumber'),
                'pure_phone_number': decrypted_data.get('purePhoneNumber'),
                'country_code': decrypted_data.get('countryCode')
            }
        except Exception as e:
            logger.error(f"手机号解密失败: {e}")
            return None 
    @staticmethod
    def get_phone_number_by_code(access_token, code):
        """
        使用新版API通过code获取手机号（推荐方式，基础库2.21.2+）
        
        参考文档：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-info/phone-number/getPhoneNumber.html
        """
        url = f"https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token={access_token}"
        
        try:
            response = requests.post(url, json={'code': code})
            result = response.json()
            
            if 'errcode' in result and result['errcode'] != 0:
                logger.error(f"微信获取手机号请求失败: {result}")
                return None
            
            phone_info = result.get('phone_info', {})
            return {
                'phone_number': phone_info.get('phoneNumber'),
                'pure_phone_number': phone_info.get('purePhoneNumber'),
                'country_code': phone_info.get('countryCode')
            }
        except Exception as e:
            logger.error(f"微信获取手机号请求异常: {e}")
            return None
    
    @staticmethod
    def get_access_token():
        """
        获取微信小程序access_token
        """
        app_id = settings.WECHAT_CONFIG.get('APP_ID')
        app_secret = settings.WECHAT_CONFIG.get('APP_SECRET')
        
        if not app_id or not app_secret:
            logger.error("微信小程序配置缺失")
            return None
        
        url = f"https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={app_id}&secret={app_secret}"
        
        try:
            response = requests.get(url)
            result = response.json()
            
            if 'errcode' in result and result['errcode'] != 0:
                logger.error(f"获取access_token失败: {result}")
                return None
            
            return result.get('access_token')
        except Exception as e:
            logger.error(f"获取access_token异常: {e}")
            return None
