import React from 'react';
import { Progress } from 'antd';

const SentimentChart = ({ sentimentData, title = "市场情绪" }) => {
  if (!sentimentData || Object.keys(sentimentData).length === 0) {
    return (
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center', 
        height: '100%',
        flexDirection: 'column',
        color: '#999'
      }}>
        <div style={{ fontSize: 24, marginBottom: 8 }}>📈</div>
        <div style={{ fontSize: '12px' }}>暂无情绪数据</div>
      </div>
    );
  }

  const renderSentimentItem = (label, value, color) => {
    // 使用渐变色，从#016A86到#447A53
    const gradientColor = {
      '0%': '#016A86',
      '100%': '#447A53'
    };
    
    return (
      <div style={{ marginBottom: 2 }}>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          marginBottom: 2,
          fontSize: 11,
        }}>
          <span style={{ color: '#fff', fontWeight: '500' }}>{label} {value}%</span>
        </div>
        <Progress 
          percent={value} 
          showInfo={false}
          strokeColor={gradientColor}
          trailColor="rgba(255,255,255,0.1)"
          size="small"
          strokeWidth={6}
        />
      </div>
    );
  };

  return (
    <div style={{ 
      width: '100%', 
      height: '100%', 
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center'
    }}>
      {/* 垂直排列的进度条 */}
      <div style={{ 
        display: 'flex', 
        flexDirection: 'column', 
        gap: '2px',
        width: '95%',
        padding: '0 8px'
      }}>
        {sentimentData.BTC_多头占比 != null && sentimentData.BTC_多头占比 > 0 && 
          renderSentimentItem(
            "BTC多头", 
            sentimentData.BTC_多头占比
          )
        }
        
        {sentimentData.目标币种_多头占比 != null && sentimentData.目标币种_多头占比 > 0 && 
          renderSentimentItem(
            "币种多头", 
            sentimentData.目标币种_多头占比
          )
        }
        
        {/* Additional sentiment data */}
        {Object.entries(sentimentData).map(([key, value]) => {
          if (key !== 'BTC_多头占比' && key !== '目标币种_多头占比' && value != null && value > 0) {
            return (
              <div key={key}>
                {renderSentimentItem(
                  key.replace('_', ' '), 
                  value
                )}
              </div>
            );
          }
          return null;
        })}
      </div>
    </div>
  );
};

export default SentimentChart;