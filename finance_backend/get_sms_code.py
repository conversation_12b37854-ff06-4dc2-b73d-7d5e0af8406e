#!/usr/bin/env python3
"""
查看SMS验证码的便捷脚本
使用方法: python3 get_sms_code.py [手机号]
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'finance_project.settings')
django.setup()

from web_api.models import SMSRecord
from django.core.cache import cache
from django.utils import timezone
from datetime import timedelta

def get_sms_code(phone=None):
    """获取SMS验证码"""
    
    if phone:
        print(f"=== 查询手机号 {phone} 的验证码 ===")
        
        # 1. 检查缓存
        cache_key = f'sms_code_{phone}'
        cached_code = cache.get(cache_key)
        if cached_code:
            print(f"✅ 缓存中的验证码: {cached_code}")
        else:
            print("❌ 缓存中没有找到验证码（可能已过期）")
        
        # 2. 检查数据库记录
        recent_time = timezone.now() - timedelta(minutes=5)
        recent_sms = SMSRecord.objects.filter(
            phone=phone, 
            sent_at__gte=recent_time
        ).order_by('-sent_at').first()
        
        if recent_sms:
            print(f"✅ 数据库中的验证码: {recent_sms.code}")
            print(f"   发送时间: {recent_sms.sent_at}")
            print(f"   是否已使用: {recent_sms.is_used}")
        else:
            print("❌ 数据库中没有找到最近5分钟的验证码")
    
    else:
        print("=== 最近5分钟的所有SMS验证码 ===")
        recent_time = timezone.now() - timedelta(minutes=5)
        recent_sms = SMSRecord.objects.filter(sent_at__gte=recent_time).order_by('-sent_at')
        
        if recent_sms:
            for sms in recent_sms:
                print(f"📱 {sms.phone}: {sms.code} ({sms.sent_at})")
        else:
            print("❌ 没有找到最近5分钟的SMS记录")

if __name__ == "__main__":
    phone = sys.argv[1] if len(sys.argv) > 1 else None
    get_sms_code(phone)