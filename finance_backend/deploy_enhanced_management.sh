#!/bin/bash
# 部署增强的进程管理方案

set -e

PROJECT_DIR="/home/<USER>/qiyuai-web/finance_backend"
cd "$PROJECT_DIR"

echo "🚀 部署增强的进程管理方案..."

# 1. 创建必要的目录
mkdir -p logs pids

# 2. 安装systemd服务
echo "安装systemd服务..."
sudo cp crypto-collector.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable crypto-collector.service

# 3. 停止现有进程（使用新的安全方法）
echo "停止现有进程..."
python3 improved_process_management.py

# 4. 启动systemd服务
echo "启动systemd服务..."
sudo systemctl start crypto-collector.service

# 5. 应用增强的cron配置
echo "应用增强的cron配置..."
read -p "是否要应用新的cron配置? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    # 备份现有crontab
    crontab -l > "crontab_backup_$(date +%Y%m%d_%H%M%S).txt" 2>/dev/null || true
    
    # 应用新配置
    crontab enhanced_crontab.txt
    echo "✅ Cron配置已更新"
else
    echo "⏭️  跳过cron配置更新"
fi

# 6. 验证部署
echo "验证部署状态..."
echo "📊 Systemd服务状态:"
sudo systemctl status crypto-collector.service --no-pager -l

echo "📊 当前进程:"
ps aux | grep -E "collect.*data" | grep -v grep || echo "无相关进程"

echo "📊 Cron任务:"
crontab -l | grep -E "(process_management|cron_predictions)" || echo "无相关cron任务"

echo ""
echo "✅ 增强进程管理方案部署完成！"
echo ""
echo "📋 管理命令:"
echo "• 查看服务状态: sudo systemctl status crypto-collector"
echo "• 重启服务: sudo systemctl restart crypto-collector"
echo "• 查看日志: sudo journalctl -u crypto-collector -f"
echo "• 手动进程管理: python3 improved_process_management.py"
echo "• 查看进程管理日志: tail -f logs/process_manager.log"