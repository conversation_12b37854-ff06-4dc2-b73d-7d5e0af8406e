/* 旗鱼AI全局样式 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  /* 主题色彩 */
  --color-primary: #447A53;
  --color-secondary: #016A86;
  --color-border: #555555;
  
  /* 深色模式 */
  --color-bg-primary: #0d1117;
  --color-bg-secondary: #161b22;
  --color-bg-hover: #21262d;
  --color-text-primary: #e6edf3;
  --color-text-secondary: #8b949e;
  --color-divider: #30363d;
  
  /* 图表色彩 */
  --color-up: #00d4aa;
  --color-down: #ff6b6b;
  --color-volume: #4299e1;
  
  /* 渐变色 */
  --gradient-primary: linear-gradient(33deg, #00C9FF 0%, #92fe9d 100%);
  --gradient-secondary: linear-gradient(135deg, #447A53 0%, #016A86 100%);
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  
  /* 边框半径 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  
  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.15);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.3);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  min-height: 100vh;
  background-color: var(--color-bg-primary);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-secondary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 4px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-secondary);
}

/* 选择文本样式 */
::selection {
  background-color: var(--color-primary);
  color: white;
}

/* 链接样式 */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--color-secondary);
}

/* 渐变按钮类 */
.gradient-button {
  background: var(--gradient-primary);
  border: none;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: var(--border-radius-md);
}

.gradient-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 卡片容器 */
.card-container {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.card-container:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
}

/* 图表容器 */
.chart-container {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  position: relative;
}

.chart-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  border-radius: 0;
}

/* 加载动画 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-text-secondary);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s linear infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-container {
    padding: var(--spacing-md);
  }
  
  .chart-container {
    border-radius: var(--border-radius-md);
  }
}

/* 工具提示样式 */
.tooltip {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-sm);
  color: var(--color-text-primary);
  font-size: 12px;
  box-shadow: var(--shadow-md);
  z-index: 1000;
}

/* 自定义按钮变体 */
.btn-primary {
  background: var(--color-primary);
  border: 1px solid var(--color-primary);
  color: white;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: #5a8a6a;
  border-color: #5a8a6a;
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--color-secondary);
  border: 1px solid var(--color-secondary);
  color: white;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: #027a9a;
  border-color: #027a9a;
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text-primary);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-outline:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
}

/* 文本样式 */
.text-primary {
  color: var(--color-text-primary);
}

.text-secondary {
  color: var(--color-text-secondary);
}

.text-success {
  color: var(--color-up);
}

.text-danger {
  color: var(--color-down);
}

/* 布局辅助类 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

.grid {
  display: grid;
}

.hidden {
  display: none;
}

/* 间距辅助类 */
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xs); }
.m-2 { margin: var(--spacing-sm); }
.m-3 { margin: var(--spacing-md); }
.m-4 { margin: var(--spacing-lg); }
.m-5 { margin: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }