-- 添加套餐配置表和返利配置表

-- 套餐配置表
CREATE TABLE IF NOT EXISTS package_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(50) NOT NULL,
    package_key VARCHAR(20) UNIQUE NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    credits INTEGER NOT NULL,
    description VARCHAR(200),
    icon VARCHAR(50) DEFAULT 'star',
    is_active BOOLEAN DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);

-- 返利配置表
CREATE TABLE IF NOT EXISTS commission_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    commission_type VARCHAR(10) UNIQUE NOT NULL,
    commission_rate DECIMAL(5,4) NOT NULL,
    description VARCHAR(200),
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);

-- 插入默认套餐配置
INSERT OR REPLACE INTO package_config (name, package_key, price, credits, description, icon, is_active, sort_order, created_at, updated_at) VALUES
('基础套餐', 'small', 3.00, 10, '0.3 USD/次', 'star', 1, 1, datetime('now'), datetime('now')),
('标准套餐', 'medium', 5.00, 20, '0.25 USD/次', 'crown', 1, 2, datetime('now'), datetime('now')),
('高级套餐', 'large', 9.00, 50, '0.18 USD/次', 'rocket', 1, 3, datetime('now'), datetime('now'));

-- 插入默认返利配置
INSERT OR REPLACE INTO commission_config (commission_type, commission_rate, description, is_active, created_at, updated_at) VALUES
('level1', 0.3333, '一级返利：直接邀请人消费时获得1/3返利', 1, datetime('now'), datetime('now')),
('level2', 0.2000, '二级返利：间接邀请人消费时获得1/5返利', 1, datetime('now'), datetime('now'));
