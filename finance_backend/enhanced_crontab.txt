# 增强版Cron调度 - 更安全的进程管理

# 进程健康检查和管理 - 每10分钟检查一次
*/10 * * * * cd /home/<USER>/qiyuai-web/finance_backend && python3 improved_process_management.py >> logs/process_manager.log 2>&1

# K线数据更新 - 每5分钟执行 (错开AI预测时间)
1,6,11,16,21,26,31,36,41,46,51,56 * * * * cd /home/<USER>/qiyuai-web/finance_backend && timeout 300 python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=1m --start=$(date +\%Y-\%m-\%d) --end=$(date +\%Y-\%m-\%d) >> logs/auto_update.log 2>&1

# 15分钟数据更新 - 每15分钟的第2分钟执行
2,17,32,47 * * * * cd /home/<USER>/qiyuai-web/finance_backend && timeout 600 python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=15m --start=$(date +\%Y-\%m-\%d) --end=$(date +\%Y-\%m-\%d) >> logs/auto_update.log 2>&1

# 1小时数据更新 - 每小时第7分钟执行
7 * * * * cd /home/<USER>/qiyuai-web/finance_backend && timeout 900 python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=1h --start=$(date +\%Y-\%m-\%d) --end=$(date +\%Y-\%m-\%d) >> logs/auto_update.log 2>&1

# 4小时数据更新 - 每4小时第12分钟执行
12 */4 * * * cd /home/<USER>/qiyuai-web/finance_backend && timeout 1200 python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=4h --start=$(date +\%Y-\%m-\%d) --end=$(date +\%Y-\%m-\%d) >> logs/auto_update.log 2>&1

# 日数据更新 - 每天凌晨1:18更新昨日数据
18 1 * * * cd /home/<USER>/qiyuai-web/finance_backend && timeout 1800 python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=1d --start=$(date -d "1 day ago" +\%Y-\%m-\%d) --end=$(date -d "1 day ago" +\%Y-\%m-\%d) >> logs/auto_update.log 2>&1

# 当日数据更新 - 每天凌晨2:05更新当日数据
5 2 * * * cd /home/<USER>/qiyuai-web/finance_backend && timeout 1800 python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=1d --start=$(date +\%Y-\%m-\%d) --end=$(date +\%Y-\%m-\%d) >> logs/auto_update.log 2>&1

# AI预测任务 - 每15分钟第3分钟执行，添加超时和错误处理
3,18,33,48 * * * * cd /home/<USER>/qiyuai-web/finance_backend && timeout 900 /usr/bin/python3 manage.py cron_predictions >> logs/predictions/cron_predictions.log 2>&1

# 日志轮转 - 每小时第55分钟执行，避免日志文件过大
55 * * * * cd /home/<USER>/qiyuai-web/finance_backend && find logs/ -name "*.log" -size +100M -exec sh -c 'tail -n 10000 "$1" > "$1.tmp" && mv "$1.tmp" "$1"' _ {} \;

# 数据库清理 - 每天凌晨3:30清理旧的失败记录
30 3 * * * cd /home/<USER>/qiyuai-web/finance_backend && python3 -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'finance_project.settings')
import django
django.setup()
from ai_analysis.models import PredictionRecord
from django.utils import timezone
from datetime import timedelta
cutoff = timezone.now() - timedelta(days=7)
PredictionRecord.objects.filter(prediction_status='failed', created_at__lt=cutoff).delete()
print('清理了7天前的失败预测记录')
" >> logs/cleanup.log 2>&1

# 系统监控 - 每5分钟检查系统资源使用情况
*/5 * * * * cd /home/<USER>/qiyuai-web/finance_backend && python3 -c "
import psutil
import json
from datetime import datetime
stats = {
    'timestamp': datetime.now().isoformat(),
    'cpu_percent': psutil.cpu_percent(interval=1),
    'memory_percent': psutil.virtual_memory().percent,
    'disk_percent': psutil.disk_usage('/').percent
}
if stats['memory_percent'] > 80 or stats['cpu_percent'] > 80:
    print(f'WARNING: High resource usage - {json.dumps(stats)}')
" >> logs/system_monitor.log 2>&1