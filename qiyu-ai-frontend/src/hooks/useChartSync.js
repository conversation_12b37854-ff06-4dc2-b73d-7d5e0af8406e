/**
 * 多图联动Hook
 * 实现十字线同步、缩放联动、时间轴对齐
 */
import { useRef, useCallback } from 'react';

export const useChartSync = () => {
  const chartsRef = useRef(new Map());
  const syncEnabledRef = useRef(true);

  // 注册图表
  const registerChart = useCallback((id, chartInstance) => {
    if (!chartInstance) return;

    chartsRef.current.set(id, chartInstance);
    
    try {
      // 添加十字线变化监听
      chartInstance.subscribeAction('onCrosshairChange', (data) => {
        if (!syncEnabledRef.current) return;
        
        // 同步到其他图表
        chartsRef.current.forEach((chart, chartId) => {
          if (chartId !== id && chart) {
            try {
              // 使用正确的API方法
              if (typeof chart.scrollToTimestamp === 'function' && data.timestamp) {
                chart.scrollToTimestamp(data.timestamp);
              }
            } catch (error) {
              // 静默失败，不显示错误
            }
          }
        });
      });

      // 添加可视区域变化监听（用于缩放和滚动同步）
      chartInstance.subscribeAction('onVisibleRangeChange', (data) => {
        if (!syncEnabledRef.current) return;
        
        // 同步可视区域到其他图表
        chartsRef.current.forEach((chart, chartId) => {
          if (chartId !== id && chart) {
            try {
              if (typeof chart.scrollToTimestamp === 'function' && data.from) {
                chart.scrollToTimestamp(data.from);
              }
            } catch (error) {
              // 静默失败
            }
          }
        });
      });
      
      console.log(`✅ 图表 ${id} 已注册联动`);
    } catch (error) {
      console.warn(`注册图表联动失败 ${id}:`, error);
    }
  }, []);

  // 取消注册图表
  const unregisterChart = useCallback((id) => {
    const chart = chartsRef.current.get(id);
    if (chart) {
      try {
        // klinecharts v9+ 可能不需要手动取消监听
        // 图表销毁时会自动清理
        if (typeof chart.unsubscribeAction === 'function') {
          chart.unsubscribeAction('onCrosshairChange');
          chart.unsubscribeAction('onVisibleRangeChange');
        }
      } catch (error) {
        // 静默失败
      }
    }
    
    chartsRef.current.delete(id);
    console.log(`🗑️ 图表 ${id} 已取消注册`);
  }, []);

  // 启用/禁用同步
  const setSyncEnabled = useCallback((enabled) => {
    syncEnabledRef.current = enabled;
    console.log(`${enabled ? '启用' : '禁用'} 图表联动`);
  }, []);

  // 同步所有图表到指定时间
  const syncAllChartsToTime = useCallback((timestamp) => {
    chartsRef.current.forEach((chart, chartId) => {
      try {
        if (typeof chart.scrollToTimestamp === 'function') {
          chart.scrollToTimestamp(timestamp);
        }
      } catch (error) {
        console.warn(`同步时间失败 ${chartId}:`, error);
      }
    });
  }, []);

  // 重置所有图表缩放
  const resetAllChartsZoom = useCallback(() => {
    chartsRef.current.forEach((chart, chartId) => {
      try {
        // 使用正确的API方法
        if (typeof chart.scrollToRealTime === 'function') {
          chart.scrollToRealTime();
        }
        // 或者使用其他可能的重置方法
        if (typeof chart.zoomAtCoordinate === 'function') {
          // 重置到默认缩放级别
          chart.zoomAtCoordinate({ x: 0, y: 0 }, 1);
        }
        console.log(`✅ 重置图表缩放: ${chartId}`);
      } catch (error) {
        console.warn(`重置缩放失败 ${chartId}:`, error);
      }
    });
    console.log('🔄 所有图表缩放已重置');
  }, []);

  // 获取已注册的图表数量
  const getRegisteredChartsCount = useCallback(() => {
    return chartsRef.current.size;
  }, []);

  // 获取同步状态
  const getSyncEnabled = useCallback(() => {
    return syncEnabledRef.current;
  }, []);

  return {
    registerChart,
    unregisterChart,
    setSyncEnabled,
    getSyncEnabled,
    syncAllChartsToTime,
    resetAllChartsZoom,
    getRegisteredChartsCount
  };
};