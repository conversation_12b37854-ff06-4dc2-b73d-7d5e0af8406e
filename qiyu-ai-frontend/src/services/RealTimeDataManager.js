/**
 * 实时数据管理器
 * 解决数据断层问题 + 多周期WebSocket数据收集与持久化
 */

import binanceWebSocketService from './BinanceWebSocketService'

class RealTimeDataManager {
  constructor() {
    this.dataBuffer = new Map() // 数据缓冲区: symbol_timeframe -> [data]
    this.lastUpdateTime = new Map() // 最后更新时间
    this.lastUploadTime = new Map() // 最后上传时间
    this.supportedTimeframes = ['1m', '15m', '1h', '4h', '1d'] // 不包括1mo，月线通过日线聚合
    
    // 🚀 服务器端持久化配置（5分钟周期）
    this.uploadInterval = 300000 // 5分钟上传周期
    this.renderUpdateInterval = 300000 // 5分钟渲染更新周期
    this.maxBufferSize = 500 // 降低缓冲区大小，避免数据丢失
    
    // 🎯 启动服务器端数据同步
    this.startServerBasedSync()
    
    console.log('🚀 RealTimeDataManager: 启用服务器端持久化模式 (5分钟周期)')
  }

  /**
   * 🎯 解决数据断层：获取缺失的历史数据
   * @param {string} symbol - 交易对
   * @param {string} timeframe - 时间周期
   * @param {number} fromTime - 开始时间戳（毫秒）
   * @param {number} toTime - 结束时间戳（毫秒）
   */
  async fillDataGap(symbol, timeframe, fromTime, toTime) {
    console.log(`🔄 填补数据断层: ${symbol} ${timeframe} ${new Date(fromTime).toISOString()} ~ ${new Date(toTime).toISOString()}`)
    
    try {
      // 使用Binance REST API获取历史数据
      const gapData = await this.fetchHistoricalData(symbol, timeframe, fromTime, toTime)
      
      if (gapData && gapData.length > 0) {
        // 格式化为标准格式
        const formattedData = gapData.map(item => ({
          timestamp: item[0], // 开盘时间
          open: parseFloat(item[1]),
          high: parseFloat(item[2]),
          low: parseFloat(item[3]),
          close: parseFloat(item[4]),
          volume: parseFloat(item[5]),
          turnover: parseFloat(item[7]) // quoteAssetVolume
        }))
        
        // 添加到缓冲区
        const bufferKey = `${symbol}_${timeframe}`
        if (!this.dataBuffer.has(bufferKey)) {
          this.dataBuffer.set(bufferKey, [])
        }
        
        const existingData = this.dataBuffer.get(bufferKey)
        const mergedData = [...existingData, ...formattedData]
          .sort((a, b) => a.timestamp - b.timestamp) // 按时间排序
        
        this.dataBuffer.set(bufferKey, mergedData)
        
        console.log(`✅ 数据断层填补完成: ${formattedData.length} 条记录`)
        return formattedData
      }
    } catch (error) {
      console.error(`❌ 数据断层填补失败:`, error)
      throw error
    }
  }

  /**
   * 获取历史数据（通过后端代理，真实Binance数据）
   */
  async fetchHistoricalData(symbol, timeframe, startTime, endTime) {
    console.log(`🔄 获取真实历史数据: ${symbol} ${timeframe}`)
    console.log(`⏰ 时间范围: ${new Date(startTime).toISOString()} ~ ${new Date(endTime).toISOString()}`)
    
    try {
      const response = await fetch('http://localhost:8000/api/crypto/binance/historical/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          symbol: symbol,
          interval: timeframe,
          start_time: startTime,
          end_time: endTime
        })
      })
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || '后端API调用失败')
      }
      
      console.log(`✅ 真实历史数据获取完成: ${result.count} 条记录`)
      return result.data
      
    } catch (error) {
      console.error(`❌ 历史数据获取失败: ${error.message}`)
      throw error
    }
  }


  /**
   * 计算批次持续时间（避免单次请求数据过多）
   */
  getBatchDuration(interval) {
    const durations = {
      '1m': 16 * 60 * 60 * 1000,  // 16小时（960条1分钟数据）
      '15m': 10 * 24 * 60 * 60 * 1000, // 10天
      '1h': 41 * 24 * 60 * 60 * 1000,  // 41天
      '4h': 166 * 24 * 60 * 60 * 1000, // 166天
      '1d': 1000 * 24 * 60 * 60 * 1000 // 1000天
    }
    return durations[interval] || 24 * 60 * 60 * 1000 // 默认1天
  }

  /**
   * 启动实时数据收集（只收集必要的时间周期）
   */
  startRealTimeCollection(symbol) {
    console.log(`📡 启动实时数据收集: ${symbol}`)
    
    // 只为1分钟数据启用WebSocket（其他周期通过1分钟聚合生成）
    const subscriptionId = binanceWebSocketService.subscribeKline(symbol, '1m', (klineData) => {
      this.handleRealTimeData(symbol, '1m', klineData)
      
      // 🎯 从1分钟数据聚合生成其他周期
      this.aggregateFromMinuteData(symbol, klineData)
    })
    
    return subscriptionId
  }

  /**
   * 处理实时数据
   */
  handleRealTimeData(symbol, timeframe, klineData) {
    // 只处理最终确认的K线
    if (!klineData.isFinal) return
    
    const bufferKey = `${symbol}_${timeframe}`
    
    // 确保时间戳为毫秒格式
    const timestamp = klineData.timestamp > 1e15 ? Math.floor(klineData.timestamp / 1000) : klineData.timestamp
    
    const dataPoint = {
      timestamp,
      open: klineData.open,
      high: klineData.high,
      low: klineData.low,
      close: klineData.close,
      volume: klineData.volume,
      turnover: klineData.turnover || (klineData.close * klineData.volume)
    }
    
    // 添加到缓冲区
    if (!this.dataBuffer.has(bufferKey)) {
      this.dataBuffer.set(bufferKey, [])
    }
    
    this.dataBuffer.get(bufferKey).push(dataPoint)
    this.lastUpdateTime.set(bufferKey, Date.now())
    
    console.log(`📊 收集实时数据: ${symbol} ${timeframe} ${new Date(timestamp).toLocaleTimeString()}`)
    
    // 检查是否需要批量上传
    this.checkBatchUpload(bufferKey)
  }

  /**
   * 🎯 从1分钟数据聚合生成其他周期（避免多个WebSocket连接）
   */
  aggregateFromMinuteData(symbol, minuteData) {
    if (!minuteData.isFinal) return
    
    const timestamp = minuteData.timestamp > 1e15 ? Math.floor(minuteData.timestamp / 1000) : minuteData.timestamp
    const date = new Date(timestamp)
    
    // 聚合15分钟数据
    if (date.getMinutes() % 15 === 14) { // 每15分钟的最后一分钟
      this.aggregateTimeframe(symbol, '15m', timestamp)
    }
    
    // 聚合1小时数据
    if (date.getMinutes() === 59) { // 每小时的最后一分钟
      this.aggregateTimeframe(symbol, '1h', timestamp)
    }
    
    // 聚合4小时数据
    if (date.getHours() % 4 === 3 && date.getMinutes() === 59) {
      this.aggregateTimeframe(symbol, '4h', timestamp)
    }
    
    // 聚合1天数据
    if (date.getHours() === 23 && date.getMinutes() === 59) {
      this.aggregateTimeframe(symbol, '1d', timestamp)
    }
  }

  /**
   * 聚合指定时间周期的数据
   */
  aggregateTimeframe(symbol, timeframe, endTimestamp) {
    const minuteKey = `${symbol}_1m`
    const minuteData = this.dataBuffer.get(minuteKey) || []
    
    if (minuteData.length === 0) return
    
    // 计算时间周期的开始时间
    const periodMs = {
      '15m': 15 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '4h': 4 * 60 * 60 * 1000,
      '1d': 24 * 60 * 60 * 1000
    }
    
    const startTimestamp = endTimestamp - periodMs[timeframe] + 60000 // +1分钟确保包含结束时间
    
    // 过滤出该时间段的1分钟数据
    const periodData = minuteData.filter(item => 
      item.timestamp >= startTimestamp && item.timestamp <= endTimestamp
    )
    
    if (periodData.length === 0) return
    
    // 聚合计算
    const aggregated = {
      timestamp: startTimestamp,
      open: periodData[0].open,
      high: Math.max(...periodData.map(d => d.high)),
      low: Math.min(...periodData.map(d => d.low)),
      close: periodData[periodData.length - 1].close,
      volume: periodData.reduce((sum, d) => sum + d.volume, 0),
      turnover: periodData.reduce((sum, d) => sum + d.turnover, 0)
    }
    
    // 添加到对应的缓冲区
    const bufferKey = `${symbol}_${timeframe}`
    if (!this.dataBuffer.has(bufferKey)) {
      this.dataBuffer.set(bufferKey, [])
    }
    
    this.dataBuffer.get(bufferKey).push(aggregated)
    this.lastUpdateTime.set(bufferKey, Date.now())
    
    console.log(`🔄 聚合数据生成: ${symbol} ${timeframe} ${new Date(startTimestamp).toLocaleTimeString()}`)
  }

  /**
   * 检查批量上传
   */
  checkBatchUpload(bufferKey) {
    const data = this.dataBuffer.get(bufferKey)
    if (data && data.length >= this.batchSize) {
      this.uploadToCOS(bufferKey)
    }
  }

  /**
   * 存储历史数据到本地缓存
   */
  storeHistoricalData(symbol, timeframe, binanceData) {
    if (!binanceData || binanceData.length === 0) return
    
    const bufferKey = `${symbol}_${timeframe}`
    
    // 转换Binance格式为内部格式
    const formattedData = binanceData.map(item => {
      const timestamp = Array.isArray(item) ? item[0] : item.timestamp
      // 确保时间戳为毫秒格式
      const correctedTimestamp = timestamp > 1e15 ? Math.floor(timestamp / 1000) : timestamp
      
      return {
        timestamp: correctedTimestamp,
        open: parseFloat(Array.isArray(item) ? item[1] : item.open),
        high: parseFloat(Array.isArray(item) ? item[2] : item.high),
        low: parseFloat(Array.isArray(item) ? item[3] : item.low),
        close: parseFloat(Array.isArray(item) ? item[4] : item.close),
        volume: parseFloat(Array.isArray(item) ? item[5] : item.volume),
        turnover: parseFloat(Array.isArray(item) ? item[7] || (item[1] * item[5]) : (item.close * item.volume))
      }
    })
    
    // 按时间戳排序
    formattedData.sort((a, b) => a.timestamp - b.timestamp)
    
    // 合并到现有缓冲区
    if (!this.dataBuffer.has(bufferKey)) {
      this.dataBuffer.set(bufferKey, [])
    }
    
    const existingData = this.dataBuffer.get(bufferKey)
    const mergedData = [...existingData, ...formattedData]
      .sort((a, b) => a.timestamp - b.timestamp)
    
    // 去重（基于时间戳）
    const uniqueData = mergedData.reduce((acc, current) => {
      const exists = acc.find(item => item.timestamp === current.timestamp)
      if (!exists) {
        acc.push(current)
      }
      return acc
    }, [])
    
    this.dataBuffer.set(bufferKey, uniqueData)
    this.lastUpdateTime.set(bufferKey, Date.now())
    
    console.log(`💾 历史数据已存储: ${symbol} ${timeframe} (${formattedData.length} 条新记录, 总计 ${uniqueData.length} 条)`)
    
    // 同时存储到localStorage作为持久化备份
    try {
      const storageKey = `crypto_gap_${bufferKey}`
      localStorage.setItem(storageKey, JSON.stringify({
        symbol,
        timeframe,
        data: uniqueData,
        updatedAt: Date.now()
      }))
      console.log(`💿 数据已备份到localStorage: ${storageKey}`)
    } catch (error) {
      console.warn('⚠️ localStorage存储失败:', error)
    }
  }

  /**
   * 从localStorage恢复数据
   */
  loadFromLocalStorage() {
    const keys = Object.keys(localStorage).filter(key => key.startsWith('crypto_gap_'))
    
    keys.forEach(storageKey => {
      try {
        const stored = JSON.parse(localStorage.getItem(storageKey))
        const bufferKey = `${stored.symbol}_${stored.timeframe}`
        
        this.dataBuffer.set(bufferKey, stored.data)
        this.lastUpdateTime.set(bufferKey, stored.updatedAt)
        
        console.log(`📥 从localStorage恢复数据: ${bufferKey} (${stored.data.length} 条记录)`)
      } catch (error) {
        console.warn(`⚠️ localStorage数据恢复失败: ${storageKey}`, error)
      }
    })
  }

  /**
   * 上传数据到COS
   */
  async uploadToCOS(bufferKey) {
    const data = this.dataBuffer.get(bufferKey)
    if (!data || data.length === 0) return
    
    console.log(`🚀 准备上传到COS: ${bufferKey} (${data.length} 条记录)`)
    
    try {
      const [symbol, timeframe] = bufferKey.split('_')
      
      // 🎯 特殊处理2025年1分钟数据的路径格式
      const cosPath = this.generateCOSPath(symbol, timeframe, data[0].timestamp)
      const formattedData = this.formatForCOS(symbol, timeframe, data)
      
      // TODO: 实现COS上传逻辑
      console.log(`📤 上传路径: ${cosPath}`)
      console.log(`📊 数据条数: ${data.length}`)
      
      // 上传成功后清理缓冲区
      this.dataBuffer.set(bufferKey, [])
      
    } catch (error) {
      console.error(`❌ COS上传失败:`, error)
    }
  }

  /**
   * 生成COS存储路径
   */
  generateCOSPath(symbol, timeframe, timestamp) {
    const date = new Date(timestamp)
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    
    // 2025年1分钟数据使用特殊路径
    if (year === 2025 && timeframe === '1m') {
      return `crypto-kline-data-v2/20250726/${symbol}/1m/daily/${symbol}_1m_${month}-${day}_2025_compressed.json`
    }
    
    // 其他数据使用标准路径
    return `crypto-kline-data-v2/20250726/${symbol}/${timeframe}/${symbol}_${timeframe}_${year}_compressed.json`
  }

  /**
   * 格式化数据为COS格式
   */
  formatForCOS(symbol, timeframe, data) {
    return {
      metadata: {
        symbol,
        timeframe,
        total_records: data.length,
        time_range: {
          start: new Date(data[0].timestamp).toISOString(),
          end: new Date(data[data.length - 1].timestamp).toISOString(),
          start_timestamp: data[0].timestamp,
          end_timestamp: data[data.length - 1].timestamp
        },
        processed_at: new Date().toISOString()
      },
      data: data.map(item => [
        item.timestamp,
        item.open,
        item.high,
        item.low,
        item.close,
        item.volume
      ])
    }
  }

  /**
   * 🚀 启动服务器端数据同步（5分钟周期）
   */
  startServerBasedSync() {
    // 1. 定期上传到服务器
    setInterval(() => {
      console.log('⏰ 5分钟定期上传检查...')
      this.uploadToServer()
    }, this.uploadInterval)
    
    // 2. 定期从服务器拉取数据并更新渲染
    setInterval(() => {
      console.log('🔄 5分钟定期渲染更新...')
      this.updateChartsFromServer()
    }, this.renderUpdateInterval)
    
    // 3. 启动时立即从服务器恢复数据
    this.loadFromServer()
  }

  /**
   * 🚀 上传数据到服务器（替代COS上传）
   */
  async uploadToServer() {
    const uploadPromises = []
    
    this.dataBuffer.forEach((data, bufferKey) => {
      if (data.length === 0) return
      
      const lastUpload = this.lastUploadTime.get(bufferKey) || 0
      const timeSinceUpload = Date.now() - lastUpload
      
      // 检查是否需要上传：数据量达到阈值 或 时间超过5分钟
      if (data.length >= this.maxBufferSize || timeSinceUpload >= this.uploadInterval) {
        uploadPromises.push(this.uploadBufferToServer(bufferKey, data))
      }
    })
    
    if (uploadPromises.length > 0) {
      try {
        await Promise.all(uploadPromises)
        console.log(`✅ 批量上传完成: ${uploadPromises.length} 个缓冲区`)
      } catch (error) {
        console.error('❌ 服务器上传失败:', error)
      }
    }
  }

  /**
   * 上传单个缓冲区到服务器
   */
  async uploadBufferToServer(bufferKey, data) {
    const [symbol, timeframe] = bufferKey.split('_')
    
    try {
      console.log(`📤 上传到服务器: ${bufferKey} (${data.length} 条记录)`)
      
      const response = await fetch('http://localhost:8000/api/crypto/realtime/upload/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          symbol: symbol,
          timeframe: timeframe,
          data: data
        })
      })
      
      const result = await response.json()
      
      if (result.success) {
        console.log(`✅ ${bufferKey} 上传成功: ${result.uploaded} 条记录`)
        
        // 上传成功后清理缓冲区
        this.dataBuffer.set(bufferKey, [])
        this.lastUploadTime.set(bufferKey, Date.now())
        
        return result
      } else {
        throw new Error(result.error)
      }
      
    } catch (error) {
      console.error(`❌ ${bufferKey} 上传失败:`, error)
      throw error
    }
  }

  /**
   * 🔄 从服务器加载数据（替代localStorage恢复）
   */
  async loadFromServer() {
    console.log('📥 从服务器恢复数据...')
    
    // 查询所有支持的时间周期数据
    const symbols = ['BTCUSDT'] // 可扩展支持多个交易对
    
    for (const symbol of symbols) {
      for (const timeframe of this.supportedTimeframes) {
        try {
          const response = await fetch('http://localhost:8000/api/crypto/realtime/query/', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              symbol: symbol,
              timeframe: timeframe,
              limit: 1000 // 加载最近1000条记录
            })
          })
          
          const result = await response.json()
          
          if (result.success && result.data.length > 0) {
            const bufferKey = `${symbol}_${timeframe}`
            // 将服务器数据加载到缓冲区（但不覆盖当前实时数据）
            console.log(`📥 从服务器恢复: ${bufferKey} (${result.data.length} 条记录)`)
          }
          
        } catch (error) {
          console.warn(`⚠️ 从服务器恢复 ${symbol} ${timeframe} 失败:`, error)
        }
      }
    }
  }

  /**
   * 🔄 定期更新图表显示（从服务器拉取最新数据）
   */
  async updateChartsFromServer() {
    try {
      // 触发图表更新事件
      if (typeof window !== 'undefined' && window.dispatchEvent) {
        const updateEvent = new CustomEvent('serverDataUpdate', {
          detail: {
            source: 'server',
            timestamp: Date.now()
          }
        })
        window.dispatchEvent(updateEvent)
        console.log('🔄 触发图表更新事件')
      }
      
      // 清除StreamDataService缓存，强制重新加载
      try {
        const streamDataService = await import('./StreamDataService.js')
        streamDataService.default.clearCache()
        console.log('🗑️ 清除图表缓存，准备更新')
      } catch (error) {
        console.warn('⚠️ 清除缓存失败:', error)
      }
      
    } catch (error) {
      console.error('❌ 图表更新失败:', error)
    }
  }

  /**
   * 获取缓冲区状态
   */
  getBufferStatus() {
    const status = {}
    this.dataBuffer.forEach((data, key) => {
      status[key] = {
        records: data.length,
        lastUpdate: this.lastUpdateTime.get(key),
        oldestRecord: data.length > 0 ? new Date(data[0].timestamp).toISOString() : null,
        newestRecord: data.length > 0 ? new Date(data[data.length - 1].timestamp).toISOString() : null
      }
    })
    return status
  }

  /**
   * 手动触发数据断层修复
   */
  async repairDataGap(symbol = 'BTCUSDT') {
    console.log(`🔧 开始修复数据断层: ${symbol}`)
    
    // 🎯 修复时间戳计算：7-28 8:00 ~ 当前时间
    const now = Date.now()
    const july28_8AM = new Date(2025, 6, 28, 8, 0, 0, 0) // 月份从0开始，6=7月
    
    console.log(`⏰ 修复时间范围: ${july28_8AM.toISOString()} ~ ${new Date(now).toISOString()}`)
    console.log(`📅 时间戳范围: ${july28_8AM.getTime()} ~ ${now}`)
    
    for (const timeframe of this.supportedTimeframes) {
      try {
        await this.fillDataGap(symbol, timeframe, july28_8AM.getTime(), now)
        console.log(`✅ ${timeframe} 数据断层修复完成`)
      } catch (error) {
        console.error(`❌ ${timeframe} 数据断层修复失败:`, error)
      }
    }
  }
}

// 创建单例
const realTimeDataManager = new RealTimeDataManager()

export default realTimeDataManager