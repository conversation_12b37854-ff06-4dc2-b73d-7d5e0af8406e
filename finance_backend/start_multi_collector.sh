#!/bin/bash

# 多货币多周期实时数据收集器启动脚本
# 使用方法: ./start_multi_collector.sh

echo "🚀 启动多货币多周期实时数据收集器"

# 检查conda环境
if ! command -v conda &> /dev/null; then
    echo "❌ 未找到conda命令，请确保已安装Anaconda/Miniconda"
    exit 1
fi

# 激活conda环境
source ~/anaconda3/etc/profile.d/conda.sh
conda activate qiyu-web

if [ $? -ne 0 ]; then
    echo "❌ 无法激活qiyu-web环境，请检查环境是否存在"
    exit 1
fi

echo "✅ conda环境已激活: qiyu-web"

# 创建日志目录
mkdir -p logs

# 配置参数
SYMBOLS="BTCUSDT,ETHUSDT,SOLUSDT,DOGEUSDT,ADAUSDT,DOTUSDT,LINKUSDT,LTCUSDT,XRPUSDT"
TIMEFRAMES="1m,15m,1h,4h,1d"
UPLOAD_INTERVAL=300  # 5分钟
DEBUG_MODE="--debug"

echo "📊 交易对: $SYMBOLS"
echo "⏰ 时间周期: $TIMEFRAMES"
echo "📤 上传间隔: ${UPLOAD_INTERVAL}秒"
echo "🐛 调试模式: 启用"

# 检查网络连接
echo "🌐 测试网络连接..."
python test_server_network.py

if [ $? -ne 0 ]; then
    echo "⚠️ 网络测试失败，但继续启动收集器"
fi

# 启动多货币收集器
echo "🚀 启动多货币多周期数据收集器..."

python manage.py collect_multi_data \
    --symbols="$SYMBOLS" \
    --timeframes="$TIMEFRAMES" \
    --interval=$UPLOAD_INTERVAL \
    $DEBUG_MODE \
    --daemon

echo "✅ 多货币数据收集器启动完成"
echo "📋 查看日志: tail -f logs/multi_collector.log"
echo "🛑 停止收集器: Ctrl+C"