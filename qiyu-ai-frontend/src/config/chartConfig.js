/**
 * 图表配置 - 定时刷新模式
 */

export const CHART_CONFIG = {
  // 禁用WebSocket实时连接
  ENABLE_WEBSOCKET: false,
  
  // 定时刷新间隔（毫秒）
  REFRESH_INTERVALS: {
    '1m': 30000,   // 1分钟数据每30秒刷新
    '15m': 60000,  // 15分钟数据每1分钟刷新
    '1h': 300000,  // 1小时数据每5分钟刷新
    '4h': 600000,  // 4小时数据每10分钟刷新
    '1d': 1200000  // 1天数据每20分钟刷新
  },
  
  // 数据源配置
  DATA_SOURCE: {
    BASE_URL: 'http://localhost:8000/api',
    ENDPOINTS: {
      HISTORICAL: '/historical',
      SAME_PERIOD: '/historical/same-period',
      INDICATORS: '/historical/period-indicators',
      COMPARATIVE: '/historical/comparative-analysis'
    }
  }
};

export default CHART_CONFIG;
