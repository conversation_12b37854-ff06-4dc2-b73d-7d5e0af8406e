================================================================================
🔍 数据完整性检查报告
================================================================================
📅 检查时间: 2025-07-30 13:15:42
📊 检查期间: 2025-07-30 ~ 2025-07-30
⏱️  检查天数: 1 天

📈 总体统计
----------------------------------------
期望分钟数: 7,200
实际分钟数: 3,779
总体覆盖率: 52.49%
重要缺失: 6 个时段

📊 分交易对统计
----------------------------------------
BTCUSDT:
  覆盖率: 49.10%
  缺失时段: 2 个
  实际/期望: 707/1440
ETHUSDT:
  覆盖率: 53.33%
  缺失时段: 1 个
  实际/期望: 768/1440
SOLUSDT:
  覆盖率: 53.33%
  缺失时段: 1 个
  实际/期望: 768/1440
DOGEUSDT:
  覆盖率: 53.33%
  缺失时段: 1 个
  实际/期望: 768/1440
XRPUSDT:
  覆盖率: 53.33%
  缺失时段: 1 个
  实际/期望: 768/1440

🚨 重要缺失时段 (>60分钟)
----------------------------------------
BTCUSDT 2025-07-30
  时间: 2025-07-30 12:48:00 ~ 2025-07-30 23:59:59
  持续: 672 分钟

ETHUSDT 2025-07-30
  时间: 2025-07-30 12:48:00 ~ 2025-07-30 23:59:59
  持续: 672 分钟

SOLUSDT 2025-07-30
  时间: 2025-07-30 12:48:00 ~ 2025-07-30 23:59:59
  持续: 672 分钟

DOGEUSDT 2025-07-30
  时间: 2025-07-30 12:48:00 ~ 2025-07-30 23:59:59
  持续: 672 分钟

XRPUSDT 2025-07-30
  时间: 2025-07-30 12:48:00 ~ 2025-07-30 23:59:59
  持续: 672 分钟

BTCUSDT 2025-07-30
  时间: 2025-07-30 11:31:00 ~ 2025-07-30 12:31:59
  持续: 61 分钟

🔧 修复建议
----------------------------------------
python3 manage.py repair_data_gaps --symbol=BTCUSDT --start='2025-07-30T11:31:00' --end='2025-07-30T12:31:59'
python3 manage.py repair_data_gaps --symbol=BTCUSDT --start='2025-07-30T12:48:00' --end='2025-07-30T23:59:59'
python3 manage.py repair_data_gaps --symbol=ETHUSDT --start='2025-07-30T12:48:00' --end='2025-07-30T23:59:59'
python3 manage.py repair_data_gaps --symbol=SOLUSDT --start='2025-07-30T12:48:00' --end='2025-07-30T23:59:59'
python3 manage.py repair_data_gaps --symbol=DOGEUSDT --start='2025-07-30T12:48:00' --end='2025-07-30T23:59:59'
