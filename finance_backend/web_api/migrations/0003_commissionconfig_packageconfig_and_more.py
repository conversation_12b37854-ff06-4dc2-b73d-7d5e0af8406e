# Generated by Django 4.2.7 on 2025-07-25 02:09

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('web_api', '0002_alter_credittransaction_type_commissionrecord_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CommissionConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('commission_type', models.CharField(choices=[('level1', '一级返利'), ('level2', '二级返利')], max_length=10, unique=True, verbose_name='返利类型')),
                ('commission_rate', models.DecimalField(decimal_places=4, max_digits=5, verbose_name='返利比例')),
                ('description', models.CharField(blank=True, max_length=200, null=True, verbose_name='描述')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '返利配置',
                'verbose_name_plural': '返利配置',
                'db_table': 'commission_config',
            },
        ),
        migrations.CreateModel(
            name='PackageConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='套餐名称')),
                ('package_key', models.CharField(max_length=20, unique=True, verbose_name='套餐标识')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='价格(USD)')),
                ('credits', models.IntegerField(verbose_name='积分数量')),
                ('description', models.CharField(blank=True, max_length=200, null=True, verbose_name='描述')),
                ('icon', models.CharField(default='star', max_length=50, verbose_name='图标')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('sort_order', models.IntegerField(default=0, verbose_name='排序')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '套餐配置',
                'verbose_name_plural': '套餐配置',
                'db_table': 'package_config',
                'ordering': ['sort_order', 'price'],
            },
        ),
        migrations.AlterField(
            model_name='agentrelation',
            name='invite_code_used',
            field=models.CharField(blank=True, max_length=12, verbose_name='使用的邀请码'),
        ),
        migrations.AlterField(
            model_name='webuser',
            name='invite_code',
            field=models.CharField(blank=True, max_length=12, null=True, unique=True, verbose_name='邀请码'),
        ),
    ]
