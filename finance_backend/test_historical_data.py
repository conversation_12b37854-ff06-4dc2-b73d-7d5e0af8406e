#!/usr/bin/env python3
"""
测试历史数据获取功能
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('/home/<USER>/qiyuai-web/finance_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'finance_project.settings')
django.setup()

from historical_data.historical_analysis_service import HistoricalAnalysisService
from datetime import datetime
import json

def test_historical_data():
    """测试历史同期数据获取"""
    print("🧪 开始测试历史同期数据获取...")
    
    # 创建服务实例
    service = HistoricalAnalysisService()
    
    # 测试参数
    target_date = "2023-01-15"
    symbol = "BTCUSDT"
    days_range = 3
    
    print(f"📊 测试参数:")
    print(f"  - 目标日期: {target_date}")
    print(f"  - 交易对: {symbol}")
    print(f"  - 天数范围: ±{days_range}天")
    
    try:
        # 测试历史同期数据获取
        print("\n🔍 步骤1: 获取历史同期数据...")
        historical_data = service.get_historical_same_period_data(
            target_date=target_date,
            symbol=symbol,
            days_range=days_range
        )
        
        if 'error' in historical_data:
            print(f"❌ 获取历史同期数据失败: {historical_data['error']}")
            return
        
        print(f"✅ 历史同期数据获取成功!")
        print(f"  - 可用年份: {historical_data.get('available_years', [])}")
        print(f"  - 历史数据年份数: {historical_data.get('total_years', 0)}")
        
        # 显示每年的数据情况
        for year, year_data in historical_data.get('historical_data', {}).items():
            print(f"  - {year}年: {year_data.get('data_points', 0)}条记录")
        
        # 测试完整的对比分析
        print("\n🔍 步骤2: 获取完整对比分析...")
        comparative_analysis = service.get_comparative_analysis(
            target_date=target_date,
            symbol=symbol,
            days_range=days_range
        )
        
        if 'error' in comparative_analysis:
            print(f"❌ 对比分析失败: {comparative_analysis['error']}")
            return
        
        print(f"✅ 对比分析成功!")
        print(f"  - 目标指标: {len(comparative_analysis.get('target_indicators', {}).get('indicators', {}))}")
        print(f"  - 历史指标: {len(comparative_analysis.get('historical_indicators', {}))}")
        
        # 保存结果到文件
        with open('/home/<USER>/qiyuai-web/test_result.json', 'w') as f:
            json.dump(comparative_analysis, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"📁 结果已保存到: /home/<USER>/qiyuai-web/test_result.json")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_historical_data()
