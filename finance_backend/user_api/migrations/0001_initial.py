# Generated by Django 4.2.7 on 2025-06-19 07:33

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='WechatUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('openid', models.CharField(max_length=100, unique=True, verbose_name='微信OpenID')),
                ('unionid', models.CharField(blank=True, max_length=100, null=True, verbose_name='微信UnionID')),
                ('nickname', models.CharField(blank=True, max_length=100, null=True, verbose_name='昵称')),
                ('avatar_url', models.URLField(blank=True, max_length=500, null=True, verbose_name='头像URL')),
                ('phone', models.Char<PERSON>ield(blank=True, max_length=20, null=True, verbose_name='手机号')),
                ('credits', models.IntegerField(default=3, verbose_name='剩余分析次数')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '微信用户',
                'verbose_name_plural': '微信用户',
                'db_table': 'wechat_user',
            },
        ),
        migrations.CreateModel(
            name='CreditRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.SmallIntegerField(choices=[(1, '初始赠送'), (2, '每日签到'), (3, '观看广告'), (4, '消费使用'), (5, '充值购买')], verbose_name='类型')),
                ('amount', models.IntegerField(verbose_name='数量')),
                ('description', models.CharField(blank=True, max_length=200, null=True, verbose_name='描述')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='credit_records', to='user_api.wechatuser', verbose_name='用户')),
            ],
            options={
                'verbose_name': '积分记录',
                'verbose_name_plural': '积分记录',
                'db_table': 'credit_record',
                'ordering': ['-created_at'],
            },
        ),
    ]
