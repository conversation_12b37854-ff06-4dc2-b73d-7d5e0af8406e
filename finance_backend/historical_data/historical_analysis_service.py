"""
历史同期数据分析服务
提供历史同期数据获取和技术指标计算功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging

from .services import HistoricalDataService, COSDataService
from .technical_indicators_service import TechnicalIndicatorService

logger = logging.getLogger(__name__)


class HistoricalAnalysisService:
    """历史同期数据分析服务"""
    
    def __init__(self):
        self.historical_service = HistoricalDataService()
        self.cos_service = COSDataService()
        self.indicator_service = TechnicalIndicatorService()
    
    def get_historical_same_period_data(self, 
                                       target_date: str, 
                                       symbol: str = 'BTCUSDT',
                                       days_range: int = 3) -> Dict:
        """
        获取历史同期数据（目标时间前后N天的历史数据）
        
        参数:
        - target_date: 目标日期 (格式: '2024-01-15')
        - symbol: 交易对符号
        - days_range: 前后天数范围
        
        返回:
        - 包含历史各年同期数据的字典
        """
        try:
            target_dt = datetime.strptime(target_date, '%Y-%m-%d')
            
            # 获取可用的历史年份
            available_years = self._get_available_years(symbol)
            
            historical_data = {}
            
            for year in available_years:
                # 跳过目标年份本身
                if year == target_dt.year:
                    continue
                
                # 构建该年份的同期日期
                try:
                    same_period_date = target_dt.replace(year=year)
                except ValueError:
                    # 处理闰年2月29日的情况
                    if target_dt.month == 2 and target_dt.day == 29:
                        same_period_date = target_dt.replace(year=year, day=28)
                    else:
                        continue
                
                # 获取前后N天的数据
                period_data = self._get_period_data(
                    symbol=symbol,
                    center_date=same_period_date,
                    days_range=days_range
                )
                
                if not period_data.empty:
                    historical_data[str(year)] = {
                        'year': year,
                        'center_date': same_period_date.strftime('%Y-%m-%d'),
                        'data_points': len(period_data),
                        'kline_data': self._format_kline_data(period_data),
                        'date_range': {
                            'start': period_data['open_time'].min().strftime('%Y-%m-%d %H:%M:%S'),
                            'end': period_data['open_time'].max().strftime('%Y-%m-%d %H:%M:%S')
                        }
                    }
            
            return {
                'target_date': target_date,
                'symbol': symbol,
                'days_range': days_range,
                'available_years': available_years,
                'historical_data': historical_data,
                'total_years': len(historical_data)
            }
            
        except Exception as e:
            logger.error(f"获取历史同期数据失败: {e}")
            return {'error': str(e)}
    
    def calculate_period_indicators(self, 
                                   target_date: str, 
                                   symbol: str = 'BTCUSDT',
                                   days_range: int = 3,
                                   indicators: List[str] = None) -> Dict:
        """
        计算目标时间段的技术指标
        
        参数:
        - target_date: 目标日期
        - symbol: 交易对符号
        - days_range: 前后天数范围
        - indicators: 需要计算的指标列表 ['rsi', 'kdj', 'macd']
        
        返回:
        - 包含技术指标的数据
        """
        try:
            if indicators is None:
                indicators = ['rsi', 'kdj', 'macd']
            
            target_dt = datetime.strptime(target_date, '%Y-%m-%d')
            
            # 获取目标时间段数据（需要更多历史数据来计算指标）
            extended_data = self._get_period_data(
                symbol=symbol,
                center_date=target_dt,
                days_range=days_range + 30,  # 扩展30天以提供足够的计算数据
                interval='1m'
            )
            
            if extended_data.empty:
                return {'error': '目标时间段无数据'}
            
            # 准备DataFrame用于指标计算
            df = self.indicator_service.prepare_dataframe(
                extended_data.to_dict('records')
            )
            
            if df.empty:
                return {'error': '数据格式化失败'}
            
            # 计算所有指标
            all_indicators = self.indicator_service.calculate_all_indicators(df)
            
            # 过滤所需指标
            filtered_indicators = {}
            
            if 'rsi' in indicators:
                filtered_indicators['rsi'] = {
                    'value': all_indicators.get('rsi', 50.0),
                    'interpretation': self._interpret_rsi(all_indicators.get('rsi', 50.0))
                }
            
            if 'kdj' in indicators or 'stoch' in indicators:
                filtered_indicators['kdj'] = {
                    'k': all_indicators.get('stoch_k', 50.0),
                    'd': all_indicators.get('stoch_d', 50.0),
                    'j': all_indicators.get('stoch_j', 50.0),
                    'interpretation': self._interpret_kdj(
                        all_indicators.get('stoch_k', 50.0),
                        all_indicators.get('stoch_d', 50.0),
                        all_indicators.get('stoch_j', 50.0)
                    )
                }
            
            if 'macd' in indicators:
                filtered_indicators['macd'] = {
                    'line': all_indicators.get('macd_line', 0.0),
                    'signal': all_indicators.get('macd_signal', 0.0),
                    'histogram': all_indicators.get('macd_histogram', 0.0),
                    'interpretation': self._interpret_macd(
                        all_indicators.get('macd_line', 0.0),
                        all_indicators.get('macd_signal', 0.0),
                        all_indicators.get('macd_histogram', 0.0)
                    )
                }
            
            # 获取核心时间段数据（不扩展）
            core_data = self._get_period_data(
                symbol=symbol,
                center_date=target_dt,
                days_range=days_range,
                interval='1m'
            )
            
            return {
                'target_date': target_date,
                'symbol': symbol,
                'days_range': days_range,
                'indicators': filtered_indicators,
                'core_period': {
                    'data_points': len(core_data),
                    'date_range': {
                        'start': core_data['open_time'].min().strftime('%Y-%m-%d %H:%M:%S'),
                        'end': core_data['open_time'].max().strftime('%Y-%m-%d %H:%M:%S')
                    },
                    'price_info': {
                        'open': float(core_data['open'].iloc[0]),
                        'close': float(core_data['close'].iloc[-1]),
                        'high': float(core_data['high'].max()),
                        'low': float(core_data['low'].min()),
                        'volume': float(core_data['volume'].sum())
                    }
                },
                'calculation_period': {
                    'total_data_points': len(extended_data),
                    'extended_days': days_range + 30
                }
            }
            
        except Exception as e:
            logger.error(f"计算时间段技术指标失败: {e}")
            return {'error': str(e)}
    
    def get_comparative_analysis(self, 
                               target_date: str, 
                               symbol: str = 'BTCUSDT',
                               days_range: int = 3) -> Dict:
        """
        获取历史同期对比分析
        
        结合历史同期数据和技术指标进行对比分析
        """
        try:
            # 获取历史同期数据
            historical_data = self.get_historical_same_period_data(
                target_date, symbol, days_range
            )
            
            if 'error' in historical_data:
                return historical_data
            
            # 计算目标时间段指标
            target_indicators = self.calculate_period_indicators(
                target_date, symbol, days_range
            )
            
            if 'error' in target_indicators:
                return target_indicators
            
            # 计算历史同期指标
            historical_indicators = {}
            
            for year, year_data in historical_data['historical_data'].items():
                year_center_date = year_data['center_date']
                year_indicators = self.calculate_period_indicators(
                    year_center_date, symbol, days_range
                )
                
                if 'error' not in year_indicators:
                    historical_indicators[year] = year_indicators['indicators']
            
            # 对比分析
            comparison = self._generate_comparison_analysis(
                target_indicators['indicators'],
                historical_indicators
            )
            
            return {
                'target_date': target_date,
                'symbol': symbol,
                'days_range': days_range,
                'target_indicators': target_indicators,
                'historical_data': historical_data,
                'historical_indicators': historical_indicators,
                'comparison_analysis': comparison
            }
            
        except Exception as e:
            logger.error(f"对比分析失败: {e}")
            return {'error': str(e)}
    
    def _get_available_years(self, symbol: str) -> List[int]:
        """获取可用的历史年份"""
        try:
            # 基于用户提供的URL模式，检查可用的历史年份
            available_years = []
            
            # 检查2017-2024年数据（使用旧路径结构）
            base_years = list(range(2017, 2025))  # 2017-2024
            
            # 检查2025年数据（使用新路径结构）
            current_year = 2025
            
            # 为了提升性能，先检查几个关键年份
            test_years = [2017, 2018, 2019, 2020, 2021, 2022, 2023, 2024, 2025]
            
            for year in test_years:
                if self._check_year_data_exists(symbol, year):
                    available_years.append(year)
            
            logger.info(f"检测到可用历史年份: {available_years}")
            return sorted(available_years)
            
        except Exception as e:
            logger.error(f"获取可用年份失败: {e}")
            return []
    
    def _check_year_data_exists(self, symbol: str, year: int) -> bool:
        """检查指定年份的数据是否存在"""
        try:
            import requests
            
            if year <= 2024:
                # Pre-2025数据路径: /finance-1324685443/finance-1324685443/crypto-kline-data-v2/20250724/{symbol}/1h/{symbol}_1h_{year}_compressed.json
                url = f"https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/finance-1324685443/finance-1324685443/crypto-kline-data-v2/20250724/{symbol}/1h/{symbol}_1h_{year}_compressed.json"
            else:
                # 2025年数据路径: /crypto-kline-data-v2/20250726/{symbol}/1m/{symbol}_1m_01_2025_compressed.json
                url = f"https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/20250726/{symbol}/1m/{symbol}_1m_01_2025_compressed.json"
            
            # 使用HEAD请求检查文件是否存在，超时5秒
            response = requests.head(url, timeout=5)
            exists = response.status_code == 200
            
            if exists:
                logger.info(f"✅ 年份 {year} 数据存在: {symbol}")
            else:
                logger.debug(f"❌ 年份 {year} 数据不存在: {symbol} (HTTP {response.status_code})")
            
            return exists
            
        except Exception as e:
            logger.warning(f"检查年份 {year} 数据存在性失败: {e}")
            return False
    
    def _get_period_data(self, 
                        symbol: str, 
                        center_date: datetime, 
                        days_range: int,
                        interval: str = '1m') -> pd.DataFrame:
        """
        获取指定日期前后N天的数据
        
        参数:
        - symbol: 交易对
        - center_date: 中心日期
        - days_range: 前后天数
        - interval: 时间间隔
        """
        try:
            start_date = center_date - timedelta(days=days_range)
            end_date = center_date + timedelta(days=days_range)
            
            # 这里需要根据实际的数据获取逻辑来实现
            # 暂时返回示例数据结构
            
            # 构建日期范围内的数据查询
            data_frames = []
            
            current_date = start_date
            while current_date <= end_date:
                # 尝试获取当天数据
                daily_data = self._get_daily_data(symbol, current_date, interval)
                if not daily_data.empty:
                    data_frames.append(daily_data)
                current_date += timedelta(days=1)
            
            if data_frames:
                combined_df = pd.concat(data_frames, ignore_index=True)
                # 按时间排序
                combined_df = combined_df.sort_values('open_time').reset_index(drop=True)
                return combined_df
            else:
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"获取时间段数据失败: {e}")
            return pd.DataFrame()
    
    def _get_daily_data(self, symbol: str, date: datetime, interval: str) -> pd.DataFrame:
        """获取单日数据 - 使用正确的URL模式获取历史数据"""
        try:
            import requests
            import json
            
            target_date_str = date.strftime('%Y-%m-%d')
            year = date.year
            month = date.month
            day = date.day

            # 根据年份使用不同的URL模式
            if year <= 2024:
                # Pre-2025数据路径
                url = f"https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/finance-1324685443/finance-1324685443/crypto-kline-data-v2/20250724/{symbol}/{interval}/{symbol}_{interval}_{year}_compressed.json"
            elif year == 2025:
                if month <= 6:
                    # 2025年1-6月使用月度文件
                    url = f"https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/20250726/{symbol}/{interval}/{symbol}_{interval}_{month:02d}_{year}_compressed.json"
                else:
                    # 2025年7月+使用每日文件
                    url = f"https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/20250726/{symbol}/{interval}/daily/{symbol}_{interval}_{month:02d}-{day:02d}_{year}_compressed.json"
            else:
                logger.warning(f"不支持的年份: {year}")
                return pd.DataFrame()

            logger.info(f"正在获取历史数据: {url}")
            
            # 获取数据，使用较长的超时时间
            response = requests.get(url, timeout=60)
            response.raise_for_status()
            
            # 处理分块传输编码
            content_text = response.text
            
            # 清理分块传输的格式
            if content_text.startswith('{') and '\n' in content_text:
                # 查找第一行完整的JSON
                lines = content_text.split('\n')
                json_line = lines[0]
                for line in lines:
                    if line.strip().startswith('{') and line.strip().endswith('}'):
                        json_line = line.strip()
                        break
            else:
                json_line = content_text.strip()
            
            # 解析JSON数据
            try:
                data = json.loads(json_line)
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败，内容预览: {json_line[:200]}...")
                return pd.DataFrame()
            
            # 检查数据格式
            if isinstance(data, dict) and 'data' in data:
                # 新格式：包含metadata和data
                kline_data = data['data']
                logger.info(f"检测到新格式数据，记录数: {len(kline_data)}")
            elif isinstance(data, list):
                # 旧格式：直接是K线数组
                kline_data = data
                logger.info(f"检测到旧格式数据，记录数: {len(kline_data)}")
            else:
                logger.warning(f"未知数据格式: {type(data)}")
                return pd.DataFrame()
            
            if not kline_data:
                logger.warning(f"未找到K线数据: {symbol} {target_date_str}")
                return pd.DataFrame()
            
            # 转换为DataFrame
            df = pd.DataFrame(kline_data)
            
            # 标准化列名（Binance格式：[timestamp, open, high, low, close, volume, ...]）
            if len(df.columns) >= 6:
                df.columns = ['open_time', 'open', 'high', 'low', 'close', 'volume'] + [f'col_{i}' for i in range(6, len(df.columns))]
                df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
            else:
                logger.error(f"数据列数不足: {len(df.columns)}")
                return pd.DataFrame()
            
            # 过滤指定日期的数据
            if 'open_time' in df.columns:
                target_date = pd.to_datetime(target_date_str).date()
                df = df[df['open_time'].dt.date == target_date]
            
            # 确保数据类型正确
            numeric_cols = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_cols:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 去除无效数据
            df = df.dropna()
            
            logger.info(f"成功获取到 {symbol} {target_date_str} 的数据: {len(df)}条记录")
            return df
            
        except requests.exceptions.RequestException as e:
            logger.error(f"HTTP请求失败 {symbol} {date}: {e}")
            return pd.DataFrame()
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败 {symbol} {date}: {e}")
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"获取单日数据失败 {symbol} {date}: {e}")
            return pd.DataFrame()
    
    def _format_kline_data(self, df: pd.DataFrame) -> List[Dict]:
        """格式化K线数据"""
        if df.empty:
            return []
        
        return df[['open_time', 'open', 'high', 'low', 'close', 'volume']].to_dict('records')
    
    def _interpret_rsi(self, rsi_value: float) -> str:
        """解释RSI指标"""
        if rsi_value >= 70:
            return "超买区域，可能面临回调压力"
        elif rsi_value <= 30:
            return "超卖区域，可能出现反弹机会"
        elif rsi_value >= 50:
            return "偏强势区域，趋势向上"
        else:
            return "偏弱势区域，趋势向下"
    
    def _interpret_kdj(self, k: float, d: float, j: float) -> str:
        """解释KDJ指标"""
        if k >= 80 and d >= 80:
            return "超买状态，注意风险"
        elif k <= 20 and d <= 20:
            return "超卖状态，关注反弹"
        elif k > d:
            return "金叉信号，偏向看多"
        else:
            return "死叉信号，偏向看空"
    
    def _interpret_macd(self, line: float, signal: float, histogram: float) -> str:
        """解释MACD指标"""
        if line > signal and histogram > 0:
            return "多头趋势，MACD金叉"
        elif line < signal and histogram < 0:
            return "空头趋势，MACD死叉"
        elif histogram > 0:
            return "动能偏强，关注突破"
        else:
            return "动能偏弱，谨慎操作"
    
    def _generate_comparison_analysis(self, 
                                    target_indicators: Dict, 
                                    historical_indicators: Dict) -> Dict:
        """生成对比分析报告"""
        try:
            comparison = {
                'rsi_comparison': [],
                'kdj_comparison': [],
                'macd_comparison': [],
                'summary': {}
            }
            
            # RSI对比
            if 'rsi' in target_indicators:
                target_rsi = target_indicators['rsi']['value']
                historical_rsi_values = []
                
                for year, indicators in historical_indicators.items():
                    if 'rsi' in indicators:
                        historical_rsi_values.append({
                            'year': year,
                            'value': indicators['rsi']['value']
                        })
                
                comparison['rsi_comparison'] = {
                    'target_value': target_rsi,
                    'historical_values': historical_rsi_values,
                    'average_historical': np.mean([h['value'] for h in historical_rsi_values]) if historical_rsi_values else 50,
                    'vs_average': target_rsi - np.mean([h['value'] for h in historical_rsi_values]) if historical_rsi_values else 0
                }
            
            # 类似地处理KDJ和MACD对比...
            
            return comparison
            
        except Exception as e:
            logger.error(f"生成对比分析失败: {e}")
            return {'error': str(e)}


# 使用示例
if __name__ == "__main__":
    service = HistoricalAnalysisService()
    
    # 测试历史同期数据获取
    result = service.get_historical_same_period_data('2024-01-15', 'BTCUSDT', 3)
    print("历史同期数据:", result)
    
    # 测试技术指标计算
    indicators = service.calculate_period_indicators('2024-01-15', 'BTCUSDT', 3)
    print("技术指标:", indicators)