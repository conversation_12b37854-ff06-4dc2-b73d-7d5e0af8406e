import React from 'react';

const SimpleAIAnalyzer = ({ indicatorData, symbol, timeframe }) => {
  return (
    <div style={{ 
      background: 'rgba(255, 255, 255, 0.03)',
      border: '1px solid rgba(255, 255, 255, 0.08)',
      borderRadius: '12px',
      padding: '16px',
      color: '#fff'
    }}>
      <h3 style={{ color: '#fff', marginBottom: '16px' }}>AI分析结果</h3>
      
      {!indicatorData ? (
        <p style={{ color: '#999' }}>等待指标数据计算完成...</p>
      ) : (
        <div>
          <p><strong>分析参数:</strong> {symbol} | {timeframe}</p>
          <p><strong>数据年份:</strong> {Object.keys(indicatorData).length}年</p>
          <p><strong>状态:</strong> <span style={{ color: '#52c41a' }}>数据已加载</span></p>
        </div>
      )}
    </div>
  );
};

export default SimpleAIAnalyzer;
