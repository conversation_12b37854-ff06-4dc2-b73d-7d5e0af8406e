#!/usr/bin/env python3
"""
进程健康监控和断点检测修复
"""

import os
import psutil
import logging
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Optional, Tuple

class ProcessHealthMonitor:
    """进程健康监控器"""
    
    def __init__(self, work_dir: str):
        self.work_dir = Path(work_dir)
        self.logger = self._setup_logger()
        self.health_file = self.work_dir / "logs" / "health_status.json"
        
    def _setup_logger(self):
        logger = logging.getLogger('HealthMonitor')
        handler = logging.FileHandler(self.work_dir / 'logs' / 'health_monitor.log')
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
        return logger
    
    def check_duplicate_servers(self) -> List[Dict]:
        """检查重复的Django服务器"""
        servers = []
        
        for proc in psutil.process_iter(['pid', 'cmdline', 'create_time', 'memory_info']):
            try:
                if proc.info['cmdline']:
                    cmdline = ' '.join(proc.info['cmdline'])
                    if 'runserver' in cmdline and 'manage.py' in cmdline:
                        servers.append({
                            'pid': proc.info['pid'],
                            'cmdline': cmdline,
                            'create_time': datetime.fromtimestamp(proc.info['create_time']),
                            'memory_mb': proc.info['memory_info'].rss / 1024 / 1024
                        })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
                
        return servers
    
    def resolve_duplicate_servers(self) -> bool:
        """解决重复服务器问题"""
        servers = self.check_duplicate_servers()
        
        if len(servers) <= 1:
            return True
            
        self.logger.warning(f"发现 {len(servers)} 个Django服务器进程")
        
        # 按创建时间排序，保留最新的
        servers.sort(key=lambda x: x['create_time'], reverse=True)
        
        keep_server = servers[0]
        kill_servers = servers[1:]
        
        for server in kill_servers:
            try:
                self.logger.info(f"终止旧服务器: PID {server['pid']}")
                proc = psutil.Process(server['pid'])
                proc.terminate()
                proc.wait(timeout=10)
                
            except (psutil.NoSuchProcess, psutil.TimeoutExpired) as e:
                try:
                    proc.kill()
                    self.logger.warning(f"强制杀死进程 {server['pid']}")
                except psutil.NoSuchProcess:
                    pass
                    
        self.logger.info(f"保留服务器: PID {keep_server['pid']}")
        return True
    
    def check_systemd_service_health(self) -> Dict:
        """检查systemd服务健康状态"""
        try:
            result = subprocess.run([
                'systemctl', 'is-active', 'crypto-collector.service'
            ], capture_output=True, text=True)
            
            is_active = result.stdout.strip() == 'active'
            
            # 获取详细状态
            status_result = subprocess.run([
                'systemctl', 'status', 'crypto-collector.service', '--no-pager'
            ], capture_output=True, text=True)
            
            return {
                'is_active': is_active,
                'status_output': status_result.stdout,
                'last_check': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"检查systemd服务失败: {e}")
            return {'error': str(e)}
    
    def detect_breakpoints_and_gaps(self) -> Dict:
        """检测数据断点和缺失"""
        gaps = {}
        
        try:
            # 检查AI预测记录的连续性
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'finance_project.settings')
            import django
            django.setup()
            
            from ai_analysis.models import PredictionRecord, AutoPredictionTask
            from django.utils import timezone
            
            # 检查每个币种的预测连续性
            for task in AutoPredictionTask.objects.filter(is_active=True):
                symbol = task.symbol
                
                # 获取最近的预测记录
                recent_records = PredictionRecord.objects.filter(
                    symbol=symbol,
                    prediction_status='completed'
                ).order_by('-created_at')[:10]
                
                if recent_records:
                    latest = recent_records[0]
                    expected_next = latest.created_at + timedelta(minutes=15)
                    now = timezone.now()
                    
                    if now > expected_next + timedelta(minutes=5):  # 5分钟容错
                        gap_minutes = (now - expected_next).total_seconds() / 60
                        gaps[symbol] = {
                            'type': 'prediction_gap',
                            'last_prediction': latest.created_at.isoformat(),
                            'gap_minutes': gap_minutes,
                            'expected_next': expected_next.isoformat()
                        }
                        
            # 检查处理中的卡住任务
            stuck_tasks = PredictionRecord.objects.filter(
                prediction_status='processing',
                created_at__lt=timezone.now() - timedelta(hours=1)
            )
            
            if stuck_tasks.exists():
                gaps['stuck_tasks'] = {
                    'type': 'stuck_processing',
                    'count': stuck_tasks.count(),
                    'oldest': stuck_tasks.order_by('created_at').first().created_at.isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"检测断点失败: {e}")
            gaps['error'] = str(e)
            
        return gaps
    
    def auto_repair_gaps(self, gaps: Dict) -> Dict:
        """自动修复检测到的问题"""
        repairs = {}
        
        for key, gap_info in gaps.items():
            if gap_info.get('type') == 'stuck_processing':
                try:
                    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'finance_project.settings')
                    import django
                    django.setup()
                    
                    from ai_analysis.models import PredictionRecord
                    from django.utils import timezone
                    
                    # 清理卡住的任务
                    stuck_count = PredictionRecord.objects.filter(
                        prediction_status='processing',
                        created_at__lt=timezone.now() - timedelta(hours=1)
                    ).update(prediction_status='failed')
                    
                    repairs[key] = {
                        'action': 'cleared_stuck_tasks',
                        'count': stuck_count,
                        'success': True
                    }
                    
                    self.logger.info(f"清理了 {stuck_count} 个卡住的预测任务")
                    
                except Exception as e:
                    repairs[key] = {
                        'action': 'clear_stuck_tasks',
                        'success': False,
                        'error': str(e)
                    }
                    
            elif gap_info.get('type') == 'prediction_gap':
                # 对于预测缺失，记录但不自动修复（避免重复预测）
                repairs[key] = {
                    'action': 'logged_gap',
                    'gap_minutes': gap_info['gap_minutes'],
                    'success': True,
                    'note': '预测缺失已记录，等待下次调度'
                }
                
        return repairs
    
    def comprehensive_health_check(self) -> Dict:
        """综合健康检查"""
        health_status = {
            'timestamp': datetime.now().isoformat(),
            'checks': {}
        }
        
        # 1. 检查重复服务器
        servers = self.check_duplicate_servers()
        health_status['checks']['duplicate_servers'] = {
            'count': len(servers),
            'servers': servers,
            'status': 'warning' if len(servers) > 1 else 'ok'
        }
        
        # 2. 检查systemd服务
        systemd_status = self.check_systemd_service_health()
        health_status['checks']['systemd_service'] = systemd_status
        
        # 3. 检查断点和缺失
        gaps = self.detect_breakpoints_and_gaps()
        health_status['checks']['data_gaps'] = {
            'gaps': gaps,
            'status': 'warning' if gaps else 'ok'
        }
        
        # 4. 系统资源检查
        health_status['checks']['system_resources'] = {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_percent': psutil.disk_usage('/').percent,
            'status': 'ok'
        }
        
        # 5. 自动修复
        # 初始化 auto_repairs 字典
        health_status['auto_repairs'] = {}
        
        if gaps:
            repairs = self.auto_repair_gaps(gaps)
            health_status['auto_repairs'].update(repairs)
            
        # 6. 解决重复服务器
        if len(servers) > 1:
            self.resolve_duplicate_servers()
            health_status['auto_repairs']['duplicate_servers'] = {
                'action': 'resolved_duplicates',
                'success': True
            }
            
        # 保存健康状态
        try:
            import json
            with open(self.health_file, 'w') as f:
                json.dump(health_status, f, indent=2, default=str)
        except Exception as e:
            self.logger.error(f"保存健康状态失败: {e}")
            
        return health_status


def main():
    """主函数"""
    monitor = ProcessHealthMonitor("/home/<USER>/qiyuai-web/finance_backend")
    
    print("🏥 执行综合健康检查...")
    health_status = monitor.comprehensive_health_check()
    
    # 输出关键信息
    checks = health_status['checks']
    
    print(f"📊 检查结果 ({health_status['timestamp']}):")
    print(f"  Django服务器: {checks['duplicate_servers']['count']} 个")
    print(f"  Systemd服务: {'✅' if checks['systemd_service'].get('is_active') else '❌'}")
    print(f"  数据缺失: {len(checks['data_gaps']['gaps'])} 个")
    print(f"  CPU使用率: {checks['system_resources']['cpu_percent']:.1f}%")
    print(f"  内存使用率: {checks['system_resources']['memory_percent']:.1f}%")
    
    # 显示自动修复结果
    if 'auto_repairs' in health_status:
        print("🔧 自动修复:")
        for key, repair in health_status['auto_repairs'].items():
            status = "✅" if repair.get('success') else "❌"
            print(f"  {key}: {status} {repair.get('action', 'unknown')}")
    
    print("✅ 健康检查完成")


if __name__ == "__main__":
    main()