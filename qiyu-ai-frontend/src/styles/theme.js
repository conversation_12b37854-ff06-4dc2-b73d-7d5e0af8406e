import { theme } from 'antd';

// 基础主题配置
export const baseTheme = {
  colors: {
    primary: '#447A53',
    secondary: '#016A86',
    border: '#555555',

    // 图表颜色配置
    chart: {
      up: '#00C853',      // 上涨颜色（绿色）
      down: '#FF1744',    // 下跌颜色（红色）
      background: '#0f0f0f',
      grid: '#333333',
      text: '#ffffff',
      volume: '#666666',
    },

    // 深色模式配色
    dark: {
      background: '#0f0f0f',
      surface: '#1a1a1a',
      text: '#ffffff',
      textSecondary: '#b3b3b3',
      divider: '#333333',
    }
  },
  
  shadows: {
    sm: '0 2px 8px rgba(0, 0, 0, 0.15)',
    md: '0 4px 16px rgba(0, 0, 0, 0.2)',
    lg: '0 8px 32px rgba(0, 0, 0, 0.3)',
  },
  
  breakpoints: {
    xs: '480px',
    sm: '768px',
    md: '992px',
    lg: '1200px',
    xl: '1600px',
  }
};

export const antdTheme = {
  algorithm: theme.darkAlgorithm,
  token: {
    colorPrimary: '#447A53',
    colorBgContainer: '#1a1a1a',
    colorBgElevated: '#1a1a1a',
    colorBgLayout: '#0f0f0f',
    colorText: '#ffffff',
    colorTextSecondary: '#b3b3b3',
    colorBorder: '#555555',
    colorBorderSecondary: '#333333',
    borderRadius: 8,
    fontSize: 14,
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
  },
  components: {
    Layout: {
      bodyBg: '#0f0f0f',
      headerBg: '#1a1a1a',
      siderBg: '#1a1a1a',
    },
    Button: {
      primaryColor: '#ffffff',
    }
  }
};

// 导出 theme 以保持向后兼容
export { baseTheme as theme };
