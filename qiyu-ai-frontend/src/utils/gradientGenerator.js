import chroma from 'chroma-js';

/**
 * 使用 chroma-js 生成自然的渐变色
 * @param {string} startColor - 起始颜色
 * @param {string} endColor - 结束颜色
 * @param {number} steps - 渐变步数
 * @param {string} mode - 颜色空间模式 ('lab', 'hsl', 'rgb', 'lch')
 * @param {string} direction - 渐变方向 (默认 '180deg')
 * @returns {string} CSS 渐变字符串
 */
export const generateNaturalGradient = (
  startColor,
  endColor,
  steps = 8,
  mode = 'hsl',
  direction = '360deg'
) => {
  // 使用 chroma-js 生成颜色序列
  const colors = chroma.scale([startColor, endColor])
    .mode(mode)
    .colors(steps);

  // 生成渐变停止点
  const gradientStops = colors.map((color, index) => {
    const percentage = (index / (steps - 1)) * 100;
    return `${color} ${percentage}%`;
  }).join(', ');

  return `linear-gradient(${direction}, ${gradientStops})`;
};
