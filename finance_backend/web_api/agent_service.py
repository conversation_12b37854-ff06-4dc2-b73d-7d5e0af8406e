"""
代理返利服务
处理邀请码注册、代理关系绑定和返利计算
"""

import logging
from decimal import Decimal
from django.db import transaction
from django.db import models
from django.utils import timezone
from .models import WebUser, AgentRelation, CommissionRecord, CreditTransaction

logger = logging.getLogger(__name__)


class AgentService:
    """代理返利服务类"""
    
    # 返利比例配置
    LEVEL1_COMMISSION_RATE = Decimal('0.3333')  # 1/3
    LEVEL2_COMMISSION_RATE = Decimal('0.2000')  # 1/5
    
    @classmethod
    def bind_agent_relation(cls, user, invite_code):
        """
        绑定代理关系
        
        Args:
            user: 新注册的用户
            invite_code: 使用的邀请码
            
        Returns:
            tuple: (success, message)
        """
        if not invite_code:
            return True, "未使用邀请码"
        
        try:
            # 查找邀请人
            parent_agent = WebUser.objects.get(invite_code=invite_code)
            
            # 不能邀请自己
            if parent_agent.id == user.id:
                return False, "不能使用自己的邀请码"
            
            # 检查是否已有代理关系
            if hasattr(user, 'agent_relation'):
                return False, "用户已有代理关系"
            
            with transaction.atomic():
                # 查找二级邀请人
                grandparent_agent = None
                if hasattr(parent_agent, 'agent_relation') and parent_agent.agent_relation.parent_agent:
                    grandparent_agent = parent_agent.agent_relation.parent_agent
                
                # 创建代理关系
                AgentRelation.objects.create(
                    user=user,
                    parent_agent=parent_agent,
                    grandparent_agent=grandparent_agent,
                    invite_code_used=invite_code,
                    level=1
                )
                
                logger.info(f"代理关系绑定成功: {user.phone} -> {parent_agent.phone}")
                
                # 如果有二级邀请人，也记录日志
                if grandparent_agent:
                    logger.info(f"二级代理关系: {user.phone} -> {grandparent_agent.phone}")
                
                return True, "代理关系绑定成功"
                
        except WebUser.DoesNotExist:
            return False, "邀请码不存在"
        except Exception as e:
            logger.error(f"绑定代理关系失败: {e}")
            return False, "绑定代理关系失败"
    
    @classmethod
    def calculate_and_distribute_commission(cls, order_user, amount, order_id=""):
        """
        计算并分发返利
        
        Args:
            order_user: 下单用户
            amount: 订单金额
            order_id: 订单ID
            
        Returns:
            tuple: (success, message, commission_records)
        """
        try:
            commission_records = []
            
            # 检查用户是否有代理关系
            if not hasattr(order_user, 'agent_relation'):
                return True, "用户无代理关系", []
            
            agent_relation = order_user.agent_relation
            
            with transaction.atomic():
                # 一级返利 (1/3)
                if agent_relation.parent_agent:
                    level1_commission = amount * cls.LEVEL1_COMMISSION_RATE
                    
                    commission_record = CommissionRecord.objects.create(
                        agent=agent_relation.parent_agent,
                        invitee=order_user,
                        order_user=order_user,
                        commission_type='level1',
                        original_amount=amount,
                        commission_amount=level1_commission,
                        commission_rate=cls.LEVEL1_COMMISSION_RATE,
                        order_id=order_id
                    )
                    commission_records.append(commission_record)
                    
                    # 发放返利到一级代理
                    cls._distribute_commission_to_agent(
                        agent_relation.parent_agent, 
                        level1_commission, 
                        f"一级返利 - {order_user.phone}"
                    )
                    
                    logger.info(f"一级返利: {agent_relation.parent_agent.phone} 获得 {level1_commission}")
                
                # 二级返利 (1/5)
                if agent_relation.grandparent_agent:
                    level2_commission = amount * cls.LEVEL2_COMMISSION_RATE
                    
                    commission_record = CommissionRecord.objects.create(
                        agent=agent_relation.grandparent_agent,
                        invitee=order_user,
                        order_user=order_user,
                        commission_type='level2',
                        original_amount=amount,
                        commission_amount=level2_commission,
                        commission_rate=cls.LEVEL2_COMMISSION_RATE,
                        order_id=order_id
                    )
                    commission_records.append(commission_record)
                    
                    # 发放返利到二级代理
                    cls._distribute_commission_to_agent(
                        agent_relation.grandparent_agent, 
                        level2_commission, 
                        f"二级返利 - {order_user.phone}"
                    )
                    
                    logger.info(f"二级返利: {agent_relation.grandparent_agent.phone} 获得 {level2_commission}")
                
                # 标记返利为已发放
                for record in commission_records:
                    record.is_paid = True
                    record.paid_at = timezone.now()
                    record.save()
                
                return True, "返利分发成功", commission_records
                
        except Exception as e:
            logger.error(f"返利分发失败: {e}")
            return False, f"返利分发失败: {e}", []
    
    @classmethod
    def _distribute_commission_to_agent(cls, agent, amount, description):
        """
        向代理发放返利
        
        Args:
            agent: 代理用户
            amount: 返利金额
            description: 描述
        """
        # 转换为积分 (假设1元=1积分)
        credits = int(amount)
        
        # 更新用户积分
        balance_before = agent.credits
        agent.credits += credits
        agent.save()
        
        # 记录积分交易
        CreditTransaction.objects.create(
            user=agent,
            type='commission',
            amount=credits,
            balance_before=balance_before,
            balance_after=agent.credits,
            description=description
        )
    
    @classmethod
    def get_agent_stats(cls, user):
        """
        获取代理统计信息
        
        Args:
            user: 用户对象
            
        Returns:
            dict: 统计信息
        """
        try:
            # 直接邀请人数
            direct_invitees_count = user.direct_invitees.count()
            
            # 二级邀请人数
            indirect_invitees_count = user.indirect_invitees.count()
            
            # 总返利金额
            total_commission = CommissionRecord.objects.filter(
                agent=user,
                is_paid=True
            ).aggregate(
                total=models.Sum('commission_amount')
            )['total'] or Decimal('0')
            
            # 本月返利
            current_month = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            monthly_commission = CommissionRecord.objects.filter(
                agent=user,
                is_paid=True,
                paid_at__gte=current_month
            ).aggregate(
                total=models.Sum('commission_amount')
            )['total'] or Decimal('0')
            
            # 最近返利记录
            recent_commissions = CommissionRecord.objects.filter(
                agent=user,
                is_paid=True
            ).order_by('-paid_at')[:10]
            
            return {
                'direct_invitees_count': direct_invitees_count,
                'indirect_invitees_count': indirect_invitees_count,
                'total_invitees_count': direct_invitees_count + indirect_invitees_count,
                'total_commission': float(total_commission),
                'monthly_commission': float(monthly_commission),
                'recent_commissions': [
                    {
                        'amount': float(record.commission_amount),
                        'type': record.get_commission_type_display(),
                        'from_user': record.order_user.nickname or record.order_user.phone,
                        'created_at': record.paid_at.strftime('%Y-%m-%d %H:%M:%S') if record.paid_at else ''
                    }
                    for record in recent_commissions
                ]
            }
            
        except Exception as e:
            logger.error(f"获取代理统计失败: {e}")
            return {
                'direct_invitees_count': 0,
                'indirect_invitees_count': 0,
                'total_invitees_count': 0,
                'total_commission': 0,
                'monthly_commission': 0,
                'recent_commissions': []
            }