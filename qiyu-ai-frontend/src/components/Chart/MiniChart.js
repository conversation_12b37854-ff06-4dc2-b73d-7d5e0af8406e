import React, { useEffect, useRef, useState } from 'react';
import { init, dispose } from 'klinecharts';
import klineDataService from '../../services/KlineDataService';
import IndicatorToggle from './IndicatorToggle';
import { useRealTimeData } from '../../hooks/useRealTimeData';
import errorLogger from '../../utils/errorLogger';
import './MiniChart.css';

const MiniChart = ({
  symbol = 'BTCUSDT',
  year = '2025',
  width = 300,
  height = 200,
  showSettings = true,
  chartId = '',
  showMACD = false,
  globalTimeframe = '1d',
  enableRealTime = false, // 是否启用实时数据
  externalIndicators = null, // 外部传入的指标状态
  onChartReady = null,
  onChartDestroy = null
}) => {
  const chartRef = useRef(null);
  const kLineChart = useRef(null);
  const currentConfigRef = useRef(null); // 跟踪当前配置，防止重复初始化
  const isLoadingRef = useRef(false); // 防止重复数据加载
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dataStats, setDataStats] = useState(null);
  
  // 实时数据Hook（仅当年数据启用）
  const shouldUseRealTime = enableRealTime && (year === '2025' || year === 'current');
  useRealTimeData(
    symbol,
    globalTimeframe,
    kLineChart.current,
    shouldUseRealTime
  );
  // 使用外部指标状态或默认状态
  const [activeIndicators, setActiveIndicators] = useState(() => {
    if (externalIndicators) {
      // 如果有外部指标状态，使用外部状态但确保MACD只在当年图表显示
      return {
        ...externalIndicators,
        MACD: showMACD && externalIndicators.MACD
      };
    }
    // 默认指标状态
    return {
      MA: true,
      VOL: true,
      BOLL: true,
      MACD: showMACD,
      RSI: true,
      KDJ: true,
      StochRSI: false
    };
  });

  // 监听外部指标状态变化
  useEffect(() => {
    if (externalIndicators) {
      setActiveIndicators(prev => ({
        ...externalIndicators,
        MACD: showMACD && externalIndicators.MACD // 确保MACD只在当年图表显示
      }));
    }
  }, [externalIndicators, showMACD]);
  
  // 获取当前图表允许的指标
  const getAllowedIndicators = () => {
    const baseIndicators = ['MA', 'VOL', 'BOLL'];
    const optionalIndicators = ['RSI', 'KDJ', 'StochRSI'];
    
    if (showMACD) {
      return [...baseIndicators, 'MACD', ...optionalIndicators];
    }
    return [...baseIndicators, ...optionalIndicators];
  };
  
  // 处理指标切换
  const handleIndicatorChange = (indicatorKey, checked) => {
    setActiveIndicators(prev => ({
      ...prev,
      [indicatorKey]: checked
    }));
    
    // 更新图表指标显示
    if (kLineChart.current) {
      if (checked) {
        addIndicatorToChart(indicatorKey);
      } else {
        removeIndicatorFromChart(indicatorKey);
      }
    }
  };
  
  // 添加默认指标 - 临时禁用避免API错误
  const addDefaultIndicators = () => {
    if (!kLineChart.current) return;
    
    console.log('⏳ 指标功能暂时禁用，等待API修复');
    // try {
    //   // 后续会修复 klinecharts API 调用
    //   if (activeIndicators.MA) {
    //     kLineChart.current.addIndicator({ name: 'MA', calcParams: [5, 10, 20, 60] });
    //   }
    // } catch (error) {
    //   console.warn('添加默认指标失败:', error);
    // }
  };
  
  // 添加指标到图表
  const addIndicatorToChart = (indicatorKey) => {
    if (!kLineChart.current) return;
    
    try {
      switch (indicatorKey) {
        case 'MA':
          kLineChart.current.addIndicator({ name: 'MA', calcParams: [5, 10, 20, 60] });
          break;
        case 'VOL':
          kLineChart.current.addIndicator({ name: 'VOL' }, 'vol_pane');
          break;
        case 'BOLL':
          kLineChart.current.addIndicator({ name: 'BOLL', calcParams: [20, 2] });
          break;
        case 'MACD':
          if (showMACD) {
            kLineChart.current.addIndicator({ name: 'MACD', calcParams: [12, 26, 9] }, 'macd_pane');
          }
          break;
        case 'RSI':
          kLineChart.current.addIndicator({ name: 'RSI', calcParams: [14] }, 'rsi_pane');
          break;
        case 'KDJ':
          kLineChart.current.addIndicator({ name: 'KDJ', calcParams: [9, 3, 3] }, 'kdj_pane');
          break;
        case 'StochRSI':
          kLineChart.current.addIndicator({ name: 'StochRSI', calcParams: [14, 14, 3, 3] }, 'stochrsi_pane');
          break;
        default:
          console.warn(`未知指标: ${indicatorKey}`);
      }
      console.log(`添加指标: ${indicatorKey}`);
    } catch (error) {
      console.warn(`添加指标${indicatorKey}失败:`, error);
    }
  };
  
  // 从图表移除指标
  const removeIndicatorFromChart = (indicatorKey) => {
    if (!kLineChart.current) return;
    
    try {
      // 使用正确的 removeIndicator API
      kLineChart.current.removeIndicator({ name: indicatorKey });
      console.log(`移除指标: ${indicatorKey}`);
    } catch (error) {
      console.warn(`移除指标${indicatorKey}失败:`, error);
    }
  };

  // 获取年份显示文本
  const getYearDisplayText = () => {
    if (year === 'overview' || year === '8年概览') {
      return '8年概览';
    }
    return `${year}年`;
  };

  // 标准化年份值，确保比较一致性
  const normalizeYear = (yearValue) => {
    if (typeof yearValue === 'number') {
      return yearValue.toString();
    }
    return yearValue;
  };

  // 获取币种显示名称
  const getSymbolDisplayName = () => {
    const symbolNames = {
      'BTCUSDT': 'BTC',
      'ETHUSDT': 'ETH', 
      'DOGEUSDT': 'DOGE',
      'LTCUSDT': 'LTC',
      'TRUMPUSDT': 'TRUMP',
      'SOLUSDT': 'SOL',
      'XRPUSDT': 'XRP',
      'PIUSDT': 'PI'
    };
    return symbolNames[symbol] || symbol;
  };

  // 预加载数据统计
  const preloadDataStats = async () => {
    try {
      let data = null;
      
      // 根据年份选择数据源
      if (year === '2025' || year === 'current') {
        try {
          data = await klineDataService.getCurrentYearData(symbol, '1d');
        } catch (err) {
          data = await klineDataService.getYearlyData(symbol, 2024);
        }
      } else if (year === 'overview' || year === '8年概览') {
        data = await klineDataService.getOverviewData(symbol);
      } else {
        const yearNum = parseInt(year);
        if (!isNaN(yearNum)) {
          data = await klineDataService.getYearlyData(symbol, yearNum);
        }
      }
      
      if (data && data.data && data.data.length > 0) {
        const stats = klineDataService.getDataStats(data);
        setDataStats(stats);
      }
    } catch (err) {
      console.warn(`Failed to preload stats for ${symbol}-${year}:`, err);
    }
  };

  // 初始化图表 - 修复版本，避免无限循环
  useEffect(() => {
    const chartElement = chartRef.current;
    if (!chartElement) return;

    let isMounted = true; // 防止组件卸载后设置状态
    let loadingTimeout = null; // 加载超时定时器

    const initChart = async () => {
      try {
        const configKey = `${symbol}-${normalizeYear(year)}`;

        // 检查是否已经在初始化相同的配置
        if (currentConfigRef.current === configKey) {
          console.log(`⏭️ ${configKey} 已在初始化中，跳过重复初始化`);
          return;
        }

        currentConfigRef.current = configKey;

        if (isMounted) setLoading(true);
        if (isMounted) setError(null);

        console.log(`🔄 ${configKey} 图表初始化...`);

        // 设置加载超时（30秒）
        loadingTimeout = setTimeout(() => {
          if (isMounted && loading) {
            console.warn(`⏰ ${symbol}-${year} 数据加载超时`);
            setError('数据加载超时，请刷新重试');
            setLoading(false);
          }
        }, 30000);

        // 创建图表实例，使用最简配置
        kLineChart.current = init(chartElement);

        // 延迟应用样式，确保图表完全初始化
        setTimeout(() => {
          if (kLineChart.current && isMounted) {
            try {
              kLineChart.current.setStyles({
                grid: {
                  horizontal: { color: 'rgba(255, 255, 255, 0.1)' },
                  vertical: { color: 'rgba(255, 255, 255, 0.1)' }
                },
                candle: {
                  priceMark: {
                    high: { color: '#26a69a' },
                    low: { color: '#ef5350' }
                  }
                },
                xAxis: {
                  axisLine: { color: 'rgba(255, 255, 255, 0.2)' },
                  tickText: { color: 'rgba(255, 255, 255, 0.6)', size: 10 }
                },
                yAxis: {
                  axisLine: { color: 'rgba(255, 255, 255, 0.2)' },
                  tickText: { color: 'rgba(255, 255, 255, 0.6)', size: 10 }
                }
              });
            } catch (styleError) {
              console.warn('样式设置失败，使用默认样式:', styleError);
            }
          }
        }, 50);
        
        // 加载真实数据
        const klineData = await loadRealData();

        if (!isMounted || !kLineChart.current) return;


        
        console.log(`📊 ${symbol}-${year} 数据:`, {
          length: klineData.length,
          sample: klineData.slice(0, 2)
        });
        
        if (klineData.length > 0) {
          // 验证并清理数据格式
          const cleanedData = klineData.map(item => ({
            timestamp: typeof item.timestamp === 'number' ? item.timestamp : Date.now(),
            open: parseFloat(item.open) || 0,
            high: parseFloat(item.high) || 0,
            low: parseFloat(item.low) || 0,
            close: parseFloat(item.close) || 0,
            volume: parseFloat(item.volume) || 0
          })).filter(item =>
            item.timestamp > 0 &&
            item.open > 0 &&
            item.high > 0 &&
            item.low > 0 &&
            item.close > 0
          );

          console.log(`🔍 数据清理完成:`, {
            原始数量: klineData.length,
            清理后数量: cleanedData.length,
            样本: cleanedData.slice(0, 2)
          });

          if (cleanedData.length === 0) {
            console.error('❌ 清理后无有效数据');
            if (isMounted) setError('数据格式错误');
            return;
          }

          // 应用清理后的数据
          try {
            kLineChart.current.applyNewData(cleanedData);
            console.log(`✅ 数据应用成功: ${cleanedData.length} 条记录`);

            // 延迟添加指标，避免阻塞
            setTimeout(() => {
              if (isMounted && kLineChart.current) {
                try {
                  kLineChart.current.resize();
                  addDefaultIndicators();

                  // 通知图表就绪
                  if (onChartReady && typeof onChartReady === 'function') {
                    onChartReady(kLineChart.current);
                  }
                  console.log(`✅ 图表完全就绪: ${configKey}`);
                } catch (indicatorError) {
                  console.warn('指标添加失败:', indicatorError);
                }
              }
            }, 100);
          } catch (dataError) {
            console.error('❌ 数据应用失败:', dataError);
            if (isMounted) setError('数据应用失败');
          }
        } else {
          if (isMounted) setError('暂无数据');
        }
        
        if (isMounted) {
          setLoading(false);
          console.log(`✅ ${configKey} 图表初始化完成`);
        }

        // 清除超时定时器
        if (loadingTimeout) {
          clearTimeout(loadingTimeout);
          loadingTimeout = null;
        }

      } catch (error) {
        console.error(`❌ ${symbol}-${year} 初始化失败:`, error);
        errorLogger.logChartError(`${symbol}-${year}`, error);

        if (isMounted) {
          setError(`初始化失败: ${error.message}`);
          setLoading(false);
        }

        // 清除超时定时器
        if (loadingTimeout) {
          clearTimeout(loadingTimeout);
          loadingTimeout = null;
        }
      }
    };

    initChart();

    // 清理函数
    return () => {
      isMounted = false;
      currentConfigRef.current = null; // 重置配置ref
      isLoadingRef.current = false; // 重置加载锁

      // 清除超时定时器
      if (loadingTimeout) {
        clearTimeout(loadingTimeout);
        loadingTimeout = null;
      }

      if (kLineChart.current && chartElement) {
        if (onChartDestroy && typeof onChartDestroy === 'function') {
          onChartDestroy();
        }
        dispose(chartElement);
        kLineChart.current = null;
      }
    };
  }, [symbol, normalizeYear(year)]); // 使用标准化的年份确保一致性

  // 监听容器尺寸变化，自动调整图表大小
  useEffect(() => {
    const handleResize = () => {
      if (kLineChart.current) {
        // 延迟调整，确保DOM更新完成
        setTimeout(() => {
          if (kLineChart.current) {
            kLineChart.current.resize();
          }
        }, 100);
      }
    };

    window.addEventListener('resize', handleResize);

    // 组件挂载后也调整一次
    handleResize();

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);



  // 加载真实数据
  const loadRealData = async () => {
    const configKey = `${symbol}-${normalizeYear(year)}`;

    // 防止重复加载
    if (isLoadingRef.current) {
      console.log(`⏭️ ${configKey} 数据正在加载中，跳过重复请求`);
      return [];
    }

    isLoadingRef.current = true;
    const startTime = Date.now();

    try {
      console.log(`📊 开始加载 ${configKey} 数据...`);
      let data = null;

      // 添加详细的调试信息
      console.log(`🔍 数据加载参数:`, {
        symbol,
        year,
        yearType: typeof year,
        isCurrentYear: year === '2025' || year === 'current',
        isOverview: year === 'overview' || year === '8年概览'
      });

      // 根据年份选择数据源
      if (year === '2025' || year === 'current') {
        try {
          console.log(`🔄 尝试加载 ${symbol} 2025年数据...`);
          data = await Promise.race([
            klineDataService.getCurrentYearData(symbol, '1d'),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('当年数据加载超时')), 5000)
            )
          ]);
          console.log(`✅ ${symbol} 2025年数据加载成功`);
        } catch (err) {
          console.warn(`⚠️ ${symbol} 2025年数据不可用，使用2024年数据:`, err.message);
          try {
            data = await Promise.race([
              klineDataService.getYearlyData(symbol, 2024),
              new Promise((_, reject) =>
                setTimeout(() => reject(new Error('2024年数据加载超时')), 5000)
              )
            ]);
            console.log(`✅ ${symbol} 2024年数据加载成功（作为2025年替代）`);
          } catch (fallbackErr) {
            console.error(`❌ ${symbol} 2024年数据也加载失败:`, fallbackErr.message);
            throw new Error('无法加载当年数据');
          }
        }
      } else if (year === 'overview' || year === '8年概览') {
        console.log(`🔄 尝试加载 ${symbol} 概览数据...`);
        data = await Promise.race([
          klineDataService.getOverviewData(symbol),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('概览数据加载超时')), 8000)
          )
        ]);
        console.log(`✅ ${symbol} 概览数据加载成功`);
      } else {
        const yearNum = parseInt(year);
        if (!isNaN(yearNum)) {
          console.log(`🔄 尝试加载 ${symbol} ${yearNum}年数据...`);
          data = await Promise.race([
            klineDataService.getYearlyData(symbol, yearNum),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error(`${yearNum}年数据加载超时`)), 8000)
            )
          ]);
          console.log(`✅ ${symbol} ${yearNum}年数据加载成功`);
        }
      }

      if (!data || !data.data || data.data.length === 0) {
        console.warn(`${symbol}-${year} 主数据源无数据，尝试概览数据...`);
        data = await Promise.race([
          klineDataService.getOverviewData(symbol),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('备用数据加载超时')), 10000)
          )
        ]);
        if (!data || !data.data || data.data.length === 0) {
          throw new Error('暂无数据');
        }
      }

      // 转换为K线图格式
      const klineData = klineDataService.convertToKlineFormat(data);
      const loadTime = Date.now() - startTime;
      console.log(`✅ ${configKey} 数据加载完成，耗时: ${loadTime}ms，数据量: ${klineData.length}`);
      return klineData;

    } catch (err) {
      const loadTime = Date.now() - startTime;
      console.error(`❌ ${configKey} 数据加载失败，耗时: ${loadTime}ms:`, err);
      errorLogger.logDataError(symbol, year, err);
      setError(err.message);
      return [];
    } finally {
      isLoadingRef.current = false; // 重置加载锁
    }
  };



  // 获取指标配置


  return (
    <div className="mini-chart" style={{ width, height }}>
      {/* 图表标题 */}
      <div className="mini-chart-header">
        <div className="chart-info">
          <span className="chart-year">{getYearDisplayText()}</span>
          {dataStats && (
            <span className={`chart-stats ${dataStats.price.change >= 0 ? 'positive' : 'negative'}`}>
              {dataStats.price.changePercent}%
            </span>
          )}
        </div>
        <div className="chart-controls">
          {showSettings && (
            <IndicatorToggle
              indicators={activeIndicators}
              onIndicatorChange={handleIndicatorChange}
              allowedIndicators={getAllowedIndicators()}
            />
          )}
        </div>
      </div>


      {/* 图表容器 */}
      <div className="mini-chart-container">
        {loading && (
          <div className="chart-loading">
            <div className="loading-spinner"></div>
            <span>正在加载{getSymbolDisplayName()}{getYearDisplayText()}数据...</span>
          </div>
        )}
        {error && (
          <div className="chart-error">
            <span>⚠️ {error}</span>
          </div>
        )}

        {/* 调试信息 */}
        {process.env.NODE_ENV === 'development' && (
          <div style={{
            position: 'absolute',
            top: '5px',
            right: '5px',
            background: 'rgba(0,0,0,0.7)',
            color: 'white',
            padding: '5px',
            fontSize: '10px',
            borderRadius: '3px',
            zIndex: 1000
          }}>
            {symbol}-{year} | {loading ? 'Loading...' : 'Ready'}
          </div>
        )}
        <div 
          ref={chartRef} 
          className="chart-canvas"
          style={{
            width: '100%',
            height: 'calc(100% - 30px)',
            display: (loading || error) ? 'none' : 'block'
          }}
        />
      </div>
    </div>
  );
};

export default MiniChart;
