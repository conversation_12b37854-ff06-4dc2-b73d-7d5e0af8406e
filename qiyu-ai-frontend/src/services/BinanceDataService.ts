interface KLineData {
  timestamp: number
  open: number
  high: number
  low: number
  close: number
  volume: number
  turnover?: number
}

interface BinanceKlineResponse {
  e: string      // Event type
  E: number      // Event time
  s: string      // Symbol
  k: {
    t: number    // Kline start time
    T: number    // Kline close time
    s: string    // Symbol
    i: string    // Interval
    f: number    // First trade ID
    L: number    // Last trade ID
    o: string    // Open price
    c: string    // Close price
    h: string    // High price
    l: string    // Low price
    v: string    // Base asset volume
    n: number    // Number of trades
    x: boolean   // Is this kline closed?
    q: string    // Quote asset volume
    V: string    // Taker buy base asset volume
    Q: string    // Taker buy quote asset volume
    B: string    // Ignore
  }
}

class BinanceDataService {
  private ws: WebSocket | null = null
  private subscribers: Map<string, (data: KLineData) => void> = new Map()
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000

  // 连接Binance WebSocket流
  connect(symbol: string, interval: string = '1m'): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const streamName = `${symbol.toLowerCase()}@kline_${interval}`
        const wsUrl = `wss://stream.binance.com:9443/ws/${streamName}`
        
        console.log(`🔌 连接Binance WebSocket: ${wsUrl}`)
        
        this.ws = new WebSocket(wsUrl)
        
        this.ws.onopen = () => {
          console.log('✅ Binance WebSocket连接成功')
          this.reconnectAttempts = 0
          resolve()
        }
        
        this.ws.onmessage = (event) => {
          try {
            const data: BinanceKlineResponse = JSON.parse(event.data)
            
            if (data.e === 'kline' && data.k.x) { // 只处理已关闭的K线
              const klineData: KLineData = {
                timestamp: data.k.t,
                open: parseFloat(data.k.o),
                high: parseFloat(data.k.h),
                low: parseFloat(data.k.l),
                close: parseFloat(data.k.c),
                volume: parseFloat(data.k.v),
                turnover: parseFloat(data.k.q)
              }
              
              // 通知所有订阅者
              this.subscribers.forEach((callback) => {
                callback(klineData)
              })
            }
          } catch (error) {
            console.error('❌ 解析Binance数据失败:', error)
          }
        }
        
        this.ws.onerror = (error) => {
          console.error('❌ Binance WebSocket错误:', error)
          reject(error)
        }
        
        this.ws.onclose = (event) => {
          console.log('🔌 Binance WebSocket连接关闭:', event.code, event.reason)
          
          // 自动重连
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++
            console.log(`🔄 尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`)
            
            setTimeout(() => {
              this.connect(symbol, interval).catch(console.error)
            }, this.reconnectDelay * this.reconnectAttempts)
          } else {
            console.error('❌ WebSocket重连失败，已达到最大重连次数')
          }
        }
        
      } catch (error) {
        console.error('❌ 创建WebSocket连接失败:', error)
        reject(error)
      }
    })
  }
  
  // 订阅数据更新
  subscribe(key: string, callback: (data: KLineData) => void) {
    this.subscribers.set(key, callback)
    console.log(`📡 订阅数据更新: ${key}`)
  }
  
  // 取消订阅
  unsubscribe(key: string) {
    this.subscribers.delete(key)
    console.log(`📡 取消订阅: ${key}`)
  }
  
  // 断开连接
  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
      this.subscribers.clear()
      console.log('🔌 Binance WebSocket已断开')
    }
  }
  
  // 获取连接状态
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN
  }
  
  // 获取历史K线数据（REST API）
  async getHistoricalData(
    symbol: string,
    interval: string = '1d',
    limit: number = 500
  ): Promise<KLineData[]> {
    try {
      const url = `https://api.binance.com/api/v3/klines?symbol=${symbol}&interval=${interval}&limit=${limit}`
      
      console.log(`📊 获取历史数据: ${url}`)
      
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      
      return data.map((item: any[]) => ({
        timestamp: item[0],
        open: parseFloat(item[1]),
        high: parseFloat(item[2]),
        low: parseFloat(item[3]),
        close: parseFloat(item[4]),
        volume: parseFloat(item[5]),
        turnover: parseFloat(item[7]) // Quote asset volume
      }))
      
    } catch (error) {
      console.error('❌ 获取历史数据失败:', error)
      throw error
    }
  }
  
  // 获取交易对信息
  async getSymbolInfo(symbol: string) {
    try {
      const url = `https://api.binance.com/api/v3/exchangeInfo?symbol=${symbol}`
      const response = await fetch(url)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      return data.symbols[0]
      
    } catch (error) {
      console.error('❌ 获取交易对信息失败:', error)
      throw error
    }
  }
  
  // 获取当前价格
  async getCurrentPrice(symbol: string): Promise<number> {
    try {
      const url = `https://api.binance.com/api/v3/ticker/price?symbol=${symbol}`
      const response = await fetch(url)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      return parseFloat(data.price)
      
    } catch (error) {
      console.error('❌ 获取当前价格失败:', error)
      throw error
    }
  }
}

// 单例模式
const binanceDataService = new BinanceDataService()

export default binanceDataService
export type { KLineData, BinanceKlineResponse }