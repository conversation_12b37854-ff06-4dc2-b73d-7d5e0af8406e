#!/usr/bin/env python3
"""
高级锁管理和防死锁机制
"""

import os
import time
import fcntl
import signal
import psutil
import logging
from pathlib import Path
from datetime import datetime, timedelta
from contextlib import contextmanager
from typing import Optional, Dict, Any

class AdvancedLockManager:
    """高级锁管理器 - 防止长时间锁死"""
    
    def __init__(self, lock_dir: str = "/tmp"):
        self.lock_dir = Path(lock_dir)
        self.logger = self._setup_logger()
        
    def _setup_logger(self):
        logger = logging.getLogger('AdvancedLock')
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
        return logger
    
    @contextmanager
    def acquire_lock(self, lock_name: str, timeout: int = 3600, force_after: int = 7200):
        """
        获取锁，带超时和强制释放机制
        
        Args:
            lock_name: 锁名称
            timeout: 正常超时时间（秒）
            force_after: 强制释放时间（秒）
        """
        lock_file = self.lock_dir / f"{lock_name}.lock"
        lock_info_file = self.lock_dir / f"{lock_name}.info"
        
        try:
            # 检查是否有过期的锁
            self._cleanup_expired_lock(lock_file, lock_info_file, force_after)
            
            # 尝试获取锁
            fd = os.open(str(lock_file), os.O_CREAT | os.O_EXCL | os.O_RDWR)
            
            try:
                # 应用文件锁
                fcntl.flock(fd, fcntl.LOCK_EX | fcntl.LOCK_NB)
                
                # 记录锁信息
                lock_info = {
                    'pid': os.getpid(),
                    'start_time': datetime.now().isoformat(),
                    'timeout': timeout,
                    'command': ' '.join(psutil.Process().cmdline()),
                }
                
                with open(lock_info_file, 'w') as f:
                    import json
                    json.dump(lock_info, f, indent=2)
                
                os.write(fd, f"PID:{os.getpid()}\nSTART:{datetime.now().isoformat()}\n".encode())
                
                self.logger.info(f"🔒 获取锁成功: {lock_name} (PID: {os.getpid()})")
                
                # 设置超时信号
                def timeout_handler(signum, frame):
                    self.logger.error(f"⏰ 锁超时: {lock_name} 超过 {timeout} 秒")
                    raise TimeoutError(f"Lock {lock_name} timeout after {timeout} seconds")
                
                old_handler = signal.signal(signal.SIGALRM, timeout_handler)
                signal.alarm(timeout)
                
                yield lock_name
                
            finally:
                # 清理信号
                signal.alarm(0)
                signal.signal(signal.SIGALRM, old_handler)
                
                # 释放文件锁
                fcntl.flock(fd, fcntl.LOCK_UN)
                os.close(fd)
                
                # 删除锁文件
                try:
                    lock_file.unlink()
                    lock_info_file.unlink(missing_ok=True)
                    self.logger.info(f"🔓 释放锁成功: {lock_name}")
                except FileNotFoundError:
                    pass
                    
        except OSError as e:
            if e.errno == 17:  # File exists
                self.logger.warning(f"⚠️ 锁已被占用: {lock_name}")
                raise LockAlreadyAcquiredError(f"Lock {lock_name} is already acquired")
            else:
                raise
    
    def _cleanup_expired_lock(self, lock_file: Path, lock_info_file: Path, force_after: int):
        """清理过期的锁"""
        if not lock_file.exists():
            return
            
        try:
            import json
            
            # 检查锁信息
            if lock_info_file.exists():
                with open(lock_info_file) as f:
                    lock_info = json.load(f)
                
                start_time = datetime.fromisoformat(lock_info['start_time'])
                age_seconds = (datetime.now() - start_time).total_seconds()
                
                # 检查进程是否还活着
                try:
                    pid = lock_info['pid']
                    if not psutil.pid_exists(pid):
                        self.logger.warning(f"🧹 清理僵尸锁: PID {pid} 已不存在")
                        lock_file.unlink()
                        lock_info_file.unlink()
                        return
                        
                    # 检查是否超过强制释放时间
                    if age_seconds > force_after:
                        self.logger.error(f"💀 强制释放锁: 已存在 {age_seconds:.0f} 秒")
                        try:
                            proc = psutil.Process(pid)
                            proc.terminate()
                            time.sleep(5)
                            if proc.is_running():
                                proc.kill()
                                self.logger.warning(f"🔫 强制杀死进程: {pid}")
                        except psutil.NoSuchProcess:
                            pass
                        
                        lock_file.unlink()
                        lock_info_file.unlink()
                        
                except Exception as e:
                    self.logger.error(f"清理锁失败: {e}")
                    
        except Exception as e:
            self.logger.error(f"检查锁信息失败: {e}")
            # 如果无法解析，但文件很旧，就删除
            if lock_file.stat().st_mtime < time.time() - force_after:
                self.logger.warning("删除无法解析的旧锁文件")
                lock_file.unlink()
                lock_info_file.unlink(missing_ok=True)
    
    def list_active_locks(self) -> Dict[str, Any]:
        """列出所有活跃的锁"""
        locks = {}
        
        for lock_file in self.lock_dir.glob("*.lock"):
            lock_name = lock_file.stem
            info_file = self.lock_dir / f"{lock_name}.info"
            
            try:
                if info_file.exists():
                    import json
                    with open(info_file) as f:
                        lock_info = json.load(f)
                    
                    start_time = datetime.fromisoformat(lock_info['start_time'])
                    age_seconds = (datetime.now() - start_time).total_seconds()
                    
                    locks[lock_name] = {
                        **lock_info,
                        'age_seconds': age_seconds,
                        'is_alive': psutil.pid_exists(lock_info['pid']) if 'pid' in lock_info else False
                    }
                    
            except Exception as e:
                locks[lock_name] = {'error': str(e)}
                
        return locks


class LockAlreadyAcquiredError(Exception):
    """锁已被占用异常"""
    pass


def main():
    """测试和管理锁"""
    import argparse
    
    parser = argparse.ArgumentParser(description='高级锁管理工具')
    parser.add_argument('--list', action='store_true', help='列出所有锁')
    parser.add_argument('--cleanup', action='store_true', help='清理过期锁')
    parser.add_argument('--force-cleanup', type=str, help='强制清理指定锁')
    
    args = parser.parse_args()
    
    manager = AdvancedLockManager()
    
    if args.list:
        locks = manager.list_active_locks()
        if locks:
            print("🔒 活跃的锁:")
            for name, info in locks.items():
                if 'error' in info:
                    print(f"  {name}: ❌ {info['error']}")
                else:
                    age_min = info['age_seconds'] / 60
                    status = "✅" if info['is_alive'] else "💀"
                    print(f"  {name}: {status} PID {info['pid']}, 运行 {age_min:.1f} 分钟")
        else:
            print("🔓 当前无活跃锁")
            
    elif args.cleanup:
        print("🧹 清理过期锁...")
        locks = manager.list_active_locks()
        for name, info in locks.items():
            if not info.get('is_alive', True):
                lock_file = Path(f"/tmp/{name}.lock")
                info_file = Path(f"/tmp/{name}.info")
                try:
                    lock_file.unlink()
                    info_file.unlink(missing_ok=True)
                    print(f"  清理: {name}")
                except Exception as e:
                    print(f"  清理失败 {name}: {e}")
                    
    elif args.force_cleanup:
        name = args.force_cleanup
        lock_file = Path(f"/tmp/{name}.lock")
        info_file = Path(f"/tmp/{name}.info")
        try:
            lock_file.unlink()
            info_file.unlink(missing_ok=True)
            print(f"🔫 强制清理锁: {name}")
        except FileNotFoundError:
            print(f"⚠️ 锁不存在: {name}")
    else:
        # 演示用法
        print("🔧 锁管理器演示")
        try:
            with manager.acquire_lock("test_lock", timeout=10):
                print("获取锁成功，执行任务...")
                time.sleep(2)
                print("任务完成")
        except LockAlreadyAcquiredError:
            print("锁已被占用")
        except TimeoutError:
            print("任务超时")


if __name__ == "__main__":
    main()