#!/usr/bin/env python3
"""
详细分析7月30日的价格数据，查找价格跳跃点
"""

import json
import requests
from datetime import datetime
import pandas as pd

def download_and_analyze_july30():
    """下载并详细分析7月30日数据"""
    url = "https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/20250726/BTCUSDT/1m/daily/BTCUSDT_1m_07-30_2025_compressed.json"
    
    print("📥 下载7月30日详细数据...")
    response = requests.get(url, timeout=30)
    data = json.loads(response.text)['data']
    
    # 转换数据
    processed_data = []
    for i, item in enumerate(data):
        timestamp = item[0]
        if timestamp > 1e15:  # 微秒转毫秒
            timestamp = timestamp // 1000
            
        processed_data.append({
            'index': i,
            'timestamp': timestamp,
            'datetime': datetime.fromtimestamp(timestamp / 1000),
            'open': float(item[1]),
            'high': float(item[2]),
            'low': float(item[3]),
            'close': float(item[4]),
            'volume': float(item[5])
        })
    
    df = pd.DataFrame(processed_data)
    
    print(f"📊 数据概览:")
    print(f"   总记录数: {len(df)}")
    print(f"   时间范围: {df['datetime'].min()} - {df['datetime'].max()}")
    print(f"   价格范围: {df['close'].min():,.2f} - {df['close'].max():,.2f}")
    
    # 查找价格跳跃点
    print(f"\n🔍 查找价格异常跳跃...")
    df['price_change'] = df['close'].diff()
    df['price_change_pct'] = df['close'].pct_change() * 100
    
    # 查找大幅跳跃（超过1%的变化）
    large_jumps = df[abs(df['price_change_pct']) > 1.0].copy()
    
    if len(large_jumps) > 0:
        print(f"🚨 发现{len(large_jumps)}个大幅价格跳跃 (>1%):")
        for _, row in large_jumps.iterrows():
            prev_price = df.iloc[row['index']-1]['close'] if row['index'] > 0 else 0
            print(f"   [{row['index']}] {row['datetime'].strftime('%H:%M:%S')}")
            print(f"       {prev_price:,.2f} -> {row['close']:,.2f} ({row['price_change_pct']:+.2f}%)")
    
    # 特别关注从正常价格(11万+)跳到异常价格(4-5万)的点
    print(f"\n🎯 查找从11万+跳到4-5万的异常点...")
    
    for i in range(1, len(df)):
        prev_close = df.iloc[i-1]['close']
        curr_close = df.iloc[i]['close']
        
        # 从11万+跳到5万以下
        if prev_close > 110000 and curr_close < 50000:
            print(f"🚨 发现异常跳跃点:")
            print(f"   时间: {df.iloc[i-1]['datetime']} -> {df.iloc[i]['datetime']}")
            print(f"   价格: {prev_close:,.2f} -> {curr_close:,.2f}")
            print(f"   变化: {((curr_close - prev_close) / prev_close * 100):+.2f}%")
            
            # 显示前后几条记录
            start_idx = max(0, i-5)
            end_idx = min(len(df), i+5)
            context = df.iloc[start_idx:end_idx].copy()
            
            print(f"\n📋 前后上下文 (索引 {start_idx}-{end_idx-1}):")
            for _, ctx_row in context.iterrows():
                marker = "👉 " if ctx_row['index'] in [i-1, i] else "   "
                print(f"{marker}[{ctx_row['index']:4d}] {ctx_row['datetime'].strftime('%H:%M:%S')} - "
                      f"O:{ctx_row['open']:8,.2f} H:{ctx_row['high']:8,.2f} "
                      f"L:{ctx_row['low']:8,.2f} C:{ctx_row['close']:8,.2f} V:{ctx_row['volume']:8.2f}")
            
            break
    
    # 分析4-5万价格区间的持续时间
    low_price_data = df[(df['close'] >= 44000) & (df['close'] <= 50000)].copy()
    if len(low_price_data) > 0:
        print(f"\n📊 4-5万价格区间分析:")
        print(f"   记录数: {len(low_price_data)}")
        print(f"   时间段: {low_price_data['datetime'].min()} - {low_price_data['datetime'].max()}")
        print(f"   持续时间: {(low_price_data['datetime'].max() - low_price_data['datetime'].min()).total_seconds() / 60:.0f} 分钟")
    
    # 分析回到正常价格的点
    print(f"\n🔄 查找价格恢复点...")
    for i in range(1, len(df)):
        prev_close = df.iloc[i-1]['close']
        curr_close = df.iloc[i]['close']
        
        # 从5万以下跳回11万+
        if prev_close < 50000 and curr_close > 110000:
            print(f"✅ 发现价格恢复点:")
            print(f"   时间: {df.iloc[i-1]['datetime']} -> {df.iloc[i]['datetime']}")
            print(f"   价格: {prev_close:,.2f} -> {curr_close:,.2f}")
            print(f"   变化: {((curr_close - prev_close) / prev_close * 100):+.2f}%")
            
            # 显示前后几条记录
            start_idx = max(0, i-3)
            end_idx = min(len(df), i+3)
            context = df.iloc[start_idx:end_idx].copy()
            
            print(f"\n📋 恢复点上下文:")
            for _, ctx_row in context.iterrows():
                marker = "👉 " if ctx_row['index'] in [i-1, i] else "   "
                print(f"{marker}[{ctx_row['index']:4d}] {ctx_row['datetime'].strftime('%H:%M:%S')} - "
                      f"C:{ctx_row['close']:8,.2f}")
            break

if __name__ == "__main__":
    download_and_analyze_july30()