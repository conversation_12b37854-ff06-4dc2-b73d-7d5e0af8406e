import React, { useState } from 'react';
import { Button, Dropdown, Switch } from 'antd';
import { SettingOutlined } from '@ant-design/icons';
import './IndicatorToggle.css';

const IndicatorToggle = ({ 
  indicators = {},
  onIndicatorChange,
  allowedIndicators = ['MA', 'VOL', 'BOLL', 'MACD', 'RSI', 'KDJ', 'StochRSI']
}) => {
  const [isVisible, setIsVisible] = useState(false);

  const indicatorLabels = {
    MA: '移动平均线',
    VOL: '成交量',
    BOLL: '布林带',
    MACD: 'MACD',
    RSI: 'RSI',
    KDJ: 'KDJ',
    StochRSI: 'Stoch RSI'
  };

  const handleIndicatorToggle = (indicatorKey, checked) => {
    onIndicatorChange && onIndicatorChange(indicatorKey, checked);
  };

  const menuItems = allowedIndicators.map(key => ({
    key,
    label: (
      <div className="indicator-menu-item">
        <span className="indicator-label">{indicatorLabels[key]}</span>
        <Switch
          size="small"
          checked={indicators[key] || false}
          onChange={(checked) => handleIndicatorToggle(key, checked)}
          onClick={(e) => e.stopPropagation()}
        />
      </div>
    ),
  }));

  return (
    <Dropdown
      menu={{ items: menuItems }}
      trigger={['click']}
      placement="bottomRight"
      open={isVisible}
      onOpenChange={setIsVisible}
      dropdownRender={(menu) => (
        <div className="indicator-dropdown">
          <div className="indicator-dropdown-header">
            <span>技术指标</span>
          </div>
          <div className="indicator-dropdown-content">
            {menu}
          </div>
        </div>
      )}
    >
      <Button
        type="text"
        size="small"
        icon={<SettingOutlined />}
        className="indicator-toggle-btn"
        title="指标设置"
      />
    </Dropdown>
  );
};

export default IndicatorToggle;