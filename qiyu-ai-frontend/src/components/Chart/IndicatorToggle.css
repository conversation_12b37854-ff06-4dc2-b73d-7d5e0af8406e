/* 指标切换按钮样式 */
.indicator-toggle-btn {
  position: relative;
  z-index: 10;
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.indicator-toggle-btn:hover {
  background: rgba(0, 0, 0, 0.8);
  color: rgba(255, 255, 255, 1);
  border-color: rgba(255, 255, 255, 0.4);
}

/* 下拉菜单样式 */
.indicator-dropdown {
  background: rgba(25, 30, 40, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  min-width: 200px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.indicator-dropdown-header {
  padding: 12px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.indicator-dropdown-content {
  padding: 8px 0;
}

/* 指标菜单项样式 */
.indicator-menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  color: rgba(255, 255, 255, 0.8);
  transition: background-color 0.2s ease;
}

.indicator-menu-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.indicator-label {
  font-size: 13px;
  flex: 1;
}

/* 开关样式覆盖 */
.indicator-dropdown .ant-switch-small {
  background-color: rgba(255, 255, 255, 0.2);
}

.indicator-dropdown .ant-switch-small.ant-switch-checked {
  background-color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .indicator-dropdown {
    min-width: 180px;
  }
  
  .indicator-menu-item {
    padding: 10px 12px;
  }
  
  .indicator-label {
    font-size: 12px;
  }
}