import jwt
from django.conf import settings
from django.contrib.auth.models import AnonymousUser
from rest_framework import authentication, exceptions
from rest_framework_simplejwt.authentication import JWTAuthentication as BaseJWTAuthentication
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from rest_framework_simplejwt.tokens import UntypedToken
from .models import WechatUser


class WechatJWTAuthentication(BaseJWTAuthentication):
    """
    微信小程序JWT认证类
    支持基于openid的token生成和验证
    """
    
    def authenticate(self, request):
        """
        重写认证方法，支持微信用户认证
        """
        header = self.get_header(request)
        if header is None:
            return None

        raw_token = self.get_raw_token(header)
        if raw_token is None:
            return None

        try:
            validated_token = self.get_validated_token(raw_token)
            user = self.get_user(validated_token)
            return (user, validated_token)
        except TokenError:
            return None
        except Exception:
            return None

    def get_user(self, validated_token):
        """
        根据token获取微信用户
        """
        try:
            openid = validated_token.get('openid')
            if not openid:
                raise exceptions.AuthenticationFailed('Token中缺少openid')
            
            user = WechatUser.objects.get(openid=openid, is_active=True)
            return user
        except WechatUser.DoesNotExist:
            raise exceptions.AuthenticationFailed('用户不存在或已被禁用')


class WechatTokenGenerator:
    """
    微信小程序Token生成器
    """
    
    @staticmethod
    def generate_tokens(user):
        """
        为微信用户生成JWT tokens
        """
        from rest_framework_simplejwt.tokens import RefreshToken
        
        refresh = RefreshToken()
        
        # 添加自定义字段
        refresh['openid'] = user.openid
        refresh['nickname'] = user.nickname or ''
        refresh['user_id'] = user.id
        
        # 生成access token
        access = refresh.access_token
        access['openid'] = user.openid
        access['nickname'] = user.nickname or ''
        access['user_id'] = user.id
        
        return {
            'access_token': str(access),
            'refresh_token': str(refresh),
            'access_expires_in': settings.SIMPLE_JWT['ACCESS_TOKEN_LIFETIME'].total_seconds(),
            'refresh_expires_in': settings.SIMPLE_JWT['REFRESH_TOKEN_LIFETIME'].total_seconds(),
        }

    @staticmethod
    def verify_token(token):
        """
        验证token是否有效
        """
        try:
            UntypedToken(token)
            return True
        except (InvalidToken, TokenError):
            return False

    @staticmethod
    def decode_token(token):
        """
        解码token获取用户信息
        """
        try:
            decoded = jwt.decode(
                token,
                settings.SECRET_KEY,
                algorithms=[settings.SIMPLE_JWT['ALGORITHM']]
            )
            return decoded
        except jwt.InvalidTokenError:
            return None