/**
 * 错误日志记录工具
 * 用于记录前端错误到.log文件
 */

class ErrorLogger {
    constructor() {
        this.logs = [];
        this.maxLogs = 1000; // 最大日志条数
    }

    /**
     * 记录错误信息
     * @param {string} type - 错误类型
     * @param {string} message - 错误消息
     * @param {Object} details - 详细信息
     */
    log(type, message, details = {}) {
        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            type,
            message,
            details,
            url: window.location.href,
            userAgent: navigator.userAgent
        };

        this.logs.push(logEntry);
        
        // 保持日志数量在限制内
        if (this.logs.length > this.maxLogs) {
            this.logs = this.logs.slice(-this.maxLogs);
        }

        // 输出到控制台
        console.error(`[${timestamp}] ${type}: ${message}`, details);

        // 尝试保存到本地存储
        this.saveToLocalStorage();
    }

    /**
     * 记录数据加载错误
     */
    logDataError(symbol, year, error) {
        this.log('DATA_LOAD_ERROR', `数据加载失败: ${symbol}-${year}`, {
            symbol,
            year,
            error: error.message,
            stack: error.stack
        });
    }

    /**
     * 记录时间戳格式错误
     */
    logTimestampError(timestamp, context) {
        this.log('TIMESTAMP_ERROR', `时间戳格式错误`, {
            timestamp,
            timestampType: typeof timestamp,
            context
        });
    }

    /**
     * 记录图表初始化错误
     */
    logChartError(chartId, error) {
        this.log('CHART_ERROR', `图表初始化失败: ${chartId}`, {
            chartId,
            error: error.message,
            stack: error.stack
        });
    }

    /**
     * 保存日志到本地存储
     */
    saveToLocalStorage() {
        try {
            localStorage.setItem('qiyu_error_logs', JSON.stringify(this.logs));
        } catch (e) {
            console.warn('无法保存错误日志到本地存储:', e);
        }
    }

    /**
     * 从本地存储加载日志
     */
    loadFromLocalStorage() {
        try {
            const saved = localStorage.getItem('qiyu_error_logs');
            if (saved) {
                this.logs = JSON.parse(saved);
            }
        } catch (e) {
            console.warn('无法从本地存储加载错误日志:', e);
        }
    }

    /**
     * 获取所有日志
     */
    getLogs() {
        return this.logs;
    }

    /**
     * 清除所有日志
     */
    clearLogs() {
        this.logs = [];
        localStorage.removeItem('qiyu_error_logs');
    }

    /**
     * 导出日志为文本格式
     */
    exportLogs() {
        const logText = this.logs.map(log => {
            return `[${log.timestamp}] ${log.type}: ${log.message}\n` +
                   `详情: ${JSON.stringify(log.details, null, 2)}\n` +
                   `URL: ${log.url}\n` +
                   `---\n`;
        }).join('\n');

        return logText;
    }

    /**
     * 下载日志文件
     */
    downloadLogs() {
        const logText = this.exportLogs();
        const blob = new Blob([logText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `qiyu_error_logs_${new Date().toISOString().split('T')[0]}.log`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);
    }
}

// 创建全局实例
const errorLogger = new ErrorLogger();

// 页面加载时从本地存储恢复日志
errorLogger.loadFromLocalStorage();

// 监听全局错误
window.addEventListener('error', (event) => {
    errorLogger.log('GLOBAL_ERROR', event.message, {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error?.stack
    });
});

// 监听未处理的Promise拒绝
window.addEventListener('unhandledrejection', (event) => {
    errorLogger.log('UNHANDLED_PROMISE_REJECTION', event.reason?.message || 'Unknown promise rejection', {
        reason: event.reason,
        stack: event.reason?.stack
    });
});

export default errorLogger;
