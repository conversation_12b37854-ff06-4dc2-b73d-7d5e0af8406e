import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react'
import { init, dispose } from 'klinecharts'
import { Space, Switch, Select } from 'antd'
import realCryptoDataService from '../../services/StreamDataService'
// import binanceWebSocketService from '../../services/BinanceWebSocketService' // 已禁用WebSocket
// import realTimeDataManager from '../../services/RealTimeDataManager' // 已禁用WebSocket
import './EnhancedKLineChart.css'


const EnhancedKLineChart = ({
  symbol = 'BTCUSDT',
  year = '2024', // 默认2024年
  timeframe = '1m', // 新增时间周期参数
  width = '100%',
  height = 600,
  showMACD = false,
  enableRealTime = false, // 强制禁用WebSocket实时连接
  onDataUpdate
}) => {
  const chartRef = useRef(null)
  const chart = useRef(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [realTimeConnected, setRealTimeConnected] = useState(false)
  const [currentData, setCurrentData] = useState([])
  const [indicators, setIndicators] = useState({
    BOLL: true,   // 主图BOLL
    RSI: true,    // 副图RSI
    KDJ: true,    // 副图KDJ
    MACD: showMACD && year === new Date().getFullYear().toString() // 仅最新年份显示MACD
  })
  const [indicatorPaneIds, setIndicatorPaneIds] = useState({}) // 存储指标的面板ID

  // 添加日志记录状态
  const loggedCharts = useRef(new Set())
  const timersRef = useRef([])
  const initializingRef = useRef(false)
  const chartConfigRef = useRef(null)

  // 清理所有定时器的辅助函数
  const clearAllTimers = useCallback(() => {
    timersRef.current.forEach(timer => clearTimeout(timer))
    timersRef.current = []
  }, [])

  // 安全的setTimeout包装器
  const safeSetTimeout = useCallback((callback, delay) => {
    const timer = setTimeout(() => {
      // 从定时器数组中移除已完成的定时器
      timersRef.current = timersRef.current.filter(t => t !== timer)
      callback()
    }, delay)
    timersRef.current.push(timer)
    return timer
  }, [])

  // 实时数据节流和去重
  const lastUpdateRef = useRef({})
  const updateThrottleRef = useRef({})

  // 处理实时数据更新
  const handleRealTimeUpdate = useCallback((klineData) => {
    if (!chart.current) return

    try {
      // 时间周期配置 - 更节能的更新频率
      const throttleIntervals = {
        '1m': 10000,   // 1分钟：10秒更新一次
        '15m': 60000,  // 15分钟：1分钟更新一次  
        '1h': 300000,  // 1小时：5分钟更新一次
        '4h': 600000,  // 4小时：10分钟更新一次
        '1d': 1200000  // 1天：20分钟更新一次
      }

      // 转换数据格式为klinecharts需要的格式
      const chartData = {
        timestamp: klineData.timestamp,
        open: klineData.open,
        high: klineData.high,
        low: klineData.low,
        close: klineData.close,
        volume: klineData.volume
      }

      const dataKey = `${symbol}_${klineData.timestamp}`
      const now = Date.now()

      // 数据去重：检查是否与上次数据相同
      const lastData = lastUpdateRef.current[dataKey]
      if (lastData && 
          lastData.close === klineData.close && 
          lastData.volume === klineData.volume &&
          lastData.isFinal === klineData.isFinal) {
        return // 数据未变化，跳过更新
      }

      // 根据时间周期智能节流
      if (!klineData.isFinal) {
        const throttleInterval = throttleIntervals[timeframe] || 5000
        const lastThrottleTime = updateThrottleRef.current[dataKey] || 0
        
        if (now - lastThrottleTime < throttleInterval) {
          return // 节流中，跳过更新
        }
        updateThrottleRef.current[dataKey] = now
      }

      // 更新图表数据
      if (klineData.isFinal) {
        // 最终K线：添加到历史数据
        chart.current.updateData(chartData)
        setCurrentData(prev => {
          const newData = [...prev]
          const lastIndex = newData.length - 1
          if (lastIndex >= 0 && newData[lastIndex].timestamp === chartData.timestamp) {
            newData[lastIndex] = chartData
          } else {
            newData.push(chartData)
          }
          return newData
        })
        
        // 清理节流缓存
        delete updateThrottleRef.current[dataKey]
        
        // 🎯 实时数据收集已禁用，改为使用定时刷新模式
        // realTimeDataManager.handleRealTimeData(symbol, timeframe, klineData)
        
        console.log('📊 最终K线更新:', {
          symbol,
          price: klineData.close,
          timestamp: new Date(klineData.timestamp).toLocaleTimeString()
        })
      } else {
        // 实时更新：只更新图表显示，不修改数据状态
        chart.current.updateData(chartData)
        
        // 减少日志频率
        const logInterval = throttleIntervals[timeframe] || 30000
        if (now - (lastUpdateRef.current[`${dataKey}_log`] || 0) > logInterval) {
          // 计算下一个K线结束时间
          const timeframeMs = {
            '1m': 60000,
            '15m': 900000,
            '1h': 3600000,
            '4h': 14400000,
            '1d': 86400000
          }
          const nextFinalTime = klineData.timestamp + (timeframeMs[timeframe] || 3600000)
          
          console.log('📊 实时价格更新:', {
            symbol,
            price: klineData.close,
            timeframe,
            next_final: new Date(nextFinalTime).toLocaleTimeString()
          })
          lastUpdateRef.current[`${dataKey}_log`] = now
        }
      }

      // 更新缓存
      lastUpdateRef.current[dataKey] = klineData

      // 触发数据更新回调（仅最终数据）
      if (klineData.isFinal && onDataUpdate) {
        onDataUpdate(currentData)
      }

    } catch (error) {
      console.error('❌ 实时数据更新失败:', error)
    }
  }, [symbol, currentData, onDataUpdate])

  // 深色主题配置 - 使用useMemo缓存
  const darkThemeConfig = useMemo(() => ({
    locale: 'zh-CN',
    timezone: 'Asia/Shanghai',
    styles: {
      grid: {
        horizontal: {
          color: '#333333',
          size: 1,
          style: 'dashed'
        },
        vertical: {
          color: '#333333', 
          size: 1,
          style: 'dashed'
        }
      },
      candle: {
        type: 'candle_solid',
        bar: {
          upColor: '#26A69A',
          downColor: '#EF5350',
          noChangeColor: '#888888'
        },
        tooltip: {
          showRule: 'follow_cross',
          showType: 'standard',
          labels: ['时间: ', '开盘: ', '收盘: ', '最高: ', '最低: ', '成交量: '],
          defaultValue: 'n/a',
          rect: {
            position: 'fixed',
            paddingLeft: 8,
            paddingRight: 8,
            paddingTop: 6,
            paddingBottom: 6,
            offsetLeft: 8,
            offsetTop: 8,
            offsetRight: 8,
            borderRadius: 4,
            borderSize: 1,
            borderColor: '#3f4254',
            color: '#1e1e1e'
          },
          text: {
            size: 12,
            family: 'Arial',
            weight: 'normal',
            color: '#D9D9D9',
            marginLeft: 8,
            marginTop: 6,
            marginRight: 8,
            marginBottom: 6
          }
        }
      },
      technicalIndicator: {
        margin: {
          top: 0.2,
          bottom: 0.1
        },
        bar: {
          upColor: '#26A69A',
          downColor: '#EF5350',
          noChangeColor: '#888888'
        },
        line: {
          size: 1,
          colors: ['#FF9600', '#9D65C9', '#2196F3', '#E11D74', '#01C5C4']
        },
        circle: {
          upColor: '#26A69A',
          downColor: '#EF5350',
          noChangeColor: '#888888'
        }
      },
      xAxis: {
        axisLine: {
          color: '#888888',
          size: 1
        },
        tickText: {
          color: '#D9D9D9',
          size: 12,
          family: 'Arial',
          weight: 'normal',
          marginStart: 4,
          marginEnd: 4
        },
        tickLine: {
          color: '#888888',
          size: 1,
          length: 3
        }
      },
      yAxis: {
        show: true,
        axisLine: {
          show: true,
          color: '#888888',
          size: 1
        },
        tickText: {
          show: true,
          color: '#D9D9D9',
          size: 12,
          family: 'Arial',
          weight: 'normal',
          marginStart: 4,
          marginEnd: 4
        },
        tickLine: {
          show: true,
          color: '#888888',
          size: 1,
          length: 3
        }
      },
      separator: {
        size: 1,
        color: '#333333'
      },
      crosshair: {
        show: true,
        horizontal: {
          show: true,
          line: {
            color: '#FFFFFF',
            size: 1,
            style: 'dashed'
          },
          text: {
            color: '#FFFFFF',
            size: 12,
            family: 'Arial',
            weight: 'normal',
            paddingLeft: 2,
            paddingRight: 2,
            paddingTop: 2,
            paddingBottom: 2,
            borderSize: 1,
            borderColor: '#505050',
            borderRadius: 2,
            backgroundColor: '#505050'
          }
        },
        vertical: {
          show: true,
          line: {
            color: '#FFFFFF',
            size: 1,
            style: 'dashed'
          },
          text: {
            color: '#FFFFFF',
            size: 12,
            family: 'Arial',
            weight: 'normal',
            paddingLeft: 2,
            paddingRight: 2,
            paddingTop: 2,
            paddingBottom: 2,
            borderSize: 1,
            borderColor: '#505050',
            borderRadius: 2,
            backgroundColor: '#505050'
          }
        }
      }
    }
  }), []) // 空依赖数组，配置不变

  // 初始化图表
  useEffect(() => {
    const chartElement = chartRef.current
    if (!chartElement) return

    const currentConfig = `${symbol}-${timeframe}-${year}`
    
    if (chartConfigRef.current === currentConfig && chart.current) {
      // 静默跳过，不记录日志
      if (onDataUpdate && chart.current) {
        const existingData = chart.current.getDataList()
        if (existingData && existingData.length > 0) {
          onDataUpdate(existingData)
        }
      }
      return
    }
    
    if (initializingRef.current && chartConfigRef.current === currentConfig) {
      return
    }

    const initChart = async () => {
      try {
        initializingRef.current = true
        chartConfigRef.current = currentConfig
        
        setLoading(true)
        setError(null)

        // 只在首次初始化时记录
        if (!loggedCharts.current.has(currentConfig)) {
          console.log(`🔄 初始化图表: ${currentConfig}`)
          loggedCharts.current.add(currentConfig)
        }
        
        chart.current = init(chartElement, darkThemeConfig)
        
        const klineData = await realCryptoDataService.loadYearDataWithRetry(symbol, timeframe, year)
        
        if (klineData && klineData.length > 0) {
          // 简化数据应用日志
          if (!loggedCharts.current.has(`${currentConfig}_data`)) {
            // console.log(`� 加载数据: ${symbol} ${timeframe} ${year} (${klineData.length}条)`) // 已在StreamDataService中处理
            loggedCharts.current.add(`${currentConfig}_data`)
          }
          
          if (chart.current) {
            chart.current.applyNewData(klineData)
          } else {
            console.error('❌ 图表实例为null，无法应用数据')
            return
          }
          
          // 验证数据应用 - 静默验证，只在失败时记录
          safeSetTimeout(() => {
            if (chart.current) {
              const appliedData = chart.current.getDataList()
              if (!appliedData || appliedData.length === 0) {
                console.error(`❌ 数据应用失败: ${symbol} ${timeframe} ${year}`)
              }
            }
          }, 100)
          
          if (onDataUpdate) {
            onDataUpdate(klineData)
          }
        } else {
          console.warn(`⚠️ 没有找到数据: ${symbol} ${timeframe} ${year}`)
          setError(`没有找到${year}年的${timeframe}数据`)
        }
        
        // 初始化指标
        safeSetTimeout(() => {
          initializeIndicators()
        }, 100)
        
        setLoading(false)
        
        // 实时WebSocket连接已禁用，改为使用定时刷新模式
        // console.log('ℹ️ WebSocket实时连接已禁用，使用定时刷新模式') // 减少日志输出
        // if (year === new Date().getFullYear().toString() && enableRealTime) {
        //   // WebSocket连接已禁用
        // }
        
      } catch (error) {
        console.error('❌ 图表初始化失败:', error)
        setError(`初始化失败: ${error.message || '未知错误'}`)
        setLoading(false)
      } finally {
        initializingRef.current = false
      }
    }

    initChart()

    return () => {
      initializingRef.current = false
      chartConfigRef.current = null
      clearAllTimers()
      
      if (chart.current && chartElement) {
        dispose(chartElement)
        chart.current = null
      }
    }
  }, [symbol, timeframe, year, clearAllTimers, safeSetTimeout]) // 移除onDataUpdate依赖，避免重复渲染

  // 简化指标初始化日志
  const initializeIndicators = useCallback(() => {
    if (!chart.current) return

    try {
      const currentConfig = `${symbol}-${timeframe}-${year}`
      const paneIds = {}
      
      // 静默添加指标，只在首次或失败时记录
      chart.current.createIndicator('BOLL', true, { id: 'candle_pane' })

      if (indicators.RSI) {
        const rsiPaneId = chart.current.createIndicator('RSI', false, {
          calcParams: [6, 12, 24]
        })
        paneIds.RSI = rsiPaneId
      }

      if (indicators.KDJ) {
        const kdjPaneId = chart.current.createIndicator('KDJ', false, {
          calcParams: [9, 3, 3]
        })
        paneIds.KDJ = kdjPaneId
      }

      if (indicators.MACD && year === new Date().getFullYear().toString()) {
        const macdPaneId = chart.current.createIndicator('MACD', false, {
          calcParams: [12, 26, 9]
        })
        paneIds.MACD = macdPaneId
      }

      setIndicatorPaneIds(paneIds)
      
      // 🔧 指标初始化合并为一行 - 只在首次成功时记录
      if (!loggedCharts.current.has(`${currentConfig}_indicators`)) {
        console.log(`🔧 [指标] ${symbol} ${timeframe} ${year}: ${Object.keys(paneIds).join(', ')}`)
        loggedCharts.current.add(`${currentConfig}_indicators`)
      }
      
      safeSetTimeout(() => {
        if (chart.current) {
          chart.current.resize()
        }
      }, 50)

    } catch (error) {
      console.error('❌ 初始化指标失败:', error)
    }
  }, [indicators, year, safeSetTimeout])

  // 指标切换处理 - 简化日志
  const handleIndicatorChange = (name, checked) => {
    if (!chart.current) {
      console.warn('图表实例不存在')
      return
    }

    try {
      if (!checked) {
        if (name === 'BOLL') {
          const removed = chart.current.removeIndicator('candle_pane', 'BOLL')
          if (!removed) console.warn(`${name} 指标移除失败`)
        } else {
          const paneId = indicatorPaneIds[name]
          if (paneId) {
            const removed = chart.current.removeIndicator(paneId, name)
            if (!removed) console.warn(`${name} 指标移除失败`)
            
            const newPaneIds = { ...indicatorPaneIds }
            delete newPaneIds[name]
            setIndicatorPaneIds(newPaneIds)
          }
        }
      } else {
        if (name === 'BOLL') {
          chart.current.createIndicator('BOLL', true, { id: 'candle_pane' })
        } else {
          let newPaneId
          switch (name) {
            case 'RSI':
              newPaneId = chart.current.createIndicator('RSI', false, {
                calcParams: [6, 12, 24]
              })
              break
            case 'KDJ':
              newPaneId = chart.current.createIndicator('KDJ', false, {
                calcParams: [9, 3, 3]
              })
              break
            case 'MACD':
              newPaneId = chart.current.createIndicator('MACD', false, {
                calcParams: [12, 26, 9]
              })
              break
            default:
              console.warn(`未知指标类型: ${name}`)
              return
          }
          
          if (newPaneId) {
            const newPaneIds = { ...indicatorPaneIds, [name]: newPaneId }
            setIndicatorPaneIds(newPaneIds)
          }
        }
      }
      
      setIndicators(prev => ({ ...prev, [name]: checked }))
      
    } catch (error) {
      console.error(`❌ 指标切换失败 (${name}):`, error)
    }
  }



  return (
    <div className="enhanced-kline-chart" style={{ 
      width, 
      height,
      display: 'flex',
      flexDirection: 'column',
      backgroundColor: '#1a1a1a'
    }}>
      {/* 紧凑工具栏 - 年份和指标在同一行 */}
      <div className="chart-toolbar-compact">
        <div className="toolbar-left-compact">
          <span className="chart-year-badge">
            {year}年
          </span>
          {realTimeConnected && (
            <span className="realtime-status-compact">
              🟢
            </span>
          )}
        </div>

        <div className="toolbar-center-compact">
          <Space size="small">
            <Switch
              size="small"
              checked={indicators.BOLL}
              onChange={(checked) => handleIndicatorChange('BOLL', checked)}
              checkedChildren="BOLL"
              unCheckedChildren="BOLL"
            />
            <Switch
              size="small"
              checked={indicators.RSI}
              onChange={(checked) => handleIndicatorChange('RSI', checked)}
              checkedChildren="RSI"
              unCheckedChildren="RSI"
            />
            <Switch
              size="small"
              checked={indicators.KDJ}
              onChange={(checked) => handleIndicatorChange('KDJ', checked)}
              checkedChildren="KDJ"
              unCheckedChildren="KDJ"
            />
            {year === new Date().getFullYear().toString() && (
              <Switch
                size="small"
                checked={indicators.MACD}
                onChange={(checked) => handleIndicatorChange('MACD', checked)}
                checkedChildren="MACD"
                unCheckedChildren="MACD"
              />
            )}
          </Space>
        </div>


      </div>

      {/* 图表容器 */}
      <div className="chart-wrapper" style={{
        flex: 1,
        position: 'relative',
        minHeight: showMACD ? '250px' : '200px', // 进一步减少高度以适应2*2布局
        overflow: 'hidden' // 确保不显示滚动条
      }}>
        {loading && (
          <div className="chart-loading">
            <div className="loading-spinner"></div>
            <span>正在加载{year}年数据...</span>
          </div>
        )}
        {error && (
          <div className="chart-error">
            <span>⚠️ {error}</span>
          </div>
        )}
        <div
          ref={chartRef}
          className="chart-container"
          style={{
            width: '100%',
            height: '100%',
            display: (loading || error) ? 'none' : 'block'
          }}
        />
      </div>
    </div>
  )
}

export default EnhancedKLineChart
