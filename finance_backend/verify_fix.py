#!/usr/bin/env python3
"""
验证跨日数据修复结果
"""

import os
import sys
import django
from datetime import datetime

# Django环境设置
sys.path.insert(0, '/home/<USER>/qiyuai-web/finance_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'finance_project.settings')
django.setup()

from crypto_api.services.cos_service import COSRealtimeService

def verify_crossday_fix():
    """验证跨日数据修复结果"""
    
    cos_service = COSRealtimeService(debug=True)
    symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'DOGEUSDT', 'XRPUSDT']
    
    print("🔍 验证跨日数据修复结果")
    print("="*50)
    
    for symbol in symbols:
        try:
            # 获取最后上传时间
            last_upload = cos_service.get_last_upload_time(symbol)
            
            if last_upload:
                # 处理不同类型的时间戳
                if isinstance(last_upload, datetime):
                    dt = last_upload
                elif isinstance(last_upload, (int, float)):
                    # 时间戳转换
                    if last_upload > 1e12:  # 毫秒时间戳
                        dt = datetime.fromtimestamp(last_upload / 1000)
                    else:  # 秒时间戳
                        dt = datetime.fromtimestamp(last_upload)
                else:
                    dt = last_upload
                    
                print(f"📊 {symbol}: 最后数据时间 {dt.strftime('%Y-%m-%d %H:%M:%S')}")
            else:
                print(f"❌ {symbol}: 未找到数据")
                
        except Exception as e:
            print(f"❌ {symbol}: 检查失败 - {e}")
    
    print("="*50)
    print("✅ 数据修复验证完成")

if __name__ == "__main__":
    verify_crossday_fix()