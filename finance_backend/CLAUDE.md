# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Django-based finance backend API that supports a cryptocurrency analysis system. Originally built for WeChat Mini Programs, it's designed to migrate to web platforms. The system provides:

- User authentication with WeChat OAuth
- Cryptocurrency data proxy from Binance API  
- AI-powered crypto analysis using streaming responses
- Credit-based usage tracking
- File storage via Tencent Cloud COS

## Core Architecture

### Django Apps Structure
- **finance_project/**: Main Django project configuration
- **user_api/**: User authentication, WeChat OAuth, credit management
- **crypto_api/**: Cryptocurrency data endpoints (minimal implementation)
- **ai_analysis/**: AI analysis endpoints and streaming responses

### Key Models
- **WechatUser**: User authentication model with credits system
- **CreditRecord**: Tracks user credit usage and transactions

### External Integrations
- **Binance API**: Proxied through `/api/ai/quotation/binance/` endpoints
- **DeepSeek AI**: Used for cryptocurrency analysis via streaming API
- **Tencent Cloud COS**: File storage backend
- **WeChat API**: User authentication and profile management

## Common Development Commands

### Environment Setup
```bash
# Navigate to backend directory
cd finance_backend
```

### Running the Development Server
```bash
cd finance_backend
python manage.py runserver 0.0.0.0:8000

# Background mode with nohup
nohup python manage.py runserver 0.0.0.0:8000 > server.log 2>&1 &
```

### Database Operations
```bash
# Run migrations
python manage.py migrate

# Create migrations after model changes
python manage.py makemigrations

# Database shell
python manage.py dbshell
```

### Testing
```bash
# Run all tests
python manage.py test

# Run specific app tests
python manage.py test user_api
python manage.py test crypto_api
python manage.py test ai_analysis
```

### Django Admin
```bash
# Create superuser
python manage.py createsuperuser

# Collect static files (production)
python manage.py collectstatic
```

### 🚀 Realtime Data Collection (New Feature)
```bash
# Test gap detection (check what data needs to be filled)
python simple_gap_test.py

# Start realtime data collector (24/7 WebSocket data collection)
python manage.py collect_realtime_data --symbol=BTCUSDT --daemon

# Debug mode (with detailed logs)
python manage.py collect_realtime_data --symbol=BTCUSDT --debug

# Custom upload interval (default 300 seconds = 5 minutes)
python manage.py collect_realtime_data --symbol=BTCUSDT --interval=600

# Using startup script
./start_realtime_collector.sh
```

### Production Deployment
```bash
# Run with Gunicorn
gunicorn finance_project.wsgi:application --bind 0.0.0.0:8000 --workers 3
```

## Environment Configuration

The application uses environment variables for sensitive configuration:

- `SECRET_KEY`: Django secret key
- `DEBUG`: Debug mode (True/False)
- `ALLOWED_HOSTS`: Comma-separated list of allowed hosts
- `WECHAT_APP_ID`: WeChat Mini Program App ID
- `WECHAT_APP_SECRET`: WeChat Mini Program App Secret  
- `DEEPSEEK_API_KEY`: DeepSeek AI API key
- `COS_BUCKET_NAME`: Tencent Cloud COS bucket name
- `COS_SECRET_ID`: Tencent Cloud COS access key
- `COS_SECRET_KEY`: Tencent Cloud COS secret key
- `COS_REGION`: Tencent Cloud COS region

## API Endpoints

### Core API Structure
- `/api/user/`: User authentication and profile management
- `/api/crypto/`: Basic cryptocurrency endpoints
- `/api/ai/`: AI analysis endpoints and data proxying
- `/api/ai/quotation/binance/`: Proxied Binance API endpoints

### Key Endpoints
- `POST /api/ai/chat/`: Streaming AI analysis (600s timeout)
- `GET /api/ai/quotation/binance/api/v3/klines`: K-line data proxy
- `GET /api/ai/quotation/binance/api/v3/ticker/24hr`: Price data proxy
- `GET /api/ai/quotation/binance/api/v3/depth`: Order book data proxy

## Migration Context

This system is transitioning from WeChat Mini Program to web platform:

### Authentication Migration
- **Current**: WeChat OAuth with `wx.login()`
- **Target**: Email/password + third-party OAuth (Google, GitHub, etc.)

### Frontend Migration  
- **Current**: WeChat Mini Program APIs
- **Target**: Standard web APIs with CORS support

### Data Flow Changes
- Replace `wx.request()` with `fetch()` or `axios`
- Replace `wx.setStorageSync()` with `localStorage`
- Implement proper CORS handling for cross-origin requests

## Development Notes

### File Storage
Uses Tencent Cloud COS with S3-compatible API through django-storages. Files are stored with public-read access and custom domain support.

### Streaming Responses
AI analysis uses Server-Sent Events (SSE) format for real-time response streaming. The frontend should handle chunked responses properly.

### Database
Currently using SQLite for development. MySQL support is configured via PyMySQL but requires database connection setup.

### CORS Configuration
Configured to allow requests from localhost:8080 and localhost:8888 for development. Update CORS_ALLOWED_ORIGINS for production domains.

### Credit System
Users start with 3 free analysis credits. Credit consumption and top-up logic is handled in the user_api app.

### Error Handling
Implement proper timeout handling for the 10-minute AI analysis requests. Consider implementing retry logic for external API failures.