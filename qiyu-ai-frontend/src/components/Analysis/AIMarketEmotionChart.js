import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const AIMarketEmotionChart = ({ sentimentData = {} }) => {
  const [data, setData] = useState([]);

  useEffect(() => {
    // 处理情绪数据
    if (sentimentData && Object.keys(sentimentData).length > 0) {
      const chartData = Object.entries(sentimentData).map(([key, value]) => {
        // 转换键名
        let name = key;
        if (key === "BTC_多头占比") name = "BTC多头";
        if (key === "目标币种_多头占比") name = "目标币种多头";
        
        return {
          name: name,
          value: value
        };
      });
      setData(chartData);
    } else {
      // 默认数据
      setData([
        { name: 'BTC多头', value: 50 },
        { name: '目标币种多头', value: 50 },
      ]);
    }
  }, [sentimentData]);

  return (
    <ResponsiveContainer width="100%" height={200}>
      <Bar<PERSON>hart
        data={data}
        margin={{
          top: 20,
          right: 10,
          left: 10,
          bottom: 5,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#333" />
        <XAxis 
          dataKey="name" 
          tick={{ fill: '#fff', fontSize: 10 }}
        />
        <YAxis 
          domain={[0, 100]}
          tick={{ fill: '#fff', fontSize: 10 }}
        />
        <Tooltip 
          formatter={(value) => [`${value}%`, '占比']}
          contentStyle={{ backgroundColor: '#333', border: '1px solid #444' }}
          labelStyle={{ color: '#fff' }}
        />
        <Bar 
          dataKey="value" 
          fill="#447A53" 
          label={{ 
            position: 'top', 
            fill: '#fff',
            fontSize: 12,
            formatter: (value) => `${value}%`
          }}
        />
      </BarChart>
    </ResponsiveContainer>
  );
};

export default AIMarketEmotionChart; 