/**
 * Binance WebSocket 统一服务
 * 基于官方API文档实现，支持多流管理和自动重连
 * 文档：https://developers.binance.com/docs/binance-spot-api-docs/web-socket-streams
 */

// 连接状态枚举
const ConnectionStatus = {
  DISCONNECTED: 'disconnected',
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  RECONNECTING: 'reconnecting',
  ERROR: 'error'
}

class BinanceWebSocketService {
  constructor() {
    this.connections = new Map()
    this.baseUrl = 'wss://stream.binance.com:9443'
    this.maxReconnectAttempts = 5
    this.reconnectDelay = 3000
    this.pingInterval = 30000 // 30秒心跳
    this.maxStreamsPerConnection = 200 // Binance限制
    this.healthCheckTimer = null

    this.startHealthCheck()
    
    // 页面卸载时清理所有连接
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.closeAllConnections()
      })
    }
  }

  /**
   * 订阅K线数据
   * @param {string} symbol - 交易对，如 'BTCUSDT'
   * @param {string} interval - 时间间隔，如 '1m', '1h', '1d'
   * @param {function} callback - 数据回调函数
   * @returns {string} 订阅ID
   */
  subscribeKline(symbol, interval, callback) {
    const streamName = `${symbol.toLowerCase()}@kline_${interval}`
    return this.subscribe(streamName, callback)
  }

  /**
   * 订阅Ticker数据
   * @param {string} symbol - 交易对，如 'BTCUSDT'
   * @param {function} callback - 数据回调函数
   * @returns {string} 订阅ID
   */
  subscribeTicker(symbol, callback) {
    const streamName = `${symbol.toLowerCase()}@ticker`
    return this.subscribe(streamName, callback)
  }

  /**
   * 通用订阅方法
   * @param {string} streamName - 流名称
   * @param {function} callback - 回调函数
   * @returns {string} 订阅ID
   */
  subscribe(streamName, callback) {
    const connectionKey = this.getOptimalConnection()
    let connection = this.connections.get(connectionKey)

    if (!connection) {
      connection = this.createConnection(connectionKey)
    }

    // 添加流到连接
    connection.streams.add(streamName)
    
    // 添加订阅者
    if (!connection.subscribers.has(streamName)) {
      connection.subscribers.set(streamName, new Set())
    }
    connection.subscribers.get(streamName).add(callback)

    // 如果连接已建立，发送订阅消息
    if (connection.status === ConnectionStatus.CONNECTED) {
      this.sendSubscribeMessage(connection.ws, [streamName])
    }

    console.log(`📡 订阅流: ${streamName}`)
    return `${connectionKey}_${streamName}`
  }

  /**
   * 取消订阅
   * @param {string} subscriptionId - 订阅ID
   * @param {function} callback - 要移除的回调函数
   */
  unsubscribe(subscriptionId, callback) {
    const [connectionKey, streamName] = subscriptionId.split('_', 2)
    const connection = this.connections.get(connectionKey)

    if (!connection || !connection.subscribers.has(streamName)) {
      return
    }

    const subscribers = connection.subscribers.get(streamName)
    subscribers.delete(callback)

    // 如果没有订阅者了，取消流订阅
    if (subscribers.size === 0) {
      connection.subscribers.delete(streamName)
      connection.streams.delete(streamName)

      if (connection.status === ConnectionStatus.CONNECTED) {
        this.sendUnsubscribeMessage(connection.ws, [streamName])
      }

      console.log(`📡 取消订阅流: ${streamName}`)

      // 如果连接没有流了，关闭连接
      if (connection.streams.size === 0) {
        this.closeConnection(connectionKey)
      }
    }
  }

  /**
   * 获取最优连接
   * 选择流数量最少的连接，或创建新连接
   */
  getOptimalConnection() {
    let minStreams = this.maxStreamsPerConnection
    let optimalKey = `connection_${Date.now()}`

    for (const [key, connection] of this.connections) {
      if (connection.streams.size < minStreams && connection.status !== ConnectionStatus.ERROR) {
        minStreams = connection.streams.size
        optimalKey = key
      }
    }

    return optimalKey
  }

  /**
   * 创建新连接
   * @param {string} connectionKey - 连接键
   */
  createConnection(connectionKey) {
    const wsUrl = `${this.baseUrl}/ws`
    const ws = new WebSocket(wsUrl)

    const connection = {
      ws,
      streams: new Set(),
      status: ConnectionStatus.CONNECTING,
      reconnectAttempts: 0,
      lastPingTime: Date.now(),
      subscribers: new Map()
    }

    this.connections.set(connectionKey, connection)
    this.setupWebSocketHandlers(connectionKey, connection)

    console.log(`🔌 创建WebSocket连接: ${connectionKey}`)
    return connection
  }

  /**
   * 设置WebSocket事件处理器
   * @param {string} connectionKey - 连接键
   * @param {object} connection - 连接信息
   */
  setupWebSocketHandlers(connectionKey, connection) {
    const { ws } = connection

    ws.onopen = () => {
      console.log(`✅ WebSocket连接成功: ${connectionKey}`)
      connection.status = ConnectionStatus.CONNECTED
      connection.reconnectAttempts = 0
      connection.lastPingTime = Date.now()

      // 订阅所有流
      if (connection.streams.size > 0) {
        this.sendSubscribeMessage(ws, Array.from(connection.streams))
      }
    }

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        
        // 处理不同类型的数据
        if (data.e === 'kline') {
          this.handleKlineData(data, connection)
        } else if (data.e === '24hrTicker') {
          this.handleTickerData(data, connection)
        }
        
        // 更新最后活动时间
        connection.lastPingTime = Date.now()
      } catch (error) {
        console.error('❌ WebSocket数据解析错误:', error)
      }
    }

    ws.onerror = (error) => {
      console.error(`❌ WebSocket错误 ${connectionKey}:`, error)
      connection.status = ConnectionStatus.ERROR
    }

    ws.onclose = (event) => {
      console.log(`🔌 WebSocket连接关闭 ${connectionKey}:`, event.code, event.reason)
      
      if (connection.status !== ConnectionStatus.ERROR) {
        connection.status = ConnectionStatus.DISCONNECTED
        this.handleReconnect(connectionKey, connection)
      }
    }
  }

  /**
   * 处理K线数据
   * @param {object} data - Binance K线数据
   * @param {object} connection - 连接信息
   */
  handleKlineData(data, connection) {
    const streamName = `${data.s.toLowerCase()}@kline_${data.k.i}`
    const subscribers = connection.subscribers.get(streamName)

    if (!subscribers || subscribers.size === 0) return

    const klineData = {
      timestamp: data.k.t,
      open: parseFloat(data.k.o),
      high: parseFloat(data.k.h),
      low: parseFloat(data.k.l),
      close: parseFloat(data.k.c),
      volume: parseFloat(data.k.v),
      turnover: parseFloat(data.k.q),
      isFinal: data.k.x
    }

    // 通知所有订阅者
    subscribers.forEach((callback) => {
      try {
        callback(klineData)
      } catch (error) {
        console.error('❌ K线数据回调执行错误:', error)
      }
    })
  }

  /**
   * 处理Ticker数据
   * @param {object} data - Binance Ticker数据
   * @param {object} connection - 连接信息
   */
  handleTickerData(data, connection) {
    const streamName = `${data.s.toLowerCase()}@ticker`
    const subscribers = connection.subscribers.get(streamName)

    if (!subscribers || subscribers.size === 0) return

    const tickerData = {
      symbol: data.s,
      price: parseFloat(data.c),
      priceChange: parseFloat(data.p),
      priceChangePercent: parseFloat(data.P),
      volume: parseFloat(data.v),
      high: parseFloat(data.h),
      low: parseFloat(data.l),
      open: parseFloat(data.o),
      timestamp: data.E
    }

    // 通知所有订阅者
    subscribers.forEach((callback) => {
      try {
        callback(tickerData)
      } catch (error) {
        console.error('❌ Ticker数据回调执行错误:', error)
      }
    })
  }

  /**
   * 发送订阅消息
   * @param {WebSocket} ws - WebSocket连接
   * @param {string[]} streams - 流名称数组
   */
  sendSubscribeMessage(ws, streams) {
    if (ws.readyState !== WebSocket.OPEN) return

    const message = {
      method: 'SUBSCRIBE',
      params: streams,
      id: Date.now()
    }

    ws.send(JSON.stringify(message))
    console.log(`📡 发送订阅消息:`, streams)
  }

  /**
   * 发送取消订阅消息
   * @param {WebSocket} ws - WebSocket连接
   * @param {string[]} streams - 流名称数组
   */
  sendUnsubscribeMessage(ws, streams) {
    if (ws.readyState !== WebSocket.OPEN) return

    const message = {
      method: 'UNSUBSCRIBE',
      params: streams,
      id: Date.now()
    }

    ws.send(JSON.stringify(message))
    console.log(`📡 发送取消订阅消息:`, streams)
  }

  /**
   * 处理重连逻辑
   * @param {string} connectionKey - 连接键
   * @param {object} connection - 连接信息
   */
  handleReconnect(connectionKey, connection) {
    if (connection.streams.size === 0) {
      // 没有活跃流，直接删除连接
      this.connections.delete(connectionKey)
      return
    }

    if (connection.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error(`❌ 重连失败，已达到最大尝试次数: ${connectionKey}`)
      connection.status = ConnectionStatus.ERROR
      return
    }

    connection.status = ConnectionStatus.RECONNECTING
    connection.reconnectAttempts++

    const delay = this.reconnectDelay * Math.pow(2, connection.reconnectAttempts - 1)
    console.log(`🔄 尝试重连 ${connectionKey} (${connection.reconnectAttempts}/${this.maxReconnectAttempts}) 延迟: ${delay}ms`)

    setTimeout(() => {
      if (connection.streams.size > 0) {
        const newConnection = this.createConnection(connectionKey)
        // 复制订阅者信息
        newConnection.subscribers = connection.subscribers
        newConnection.streams = connection.streams
      }
    }, delay)
  }

  /**
   * 关闭指定连接
   * @param {string} connectionKey - 连接键
   */
  closeConnection(connectionKey) {
    const connection = this.connections.get(connectionKey)
    if (!connection) return

    if (connection.ws.readyState === WebSocket.OPEN) {
      connection.ws.close()
    }

    this.connections.delete(connectionKey)
    console.log(`🔌 连接已关闭: ${connectionKey}`)
  }

  /**
   * 关闭所有连接
   */
  closeAllConnections() {
    this.connections.forEach((_, key) => {
      this.closeConnection(key)
    })

    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer)
      this.healthCheckTimer = null
    }

    console.log('🔌 所有WebSocket连接已关闭')
  }

  /**
   * 开始健康检查
   */
  startHealthCheck() {
    this.healthCheckTimer = setInterval(() => {
      const now = Date.now()

      this.connections.forEach((connection, key) => {
        // 检查连接是否超时
        if (now - connection.lastPingTime > this.pingInterval * 2) {
          console.warn(`⚠️ 连接超时，准备重连: ${key}`)
          connection.ws.close()
        }
        // 检查连接活跃度，浏览器WebSocket不支持ping，通过数据接收时间判断
        else if (connection.status === ConnectionStatus.CONNECTED && 
                 now - connection.lastPingTime > this.pingInterval) {
          // 浏览器WebSocket会自动处理心跳，这里仅更新时间用于监控
          connection.lastPingTime = now
        }
      })
    }, this.pingInterval)
  }

  /**
   * 获取连接统计信息
   */
  getStats() {
    const stats = {
      totalConnections: this.connections.size,
      activeConnections: 0,
      totalStreams: 0,
      totalSubscribers: 0,
      connections: []
    }

    this.connections.forEach((connection, key) => {
      if (connection.status === ConnectionStatus.CONNECTED) {
        stats.activeConnections++
      }
      
      stats.totalStreams += connection.streams.size
      
      connection.subscribers.forEach((subscribers) => {
        stats.totalSubscribers += subscribers.size
      })

      stats.connections.push({
        key,
        status: connection.status,
        streams: connection.streams.size,
        subscribers: Array.from(connection.subscribers.keys()).length,
        reconnectAttempts: connection.reconnectAttempts
      })
    })

    return stats
  }

  /**
   * 获取连接状态
   * @param {string} symbol - 交易对
   * @param {string} interval - 时间间隔（可选，用于K线）
   * @returns {string} 连接状态
   */
  getConnectionStatus(symbol, interval) {
    const streamName = interval 
      ? `${symbol.toLowerCase()}@kline_${interval}`
      : `${symbol.toLowerCase()}@ticker`

    for (const connection of this.connections.values()) {
      if (connection.streams.has(streamName)) {
        return connection.status
      }
    }

    return ConnectionStatus.DISCONNECTED
  }
}

// 创建单例实例
const binanceWebSocketService = new BinanceWebSocketService()

export default binanceWebSocketService
export { BinanceWebSocketService, ConnectionStatus }