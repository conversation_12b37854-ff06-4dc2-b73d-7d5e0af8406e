#!/usr/bin/env python3
"""
测试时间戳分析服务
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'finance_project.settings')
django.setup()

from historical_data.timestamp_analysis_service import TimestampAnalysisService
import json
from datetime import datetime

def test_timestamp_analysis():
    """测试时间戳分析服务"""
    print("🕒 测试时间戳分析服务...")
    
    # 测试时间戳: 2025-07-29 00:00:00 UTC
    test_timestamp = 1753718400000
    test_datetime = datetime.fromtimestamp(test_timestamp / 1000)
    
    print(f"📅 测试时间戳: {test_timestamp}")
    print(f"📅 对应时间: {test_datetime}")
    
    try:
        service = TimestampAnalysisService()
        
        # 测试数据获取
        result = service.get_timestamp_analysis_data(
            timestamp=test_timestamp,
            symbol='BTCUSDT',
            interval='1m'
        )
        
        if 'error' in result:
            print(f"❌ 分析失败: {result['error']}")
            return False
        
        print(f"✅ 分析成功!")
        print(f"   目标时间: {result.get('target_datetime')}")
        print(f"   交易对: {result.get('symbol')}")
        print(f"   时间间隔: {result.get('interval')}")
        print(f"   总数据点: {result.get('total_data_points', 0)}")
        
        # 检查每日数据
        daily_data = result.get('daily_data', {})
        print(f"\n📊 每日数据:")
        for day_name, day_info in daily_data.items():
            if 'error' in day_info:
                print(f"   {day_name}: ❌ {day_info['error']}")
            else:
                print(f"   {day_name} ({day_info.get('date')}): {day_info.get('data_points', 0)} 条数据")
        
        # 检查技术指标
        tech_indicators = result.get('technical_indicators', {})
        print(f"\n📈 技术指标:")
        if 'error' in tech_indicators:
            print(f"   ❌ {tech_indicators['error']}")
        else:
            for indicator, values in tech_indicators.items():
                if isinstance(values, dict):
                    print(f"   {indicator}: {values}")
                else:
                    print(f"   {indicator}: {values}")
        
        # 检查纵向分析
        vertical_analysis = result.get('vertical_analysis', {})
        print(f"\n📊 纵向分析:")
        for analysis_type, analysis_result in vertical_analysis.items():
            print(f"   {analysis_type}: {analysis_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 时间戳分析服务测试")
    print("=" * 50)
    
    if test_timestamp_analysis():
        print("\n🎉 时间戳分析服务测试通过！")
    else:
        print("\n⚠️  时间戳分析服务测试失败")
        sys.exit(1)