#!/bin/bash

# 快速数据完整性检查脚本
# 检查最近3天的数据完整性，用于定时任务

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

LOG_FILE="logs/integrity_quick.log"
REPORT_DIR="logs/integrity_reports"

# 确保目录存在
mkdir -p "$REPORT_DIR"

# 写日志函数
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log_message "🔍 开始快速完整性检查..."

# 计算检查日期（最近3天）
START_DATE=$(date -d '3 days ago' '+%Y-%m-%d')
END_DATE=$(date '+%Y-%m-%d')

log_message "📅 检查日期范围: $START_DATE ~ $END_DATE"

# 执行完整性检查
REPORT_FILE="$REPORT_DIR/quick_check_$(date '+%Y%m%d_%H%M%S').txt"

python3 check_data_integrity.py \
    --start-date="$START_DATE" \
    --end-date="$END_DATE" \
    --output="$REPORT_FILE" 2>&1 | tee -a "$LOG_FILE"

EXIT_CODE=$?

# 检查结果分析
if [ $EXIT_CODE -eq 0 ]; then
    log_message "✅ 数据完整性检查通过"
elif [ $EXIT_CODE -eq 1 ]; then
    log_message "⚠️ 发现数据完整性问题，需要关注"
    
    # 提取关键信息
    COVERAGE=$(grep "总体覆盖率" "$REPORT_FILE" | grep -o '[0-9.]*%' | head -1)
    CRITICAL_GAPS=$(grep "重要缺失" "$REPORT_FILE" | grep -o '[0-9]*' | head -1)
    
    log_message "📊 总体覆盖率: $COVERAGE"
    log_message "🚨 重要缺失: ${CRITICAL_GAPS:-0} 个时段"
    
    # 如果覆盖率太低，发送警告
    COVERAGE_NUM=$(echo "$COVERAGE" | sed 's/%//')
    if (( $(echo "$COVERAGE_NUM < 80" | bc -l) )); then
        log_message "🚨 警告: 数据完整性严重不足 (<80%)，建议立即检查收集器状态"
        
        # 检查收集器进程状态
        COLLECTOR_PID=$(cat multi_collector.pid 2>/dev/null)
        if [ -n "$COLLECTOR_PID" ] && ps -p "$COLLECTOR_PID" > /dev/null 2>&1; then
            log_message "✅ 多收集器进程运行正常 (PID: $COLLECTOR_PID)"
        else
            log_message "❌ 多收集器进程异常，需要重启"
            
            # 可选：自动重启收集器
            # ./monitor_collector.sh
        fi
    fi
else
    log_message "❌ 完整性检查执行失败"
fi

# 清理旧报告（保留最近10个）
find "$REPORT_DIR" -name "quick_check_*.txt" -type f | sort -r | tail -n +11 | xargs rm -f 2>/dev/null

log_message "🏁 快速完整性检查完成"
log_message "📄 报告文件: $REPORT_FILE"
echo