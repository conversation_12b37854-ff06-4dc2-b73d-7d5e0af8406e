/* K<PERSON>ine<PERSON>hart 样式 */
.kline-chart {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  position: relative;
  height: 100%;
}

.kline-chart.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  border-radius: 0;
  border: none;
}

/* 图表工具栏 */
.chart-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--color-bg-secondary);
  border-bottom: 1px solid var(--color-border);
  min-height: 48px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-symbol {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.chart-interval {
  font-size: 14px;
  color: var(--color-text-secondary);
  background: var(--color-bg-primary);
  padding: 2px 8px;
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--color-border);
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.toolbar-btn {
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border);
  color: var(--color-text-primary);
  border-radius: var(--border-radius-sm);
  transition: all 0.2s ease;
}

.toolbar-btn:hover {
  border-color: var(--color-primary);
  background: rgba(68, 122, 83, 0.1);
  color: var(--color-primary);
}

.toolbar-btn:focus {
  border-color: var(--color-primary);
  background: rgba(68, 122, 83, 0.1);
  color: var(--color-primary);
}

/* 图表容器 */
.chart-wrapper {
  position: relative;
  height: calc(100% - 48px);
}

.chart-container {
  background: var(--color-bg-secondary);
  position: relative;
}

/* 图表加载状态 */
.chart-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  z-index: 10;
  background: var(--color-bg-secondary);
  padding: 24px;
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--color-border);
}

.chart-loading span {
  color: var(--color-text-secondary);
  font-size: 14px;
}

/* 自定义下拉菜单样式 */
.ant-dropdown-menu {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
}

.ant-dropdown-menu-item {
  color: var(--color-text-primary);
  background: var(--color-bg-secondary);
  transition: all 0.2s ease;
}

.ant-dropdown-menu-item:hover {
  background: var(--color-bg-hover);
  color: var(--color-primary);
}

.ant-dropdown-menu-submenu-title {
  color: var(--color-text-primary);
  background: var(--color-bg-secondary);
}

.ant-dropdown-menu-submenu-title:hover {
  background: var(--color-bg-hover);
  color: var(--color-primary);
}

.ant-dropdown-menu-divider {
  background: var(--color-border);
}

/* 指标选择器 */
.indicator-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.indicator-tag {
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid var(--color-border);
  background: var(--color-bg-primary);
  color: var(--color-text-secondary);
}

.indicator-tag.active {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: white;
}

.indicator-tag:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
}

/* 图表控件 */
.chart-controls {
  position: absolute;
  top: 60px;
  right: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 5;
}

.chart-control-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.chart-control-btn:hover {
  background: var(--color-bg-hover);
  border-color: var(--color-primary);
  color: var(--color-primary);
}

/* 价格信息显示 */
.price-info {
  position: absolute;
  top: 16px;
  left: 16px;
  background: var(--color-bg-secondary);
  padding: 12px 16px;
  border-radius: var(--border-radius-md);
  border: 1px solid var(--color-border);
  backdrop-filter: blur(10px);
  z-index: 5;
}

.price-current {
  font-size: 20px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 4px;
}

.price-change {
  font-size: 14px;
  font-weight: 500;
}

.price-change.positive {
  color: var(--color-up);
}

.price-change.negative {
  color: var(--color-down);
}

.price-volume {
  font-size: 12px;
  color: var(--color-text-secondary);
  margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-toolbar {
    padding: 8px 12px;
  }
  
  .toolbar-left {
    gap: 8px;
  }
  
  .chart-symbol {
    font-size: 14px;
  }
  
  .chart-interval {
    font-size: 12px;
    padding: 1px 6px;
  }
  
  .toolbar-btn {
    padding: 4px 8px;
    font-size: 12px;
  }
  
  .price-info {
    top: 12px;
    left: 12px;
    padding: 8px 12px;
  }
  
  .price-current {
    font-size: 16px;
  }
  
  .chart-controls {
    top: 50px;
    right: 12px;
  }
  
  .chart-control-btn {
    width: 32px;
    height: 32px;
  }
}

/* 图表主题覆盖 */
.kline-chart .klinecharts-tooltip {
  background: var(--color-bg-secondary) !important;
  border: 1px solid var(--color-border) !important;
  color: var(--color-text-primary) !important;
  border-radius: var(--border-radius-sm) !important;
  backdrop-filter: blur(10px);
}

.kline-chart .klinecharts-crosshair-info {
  background: var(--color-bg-secondary) !important;
  border: 1px solid var(--color-border) !important;
  color: var(--color-text-primary) !important;
}

/* 全屏模式特殊样式 */
.kline-chart.fullscreen .chart-toolbar {
  background: rgba(22, 27, 34, 0.9);
  backdrop-filter: blur(20px);
}

.kline-chart.fullscreen .price-info {
  background: rgba(22, 27, 34, 0.9);
  backdrop-filter: blur(20px);
}

.kline-chart.fullscreen .chart-controls {
  background: rgba(22, 27, 34, 0.9);
  backdrop-filter: blur(20px);
  border-radius: var(--border-radius-md);
  padding: 8px;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chart-loading {
  animation: fadeIn 0.3s ease-out;
}

.price-info {
  animation: fadeIn 0.3s ease-out;
}

/* 工具提示样式 */
.chart-tooltip {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  padding: 8px 12px;
  color: var(--color-text-primary);
  font-size: 12px;
  box-shadow: var(--shadow-md);
  z-index: 1000;
  max-width: 200px;
  line-height: 1.4;
}