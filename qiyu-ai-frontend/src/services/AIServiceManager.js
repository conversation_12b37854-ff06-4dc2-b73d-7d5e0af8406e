/**
 * AI服务管理器 - 全局管理AI分析服务的启动和配置
 * 在应用启动时自动开始AI分析服务
 */

import autoAIAnalysisService from './AutoAIAnalysisService';

class AIServiceManager {
  constructor() {
    this.isInitialized = false;
    this.defaultSymbols = ['BTC', 'ETH', 'SOL', 'XRP', 'DOGE'];
    this.autoStartEnabled = true; // 是否自动启动
    this.startupDelay = 5000; // 启动延迟（毫秒）
  }

  /**
   * 初始化AI服务管理器
   * 在应用启动时调用
   */
  async initialize() {
    if (this.isInitialized) {
      console.log('🔄 AI服务管理器已初始化');
      return;
    }

    console.log('🚀 初始化AI服务管理器...');
    
    try {
      // 检查后端服务是否可用
      const isBackendAvailable = await this.checkBackendHealth();
      
      if (!isBackendAvailable) {
        console.warn('⚠️ 后端服务不可用，延迟启动AI分析');
        // 延迟重试
        setTimeout(() => this.initialize(), 30000); // 30秒后重试
        return;
      }

      // 如果启用自动启动
      if (this.autoStartEnabled) {
        console.log(`⏰ ${this.startupDelay/1000}秒后自动启动AI分析服务...`);
        
        setTimeout(() => {
          this.startAutoAnalysis();
        }, this.startupDelay);
      }

      this.isInitialized = true;
      console.log('✅ AI服务管理器初始化完成');

    } catch (error) {
      console.error('❌ AI服务管理器初始化失败:', error);
      // 重试机制
      setTimeout(() => this.initialize(), 60000); // 1分钟后重试
    }
  }

  /**
   * 检查后端服务健康状态
   */
  async checkBackendHealth() {
    try {
      const baseURL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000';
      const response = await fetch(`${baseURL}/api/ai/quotation/binance/api/v3/ticker/24hr?symbol=BTCUSDT`, {
        method: 'GET',
        timeout: 5000
      });
      
      return response.ok;
    } catch (error) {
      console.warn('后端健康检查失败:', error.message);
      return false;
    }
  }

  /**
   * 启动自动AI分析
   */
  startAutoAnalysis() {
    if (autoAIAnalysisService.isRunning) {
      console.log('🔄 AI分析服务已在运行中');
      return;
    }

    console.log('🤖 启动自动AI分析服务...');
    console.log(`📊 分析币种: ${this.defaultSymbols.join(', ')}`);
    console.log('⏰ 分析间隔: 15分钟');

    try {
      autoAIAnalysisService.start(this.defaultSymbols);
      
      // 监听服务事件
      autoAIAnalysisService.subscribe((event) => {
        this.handleServiceEvent(event);
      });

      console.log('✅ AI分析服务启动成功');
      
      // 发送启动通知
      this.notifyServiceStarted();

    } catch (error) {
      console.error('❌ AI分析服务启动失败:', error);
    }
  }

  /**
   * 停止自动AI分析
   */
  stopAutoAnalysis() {
    console.log('🛑 停止自动AI分析服务...');
    autoAIAnalysisService.stop();
    console.log('✅ AI分析服务已停止');
  }

  /**
   * 处理AI服务事件
   */
  handleServiceEvent(event) {
    switch (event.type) {
      case 'service_started':
        console.log('🚀 AI分析服务已启动');
        break;
        
      case 'analysis_started':
        console.log('🔄 开始新一轮AI分析');
        break;
        
      case 'analysis_completed':
        const { results, errors } = event.data;
        const successCount = Object.keys(results).length - errors.length;
        console.log(`🎉 AI分析完成: 成功${successCount}个，失败${errors.length}个`);
        
        if (errors.length > 0) {
          console.warn('⚠️ 分析错误:', errors);
        }
        break;
        
      case 'service_stopped':
        console.log('🛑 AI分析服务已停止');
        break;
        
      default:
        // 其他事件静默处理
        break;
    }
  }

  /**
   * 发送服务启动通知
   */
  notifyServiceStarted() {
    // 可以在这里添加用户通知逻辑
    // 例如：显示Toast通知、发送系统通知等
    
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('AI分析服务已启动', {
        body: `正在分析 ${this.defaultSymbols.join(', ')} 等币种，每15分钟更新一次`,
        icon: '/favicon.ico'
      });
    }
  }

  /**
   * 请求通知权限
   */
  async requestNotificationPermission() {
    if ('Notification' in window && Notification.permission === 'default') {
      const permission = await Notification.requestPermission();
      console.log('通知权限:', permission);
      return permission === 'granted';
    }
    return false;
  }

  /**
   * 配置AI服务
   */
  configure(options = {}) {
    const {
      symbols = this.defaultSymbols,
      autoStart = this.autoStartEnabled,
      startupDelay = this.startupDelay
    } = options;

    this.defaultSymbols = symbols;
    this.autoStartEnabled = autoStart;
    this.startupDelay = startupDelay;

    console.log('⚙️ AI服务配置已更新:', {
      symbols: this.defaultSymbols,
      autoStart: this.autoStartEnabled,
      startupDelay: this.startupDelay
    });
  }

  /**
   * 获取服务状态
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isRunning: autoAIAnalysisService.isRunning,
      symbols: this.defaultSymbols,
      autoStartEnabled: this.autoStartEnabled,
      analysisStatus: autoAIAnalysisService.getStatus()
    };
  }

  /**
   * 手动重启服务
   */
  restart() {
    console.log('🔄 重启AI分析服务...');
    this.stopAutoAnalysis();
    
    setTimeout(() => {
      this.startAutoAnalysis();
    }, 2000); // 2秒后重启
  }
}

// 创建全局实例
const aiServiceManager = new AIServiceManager();

export default aiServiceManager;
