#!/bin/bash

# 数据完整性检查定时任务配置脚本
# 配置cron定时任务进行自动化数据完整性监控

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "🔧 配置数据完整性检查定时任务..."

# 确保脚本有执行权限
chmod +x "$SCRIPT_DIR/quick_integrity_check.sh"
chmod +x "$SCRIPT_DIR/check_data_integrity.py"

echo "✅ 脚本权限设置完成"

# 备份当前crontab
crontab -l > /tmp/crontab_backup_$(date +%Y%m%d_%H%M%S) 2>/dev/null || echo "# 新建crontab" > /tmp/crontab_backup_$(date +%Y%m%d_%H%M%S)

echo "📋 当前crontab备份完成"

# 创建新的crontab条目
CRON_ENTRIES=$(cat << 'EOF'

# 数据完整性检查任务
# 每小时执行快速检查（检查最近3天）
30 * * * * cd /home/<USER>/qiyuai-web/finance_backend && ./quick_integrity_check.sh

# 每天凌晨2点执行详细检查（检查最近7天）
0 2 * * * cd /home/<USER>/qiyuai-web/finance_backend && python3 check_data_integrity.py --start-date=$(date -d '7 days ago' '+\%Y-\%m-\%d') --output=logs/integrity_reports/daily_check_$(date '+\%Y\%m\%d').txt

# 每周日凌晨3点执行完整检查（检查最近30天）
0 3 * * 0 cd /home/<USER>/qiyuai-web/finance_backend && python3 check_data_integrity.py --start-date=$(date -d '30 days ago' '+\%Y-\%m-\%d') --output=logs/integrity_reports/weekly_check_$(date '+\%Y\%m\%d').txt

EOF
)

# 检查是否已存在相关任务
if crontab -l 2>/dev/null | grep -q "integrity_check\|quick_integrity_check"; then
    echo "⚠️ 发现已存在的完整性检查任务"
    echo "📋 当前相关任务:"
    crontab -l 2>/dev/null | grep -E "integrity_check|quick_integrity_check"
    echo ""
    read -p "是否要替换现有任务? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # 删除现有的完整性检查任务
        crontab -l 2>/dev/null | grep -v -E "integrity_check|quick_integrity_check" | crontab -
        echo "🗑️ 已删除现有任务"
    else
        echo "❌ 取消配置，保持现有任务不变"
        exit 0
    fi
fi

# 添加新任务到crontab
(crontab -l 2>/dev/null; echo "$CRON_ENTRIES") | crontab -

echo "✅ 定时任务配置完成!"
echo ""
echo "📅 配置的定时任务:"
echo "  • 每小时检查: 30分执行快速检查（最近3天）"
echo "  • 每日检查:   凌晨2点执行详细检查（最近7天）"
echo "  • 每周检查:   周日凌晨3点执行完整检查（最近30天）"
echo ""
echo "📋 当前完整crontab:"
crontab -l

echo ""
echo "🔍 测试脚本执行:"
echo "手动测试: ./quick_integrity_check.sh"
echo "详细检查: python3 check_data_integrity.py --start-date=2025-07-30"
echo ""
echo "📄 日志文件位置:"
echo "  • 快速检查日志: logs/integrity_quick.log"
echo "  • 详细报告目录: logs/integrity_reports/"
echo ""
echo "💡 监控建议:"
echo "  • 使用 tail -f logs/integrity_quick.log 监控检查状态"
echo "  • 覆盖率低于80%时需要立即关注"
echo "  • 重要缺失时段超过10个时需要修复"