#!/usr/bin/env python3
"""
基础API测试 - 不包含时间戳参数
"""
import requests
import json

def test_basic_api():
    """测试基础API调用"""
    print("🔗 测试基础API调用...")
    
    # 构建最简单的请求数据
    request_data = {
        "messages": [
            {"role": "user", "content": "Hello"}
        ]
    }
    
    try:
        print("📡 发送基础API请求...")
        response = requests.post(
            'http://localhost:8000/api/ai/chat/',
            headers={'Content-Type': 'application/json'},
            json=request_data,
            timeout=10  # 减少到10秒
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 基础API响应成功!")
            return True
        else:
            print(f"❌ 基础API请求失败: {response.status_code}")
            print(f"   错误内容: {response.text[:100]}...")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 基础API请求超时（10秒）")
        return False
    except Exception as e:
        print(f"❌ 基础API其他错误: {e}")
        return False

if __name__ == "__main__":
    print("🚀 基础API测试")
    print("=" * 30)
    
    if test_basic_api():
        print("\n🎉 基础API测试通过！")
    else:
        print("\n⚠️  基础API测试失败")