import React from 'react';
import { Card, List, Tag, Empty, Tooltip } from 'antd';
import { ClockCircleOutlined } from '@ant-design/icons';

const PredictionHistory = ({ predictions = [], symbol = 'BTCUSDT', title = "AI预测分析" }) => {
  if (!predictions || predictions.length === 0) {
    return (
      <Card title={title} size="small" style={{ width: '100%' }}>
        <Empty 
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="暂无预测历史"
          style={{ margin: '20px 0' }}
        />
      </Card>
    );
  }

  const getSentimentColor = (sentimentScore) => {
    if (sentimentScore > 0) return '#52c41a';  // Green for bullish
    if (sentimentScore < 0) return '#ff4d4f';  // Red for bearish
    return '#faad14';  // Orange for neutral
  };

  const getSentimentText = (sentimentScore) => {
    if (sentimentScore > 0) return '看涨';
    if (sentimentScore < 0) return '看跌';
    return '中性';
  };

  const formatTime = (timeStr) => {
    try {
      const date = new Date(timeStr);
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return timeStr;
    }
  };

  const formatPrice = (price) => {
    if (!price || price === 0) return '数据异常';
    return `$${parseFloat(price).toLocaleString()}`;
  };

  return (
    <div style={{
      background: 'rgba(255, 255, 255, 0.03)',
      border: '1px solid rgba(255, 255, 255, 0.08)',
      padding: '5px 16px 5px 16px',
      color: '#fff',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      overflowY: 'auto'
    }}>
      {/* Title header */}
      <div style={{ 
        fontSize: 14, 
        fontWeight: 500, 
        color: '#fff', 
        marginBottom: 8,
        padding: '8px 0'
      }}>
        {title}
      </div>
      
      <List
        size="small"
        dataSource={predictions} // Show all predictions with scroll
        renderItem={(prediction, index) => (
          <List.Item
            key={prediction.id || index}
            style={{ 
              padding: '12px 12px',
              borderBottom: index === predictions.length - 1 ? 'none' : '1px solid #f0f0f0',
              minHeight: '80px'
            }}
          >
            {/* Single row layout with all non-content elements */}
            <div style={{ width: '100%' }}>
              {/* All non-content elements in one row */}
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: 8,
                marginBottom: 8,
                flexWrap: 'wrap'
              }}>
                <ClockCircleOutlined style={{ color: '#999', fontSize: 12 }} />
                <span style={{ fontSize: 12, color: '#666' }}>
                  {formatTime(prediction.created_at)}
                </span>
                <Tag 
                  color={getSentimentColor(prediction.sentiment_score)}
                  size="small"
                >
                  {getSentimentText(prediction.sentiment_score)}
                </Tag>
              </div>
              
              {/* Second row: AI Analysis Summary with tooltip */}
              {prediction.analysis_summary && (
                <Tooltip 
                  title={prediction.analysis_summary}
                  placement="topLeft"
                  overlayStyle={{ maxWidth: '400px' }}
                >
                  <div style={{ 
                    fontSize: 11, 
                    color: '#666', 
                    lineHeight: '1.4',
                    marginBottom: 8,
                    maxHeight: '2.8em',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical',
                    cursor: 'help'
                  }}>
                    <strong style={{ color: '#888', fontSize: 10 }}>AI分析: </strong>
                    {prediction.analysis_summary}
                  </div>
                </Tooltip>
              )}
              
              {/* Third row: Prediction distribution tags */}
              <div style={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                {prediction.prediction_distribution && Object.entries(prediction.prediction_distribution).map(([key, value]) => (
                  value > 0 && (
                    <Tag 
                      key={key} 
                      size="small" 
                      style={{ margin: 0, fontSize: 9, padding: '0 4px' }}
                      color={
                        key.includes('看涨') ? 'red' : 
                        key.includes('看跌') ? 'green' : 
                        'orange'
                      }
                    >
                      {key}:{value}%
                    </Tag>
                  )
                ))}
              </div>
            </div>
          </List.Item>
        )}
      />
    </div>
  );
};

export default PredictionHistory;