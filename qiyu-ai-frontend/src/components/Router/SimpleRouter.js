import React, { useState, useEffect } from 'react';
import { message } from 'antd';
import Dashboard from '../../pages/Dashboard/Dashboard.advanced';
import AuthPage from '../../pages/AuthPage';
import Profile from '../../pages/Profile/Profile';
import { useAuth } from '../../contexts/AuthContext';

const SimpleRouter = () => {
  const [currentPath, setCurrentPath] = useState(window.location.pathname);
  const { user, loading } = useAuth();

  useEffect(() => {
    const handlePopState = () => {
      setCurrentPath(window.location.pathname);
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  // 检查是否已登录
  const isAuthenticated = () => {
    const token = localStorage.getItem('access_token');
    return !!token && !!user;
  };

  // 简单的路由匹配
  const renderPage = () => {
    // 如果正在加载认证状态，显示加载页面
    if (loading) {
      return (
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          backgroundColor: '#0d1117',
          color: '#f0f6fc'
        }}>
          加载中...
        </div>
      );
    }

    switch (currentPath) {
      case '/auth':
        // 如果已登录，重定向到首页
        if (isAuthenticated()) {
          window.location.href = '/';
          return null;
        }
        return <AuthPage />;

      case '/profile':
        // Profile页面需要鉴权
        if (!isAuthenticated()) {
          message.error('请先登录');
          window.location.href = '/auth';
          return null;
        }
        return <Profile />;

      case '/':
      default:
        // 其他页面不需要鉴权，但如果已登录会传递JWT
        return <Dashboard />;
    }
  };

  return renderPage();
};

export default SimpleRouter;
