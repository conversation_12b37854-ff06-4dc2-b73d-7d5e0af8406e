from django.db import models


class HistoricalKlineData(models.Model):
    """历史K线数据模型"""
    
    INTERVAL_CHOICES = [
        ('1m', '1分钟'),
        ('3m', '3分钟'),
        ('5m', '5分钟'),
        ('15m', '15分钟'),
        ('30m', '30分钟'),
        ('1h', '1小时'),
        ('2h', '2小时'),
        ('4h', '4小时'),
        ('6h', '6小时'),
        ('8h', '8小时'),
        ('12h', '12小时'),
        ('1d', '1天'),
        ('3d', '3天'),
        ('1w', '1周'),
        ('1mo', '1月'),
    ]
    
    symbol = models.CharField(max_length=20, db_index=True, help_text="交易对符号")
    interval = models.CharField(max_length=10, choices=INTERVAL_CHOICES, db_index=True, help_text="时间间隔")
    open_time = models.DateTimeField(db_index=True, help_text="开盘时间")
    close_time = models.DateTimeField(help_text="收盘时间")
    
    open_price = models.DecimalField(max_digits=20, decimal_places=8, help_text="开盘价")
    high_price = models.DecimalField(max_digits=20, decimal_places=8, help_text="最高价")
    low_price = models.DecimalField(max_digits=20, decimal_places=8, help_text="最低价")
    close_price = models.DecimalField(max_digits=20, decimal_places=8, help_text="收盘价")
    volume = models.DecimalField(max_digits=20, decimal_places=8, help_text="成交量")
    quote_volume = models.DecimalField(max_digits=20, decimal_places=8, help_text="成交额")
    trades_count = models.IntegerField(help_text="成交笔数")
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'historical_kline_data'
        indexes = [
            models.Index(fields=['symbol', 'interval', 'open_time']),
            models.Index(fields=['symbol', 'open_time']),
            models.Index(fields=['interval', 'open_time']),
        ]
        unique_together = ['symbol', 'interval', 'open_time']
        ordering = ['-open_time']
    
    def __str__(self):
        return f"{self.symbol} {self.interval} {self.open_time}"


class TechnicalIndicatorData(models.Model):
    """技术指标数据模型"""
    
    symbol = models.CharField(max_length=20, db_index=True)
    interval = models.CharField(max_length=10, db_index=True)
    timestamp = models.DateTimeField(db_index=True)
    
    # MACD指标
    macd_line = models.DecimalField(max_digits=20, decimal_places=8, null=True, blank=True)
    macd_signal = models.DecimalField(max_digits=20, decimal_places=8, null=True, blank=True)
    macd_histogram = models.DecimalField(max_digits=20, decimal_places=8, null=True, blank=True)
    
    # RSI指标
    rsi = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True)
    
    # KDJ指标
    kdj_k = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True)
    kdj_d = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True)
    kdj_j = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True)
    
    # 移动平均线
    ma5 = models.DecimalField(max_digits=20, decimal_places=8, null=True, blank=True)
    ma10 = models.DecimalField(max_digits=20, decimal_places=8, null=True, blank=True)
    ma20 = models.DecimalField(max_digits=20, decimal_places=8, null=True, blank=True)
    ma50 = models.DecimalField(max_digits=20, decimal_places=8, null=True, blank=True)
    
    # 布林带
    bb_upper = models.DecimalField(max_digits=20, decimal_places=8, null=True, blank=True)
    bb_middle = models.DecimalField(max_digits=20, decimal_places=8, null=True, blank=True)
    bb_lower = models.DecimalField(max_digits=20, decimal_places=8, null=True, blank=True)
    bb_width = models.DecimalField(max_digits=10, decimal_places=6, null=True, blank=True)
    bb_position = models.DecimalField(max_digits=5, decimal_places=4, null=True, blank=True)
    
    # 威廉指标
    williams_r = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True)
    
    # 成交量指标
    volume_ma = models.DecimalField(max_digits=20, decimal_places=8, null=True, blank=True)
    volume_ratio = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'technical_indicator_data'
        indexes = [
            models.Index(fields=['symbol', 'interval', 'timestamp']),
        ]
        unique_together = ['symbol', 'interval', 'timestamp']
        ordering = ['-timestamp']


class AIAnalysisResult(models.Model):
    """AI分析结果模型"""
    
    RECOMMENDATION_CHOICES = [
        ('BUY', '买入'),
        ('SELL', '卖出'),
        ('HOLD', '持有'),
    ]
    
    SENTIMENT_CHOICES = [
        ('bullish', '看涨'),
        ('bearish', '看跌'),
        ('neutral', '中性'),
    ]
    
    symbol = models.CharField(max_length=20, db_index=True)
    timestamp = models.DateTimeField(db_index=True)
    
    current_price = models.DecimalField(max_digits=20, decimal_places=8)
    price_change_24h = models.DecimalField(max_digits=20, decimal_places=8)
    price_change_pct = models.DecimalField(max_digits=10, decimal_places=4)
    volume_24h = models.DecimalField(max_digits=30, decimal_places=8)
    
    # AI分析结果
    ai_analysis = models.TextField(help_text="AI分析内容")
    recommendation = models.CharField(max_length=10, choices=RECOMMENDATION_CHOICES)
    confidence = models.DecimalField(max_digits=5, decimal_places=2, help_text="信心度(0-100)")
    market_sentiment = models.CharField(max_length=10, choices=SENTIMENT_CHOICES)
    
    # 关联技术指标
    technical_indicator = models.ForeignKey(
        TechnicalIndicatorData, 
        on_delete=models.CASCADE,
        related_name='ai_analyses'
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'ai_analysis_result'
        indexes = [
            models.Index(fields=['symbol', 'timestamp']),
            models.Index(fields=['recommendation', 'timestamp']),
        ]
        ordering = ['-timestamp']