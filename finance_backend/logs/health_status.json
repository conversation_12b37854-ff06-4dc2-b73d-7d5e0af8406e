{"timestamp": "2025-07-31T12:58:19.911939", "checks": {"duplicate_servers": {"count": 2, "servers": [{"pid": 1949214, "cmdline": "python3 manage.py runserver 0.0.0.0:8000", "create_time": "2025-07-31 12:55:44.690000", "memory_mb": 39.2890625}, {"pid": 1949222, "cmdline": "/usr/bin/python3 manage.py runserver 0.0.0.0:8000", "create_time": "2025-07-31 12:55:45.140000", "memory_mb": 131.4609375}], "status": "warning"}, "systemd_service": {"is_active": true, "status_output": "● crypto-collector.service - Cryptocurrency Data Collector\n     Loaded: loaded (/etc/systemd/system/crypto-collector.service; enabled; vendor preset: enabled)\n     Active: active (running) since Thu 2025-07-31 12:30:02 CST; 28min ago\n   Main PID: 1929886 (python3)\n      Tasks: 5 (limit: 9151)\n     Memory: 109.2M (max: 500.0M available: 390.7M)\n        CPU: 3.022s\n     CGroup: /system.slice/crypto-collector.service\n             └─1929886 /usr/bin/python3 manage.py collect_multi_data --daemon\n\nJul 31 12:30:10 VM-0-5-ubuntu crypto-collector[1929886]: 2025-07-31 12:30:10,905 - INFO - 🔀 数据合并完成: 原有720条 + 新增30条 + 更新1条 = 总计750条\nJul 31 12:30:11 VM-0-5-ubuntu crypto-collector[1929886]: 2025-07-31 12:30:11,101 - INFO - 📤 COS合并上传: 新增31条, 总计750条\nJul 31 12:30:11 VM-0-5-ubuntu crypto-collector[1929886]: 2025-07-31 12:30:11,101 - INFO - 📤 COS上传完成: 1/1 个文件\nJul 31 12:30:11 VM-0-5-ubuntu crypto-collector[1929886]: 2025-07-31 12:30:11,101 - INFO - ✅ 缺口修复完成: 31条数据已上传到COS\nJul 31 12:30:11 VM-0-5-ubuntu crypto-collector[1929886]: 2025-07-31 12:30:11,101 - INFO - ✅ SOLUSDT 缺口修复完成\nJul 31 12:30:11 VM-0-5-ubuntu crypto-collector[1929886]: 2025-07-31 12:30:11,102 - INFO - ⏰ 定时上传任务已启动 (间隔: 300秒)\nJul 31 12:30:11 VM-0-5-ubuntu crypto-collector[1929886]: 2025-07-31 12:30:11,102 - INFO - 🚀 启动WebSocket实时数据收集...\nJul 31 12:30:11 VM-0-5-ubuntu crypto-collector[1929886]: 2025-07-31 12:30:11,102 - INFO - 🔗 尝试连接WebSocket: 3个数据流\nJul 31 12:30:11 VM-0-5-ubuntu crypto-collector[1929886]: 2025-07-31 12:30:11,323 - INFO - ✅ WebSocket连接成功: 3个数据流\nJul 31 12:30:11 VM-0-5-ubuntu crypto-collector[1929886]: 2025-07-31 12:30:11,324 - INFO - 📡 已订阅 3 个数据流\n", "last_check": "2025-07-31T12:58:19.974834"}, "data_gaps": {"gaps": {"BTCUSDT": {"type": "prediction_gap", "last_prediction": "2025-07-31T04:33:03.147469+00:00", "gap_minutes": 10.2853388, "expected_next": "2025-07-31T04:48:03.147469+00:00"}, "ETHUSDT": {"type": "prediction_gap", "last_prediction": "2025-07-31T04:33:28.786232+00:00", "gap_minutes": 9.8580518, "expected_next": "2025-07-31T04:48:28.786232+00:00"}, "BNBUSDT": {"type": "prediction_gap", "last_prediction": "2025-07-31T04:33:51.020540+00:00", "gap_minutes": 9.487502633333333, "expected_next": "2025-07-31T04:48:51.020540+00:00"}, "ADAUSDT": {"type": "prediction_gap", "last_prediction": "2025-07-31T04:34:15.611167+00:00", "gap_minutes": 9.077681, "expected_next": "2025-07-31T04:49:15.611167+00:00"}, "XRPUSDT": {"type": "prediction_gap", "last_prediction": "2025-07-31T04:34:38.210207+00:00", "gap_minutes": 8.70105465, "expected_next": "2025-07-31T04:49:38.210207+00:00"}, "SOLUSDT": {"type": "prediction_gap", "last_prediction": "2025-07-31T04:35:02.266620+00:00", "gap_minutes": 8.300137933333334, "expected_next": "2025-07-31T04:50:02.266620+00:00"}, "DOGEUSDT": {"type": "prediction_gap", "last_prediction": "2025-07-31T04:35:25.814943+00:00", "gap_minutes": 7.907689966666666, "expected_next": "2025-07-31T04:50:25.814943+00:00"}}, "status": "warning"}, "system_resources": {"cpu_percent": 3.8, "memory_percent": 51.3, "disk_percent": 8.2, "status": "ok"}}, "auto_repairs": {"BTCUSDT": {"action": "logged_gap", "gap_minutes": 10.2853388, "success": true, "note": "预测缺失已记录，等待下次调度"}, "ETHUSDT": {"action": "logged_gap", "gap_minutes": 9.8580518, "success": true, "note": "预测缺失已记录，等待下次调度"}, "BNBUSDT": {"action": "logged_gap", "gap_minutes": 9.487502633333333, "success": true, "note": "预测缺失已记录，等待下次调度"}, "ADAUSDT": {"action": "logged_gap", "gap_minutes": 9.077681, "success": true, "note": "预测缺失已记录，等待下次调度"}, "XRPUSDT": {"action": "logged_gap", "gap_minutes": 8.70105465, "success": true, "note": "预测缺失已记录，等待下次调度"}, "SOLUSDT": {"action": "logged_gap", "gap_minutes": 8.300137933333334, "success": true, "note": "预测缺失已记录，等待下次调度"}, "DOGEUSDT": {"action": "logged_gap", "gap_minutes": 7.907689966666666, "success": true, "note": "预测缺失已记录，等待下次调度"}, "duplicate_servers": {"action": "resolved_duplicates", "success": true}}}