/* 自定义按钮样式 */
.qiyu-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  box-shadow: none;
}

/* 主要按钮 */
.qiyu-button--primary {
  background: var(--button-gradient, linear-gradient(135deg, #4CAF50 0%, #45a049 100%));
  color: white;
  border: none;
  transition: all 0.3s ease;
}

.qiyu-button--primary:hover {
  background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.qiyu-button--primary:active {
  transform: translateY(0);
}

/* 次要按钮 */
.qiyu-button--secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.qiyu-button--secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  border-color: rgba(255, 255, 255, 0.3);
}

/* 幽灵按钮 */
.qiyu-button--ghost {
  background: transparent;
  color: #4CAF50;
  border: 1px solid #4CAF50;
}

.qiyu-button--ghost:hover {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
  border-color: #4CAF50;
}

/* 危险按钮 */
.qiyu-button--danger {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
  color: white;
  border: none;
}

.qiyu-button--danger:hover {
  background: linear-gradient(135deg, #d32f2f 0%, #c62828 100%);
  color: white;
}

/* 尺寸变体 */
.qiyu-button--small {
  height: 28px;
  padding: 0 12px;
  font-size: 12px;
}

.qiyu-button--medium {
  height: 36px;
  padding: 0 16px;
  font-size: 14px;
}

.qiyu-button--large {
  height: 44px;
  padding: 0 24px;
  font-size: 16px;
}

/* 禁用状态 */
.qiyu-button:disabled {
  background: rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.3) !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* 加载状态 */
.qiyu-button .ant-btn-loading-icon {
  margin-right: 8px;
}
