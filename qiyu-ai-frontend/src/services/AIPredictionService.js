class AIPredictionService {
  constructor() {
    // 在生产环境中使用相对路径，开发环境使用localhost
    this.baseUrl = process.env.NODE_ENV === 'production' 
      ? '/api/web'  // 使用相对路径，通过Nginx代理
      : (process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000/api/web');
  }

  /**
   * Get latest AI predictions for a symbol
   * @param {string} symbol - Cryptocurrency symbol (e.g., 'BTCUSDT')
   * @param {number} limit - Number of predictions to fetch
   * @returns {Promise<Object>} API response with predictions
   */
  async getLatestPredictions(symbol = 'BTCUSDT', limit = 10) {
    try {
      const url = `/api/ai/predictions/history/?symbol=${symbol}&limit=${limit}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('🔥 [AIPredictionService] 预测历史API返回:', data);
      return {
        success: true,
        data: data.data?.predictions || [],
        total: data.data?.total || 0,
        symbol: symbol
      };
    } catch (error) {
      console.error('Error fetching AI predictions:', error);
      return {
        success: false,
        error: error.message,
        data: [],
        total: 0
      };
    }
  }

  /**
   * Get prediction statistics for a symbol
   * @param {string} symbol - Cryptocurrency symbol
   * @returns {Promise<Object>} Prediction statistics
   */
  async getPredictionStats(symbol = 'BTCUSDT') {
    try {
      const url = `/api/ai/predictions/stats/?symbol=${symbol}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return {
        success: true,
        data: data
      };
    } catch (error) {
      console.error('Error fetching prediction stats:', error);
      return {
        success: false,
        error: error.message,
        data: {}
      };
    }
  }

  /**
   * Get the latest single prediction for a symbol
   * @param {string} symbol - Cryptocurrency symbol
   * @returns {Promise<Object>} Latest prediction
   */
  async getLatestPrediction(symbol = 'BTCUSDT') {
    try {
      const url = `/api/ai/prediction/latest/?symbol=${symbol}`;
      console.log('🔥 [AIPredictionService] 请求最新预测:', url);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('🔥 [AIPredictionService] 获取到最新预测数据:', data);
      return {
        success: true,
        data: data // API直接返回预测数据，不需要.prediction
      };
    } catch (error) {
      console.error('Error fetching latest prediction:', error);
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  /**
   * Get auto prediction task status
   * @returns {Promise<Object>} Task status for all symbols
   */
  async getAutoTaskStatus() {
    try {
      const url = `/api/ai/tasks/status/`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return {
        success: true,
        data: data.tasks || []
      };
    } catch (error) {
      console.error('Error fetching auto task status:', error);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  /**
   * Format prediction data for components
   * @param {Object} prediction - Raw prediction data from API
   * @returns {Object} Formatted prediction data
   */
  formatPredictionData(prediction) {
    if (!prediction) return null;

    return {
      id: prediction.id,
      symbol: prediction.symbol,
      current_price: prediction.current_price,
      prediction_distribution: prediction.prediction_distribution || {},
      market_sentiment: prediction.market_sentiment || {},
      analysis_summary: prediction.analysis_summary || '',
      confidence_level: prediction.confidence_level || '中',
      sentiment_score: prediction.sentiment_score || 0,
      is_bullish: prediction.is_bullish || false,
      created_at: prediction.created_at,
      prediction_datetime: prediction.prediction_datetime,
      processing_time_seconds: prediction.processing_time_seconds
    };
  }

  /**
   * Get predictions for multiple symbols
   * @param {Array<string>} symbols - Array of symbol names
   * @param {number} limit - Limit per symbol
   * @returns {Promise<Object>} Predictions grouped by symbol
   */
  async getMultiSymbolPredictions(symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT'], limit = 5) {
    try {
      const promises = symbols.map(symbol => this.getLatestPredictions(symbol, limit));
      const results = await Promise.all(promises);
      
      const predictions = {};
      results.forEach((result, index) => {
        const symbol = symbols[index];
        predictions[symbol] = {
          success: result.success,
          data: result.data.map(pred => this.formatPredictionData(pred)),
          total: result.total
        };
      });

      return {
        success: true,
        predictions
      };
    } catch (error) {
      console.error('Error fetching multi-symbol predictions:', error);
      return {
        success: false,
        error: error.message,
        predictions: {}
      };
    }
  }
}

const aiPredictionService = new AIPredictionService();
export default aiPredictionService;