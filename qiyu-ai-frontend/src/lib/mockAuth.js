// 模拟认证服务，用于开发测试
export const mockUserService = {
  // 模拟发送短信验证码
  async sendSignUpOTP(phone) {
    console.log('模拟发送短信验证码到:', phone);
    await new Promise(resolve => setTimeout(resolve, 1000));
    return { data: { success: true }, error: null };
  },

  // 模拟短信验证码注册
  async signUpWithOTP(phone, token, username) {
    console.log('模拟短信验证码注册:', { phone, token, username });
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (token !== '123456') {
      return { data: null, error: { message: '验证码错误' } };
    }
    
    const mockUser = {
      id: 'mock-user-id',
      phone: phone,
      created_at: new Date().toISOString()
    };
    
    return { data: { user: mockUser }, error: null };
  },

  // 模拟密码注册
  async signUpWithPassword(phone, password, username) {
    console.log('模拟密码注册:', { phone, password, username });
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const mockUser = {
      id: 'mock-user-id',
      phone: phone,
      created_at: new Date().toISOString()
    };
    
    return { data: { user: mockUser }, error: null };
  },

  // 模拟密码登录
  async signInWithPassword(phone, password) {
    console.log('模拟密码登录:', { phone, password });
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (phone === '13800138000' && password === '123456') {
      const mockUser = {
        id: 'mock-user-id',
        phone: phone,
        created_at: new Date().toISOString()
      };
      
      return { data: { user: mockUser }, error: null };
    }
    
    return { data: null, error: { message: 'Invalid login credentials' } };
  },

  // 模拟登出
  async signOut() {
    console.log('模拟登出');
    await new Promise(resolve => setTimeout(resolve, 500));
    return { error: null };
  },

  // 模拟获取用户档案
  async getProfile(userId) {
    console.log('模拟获取用户档案:', userId);
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const mockProfile = {
      id: userId,
      phone: '13800138000',
      username: '测试用户',
      balance: 100.00,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    return { data: mockProfile, error: null };
  },

  // 模拟更新用户档案
  async updateProfile(userId, updates) {
    console.log('模拟更新用户档案:', userId, updates);
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const mockProfile = {
      id: userId,
      phone: '13800138000',
      username: '测试用户',
      balance: 100.00,
      ...updates,
      updated_at: new Date().toISOString()
    };
    
    return { data: [mockProfile], error: null };
  },

  // 模拟更新余额
  async updateBalance(userId, newBalance, transactionType, description) {
    console.log('模拟更新余额:', { userId, newBalance, transactionType, description });
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const mockProfile = {
      id: userId,
      phone: '13800138000',
      username: '测试用户',
      balance: newBalance,
      updated_at: new Date().toISOString()
    };
    
    return { data: [mockProfile], error: null };
  },

  // 模拟获取余额变更记录
  async getBalanceTransactions(userId, limit = 50) {
    console.log('模拟获取余额变更记录:', userId, limit);
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const mockTransactions = [
      {
        id: 'tx-1',
        user_id: userId,
        amount: 100.00,
        type: 'deposit',
        description: '初始余额',
        created_at: new Date().toISOString()
      }
    ];
    
    return { data: mockTransactions, error: null };
  }
};

// 模拟 Supabase 客户端
export const mockSupabase = {
  auth: {
    async getSession() {
      // 检查是否有模拟的登录状态
      const mockSession = localStorage.getItem('mock-session');
      if (mockSession) {
        const user = JSON.parse(mockSession);
        return { data: { session: { user } }, error: null };
      }
      return { data: { session: null }, error: null };
    },
    
    onAuthStateChange(callback) {
      // 模拟认证状态变化监听
      console.log('模拟设置认证状态监听');
      
      // 立即检查当前状态
      const mockSession = localStorage.getItem('mock-session');
      if (mockSession) {
        const user = JSON.parse(mockSession);
        setTimeout(() => callback('SIGNED_IN', { user }), 100);
      } else {
        setTimeout(() => callback('SIGNED_OUT', null), 100);
      }
      
      return {
        data: {
          subscription: {
            unsubscribe: () => console.log('模拟取消认证状态监听')
          }
        }
      };
    }
  }
};

// 模拟登录成功时保存状态
export const mockSignIn = (user) => {
  localStorage.setItem('mock-session', JSON.stringify(user));
  window.dispatchEvent(new CustomEvent('mock-auth-change', { detail: { user } }));
};

// 模拟登出时清除状态
export const mockSignOut = () => {
  localStorage.removeItem('mock-session');
  window.dispatchEvent(new CustomEvent('mock-auth-change', { detail: { user: null } }));
};
