"""
Web API视图
专门为Web前端提供的API接口
"""

from rest_framework.decorators import api_view, authentication_classes, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.core.cache import cache
from django.utils import timezone
import logging

from .supabase_client import supabase_service
from .authentication import SupabaseAuthentication, SupabasePermission
from historical_data.services import HistoricalDataService

logger = logging.getLogger(__name__)

# 用户认证相关API

@api_view(['POST'])
@permission_classes([AllowAny])
def register(request):
    """
    用户注册
    
    POST data:
    {
        "phone": "13800138000",
        "password": "password123",
        "nickname": "用户昵称"
    }
    """
    try:
        phone = request.data.get('phone')
        password = request.data.get('password')
        nickname = request.data.get('nickname', '')
        
        # 验证参数
        if not phone or not password:
            return Response({
                'error': '手机号和密码不能为空'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 验证手机号格式
        if not phone.isdigit() or len(phone) != 11:
            return Response({
                'error': '手机号格式不正确'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 检查用户是否已存在
        existing_user = supabase_service.get_user_by_phone(phone)
        if existing_user:
            return Response({
                'error': '该手机号已注册'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 注册用户
        success, result, error = supabase_service.sign_up_with_phone(
            phone, 
            password, 
            {"nickname": nickname}
        )
        
        if success:
            return Response({
                'message': '注册成功',
                'user': {
                    'id': result.user.id,
                    'phone': phone,
                    'nickname': nickname,
                    'credits': 3
                }
            })
        else:
            return Response({
                'error': error or '注册失败'
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        logger.error(f"注册失败: {e}")
        return Response({
            'error': '服务器内部错误'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([AllowAny])
def login(request):
    """
    用户登录
    
    POST data:
    {
        "phone": "13800138000",
        "password": "password123"
    }
    """
    try:
        phone = request.data.get('phone')
        password = request.data.get('password')
        
        if not phone or not password:
            return Response({
                'error': '手机号和密码不能为空'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 登录
        success, session, user_data, error = supabase_service.sign_in_with_phone(phone, password)
        
        if success and session:
            return Response({
                'message': '登录成功',
                'session': {
                    'access_token': session.access_token,
                    'refresh_token': session.refresh_token,
                    'expires_at': session.expires_at
                },
                'user': {
                    'id': user_data.get('id'),
                    'phone': user_data.get('phone'),
                    'nickname': user_data.get('nickname'),
                    'credits': user_data.get('credits'),
                    'email': user_data.get('email')
                }
            })
        else:
            return Response({
                'error': error or '登录失败，请检查手机号和密码'
            }, status=status.HTTP_401_UNAUTHORIZED)
            
    except Exception as e:
        logger.error(f"登录失败: {e}")
        return Response({
            'error': '服务器内部错误'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@authentication_classes([SupabaseAuthentication])
@permission_classes([IsAuthenticated])
def user_profile(request):
    """获取用户信息"""
    try:
        user = request.user
        
        return Response({
            'user': {
                'id': user.id,
                'phone': user.phone,
                'email': user.email,
                'nickname': user.nickname,
                'credits': user.credits
            }
        })
        
    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")
        return Response({
            'error': '服务器内部错误'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 历史数据API (Web专用)

@api_view(['GET'])
@authentication_classes([SupabaseAuthentication])
@permission_classes([IsAuthenticated])
def web_chart_data(request):
    """
    Web专用图表数据API
    
    参数:
    - symbol: 交易对 (必需)
    - interval: 时间间隔 (默认15m)
    - period: 时间范围 (默认1w)
    """
    try:
        user = request.user
        
        # 检查积分
        if not SupabasePermission.check_credits(user, 1):
            return Response({
                'error': '积分不足，请联系客服充值'
            }, status=status.HTTP_402_PAYMENT_REQUIRED)
        
        # 获取参数
        symbol = request.GET.get('symbol', '').upper()
        interval = request.GET.get('interval', '15m')
        period = request.GET.get('period', '1w')
        
        if not symbol:
            return Response({
                'error': 'symbol参数是必需的'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 验证交易对
        valid_symbols = ['BTCUSDT', 'ETHUSDT', 'DOGEUSDT', 'LTCUSDT', 'TRUMPUSDT', 'SOLUSDT', 'XRPUSDT']
        if symbol not in valid_symbols:
            return Response({
                'error': f'不支持的交易对，支持: {", ".join(valid_symbols)}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 缓存键
        cache_key = f"web_chart_{symbol}_{interval}_{period}"
        
        # 尝试从缓存获取
        cached_data = cache.get(cache_key)
        if cached_data:
            # 记录API使用但不消耗积分（缓存数据）
            supabase_service.log_api_usage(user.id, f"/api/web/chart?symbol={symbol}")
            return Response(cached_data)
        
        # 获取数据
        service = HistoricalDataService()
        chart_data = service.get_chart_data(symbol, interval, period)
        
        if 'error' in chart_data:
            return Response(chart_data, status=status.HTTP_404_NOT_FOUND)
        
        # 消耗积分
        if not SupabasePermission.consume_credits(user, 1):
            return Response({
                'error': '积分扣除失败'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        # 记录API使用
        supabase_service.log_api_usage(user.id, f"/api/web/chart?symbol={symbol}")
        
        # 添加元数据
        chart_data['user_credits_remaining'] = user.credits
        chart_data['generated_at'] = timezone.now().isoformat()
        
        # 缓存数据
        cache.set(cache_key, chart_data, 300)  # 缓存5分钟
        
        return Response(chart_data)
        
    except Exception as e:
        logger.error(f"获取图表数据失败: {e}")
        return Response({
            'error': '服务器内部错误'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@authentication_classes([SupabaseAuthentication])
@permission_classes([IsAuthenticated])
def web_technical_indicators(request):
    """
    Web专用技术指标API
    
    参数:
    - symbol: 交易对 (必需)
    - interval: 时间间隔 (默认15m)
    """
    try:
        user = request.user
        
        # 检查积分
        if not SupabasePermission.check_credits(user, 1):
            return Response({
                'error': '积分不足，请联系客服充值'
            }, status=status.HTTP_402_PAYMENT_REQUIRED)
        
        symbol = request.GET.get('symbol', '').upper()
        interval = request.GET.get('interval', '15m')
        
        if not symbol:
            return Response({
                'error': 'symbol参数是必需的'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 缓存键
        cache_key = f"web_indicators_{symbol}_{interval}"
        
        # 尝试从缓存获取
        cached_data = cache.get(cache_key)
        if cached_data:
            supabase_service.log_api_usage(user.id, f"/api/web/indicators?symbol={symbol}")
            return Response(cached_data)
        
        # 获取数据并计算指标
        service = HistoricalDataService()
        df = service.load_recent_data(symbol, interval, 200)
        
        if df.empty:
            return Response({
                'error': '未找到数据'
            }, status=status.HTTP_404_NOT_FOUND)
        
        indicators = service.calculate_technical_indicators(df)
        
        if not indicators:
            return Response({
                'error': '计算技术指标失败'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        # 消耗积分
        if not SupabasePermission.consume_credits(user, 1):
            return Response({
                'error': '积分扣除失败'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        # 记录API使用
        supabase_service.log_api_usage(user.id, f"/api/web/indicators?symbol={symbol}")
        
        # 添加元数据
        response_data = {
            'symbol': symbol,
            'interval': interval,
            'timestamp': timezone.now().isoformat(),
            'indicators': indicators,
            'user_credits_remaining': user.credits
        }
        
        # 缓存数据
        cache.set(cache_key, response_data, 180)  # 缓存3分钟
        
        return Response(response_data)
        
    except Exception as e:
        logger.error(f"获取技术指标失败: {e}")
        return Response({
            'error': '服务器内部错误'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def web_supported_symbols(request):
    """获取支持的交易对列表（Web专用，无需认证）"""
    symbols = [
        {
            'symbol': 'BTCUSDT',
            'name': 'Bitcoin',
            'base_asset': 'BTC',
            'quote_asset': 'USDT',
            'start_date': '2017-08-01',
            'icon': '₿'
        },
        {
            'symbol': 'ETHUSDT',
            'name': 'Ethereum',
            'base_asset': 'ETH',
            'quote_asset': 'USDT',
            'start_date': '2017-08-01',
            'icon': 'Ξ'
        },
        {
            'symbol': 'DOGEUSDT',
            'name': 'Dogecoin',
            'base_asset': 'DOGE',
            'quote_asset': 'USDT',
            'start_date': '2019-07-01',
            'icon': 'Ð'
        },
        {
            'symbol': 'LTCUSDT',
            'name': 'Litecoin',
            'base_asset': 'LTC',
            'quote_asset': 'USDT',
            'start_date': '2017-12-01',
            'icon': 'Ł'
        },
        {
            'symbol': 'TRUMPUSDT',
            'name': 'Trump Token',
            'base_asset': 'TRUMP',
            'quote_asset': 'USDT',
            'start_date': '2025-01-01',
            'icon': '🇺🇸'
        },
        {
            'symbol': 'SOLUSDT',
            'name': 'Solana',
            'base_asset': 'SOL',
            'quote_asset': 'USDT',
            'start_date': '2020-08-01',
            'icon': '◎'
        },
        {
            'symbol': 'XRPUSDT',
            'name': 'Ripple',
            'base_asset': 'XRP',
            'quote_asset': 'USDT',
            'start_date': '2018-05-01',
            'icon': '●'
        }
    ]
    
    return Response({
        'symbols': symbols,
        'total_count': len(symbols),
        'supported_intervals': ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d', '3d', '1w', '1mo'],
        'supported_periods': ['1d', '1w', '1m', '3m', '6m', '1y'],
        'api_version': 'web_v1'
    })