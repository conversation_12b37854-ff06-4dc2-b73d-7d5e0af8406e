/* 指标计算器样式 */
.indicator-calculator {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
}

.indicator-calculator .ant-card-head {
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: rgba(0, 0, 0, 0.2);
}

.indicator-calculator .ant-card-body {
  background: transparent;
}

.indicator-calculator .ant-progress-bg {
  background: linear-gradient(135deg, #016A86 0%, #447A53 100%);
}

.indicator-calculator .ant-btn-primary {
  background: linear-gradient(135deg, #016A86 0%, #447A53 100%);
  border-color: #447A53;
  box-shadow: 0 2px 4px rgba(68, 122, 83, 0.3);
}

.indicator-calculator .ant-btn-primary:hover {
  background: linear-gradient(135deg, #018ba6 0%, #4a8259 100%);
  border-color: #4a8259;
  box-shadow: 0 4px 8px rgba(68, 122, 83, 0.4);
}

.indicator-calculator .ant-alert-success {
  background: rgba(82, 196, 26, 0.1);
  border: 1px solid rgba(82, 196, 26, 0.3);
}

.indicator-calculator .ant-alert-error {
  background: rgba(255, 77, 79, 0.1);
  border: 1px solid rgba(255, 77, 79, 0.3);
}
