# 🚀 实时数据架构说明

## 📋 架构概览

本系统采用**后端常驻WebSocket + 前端纯COS读取**的架构，解决数据连续性和用户依赖问题。

```
Binance WebSocket → 后端常驻进程 → COS存储 → 前端定期读取 → 图表展示
     24/7收集        5分钟上传      实时数据     5分钟刷新      自动更新
```

## 🏗️ 系统组件

### 后端组件

#### 1. **实时数据收集器** (`RealtimeDataCollector`)
- **功能**: 24/7 WebSocket连接，收集Binance实时数据
- **上传频率**: 每5分钟自动上传到COS
- **断点重续**: 服务器重启后自动检测和填补数据缺口
- **路径一致性**: 完全沿用现有25年COS路径格式

#### 2. **COS上传服务** (`COSRealtimeService`)
- **功能**: 将实时数据上传到腾讯云COS
- **路径格式**: `crypto-kline-data-v2/20250726/{symbol}/1m/daily/{symbol}_1m_{MM-DD}_2025_compressed.json`
- **数据格式**: 与现有JSON格式完全一致
- **年份自动切换**: 2025→2026年自动适配

#### 3. **缺口恢复服务** (`GapRecoveryService`)
- **功能**: 检测和填补数据缺口
- **数据源**: Binance REST API
- **触发时机**: 服务器启动时自动检查

### 前端组件

#### 1. **纯COS数据服务** (`PureCOSDataService`)
- **功能**: 完全基于COS读取数据，移除WebSocket逻辑
- **刷新频率**: 每5分钟自动检查COS更新
- **智能缓存**: 检测文件更新时间，避免重复下载
- **兼容性**: 保持与现有StreamDataService的接口一致性

## 🎯 使用方法

### 启动后端数据收集器

```bash
# 方法1: 使用启动脚本
cd finance_backend
./start_realtime_collector.sh

# 方法2: 直接使用Django命令
python manage.py collect_realtime_data --symbol=BTCUSDT --daemon

# 方法3: 调试模式
python manage.py collect_realtime_data --symbol=BTCUSDT --debug
```

### 前端调试命令

打开浏览器控制台，使用以下命令：

```javascript
// 查看服务状态
debugCrypto.getServiceStatus()

// 强制检查数据更新
debugCrypto.forceCheckUpdates()

// 手动触发图表刷新
debugCrypto.triggerChartRefresh()

// 清除缓存并刷新图表
debugCrypto.refreshCharts()

// 修复数据断层（后端API）
debugCrypto.repairGap('BTCUSDT')
```

## 📊 数据流程

### 实时数据收集流程

1. **WebSocket连接**: 后端连接到 `wss://stream.binance.com:9443/ws/btcusdt@kline_1m`
2. **数据缓冲**: 实时数据进入内存缓冲区
3. **定时上传**: 每5分钟将缓冲区数据上传到COS
4. **路径生成**: 按日期生成COS路径，格式与现有25年一致
5. **JSON格式**: 转换为标准JSON格式并压缩上传

### 前端数据获取流程

1. **定时检查**: 每5分钟检查COS文件更新时间
2. **增量下载**: 只下载有更新的数据文件
3. **智能缓存**: 基于文件修改时间的缓存策略
4. **事件触发**: 数据更新时自动触发图表刷新事件
5. **无缝切换**: 保持现有图表组件的接口不变

## 🔄 断点重续机制

### 缺口检测

```python
# 启动时自动检查
last_upload_time = cos_service.get_last_upload_time('BTCUSDT')
current_time = datetime.now()
gap_duration = current_time - last_upload_time

if gap_duration.total_seconds() > 300:  # 超过5分钟
    gap_recovery.fill_gap('BTCUSDT', last_upload_time, current_time)
```

### 缺口填补

1. **时间计算**: 精确计算缺失的时间段
2. **API调用**: 使用Binance REST API获取历史数据
3. **分批处理**: 按1000条记录分批获取，避免API限制
4. **数据合并**: 与现有数据合并，按时间戳去重
5. **COS上传**: 填补数据按日期上传到对应COS路径

## 📂 COS路径策略

### 当前年份（2025年）
```
crypto-kline-data-v2/20250726/
├── BTCUSDT/
│   └── 1m/
│       └── daily/
│           ├── BTCUSDT_1m_01-01_2025_compressed.json
│           ├── BTCUSDT_1m_01-02_2025_compressed.json
│           └── ...
```

### 年份自动切换（2026年）
```
crypto-kline-data-v2/20260726/
├── BTCUSDT/
│   └── 1m/
│       └── daily/
│           ├── BTCUSDT_1m_01-01_2026_compressed.json
│           └── ...
```

### 历史年份迁移
```
crypto-kline-data-v2/legacy/
├── BTCUSDT/
│   └── 1m/
│       ├── BTCUSDT_1m_2025_compressed.json
│       └── ...
```

## ⚙️ 配置参数

### 后端配置

```python
# 数据收集器配置
UPLOAD_INTERVAL = 300  # 5分钟上传间隔
MAX_KLINES_PER_REQUEST = 1000  # Binance API限制
REQUEST_DELAY = 0.1  # API请求间隔

# COS配置
AWS_STORAGE_BUCKET_NAME = 'your-bucket'
AWS_ACCESS_KEY_ID = 'your-access-key'
AWS_SECRET_ACCESS_KEY = 'your-secret-key'
```

### 前端配置

```javascript
// 纯COS数据服务配置
refreshInterval: 300000,  // 5分钟刷新间隔
maxRetries: 3,           // 最大重试次数
cacheTimeout: 300000     // 缓存超时时间
```

## 🛠️ 部署建议

### 生产环境部署

1. **Systemd服务**: 将数据收集器配置为系统服务
2. **日志轮转**: 配置日志轮转，避免日志文件过大
3. **监控告警**: 监控WebSocket连接状态和数据上传成功率
4. **备份策略**: 定期备份COS数据，设置多地域复制

### 监控指标

- WebSocket连接状态
- 数据上传成功率
- 缺口检测和修复情况
- COS存储使用量
- 前端数据刷新频率

## 🚀 优势特点

1. **数据连续性**: 24/7不间断收集，无用户依赖
2. **架构简单**: 后端只写，前端只读，职责分离
3. **资源高效**: 单一WebSocket连接，避免重复
4. **断点重续**: 自动检测和填补数据缺口
5. **路径一致**: 完全沿用现有COS路径格式
6. **年份自适**: 自动适配年份变化，无需手动干预
7. **实时性好**: 5分钟延迟，满足大多数使用场景

## 🔍 故障排查

### 常见问题

1. **WebSocket连接失败**
   - 检查网络连接
   - 验证Binance API可访问性
   - 查看防火墙设置

2. **COS上传失败**
   - 验证COS凭证配置
   - 检查存储桶权限
   - 查看网络连接状态

3. **前端数据不更新**
   - 检查COS文件是否正常生成
   - 验证前端刷新间隔设置
   - 查看浏览器控制台错误

### 日志查看

```bash
# 查看数据收集器日志
tail -f finance_backend/realtime_collector.log

# 查看Django日志
tail -f finance_backend/django.log

# 查看系统服务日志（如果使用systemd）
sudo journalctl -u realtime-collector -f
```

## 📈 性能优化

1. **批量上传**: 累积一定数量数据后批量上传
2. **压缩传输**: 使用JSON压缩减少传输量
3. **智能缓存**: 基于文件修改时间的增量更新
4. **异步处理**: 数据收集和上传异步处理
5. **连接复用**: 复用HTTP连接减少开销

---

**架构设计完成！可以开始部署测试了。** 🎉