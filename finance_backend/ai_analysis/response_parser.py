"""
AI响应解析器
用于解析和标准化AI分析响应格式
"""

import json
import re
import logging
from typing import Dict, List, Optional, Union
from datetime import datetime

logger = logging.getLogger(__name__)


class AIResponseParser:
    """AI响应解析器类"""
    
    @staticmethod
    def parse_structured_response(ai_response: str) -> Dict:
        """
        解析AI的结构化响应
        
        参数:
        - ai_response: AI的原始响应文本
        
        返回:
        - 标准化的响应字典
        """
        try:
            # 尝试直接解析JSON
            if ai_response.strip().startswith('{') and ai_response.strip().endswith('}'):
                return json.loads(ai_response)
            
            # 尝试从文本中提取JSON
            json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            
            # 如果没有找到JSON，使用文本解析
            return AIResponseParser._parse_text_response(ai_response)
            
        except Exception as e:
            logger.error(f"解析AI响应失败: {e}")
            return AIResponseParser._create_fallback_response(ai_response)
    
    @staticmethod
    def _parse_text_response(text: str) -> Dict:
        """从文本中解析关键信息"""
        response = {
            "prediction": "其他",
            "sentiment": "其他", 
            "confidence": 0.5,
            "risk_level": "中",
            "thinking_process": "",
            "key_findings": [],
            "historical_insights": {},
            "technical_signals": {},
            "conclusion": "",
            "timestamp": datetime.now().isoformat(),
            "raw_response": text
        }
        
        # 解析预测方向
        if re.search(r'(看涨|多|买入|上涨)', text, re.IGNORECASE):
            response["prediction"] = "多"
        elif re.search(r'(看跌|空|卖出|下跌)', text, re.IGNORECASE):
            response["prediction"] = "空"
        
        # 解析情绪分析
        if re.search(r'非常看跌', text):
            response["sentiment"] = "非常看跌"
        elif re.search(r'看跌', text):
            response["sentiment"] = "看跌"
        elif re.search(r'非常看涨', text):
            response["sentiment"] = "非常看涨"
        elif re.search(r'看涨', text):
            response["sentiment"] = "看涨"
        
        # 解析置信度
        confidence_match = re.search(r'置信度[：:]?\s*(\d+(?:\.\d+)?)', text)
        if confidence_match:
            confidence = float(confidence_match.group(1))
            if confidence > 1:
                confidence = confidence / 100  # 转换百分比
            response["confidence"] = confidence
        
        # 解析风险等级
        if re.search(r'高风险', text):
            response["risk_level"] = "高"
        elif re.search(r'低风险', text):
            response["risk_level"] = "低"
        
        # 提取思考过程（通常在开头部分）
        thinking_match = re.search(r'(分析|思考|判断).*?(?=结论|建议|总结)', text, re.DOTALL)
        if thinking_match:
            response["thinking_process"] = thinking_match.group().strip()
        else:
            # 取前500字符作为思考过程
            response["thinking_process"] = text[:500] + "..." if len(text) > 500 else text
        
        # 提取关键发现
        key_findings = []
        for pattern in [r'关键.*?[：:](.+)', r'重要.*?[：:](.+)', r'发现.*?[：:](.+)']:
            matches = re.findall(pattern, text)
            key_findings.extend([match.strip() for match in matches])
        
        if not key_findings:
            # 按句号分割，取前3个要点
            sentences = re.split(r'[。！？\n]', text)
            key_findings = [s.strip() for s in sentences if len(s.strip()) > 10][:3]
        
        response["key_findings"] = key_findings[:5]  # 最多5个关键发现
        
        # 提取结论（通常在末尾）
        conclusion_match = re.search(r'(结论|建议|总结)[：:](.+)', text, re.DOTALL)
        if conclusion_match:
            response["conclusion"] = conclusion_match.group(2).strip()
        else:
            # 取最后200字符作为结论
            response["conclusion"] = text[-200:] if len(text) > 200 else text
        
        return response
    
    @staticmethod
    def _create_fallback_response(text: str) -> Dict:
        """创建备用响应格式"""
        return {
            "prediction": "其他",
            "sentiment": "其他",
            "confidence": 0.5,
            "risk_level": "中",
            "thinking_process": text[:500] + "..." if len(text) > 500 else text,
            "key_findings": ["AI分析完成"],
            "historical_insights": {
                "pattern_match": "未知",
                "success_rate": "未知",
                "anomalies": "无"
            },
            "technical_signals": {
                "trend": "未明确",
                "support_resistance": "未明确",
                "indicators": "未明确"
            },
            "conclusion": text[-200:] if len(text) > 200 else text,
            "timestamp": datetime.now().isoformat(),
            "raw_response": text,
            "parse_status": "fallback"
        }
    
    @staticmethod
    def validate_response(response: Dict) -> Dict:
        """验证和标准化响应格式"""
        required_fields = [
            "prediction", "sentiment", "confidence", "risk_level",
            "thinking_process", "key_findings", "conclusion", "timestamp"
        ]
        
        # 确保所有必需字段存在
        for field in required_fields:
            if field not in response:
                if field == "prediction":
                    response[field] = "其他"
                elif field == "sentiment":
                    response[field] = "其他"
                elif field == "confidence":
                    response[field] = 0.5
                elif field == "risk_level":
                    response[field] = "中"
                elif field == "thinking_process":
                    response[field] = "分析中..."
                elif field == "key_findings":
                    response[field] = []
                elif field == "conclusion":
                    response[field] = "分析完成"
                elif field == "timestamp":
                    response[field] = datetime.now().isoformat()
        
        # 验证预测方向
        if response["prediction"] not in ["多", "空", "其他"]:
            response["prediction"] = "其他"
        
        # 验证情绪分析
        valid_sentiments = ["非常看跌", "看跌", "其他", "看涨", "非常看涨"]
        if response["sentiment"] not in valid_sentiments:
            response["sentiment"] = "其他"
        
        # 验证置信度
        try:
            confidence = float(response["confidence"])
            if confidence > 1:
                confidence = confidence / 100
            response["confidence"] = max(0, min(1, confidence))
        except (ValueError, TypeError):
            response["confidence"] = 0.5
        
        # 验证风险等级
        if response["risk_level"] not in ["高", "中", "低"]:
            response["risk_level"] = "中"
        
        # 确保key_findings是列表
        if not isinstance(response["key_findings"], list):
            response["key_findings"] = []
        
        return response
    
    @staticmethod
    def format_for_frontend(response: Dict) -> Dict:
        """格式化响应以适配前端显示"""
        formatted = response.copy()
        
        # 添加显示友好的字段
        formatted["prediction_display"] = {
            "多": "看涨 📈",
            "空": "看跌 📉", 
            "其他": "震荡 ↔️"
        }.get(response["prediction"], "未知")
        
        formatted["sentiment_display"] = {
            "非常看跌": "😰 非常看跌",
            "看跌": "😟 看跌",
            "其他": "😐 中性",
            "看涨": "😊 看涨",
            "非常看涨": "🤩 非常看涨"
        }.get(response["sentiment"], "未知")
        
        formatted["confidence_display"] = f"{response['confidence']*100:.1f}%"
        
        formatted["risk_display"] = {
            "高": "🔴 高风险",
            "中": "🟡 中风险",
            "低": "🟢 低风险"
        }.get(response["risk_level"], "未知")
        
        return formatted


# 使用示例
if __name__ == "__main__":
    parser = AIResponseParser()
    
    # 测试JSON响应
    json_response = '''
    {
        "prediction": "多",
        "sentiment": "看涨",
        "confidence": 0.75,
        "thinking_process": "基于技术分析...",
        "conclusion": "建议买入"
    }
    '''
    
    result = parser.parse_structured_response(json_response)
    print("JSON解析结果:", result)
    
    # 测试文本响应
    text_response = "基于分析，我认为价格会上涨，看涨情绪，置信度75%，建议买入。"
    result = parser.parse_structured_response(text_response)
    print("文本解析结果:", result)
