================================================================================
🔍 数据完整性检查报告
================================================================================
📅 检查时间: 2025-07-30 13:20:06
📊 检查期间: 2025-07-29 ~ 2025-07-30
⏱️  检查天数: 2 天

📈 总体统计
----------------------------------------
期望分钟数: 14,400
实际分钟数: 3,904
总体覆盖率: 27.11%
重要缺失: 11 个时段

📊 分交易对统计
----------------------------------------
BTCUSDT:
  覆盖率: 25.42%
  缺失时段: 3 个
  实际/期望: 732/2880
ETHUSDT:
  覆盖率: 27.53%
  缺失时段: 2 个
  实际/期望: 793/2880
SOLUSDT:
  覆盖率: 27.53%
  缺失时段: 2 个
  实际/期望: 793/2880
DOGEUSDT:
  覆盖率: 27.53%
  缺失时段: 2 个
  实际/期望: 793/2880
XRPUSDT:
  覆盖率: 27.53%
  缺失时段: 2 个
  实际/期望: 793/2880

🚨 重要缺失时段 (>60分钟)
----------------------------------------
BTCUSDT 2025-07-29
  时间: 2025-07-29 00:00:00 ~ 2025-07-29 23:34:59
  持续: 1415 分钟

ETHUSDT 2025-07-29
  时间: 2025-07-29 00:00:00 ~ 2025-07-29 23:34:59
  持续: 1415 分钟

SOLUSDT 2025-07-29
  时间: 2025-07-29 00:00:00 ~ 2025-07-29 23:34:59
  持续: 1415 分钟

DOGEUSDT 2025-07-29
  时间: 2025-07-29 00:00:00 ~ 2025-07-29 23:34:59
  持续: 1415 分钟

XRPUSDT 2025-07-29
  时间: 2025-07-29 00:00:00 ~ 2025-07-29 23:34:59
  持续: 1415 分钟

BTCUSDT 2025-07-30
  时间: 2025-07-30 12:48:00 ~ 2025-07-30 23:59:59
  持续: 672 分钟

ETHUSDT 2025-07-30
  时间: 2025-07-30 12:48:00 ~ 2025-07-30 23:59:59
  持续: 672 分钟

SOLUSDT 2025-07-30
  时间: 2025-07-30 12:48:00 ~ 2025-07-30 23:59:59
  持续: 672 分钟

DOGEUSDT 2025-07-30
  时间: 2025-07-30 12:48:00 ~ 2025-07-30 23:59:59
  持续: 672 分钟

XRPUSDT 2025-07-30
  时间: 2025-07-30 12:48:00 ~ 2025-07-30 23:59:59
  持续: 672 分钟

🔧 修复建议
----------------------------------------
python3 manage.py repair_data_gaps --symbol=BTCUSDT --start='2025-07-29T00:00:00' --end='2025-07-29T23:34:59'
python3 manage.py repair_data_gaps --symbol=BTCUSDT --start='2025-07-30T11:31:00' --end='2025-07-30T12:31:59'
python3 manage.py repair_data_gaps --symbol=BTCUSDT --start='2025-07-30T12:48:00' --end='2025-07-30T23:59:59'
python3 manage.py repair_data_gaps --symbol=ETHUSDT --start='2025-07-29T00:00:00' --end='2025-07-29T23:34:59'
python3 manage.py repair_data_gaps --symbol=ETHUSDT --start='2025-07-30T12:48:00' --end='2025-07-30T23:59:59'
