#!/usr/bin/env python3
"""
Cron友好的AI预测任务 - 单次执行版本
"""
import os
import sys
import logging
import fcntl
from datetime import datetime
from django.core.management.base import BaseCommand
from django.utils import timezone
from ai_analysis.auto_prediction_manager import AutoPredictionManager

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Cron友好的AI预测任务 - 单次执行，防重复运行'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制运行所有任务，忽略15分钟间隔'
        )
        
        parser.add_argument(
            '--lock-timeout',
            type=int,
            default=1800,  # 30分钟
            help='锁文件超时时间（秒），防止僵尸进程'
        )
        
        parser.add_argument(
            '--log-level',
            choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
            default='INFO',
            help='日志级别'
        )
    
    def handle(self, *args, **options):
        # 设置日志级别
        logging.basicConfig(
            level=getattr(logging, options['log_level']),
            format='%(asctime)s [%(levelname)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 防重复运行锁
        lock_file_path = '/tmp/ai_predictions.lock'
        
        try:
            # 获取锁文件
            lock_file = open(lock_file_path, 'w')
            fcntl.flock(lock_file.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
            
            # 写入当前进程信息
            lock_file.write(f"{os.getpid()}\n{datetime.now().isoformat()}\n")
            lock_file.flush()
            
            self.stdout.write(f"🔒 获取执行锁: {lock_file_path}")
            
            # 执行预测任务
            self.run_predictions(options.get('force', False))
            
        except IOError:
            self.stdout.write(self.style.WARNING(
                "⚠️ 另一个预测任务正在运行，跳过本次执行"
            ))
            sys.exit(0)
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ 执行失败: {e}"))
            logger.error(f"预测任务执行失败: {e}", exc_info=True)
            sys.exit(1)
        finally:
            try:
                fcntl.flock(lock_file.fileno(), fcntl.LOCK_UN)
                lock_file.close()
                os.unlink(lock_file_path)
                self.stdout.write("🔓 释放执行锁")
            except:
                pass
    
    def run_predictions(self, force):
        """执行预测任务"""
        start_time = datetime.now()
        self.stdout.write(f"🚀 开始执行AI预测任务 - {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        manager = AutoPredictionManager()
        result = manager.run_auto_predictions(force_all=force)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if result['status'] == 'completed':
            self.stdout.write(self.style.SUCCESS(
                f"✅ 预测任务完成 - 耗时: {duration:.1f}秒\n"
                f"   成功: {result['successful']}, 失败: {result['failed']}, 跳过: {result['skipped']}"
            ))
            
            # 输出任务详情到日志
            if result.get('task_details'):
                for detail in result['task_details']:
                    status_icon = '✅' if detail['status'] == 'success' else '❌' if detail['status'] == 'failed' else '⏭️'
                    logger.info(
                        f"{status_icon} {detail['symbol']} "
                        f"({detail['duration']}s) {detail['message']}"
                    )
            
            # 退出码：0=成功
            sys.exit(0)
            
        elif result['status'] == 'skipped':
            self.stdout.write(self.style.WARNING(f"⏭️ 任务被跳过: {result['reason']}"))
            sys.exit(0)
        else:
            self.stdout.write(self.style.ERROR(f"❌ 任务执行失败: {result.get('error')}"))
            sys.exit(1)