import React from 'react';
import { Badge } from 'antd';
import './NewsList.css';

const NewsList = ({ 
  title = '最新资讯',
  count = 3,
  className = ''
}) => {
  // 模拟新闻数据
  const newsData = [
    {
      id: 1,
      time: '2025-07-03 23:20:00',
      title: '以太坊（ETH）走势分析与投资建议',
      content: '数据显示，2025-06-13 数据显示，以太坊市场波动较大，当前价格：2538.11 USDT（24小时涨幅=7.523%）。昨日波动：开盘 2642.66，最低 2446.90，日内最大跌幅 203.60%，收盘 2538.11。建议目标：中长期持有策略收益较好。',
      isNew: true
    },
    {
      id: 2,
      time: '2025-07-03 23:10:00',
      title: '比特币市场观察',
      content: '1. 当前盘面信号：实时价格：104,688 USDT，24小时涨幅 -0.94%，成交量 15,838 BTC（减少期中等水平）。2. 关键支撑下跌趋势：趋势，从108,645跌至当前价格，最大跌幅5.4%（9月17日最高价格至今）。3. 关键支撑位：104,000 USDT（买卖价格）。',
      isNew: true
    },
    {
      id: 3,
      time: '2025-07-03 22:50:00',
      title: '24小时市场总结',
      content: '下跌40%，支撑位2590，短期超跌反弹，获利了结了，上涨40%，阻力位2640，买盘深度支撑，多头调整。12小时：上涨75%，目标区间2630，日线MACD金叉。',
      isNew: false
    }
  ];

  const formatTime = (timeStr) => {
    const date = new Date(timeStr);
    const now = new Date();
    const diff = now - date;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    
    if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else {
      return timeStr.substring(5, 16); // MM-DD HH:mm
    }
  };

  return (
    <div className={`news-list ${className}`}>
      <div className="news-header">
        <h3 className="news-title">
          {title}
          <Badge count={count} size="small" className="news-badge" />
        </h3>
      </div>
      
      <div className="news-content">
        {newsData.map((news) => (
          <div key={news.id} className="news-item">
            <div className="news-meta">
              <span className="news-time">{formatTime(news.time)}</span>
              {news.isNew && <Badge status="processing" text="新" />}
            </div>
            
            <h4 className="news-item-title">{news.title}</h4>
            
            <p className="news-excerpt">
              {news.content.length > 100 
                ? `${news.content.substring(0, 100)}...` 
                : news.content
              }
            </p>
            
            <div className="news-actions">
              <span className="read-more">查看详情</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default NewsList;
