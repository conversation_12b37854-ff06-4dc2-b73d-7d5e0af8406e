# 前端路由鉴权配置总结

## 概述
已完成前端路由鉴权配置，确保除了profile页面外，其他页面都不需要鉴权，但如果已登录会自动传递JWT token。

## 主要修改

### 1. 修改App.js
- 将TestV10组件替换为SimpleRouter
- 确保使用正确的路由系统

### 2. 增强SimpleRouter.js
- 添加了AuthContext集成
- 实现了路由级别的鉴权逻辑
- 添加了加载状态处理

#### 路由鉴权规则：
- **`/auth`**: 认证页面，如果已登录则重定向到首页
- **`/profile`**: 需要鉴权，未登录用户会被重定向到登录页
- **`/` (首页)**: 不需要鉴权，允许所有用户访问
- **其他页面**: 不需要鉴权，允许所有用户访问

### 3. 创建ApiClient工具类
创建了`src/utils/apiClient.js`，提供统一的API请求管理：

#### 功能特性：
- 自动添加JWT token到请求头（如果已登录）
- 统一的错误处理
- 自动处理401认证失败（清除本地token）
- 提供便捷的HTTP方法（GET、POST、PUT、DELETE）

#### 预定义API方法：
- `getUserProfile()` - 获取用户信息（需要认证）
- `getCreditTransactions()` - 获取积分交易记录（需要认证）
- `purchaseCredits()` - 购买积分套餐（需要认证）
- `getAIAnalysis()` - 获取AI分析数据（可选认证）
- `getChartData()` - 获取图表数据（可选认证）
- `getTechnicalIndicators()` - 获取技术指标（可选认证）

### 4. 更新Profile.js
- 替换直接的fetch调用为apiClient调用
- 简化了错误处理逻辑
- 改进了认证状态检查

### 5. 更新Dashboard.minimal.js
- 替换直接的fetch调用为apiClient调用
- 统一了API调用方式

## JWT Token传递机制

### 自动传递逻辑：
1. **检查本地存储**: ApiClient会自动检查localStorage中的`access_token`
2. **添加认证头**: 如果token存在，自动添加`Authorization: Bearer <token>`头
3. **错误处理**: 如果收到401响应，自动清除本地token并提示重新登录

### 使用示例：
```javascript
import apiClient from '../utils/apiClient';

// 自动包含JWT token（如果已登录）
const data = await apiClient.getUserProfile();

// 对于不需要认证的API，token会被自动包含但不是必需的
const chartData = await apiClient.getChartData('BTCUSDT', '1d');
```

## 页面访问控制

### Profile页面（需要鉴权）：
- 检查是否有有效的JWT token
- 未登录用户会被重定向到`/auth`页面
- 显示错误消息提示用户登录

### 其他页面（不需要鉴权）：
- 允许所有用户访问
- 如果用户已登录，API调用会自动包含JWT token
- 未登录用户仍可以查看内容，但某些功能可能受限

## 认证状态管理

### AuthContext集成：
- SimpleRouter使用AuthContext获取用户登录状态
- 支持加载状态显示
- 自动处理认证状态变化

### 本地存储管理：
- JWT token存储在localStorage中
- 自动清理过期或无效的token
- 支持token刷新机制（通过smsAuth服务）

## 安全考虑

1. **Token验证**: 后端API会验证JWT token的有效性
2. **自动清理**: 无效token会被自动清除
3. **错误处理**: 统一的认证错误处理机制
4. **最小权限**: 只有需要认证的功能才要求登录

## 测试建议

1. **未登录状态测试**:
   - 访问首页 - 应该正常显示
   - 访问profile页面 - 应该重定向到登录页
   - 查看API调用 - 不应包含Authorization头

2. **已登录状态测试**:
   - 访问所有页面 - 应该正常显示
   - 查看API调用 - 应该包含Authorization头
   - 访问认证页面 - 应该重定向到首页

3. **Token过期测试**:
   - 模拟token过期 - 应该自动清除并提示重新登录
   - 检查localStorage - 过期token应该被清除

## 开发服务器
前端开发服务器运行在: http://localhost:3001
