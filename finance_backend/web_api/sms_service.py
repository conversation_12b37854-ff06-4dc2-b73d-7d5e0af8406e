"""
腾讯云SMS服务
"""

import random
import logging
from datetime import datetime, timedelta
from django.core.cache import cache
from django.conf import settings
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
from tencentcloud.sms.v20210111 import sms_client, models

logger = logging.getLogger(__name__)


class TencentSMSService:
    """腾讯云SMS服务类"""
    
    def __init__(self):
        self.config = settings.TENCENT_SMS_CONFIG
        self.client = self._create_client()
    
    def _create_client(self):
        """创建腾讯云SMS客户端"""
        try:
            # 实例化一个认证对象
            cred = credential.Credential(
                self.config['SECRET_ID'],
                self.config['SECRET_KEY']
            )
            
            # 实例化一个http选项
            httpProfile = HttpProfile()
            httpProfile.endpoint = "sms.tencentcloudapi.com"
            
            # 实例化一个client选项
            clientProfile = ClientProfile()
            clientProfile.httpProfile = httpProfile
            
            # 实例化要请求产品的client对象
            client = sms_client.SmsClient(cred, self.config['REGION'], clientProfile)
            
            return client
            
        except Exception as e:
            logger.error(f"创建腾讯云SMS客户端失败: {e}")
            return None
    
    def generate_verification_code(self):
        """生成6位数字验证码"""
        return str(random.randint(100000, 999999))
    
    def send_verification_code(self, phone_number, code=None):
        """
        发送短信验证码
        
        Args:
            phone_number (str): 手机号
            code (str, optional): 验证码，如果不提供则自动生成
            
        Returns:
            tuple: (success, message, code)
        """
        if not self.client:
            return False, "SMS服务未正确配置", None
        
        # 验证手机号格式
        if not self._validate_phone_number(phone_number):
            return False, "手机号格式不正确", None
        
        # 检查发送频率限制
        if not self._check_rate_limit(phone_number):
            return False, "发送过于频繁，请稍后再试", None
        
        # 生成验证码
        if not code:
            code = self.generate_verification_code()
        
        try:
            # 开发模式：直接保存验证码，不发送真实短信
            if settings.DEBUG:
                logger.info(f"开发模式 - 短信验证码: {phone_number} -> {code}")
                self._save_verification_code(phone_number, code)
                self._record_send_time(phone_number)
                return True, f"验证码发送成功(开发模式): {code}", code
            
            # 实例化一个请求对象
            req = models.SendSmsRequest()
            
            # 设置请求参数
            req.PhoneNumberSet = [f"+86{phone_number}"]
            req.SmsSdkAppId = self.config['SDK_APP_ID']
            req.SignName = "成都星空三维科技有限公司"  # 使用审核通过的签名
            req.TemplateId = self.config['TEMPLATE_ID']
            req.TemplateParamSet = [code]
            
            # 发送短信
            resp = self.client.SendSms(req)
            
            # 处理响应
            if resp.SendStatusSet and len(resp.SendStatusSet) > 0:
                status = resp.SendStatusSet[0]
                if status.Code == "Ok":
                    # 保存验证码到缓存（5分钟有效）
                    self._save_verification_code(phone_number, code)
                    # 记录发送时间（用于频率限制）
                    self._record_send_time(phone_number)
                    
                    logger.info(f"短信发送成功: {phone_number}, 验证码: {code}")
                    return True, "验证码发送成功", code
                else:
                    error_msg = f"短信发送失败: {status.Code} - {status.Message}"
                    logger.error(error_msg)
                    return False, "短信发送失败，请稍后重试", None
            else:
                logger.error("短信发送响应为空")
                return False, "短信发送失败", None
                
        except TencentCloudSDKException as e:
            error_msg = f"腾讯云SMS SDK异常: {e}"
            logger.error(error_msg)
            return False, "短信服务异常，请稍后重试", None
        except Exception as e:
            error_msg = f"发送短信异常: {e}"
            logger.error(error_msg)
            return False, "短信发送失败", None
    
    def verify_code(self, phone_number, code):
        """
        验证短信验证码
        
        Args:
            phone_number (str): 手机号
            code (str): 验证码
            
        Returns:
            bool: 验证是否成功
        """
        if not phone_number or not code:
            return False
        
        # 从缓存获取验证码
        cache_key = f"sms_code_{phone_number}"
        stored_code = cache.get(cache_key)
        
        if not stored_code:
            logger.warning(f"验证码已过期或不存在: {phone_number}")
            return False
        
        if str(stored_code) == str(code):
            # 验证成功，删除验证码
            cache.delete(cache_key)
            logger.info(f"验证码验证成功: {phone_number}")
            return True
        else:
            logger.warning(f"验证码错误: {phone_number}, 提供: {code}, 期望: {stored_code}")
            return False
    
    def _validate_phone_number(self, phone_number):
        """验证手机号格式"""
        if not phone_number:
            return False
        
        # 去除可能的+86前缀
        if phone_number.startswith('+86'):
            phone_number = phone_number[3:]
        elif phone_number.startswith('86'):
            phone_number = phone_number[2:]
        
        # 验证是否为11位数字且以1开头
        return (
            phone_number.isdigit() and 
            len(phone_number) == 11 and 
            phone_number.startswith('1')
        )
    
    def _check_rate_limit(self, phone_number):
        """检查发送频率限制"""
        cache_key = f"sms_rate_{phone_number}"
        last_send_time = cache.get(cache_key)
        
        if last_send_time:
            # 60秒内只能发送一次
            time_diff = datetime.now() - last_send_time
            if time_diff.seconds < 60:
                return False
        
        return True
    
    def _save_verification_code(self, phone_number, code):
        """保存验证码到缓存"""
        cache_key = f"sms_code_{phone_number}"
        # 验证码5分钟有效
        cache.set(cache_key, code, 300)
    
    def _record_send_time(self, phone_number):
        """记录发送时间"""
        cache_key = f"sms_rate_{phone_number}"
        # 记录发送时间，用于频率限制
        cache.set(cache_key, datetime.now(), 3600)  # 缓存1小时


# 全局实例
sms_service = TencentSMSService()