import React, { useState } from 'react';
import { Form, Input, Button, message } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useAuth } from '../../contexts/AuthContext';
import './Auth.css';

const Login = ({ onSwitchToRegister, onLoginSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const { signIn } = useAuth();

  const handleLogin = async (values) => {
    setLoading(true);
    try {
      const { phone, password } = values;

      // 使用 AuthContext 登录
      const data = await signIn(phone, password);

      if (data?.user) {
        message.success('登录成功！');
        onLoginSuccess && onLoginSuccess(data.user);
        // 登录成功后跳转到首页
        window.location.href = '/';
      } else {
        throw new Error('登录失败，请重试');
      }
    } catch (error) {
      console.error('登录失败:', error);

      // 根据错误类型显示不同的错误信息
      const errorMessage = error?.message || error || '登录失败，请稍后重试';
      
      if (typeof errorMessage === 'string') {
        if (errorMessage.includes('Invalid login credentials') || errorMessage.includes('手机号或密码错误')) {
          message.error('手机号或密码错误，请重试');
        } else if (errorMessage.includes('Email not confirmed') || errorMessage.includes('未注册')) {
          message.error('该手机号未注册，请先注册');
        } else {
          message.error(errorMessage);
        }
      } else {
        message.error('登录失败，请稍后重试');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = () => {
    console.log('忘记密码');
    message.info('请联系客服重置密码');
  };

  return (
    <div className="auth-container">
      {/* 背景装饰元素 */}
      <div className="auth-decorations">
        <img src="/element1.png" alt="" className="decoration decoration-1" />
        <img src="/element2.png" alt="" className="decoration decoration-2" />
        <img src="/element3.png" alt="" className="decoration decoration-3" />
        <img src="/element4.png" alt="" className="decoration decoration-4" />
        <img src="/element5.png" alt="" className="decoration decoration-5" />
        <img src="/F.svg" alt="" className="decoration decoration-f" />
      </div>

      {/* 登录表单 */}
      <div className="auth-form-container">
        <div className="auth-form">
          {/* Logo 和标题 */}
          <div className="auth-header">
            <div className="auth-logo">
              <img src="/logo_textless.png" alt="旗鱼AI" className="auth-logo-image" />
              <span className="auth-logo-text">旗鱼AI</span>
            </div>
            <p className="auth-subtitle">智能加密货币行情分析平台</p>
          </div>

          {/* 登录表单 */}
          <Form
            form={form}
            name="login"
            onFinish={handleLogin}
            autoComplete="off"
            layout="vertical"
            className="auth-form-content"
          >
            <Form.Item
              label="手机号码"
              name="phone"
              rules={[
                { required: true, message: '请输入手机号码' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="请输入手机号码"
                size="large"
                className="auth-input"
              />
            </Form.Item>

            <Form.Item
              label="密码"
              name="password"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6位' }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="请输入密码"
                size="large"
                className="auth-input"
              />
            </Form.Item>

            <div className="auth-forgot">
              <button 
                type="button" 
                className="auth-forgot-link"
                onClick={handleForgotPassword}
              >
                忘记密码？
              </button>
            </div>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                size="large"
                className="auth-submit-btn"
                block
              >
                登录
              </Button>
            </Form.Item>
          </Form>

          {/* 切换到注册 */}
          <div className="auth-switch">
            <span>还没有账号？</span>
            <button 
              type="button" 
              className="auth-switch-link"
              onClick={onSwitchToRegister}
            >
              立即注册
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
