#!/usr/bin/env python3
"""
🧠 AI预测分析API - 数据流程链路的核心控制器

📋 主要功能：
- 接收前端/自动任务的AI预测请求
- 调用时间戳分析服务获取完整市场数据 
- 构建结构化AI提示词
- 调用DeepSeek AI API进行智能分析
- 解析AI响应并返回多空预测结果

🔄 在数据流程链路中的位置：
用户请求/定时任务 → [ai_prediction_api.py] → TimestampAnalysisService

📡 主要API端点：
- POST /api/ai/predict/ - 执行AI预测分析

🏗️ 核心方法：
- post() - 处理预测请求，协调整个分析流程
- _build_prediction_prompt() - 从analysis_result构建AI提示词
- _call_deepseek_api() - 调用DeepSeek AI进行分析
- _parse_ai_response() - 解析AI响应为结构化数据

📊 数据输入：target_timestamp, symbol, interval
📈 数据输出：多空预测分布、市场情绪、AI分析总结
"""
import json
import logging
import requests
from datetime import datetime
from django.conf import settings
from django.http import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

from historical_data.timestamp_analysis_service import TimestampAnalysisService
from .prompt_templates import AIPromptTemplates

logger = logging.getLogger(__name__)

@method_decorator(csrf_exempt, name='dispatch')
class AIPredictionAPIView(View):
    """AI预测分析API - 生成多空预测和分析总结"""
    
    def options(self, request, *args, **kwargs):
        """处理OPTIONS请求（CORS预检请求）"""
        response = JsonResponse({})
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"
        response["Access-Control-Max-Age"] = "86400"
        return response
    
    def post(self, request, *args, **kwargs):
        """
        🎯 处理AI预测分析请求 - 数据流程链路的起始点
        
        📥 接收参数：
        - target_timestamp: 目标分析时间戳（必需）
        - symbol: 交易对，默认BTCUSDT
        - interval: 时间间隔，默认1m
        
        🔄 执行流程：
        1. 参数解析和验证
        2. 调用TimestampAnalysisService获取完整市场数据
        3. 构建AI提示词
        4. 调用DeepSeek AI分析
        5. 解析并返回结构化结果
        
        📊 返回数据：
        - analysis_summary: AI分析总结
        - prediction_distribution: 多空预测分布
        - market_sentiment: 市场情绪数据
        - technical_indicators: 技术指标
        """
        try:
            logger.info(f"🚀 收到AI预测请求: {request.method} {request.path}")
            
            # 1. 解析请求数据
            data = json.loads(request.body)
            logger.info(f"📥 请求数据: {data}")
            
            target_timestamp = data.get("target_timestamp")
            symbol = data.get("symbol", "BTCUSDT")
            interval = data.get("interval", "1m")
            
            if not target_timestamp:
                return JsonResponse({
                    "error": "缺少target_timestamp参数",
                    "required_params": ["target_timestamp"],
                    "optional_params": ["symbol", "interval"]
                }, status=400)
            
            logger.info(f"🎯 开始AI预测分析: {target_timestamp} {symbol} {interval}")
            
            # 2. 🔗 调用时间戳分析服务 - 链路关键步骤
            # 这里获取的analysis_result包含：技术指标、历史数据、支撑阻力位、BTC参考等
            timestamp_service = TimestampAnalysisService()
            analysis_result = timestamp_service.get_timestamp_analysis_data(
                timestamp=target_timestamp,
                symbol=symbol,
                interval=interval
            )
            
            if 'error' in analysis_result:
                return JsonResponse({
                    "error": f"数据分析失败: {analysis_result['error']}",
                    "analysis_result": analysis_result
                }, status=500)
            
            # 构建AI预测提示词 - 使用新的完整K线数据方法
            ai_prompt = self._build_kline_analysis_prompt(analysis_result)
            
            # 调用真实的DeepSeek AI API
            ai_prediction = self._call_deepseek_api(ai_prompt)
            
            # 构建响应
            response = {
                "success": True,
                "timestamp_analysis": {
                    "target_timestamp": target_timestamp,
                    "target_datetime": analysis_result.get('target_datetime'),
                    "symbol": symbol,
                    "interval": interval
                },
                "market_data": {
                    "current_price": self._extract_current_price(analysis_result),
                    "support_resistance": analysis_result.get('support_resistance', {}),
                    "technical_indicators": analysis_result.get('technical_indicators', {}),
                    "btc_reference": analysis_result.get('btc_reference', {})
                },
                "ai_prompt": ai_prompt,
                "ai_prediction": ai_prediction,
                "analysis_timestamp": datetime.now().isoformat()
            }
            
            logger.info("✅ AI预测分析完成")
            return JsonResponse(response)
            
        except Exception as e:
            logger.error(f"AI预测请求处理失败: {e}", exc_info=True)
            return JsonResponse({"error": str(e)}, status=500)
    
    def _build_prediction_prompt(self, analysis_result: dict) -> str:
        """
        🔧 构建AI预测提示词 - 数据组装的关键环节
        
        📦 输入数据源（analysis_result包含）：
        - technical_indicators: 技术指标数据（MACD、KDJ、RSI）
        - support_resistance: 支撑阻力位
        - current_data: 当前价格数据
        - historical_years_data: 历史同期数据
        - btc_reference: BTC参考数据
        
        🔄 数据处理流程：
        1. 提取基础信息（symbol、日期）
        2. 提取价格数据（当前价格、前日价格）
        3. 提取技术指标（MACD、KDJ、RSI）
        4. 提取支撑阻力位
        5. 提取历史价格数组
        6. 提取BTC参考数据
        7. 调用提示词模板生成结构化提示词
        
        🎯 输出：格式化的AI提示词文本，包含所有分析所需数据
        """
        try:
            # 1. 提取基础信息
            symbol = analysis_result.get('symbol', 'BTCUSDT')
            target_datetime = analysis_result.get('target_datetime', '')
            target_date = target_datetime.split('T')[0] if 'T' in target_datetime else target_datetime
            
            # 2. 提取价格数据
            current_price = self._extract_current_price(analysis_result)
            prev_day_price = self._extract_prev_day_price(analysis_result)
            
            # 3. 🎯 提取支撑阻力位 - 来自链路中的计算结果
            support_resistance = analysis_result.get('support_resistance', {})
            support_level = support_resistance.get('support', current_price * 0.95)
            resistance_level = support_resistance.get('resistance', current_price * 1.05)
            
            # 4. 🎯 提取技术指标 - 这是AI分析的核心数据
            # 包含MACD、KDJ、RSI等计算结果
            technical_indicators = analysis_result.get('technical_indicators', {})
            
            # 5. 提取历史价格数据
            historical_prices = self._extract_historical_prices(analysis_result)
            
            # 6. 🎯 提取BTC参考数据 - 大盘联动分析
            btc_data = analysis_result.get('btc_reference', {})
            
            # 7. 🚀 调用提示词模板 - 生成结构化AI提示词
            prompt = AIPromptTemplates.build_structured_prediction_prompt(
                symbol=symbol,
                target_date=target_date,
                current_price=current_price,
                support_level=support_level,
                resistance_level=resistance_level,
                technical_indicators=technical_indicators,  # ← 技术指标传递给AI
                prev_day_price=prev_day_price,
                historical_prices=historical_prices,
                btc_data=btc_data
            )
            
            return prompt
            
        except Exception as e:
            logger.error(f"构建AI提示词失败: {e}")
            return f"提示词构建失败: {str(e)}"
    
    def _build_kline_analysis_prompt(self, analysis_result: dict) -> str:
        """
        🎯 构建基于完整K线数据的AI分析提示词 - 让AI自己计算支撑阻力位
        """
        try:
            # 1. 提取基础信息
            symbol = analysis_result.get('symbol', 'BTCUSDT')
            target_datetime = analysis_result.get('target_datetime', '')
            target_date = target_datetime.split('T')[0] if 'T' in target_datetime else target_datetime
            
            # 2. 提取K线数据
            kline_data_90d = []
            kline_data_15m = []
            historical_same_period = []
            
            # 从analysis_result中提取90天日K线数据
            current_data = analysis_result.get('current_data', {})
            historical_years_data = analysis_result.get('historical_years_data', {})
            
            # 构造90天日K线数据结构
            for day_name, day_data in current_data.items():
                if 'price_range' in day_data:
                    price_range = day_data['price_range']
                    kline_data_90d.append({
                        'date': day_name,
                        'open': price_range.get('open', 0),
                        'high': price_range.get('high', 0),
                        'low': price_range.get('low', 0),
                        'close': price_range.get('close', 0),
                        'volume': price_range.get('volume', 0),
                        'timestamp': day_data.get('timestamp', '')
                    })
            
            # 添加历史年份数据到90天数据中
            for year, year_data in historical_years_data.items():
                if year_data and 'period_data' in year_data:
                    period_data = year_data['period_data']
                    for day_name, day_info in period_data.items():
                        if 'price_range' in day_info and 'error' not in day_info:
                            price_range = day_info['price_range']
                            kline_data_90d.append({
                                'date': f"{year}_{day_name}",
                                'open': price_range.get('open', 0),
                                'high': price_range.get('high', 0),
                                'low': price_range.get('low', 0),
                                'close': price_range.get('close', 0),
                                'volume': price_range.get('volume', 0),
                                'timestamp': day_info.get('timestamp', '')
                            })
            
            # 构造15分钟数据（模拟，实际应该从真实数据源获取）
            # 这里先使用当前数据作为示例
            for day_name, day_data in current_data.items():
                if 'price_range' in day_data:
                    price_range = day_data['price_range']
                    # 将日K线数据分解为4个15分钟数据点（简化处理）
                    base_price = price_range.get('close', 0)
                    for i in range(4):
                        kline_data_15m.append({
                            'timestamp': f"{day_name}_15m_{i+1}",
                            'open': base_price * (1 + (i-2) * 0.001),
                            'high': base_price * (1 + (i-1) * 0.002),
                            'low': base_price * (1 + (i-3) * 0.001),
                            'close': base_price * (1 + (i-2) * 0.001),
                            'volume': price_range.get('volume', 0) / 4
                        })
            
            # 历史同期数据
            historical_same_period = kline_data_90d[-10:]  # 取最近10个数据点作为历史同期
            
            # 3. 提取技术指标
            technical_indicators = analysis_result.get('technical_indicators', {})
            
            # 4. 调用新的AI提示词模板
            prompt = AIPromptTemplates.build_ai_kline_analysis_prompt(
                symbol=symbol,
                target_date=target_date,
                kline_data_90d=kline_data_90d[-30:],  # 只传最近30天，避免数据过大
                kline_data_15m=kline_data_15m[-48:],  # 最近48个15分钟数据（12小时）
                historical_same_period=historical_same_period,
                technical_indicators=technical_indicators
            )
            
            return prompt
            
        except Exception as e:
            logger.error(f"构建K线分析提示词失败: {e}")
            # 降级到旧方法
            return self._build_prediction_prompt(analysis_result)
    
    def _call_deepseek_api(self, prompt: str) -> dict:
        """调用DeepSeek API进行分析"""
        try:
            api_url = "https://api.deepseek.com/chat/completions"
            api_key = getattr(settings, 'DEEPSEEK_API_KEY', None)
            
            if not api_key:
                logger.error("DeepSeek API密钥未配置")
                return self._fallback_analysis()
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}"
            }
            
            payload = {
                "model": "deepseek-chat",  
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "max_tokens": 1000,
                "temperature": 0.3,
                "stream": False
            }
            
            logger.info("调用DeepSeek API进行预测分析")
            response = requests.post(api_url, headers=headers, json=payload, timeout=720)  # 12分钟超时
            response.raise_for_status()
            
            api_response = response.json()
            
            if 'choices' in api_response and len(api_response['choices']) > 0:
                ai_content = api_response['choices'][0]['message']['content']
                logger.info("✅ DeepSeek AI分析完成")
                
                # 解析AI响应为结构化格式
                return self._parse_ai_response(ai_content)
            else:
                logger.warning("DeepSeek API响应格式异常")
                return self._fallback_analysis()
                
        except requests.exceptions.RequestException as e:
            logger.error(f"DeepSeek API请求失败: {e}")
            return self._fallback_analysis()
        except Exception as e:
            logger.error(f"调用DeepSeek API时发生错误: {e}")
            return self._fallback_analysis()
    
    def _parse_ai_response(self, ai_content: str) -> dict:
        """解析AI响应为结构化格式 - 修复正则解析问题"""
        try:
            import re
            
            analysis_summary = ""
            prediction_data = {}
            sentiment_data = {}
            
            # 1. 提取分析总结 - 支持多行
            summary_match = re.search(r'【分析总结】\s*(.*?)(?=【多空预测】|$)', ai_content, re.DOTALL)
            if summary_match:
                analysis_summary = summary_match.group(1).strip()
                # 清理多余的换行和空格
                analysis_summary = ' '.join(analysis_summary.split())
            
            # 2. 提取多空预测 - 支持中英文符号
            prediction_match = re.search(r'【多空预测】\s*(.*?)(?=【市场情绪】|$)', ai_content, re.DOTALL)
            if prediction_match:
                prediction_text = prediction_match.group(1).strip()
                logger.debug(f"提取到预测文本: {prediction_text}")
                
                # 查找所有 "关键词: 数字%" 模式，支持中英文冒号和逗号
                # 注意：先匹配长关键词，避免短关键词误匹配
                prediction_patterns = [
                    (r'非常看涨[：:]?\s*(\d+)%', '非常看涨'),
                    (r'非常看跌[：:]?\s*(\d+)%', '非常看跌'),
                    (r'看涨[：:]?\s*(\d+)%', '看涨'),
                    (r'看跌[：:]?\s*(\d+)%', '看跌'),
                    (r'中性[：:]?\s*(\d+)%', '中性')
                ]
                
                for pattern, key in prediction_patterns:
                    match = re.search(pattern, prediction_text)
                    if match:
                        value = int(match.group(1))
                        prediction_data[key] = value
                        logger.debug(f"解析预测数据: {key} = {value}%")
                        # 从文本中移除已匹配的部分，避免重复匹配
                        prediction_text = re.sub(pattern, '', prediction_text, count=1)
            
            # 3. 提取市场情绪 - 处理一行多个数据的情况  
            sentiment_match = re.search(r'【市场情绪】\s*(.*?)(?=（注：|$)', ai_content, re.DOTALL)
            if sentiment_match:
                sentiment_text = sentiment_match.group(1).strip()
                logger.debug(f"提取到情绪文本: {sentiment_text}")
                
                # 查找 BTC 多头占比 - 使用下划线字段名匹配前端
                btc_match = re.search(r'BTC\s*多头占比[：:]?\s*(\d+)%', sentiment_text)
                if btc_match:
                    sentiment_data['BTC_多头占比'] = int(btc_match.group(1))
                    logger.debug(f"解析BTC情绪: {btc_match.group(1)}%")
                
                # 查找目标币种的多头占比（ETH, BNB, ADA等） - 统一使用目标币种字段
                target_symbols = ['ETH', 'BNB', 'ADA', 'XRP', 'SOL', 'DOGE', 'AVAX', 'DOT', 'MATIC']
                target_sentiment_found = False
                
                for symbol in target_symbols:
                    pattern = rf'{symbol}\s*多头占比[：:]?\s*(\d+)%'
                    target_match = re.search(pattern, sentiment_text)
                    if target_match:
                        sentiment_data['目标币种_多头占比'] = int(target_match.group(1))
                        logger.debug(f"解析{symbol}情绪: {target_match.group(1)}% -> 目标币种_多头占比")
                        target_sentiment_found = True
                        break  # 只取第一个匹配的目标币种
                
                # 如果没有找到具体币种的情绪数据，尝试查找通用的目标币种描述
                if not target_sentiment_found:
                    generic_patterns = [
                        r'目标币种\s*多头占比[：:]?\s*(\d+)%',
                        r'币种\s*多头占比[：:]?\s*(\d+)%',
                        r'该币种\s*多头占比[：:]?\s*(\d+)%'
                    ]
                    for pattern in generic_patterns:
                        generic_match = re.search(pattern, sentiment_text)
                        if generic_match:
                            sentiment_data['目标币种_多头占比'] = int(generic_match.group(1))
                            logger.debug(f"解析通用目标币种情绪: {generic_match.group(1)}%")
                            break
            
            # 4. 数据验证和默认值处理
            if not prediction_data:
                logger.warning("多空预测解析失败，使用默认值")
                prediction_data = {
                    "看涨": 25, "非常看涨": 10, "中性": 30, 
                    "看跌": 25, "非常看跌": 10
                }
            
            if not sentiment_data:
                logger.warning("市场情绪解析失败，使用默认值")
                sentiment_data = {"BTC_多头占比": 50, "目标币种_多头占比": 50}
            
            # 5. 验证百分比总和
            prediction_total = sum(prediction_data.values())
            if prediction_total != 100:
                logger.warning(f"多空预测百分比总和异常: {prediction_total}%，原始数据: {prediction_data}")
            
            logger.info(f"✅ AI响应解析成功 - 预测项: {len(prediction_data)}, 情绪项: {len(sentiment_data)}")
            
            return {
                "analysis_summary": analysis_summary or "AI分析正在处理中...",
                "prediction_distribution": prediction_data,
                "market_sentiment": sentiment_data,
                "sentiment_description": "AI智能分析",
                "confidence_level": "高",
                "raw_ai_response": ai_content
            }
            
        except Exception as e:
            logger.error(f"解析AI响应失败: {e}", exc_info=True)
            return {
                "analysis_summary": ai_content[:200] + "..." if len(ai_content) > 200 else ai_content,
                "prediction_distribution": {
                    "看涨": 25, "非常看涨": 10, "中性": 30, 
                    "看跌": 25, "非常看跌": 10
                },
                "market_sentiment": {"BTC_多头占比": 50, "目标币种_多头占比": 50},
                "raw_ai_response": ai_content,
                "parsing_error": str(e)
            }
    
    def _fallback_analysis(self) -> dict:
        """备用分析方法（当AI API不可用时）"""
        return {
            "analysis_summary": "AI服务暂时不可用，建议结合技术指标进行综合判断。",
            "prediction_distribution": {
                "看涨": 25, "非常看涨": 10, "中性": 30, 
                "看跌": 25, "非常看跌": 10
            },
            "market_sentiment": {"BTC_多头占比": 50, "目标币种_多头占比": 50},
            "sentiment_description": "系统维护中",
            "confidence_level": "低",
            "fallback": True
        }
    
    def _extract_historical_prices(self, analysis_result: dict) -> list:
        """提取真实历史价格数据"""
        try:
            historical_prices = []
            
            # 从当前数据提取价格
            current_data = analysis_result.get('current_data', {})
            if '目标日' in current_data and 'price_range' in current_data['目标日']:
                historical_prices.append(float(current_data['目标日']['price_range']['close']))
            if '前一天' in current_data and 'price_range' in current_data['前一天']:
                historical_prices.append(float(current_data['前一天']['price_range']['close']))
            
            # 从历史年份数据提取价格（最多3个历史价格点）
            historical_years_data = analysis_result.get('historical_years_data', {})
            for year, year_data in list(historical_years_data.items())[:3]:
                if year_data and 'period_data' in year_data:
                    period_data = year_data['period_data']
                    for day_name in ['目标日', '前一日', '后一日']:
                        if (day_name in period_data and 
                            'price_range' in period_data[day_name] and
                            'error' not in period_data[day_name]):
                            price = float(period_data[day_name]['price_range']['close'])
                            historical_prices.append(price)
                            break  # 只取每年一个价格点
            
            # 如果没有足够的历史数据，使用当前价格作为基准
            if len(historical_prices) < 3:
                current_price = self._extract_current_price(analysis_result)
                while len(historical_prices) < 3:
                    historical_prices.append(current_price)
            
            return historical_prices[:5]  # 最多返回5个历史价格
            
        except Exception as e:
            logger.error(f"提取历史价格失败: {e}")
            # 返回默认价格
            current_price = self._extract_current_price(analysis_result)
            return [current_price, current_price, current_price]
    
    def _extract_current_price(self, analysis_result: dict) -> float:
        """提取当前价格"""
        try:
            # 优先从支撑阻力位数据获取
            support_resistance = analysis_result.get('support_resistance', {})
            if 'current_price' in support_resistance:
                return float(support_resistance['current_price'])
            
            # 从技术指标获取
            technical_indicators = analysis_result.get('technical_indicators', {})
            if 'current_price' in technical_indicators:
                return float(technical_indicators['current_price'])
            
            # 从当前数据获取
            current_data = analysis_result.get('current_data', {})
            for day_name in ['目标日', '前一天']:
                if day_name in current_data and 'price_range' in current_data[day_name]:
                    price_range = current_data[day_name]['price_range']
                    if 'close' in price_range:
                        return float(price_range['close'])
            
            return 0.0
        except Exception:
            return 0.0
    
    def _extract_prev_day_price(self, analysis_result: dict) -> float:
        """提取前一天价格"""
        try:
            current_data = analysis_result.get('current_data', {})
            if '前一天' in current_data and 'price_range' in current_data['前一天']:
                return float(current_data['前一天']['price_range']['close'])
            return self._extract_current_price(analysis_result)
        except Exception:
            return 0.0