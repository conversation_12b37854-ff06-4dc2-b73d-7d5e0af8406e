#!/usr/bin/env python3
"""
设置多时间周期数据的定时更新cron任务
"""

import os
import subprocess

def setup_cron_jobs():
    """设置定时任务"""
    print("⏰ 设置多时间周期数据定时更新任务...")
    
    # 获取当前路径
    current_path = os.path.dirname(os.path.abspath(__file__))
    
    # cron任务配置
    cron_jobs = f"""# 多时间周期数据自动更新任务
# 每15分钟更新15分钟数据
*/15 * * * * cd {current_path} && python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=15m --start=$(date +\\%Y-\\%m-\\%d) --end=$(date +\\%Y-\\%m-\\%d) >> logs/auto_update.log 2>&1

# 每小时5分更新1小时数据
5 * * * * cd {current_path} && python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=1h --start=$(date +\\%Y-\\%m-\\%d) --end=$(date +\\%Y-\\%m-\\%d) >> logs/auto_update.log 2>&1

# 每4小时10分更新4小时数据  
10 */4 * * * cd {current_path} && python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=4h --start=$(date +\\%Y-\\%m-\\%d) --end=$(date +\\%Y-\\%m-\\%d) >> logs/auto_update.log 2>&1

# 每天1:15更新昨日的日数据
15 1 * * * cd {current_path} && python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=1d --start=$(date -d "1 day ago" +\\%Y-\\%m-\\%d) --end=$(date -d "1 day ago" +\\%Y-\\%m-\\%d) >> logs/auto_update.log 2>&1

# 每天2:00更新当日的1日数据（用于当天实时更新）
0 2 * * * cd {current_path} && python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=1d --start=$(date +\\%Y-\\%m-\\%d) --end=$(date +\\%Y-\\%m-\\%d) >> logs/auto_update.log 2>&1

# 每30分钟检查实时数据收集进程状态
*/30 * * * * if ! pgrep -f "collect_realtime_data.*BTCUSDT" > /dev/null; then cd {current_path} && nohup python3 manage.py collect_realtime_data --symbol=BTCUSDT --daemon --debug > logs/btc_collector.log 2>&1 & fi

# 每小时清理日志文件（保持最新1000行）
0 * * * * cd {current_path} && tail -n 1000 logs/auto_update.log > logs/auto_update.log.tmp && mv logs/auto_update.log.tmp logs/auto_update.log
"""
    
    # 写入临时cron文件
    cron_file = '/tmp/crypto_multi_timeframe_cron.txt'
    with open(cron_file, 'w') as f:
        f.write(cron_jobs)
    
    print(f"📝 Cron任务已写入: {cron_file}")
    print("\\n📋 任务概览:")
    print("   🔄 每15分钟: 更新15分钟数据")
    print("   🔄 每小时: 更新1小时数据")
    print("   🔄 每4小时: 更新4小时数据")
    print("   🔄 每天: 更新1日数据")
    print("   🔍 每30分钟: 检查实时收集进程")
    print("   🧹 每小时: 清理日志文件")
    
    # 显示安装命令
    print(f"\\n🚀 安装命令:")
    print(f"   crontab {cron_file}")
    print("\\n📊 查看已安装的任务:")
    print("   crontab -l")
    print("\\n🔍 查看日志:")
    print(f"   tail -f {current_path}/logs/auto_update.log")
    
    return cron_file

def install_cron_jobs():
    """自动安装cron任务（需要用户权限）"""
    try:
        cron_file = setup_cron_jobs()
        
        # 尝试安装
        result = subprocess.run(['crontab', cron_file], capture_output=True, text=True)
        if result.returncode == 0:
            print("\\n✅ Cron任务安装成功!")
            
            # 验证安装
            verify_result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
            if verify_result.returncode == 0:
                lines = verify_result.stdout.split('\\n')
                crypto_lines = [line for line in lines if 'multi_timeframe' in line or 'BTCUSDT' in line]
                print(f"✅ 验证成功: 已安装 {len(crypto_lines)} 个相关任务")
            else:
                print("⚠️ 验证失败，但任务可能已安装")
        else:
            print(f"❌ Cron任务安装失败: {result.stderr}")
            print(f"🔧 请手动执行: crontab {cron_file}")
            
    except Exception as e:
        print(f"❌ 安装过程出错: {str(e)}")
        print(f"🔧 请手动执行: crontab {cron_file}")

def main():
    print("🚀 开始设置多时间周期数据定时更新...")
    
    # 创建日志目录
    logs_dir = 'logs'
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)
        print(f"📁 创建日志目录: {logs_dir}")
    
    # 安装cron任务
    install_cron_jobs()
    
    print("\\n🎉 定时任务设置完成!")
    print("\\n💡 提示:")
    print("   - 任务将在下一个时间点开始执行")
    print("   - 使用 'crontab -l' 查看已安装的任务")
    print("   - 使用 'crontab -r' 移除所有任务")
    print("   - 查看日志了解执行状态")

if __name__ == '__main__':
    main()