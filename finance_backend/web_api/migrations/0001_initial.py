# Generated by Django 4.2.7 on 2025-07-20 16:35

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SMSRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phone', models.CharField(max_length=20, verbose_name='手机号')),
                ('code', models.CharField(max_length=10, verbose_name='验证码')),
                ('purpose', models.CharField(choices=[('register', '注册'), ('login', '登录'), ('reset_password', '重置密码'), ('verify_phone', '验证手机号')], max_length=50, verbose_name='用途')),
                ('is_used', models.BooleanField(default=False, verbose_name='是否已使用')),
                ('sent_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='发送时间')),
                ('used_at', models.DateTimeField(blank=True, null=True, verbose_name='使用时间')),
                ('expires_at', models.DateTimeField(verbose_name='过期时间')),
            ],
            options={
                'verbose_name': '短信记录',
                'verbose_name_plural': '短信记录',
                'db_table': 'sms_record',
                'ordering': ['-sent_at'],
            },
        ),
        migrations.CreateModel(
            name='WebUser',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('phone', models.CharField(max_length=20, unique=True, verbose_name='手机号')),
                ('password', models.CharField(blank=True, max_length=128, null=True, verbose_name='密码')),
                ('nickname', models.CharField(default='', max_length=100, verbose_name='昵称')),
                ('avatar', models.URLField(blank=True, max_length=500, null=True, verbose_name='头像')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='邮箱')),
                ('credits', models.IntegerField(default=3, verbose_name='剩余积分')),
                ('invite_code', models.CharField(blank=True, max_length=8, null=True, unique=True, verbose_name='邀请码')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('phone_verified', models.BooleanField(default=False, verbose_name='手机号已验证')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='最后登录时间')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': 'Web用户',
                'verbose_name_plural': 'Web用户',
                'db_table': 'web_user',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CreditTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('initial', '初始赠送'), ('consume', '消费扣除'), ('purchase', '购买充值'), ('bonus', '奖励赠送'), ('refund', '退款返还')], max_length=20, verbose_name='交易类型')),
                ('amount', models.IntegerField(verbose_name='积分数量')),
                ('balance_before', models.IntegerField(verbose_name='交易前余额')),
                ('balance_after', models.IntegerField(verbose_name='交易后余额')),
                ('description', models.CharField(blank=True, max_length=200, null=True, verbose_name='描述')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='credit_transactions', to='web_api.webuser', verbose_name='用户')),
            ],
            options={
                'verbose_name': '积分交易',
                'verbose_name_plural': '积分交易',
                'db_table': 'credit_transaction',
                'ordering': ['-created_at'],
            },
        ),
    ]
