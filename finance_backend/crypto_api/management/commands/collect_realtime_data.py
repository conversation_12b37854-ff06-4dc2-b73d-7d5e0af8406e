"""
实时数据收集管理命令
24/7常驻进程，收集Binance WebSocket数据并定时上传到COS

使用方法:
python manage.py collect_realtime_data --symbol=BTCUSDT --daemon
"""

import asyncio
import websockets
import json
import threading
import time
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.conf import settings
from crypto_api.services.realtime_collector import RealtimeDataCollector


class Command(BaseCommand):
    help = '启动实时数据收集常驻进程'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--symbol', 
            default='BTCUSDT', 
            help='交易对符号 (默认: BTCUSDT)'
        )
        parser.add_argument(
            '--daemon', 
            action='store_true', 
            help='守护进程模式运行'
        )
        parser.add_argument(
            '--interval',
            default=300,
            type=int,
            help='COS上传间隔（秒，默认300=5分钟）'
        )
        parser.add_argument(
            '--debug',
            action='store_true',
            help='启用调试模式'
        )
    
    def handle(self, *args, **options):
        """启动数据收集器"""
        
        symbol = options['symbol']
        daemon = options['daemon']
        interval = options['interval']
        debug = options['debug']
        
        # 显示启动信息
        self.stdout.write(
            self.style.SUCCESS(f'🚀 启动实时数据收集器')
        )
        self.stdout.write(f'📊 交易对: {symbol}')
        self.stdout.write(f'⏰ 上传间隔: {interval}秒')
        self.stdout.write(f'🔧 守护进程: {"是" if daemon else "否"}')
        self.stdout.write(f'🐛 调试模式: {"是" if debug else "否"}')
        
        # 创建收集器实例
        collector = RealtimeDataCollector(
            symbol=symbol,
            upload_interval=interval,
            debug=debug
        )
        
        try:
            # 启动收集器
            collector.start()
            
        except KeyboardInterrupt:
            self.stdout.write(
                self.style.WARNING('\n⏹️ 收到停止信号，正在关闭...')
            )
            collector.stop()
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ 收集器异常: {e}')
            )
            raise
        
        self.stdout.write(
            self.style.SUCCESS('✅ 数据收集器已停止')
        )