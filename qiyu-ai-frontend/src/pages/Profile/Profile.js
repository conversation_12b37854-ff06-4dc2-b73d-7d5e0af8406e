import React, { useState, useEffect } from 'react';
import { Card, Button, Table, Typography, Avatar, Space, message, Modal, Input, Descriptions, Layout } from 'antd';
import {
  UserOutlined,
  CopyOutlined,
  ShareAltOutlined,
  PhoneOutlined,
  SafetyOutlined,
  CrownOutlined,
  StarOutlined,
  RocketOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import Atropos from 'atropos/react';
import Nav from '../../components/Layout/Nav';
import Footer from '../../components/Layout/Footer';
import apiClient from '../../utils/apiClient';
import './Profile.css';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Content } = Layout;

const Profile = () => {
  const [user, setUser] = useState(null);
  const [agentStats, setAgentStats] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [pagination, setPagination] = useState({ current: 1, total: 0, pageSize: 10 });
  const [loading, setLoading] = useState(true);
  const [transactionLoading, setTransactionLoading] = useState(false);
  const [purchaseLoading, setPurchaseLoading] = useState(false);
  const [inviteModalVisible, setInviteModalVisible] = useState(false);

  // 获取用户信息
  useEffect(() => {
    fetchUserProfile();
    fetchTransactions();

    // 添加一些模拟数据用于展示
    if (transactions.length === 0) {
      setTransactions([
        {
          key: '1',
          time: '2025-07-06 14:23',
          type_display: '充值',
          amount: 10,
          type: 'recharge'
        },
        {
          key: '2',
          time: '2025-07-06 14:23',
          type_display: '消耗',
          amount: 1,
          type: 'consume'
        },
        {
          key: '3',
          time: '2025-07-06 14:23',
          type_display: '邀请',
          amount: 10,
          type: 'invite'
        },
        {
          key: '4',
          time: '2025-07-06 14:23',
          type_display: '消耗',
          amount: 1,
          type: 'consume'
        },
        {
          key: '5',
          time: '2025-07-06 14:23',
          type_display: '消耗',
          amount: 1,
          type: 'consume'
        },
        {
          key: '6',
          time: '2025-07-06 14:23',
          type_display: '消耗',
          amount: 1,
          type: 'consume'
        }
      ]);
    }
  }, []);

  // 获取积分交易记录
  const fetchTransactions = async (page = 1) => {
    setTransactionLoading(true);
    try {
      const data = await apiClient.getCreditTransactions(page, 10);
      setTransactions(data.transactions);
      setPagination({
        current: data.pagination.current,
        total: data.pagination.total,
        pageSize: data.pagination.page_size
      });
    } catch (error) {
      console.error('获取交易记录失败:', error);
      message.error(error.message || '获取交易记录失败');
    } finally {
      setTransactionLoading(false);
    }
  };

  const fetchUserProfile = async () => {
    try {
      if (!apiClient.isAuthenticated()) {
        message.error('请先登录');
        return;
      }

      const data = await apiClient.getUserProfile();
      setUser(data.user);
      setAgentStats(data.user.agent_stats);
    } catch (error) {
      console.error('获取用户信息失败:', error);
      message.error(error.message || '获取用户信息失败');
    } finally {
      setLoading(false);
    }
  };

  // 购买积分套餐
  const handlePurchase = async (packageType) => {
    setPurchaseLoading(true);
    try {
      const amount = packageType === 'small' ? 3 : packageType === 'medium' ? 5 : 9;
      await apiClient.purchaseCredits(packageType, amount);
      message.success('购买成功！');
      fetchUserProfile(); // 刷新用户信息
      fetchTransactions(); // 刷新交易记录
    } catch (error) {
      console.error('购买失败:', error);
      message.error(error.message || '购买失败');
    } finally {
      setPurchaseLoading(false);
    }
  };

  // 复制邀请链接
  const copyInviteLink = () => {
    const inviteUrl = `https://www.jinseqiyu.com/auth?mode=register&ref=${user?.invite_code || 'CHEN888'}`;
    navigator.clipboard.writeText(inviteUrl).then(() => {
      message.success('邀请链接已复制到剪贴板');
    }).catch(() => {
      message.error('复制失败');
    });
  };

  // 积分明细表格列
  const transactionColumns = [
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
      width: '40%',
    },
    {
      title: '类型',
      dataIndex: 'type_display',
      key: 'type_display',
      width: '30%',
    },
    {
      title: '积分变动',
      dataIndex: 'amount',
      key: 'amount',
      width: '30%',
      render: (amount, record) => (
        <span className={record.type === 'consume' ? 'negative' : 'positive'}>
          {record.type === 'consume' ? '-' : '+'}{amount}
        </span>
      )
    },
  ];

  if (loading) {
    return (
      <Layout style={{ height: '100vh', maxHeight: '100vh', background: '#0f0f0f', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
        <Nav />
        <Content style={{ flex: 1, background: '#0f0f0f', overflow: 'hidden' }}>
          <div className="profile-loading">加载中...</div>
        </Content>
        <Footer />
      </Layout>
    );
  }

  return (
    <Layout style={{ height: '100vh', maxHeight: '100vh', background: '#0f0f0f', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
      {/* 导航栏 */}
      <Nav />

      {/* 主内容区域 */}
      <Content style={{ flex: 1, background: '#0f0f0f', overflow: 'hidden' }}>
        <div className="profile-container">
          <div className="profile-layout">
            {/* 左列 */}
            <div className="profile-left">
              {/* 账户信息卡片 */}
              <Card className="user-info-card" title="账户信息">
                <div className="user-info-content">
                  <div className="user-info-item">
                    <span className="label">手机号码</span>
                    <span className="value">{user?.phone?.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') || '138****8888'}</span>
                    <Button type="link" size="small" className="modify-btn">修改</Button>
                  </div>
                  <div className="user-info-item">
                    <span className="label">账户密码</span>
                    <span className="value">********</span>
                    <Button type="link" size="small" className="modify-btn">修改密码</Button>
                  </div>
                </div>
              </Card>

              {/* 邀请链接区域 */}
              <Card
                className="invite-card"
                title={
                  <div className="invite-card-title">
                    <span>邀请链接</span>
                    <QuestionCircleOutlined
                      className="invite-help-icon"
                      onClick={() => setInviteModalVisible(true)}
                    />
                  </div>
                }
              >
                <div className="invite-content">
                  <div className="invite-url">
                    <Text code>{`https://www.jinseqiyu.com/auth?mode=register&ref=${user?.invite_code || '系统错误，请联系管理员'}`}</Text>
                    <Button
                      type="primary"
                      icon={<CopyOutlined />}
                      onClick={copyInviteLink}
                    >
                      复制链接
                    </Button>
                  </div>
                  <Text type="secondary">与朋友分享此链接，当他们订阅时您将获得奖励</Text>
                </div>
              </Card>

              {/* 我的积分套餐 */}
              <Card
                className="credits-card"
                title={
                  <div className="credits-card-title">
                    <span>我的余额</span>
                    <span className="credits-balance">{user?.credits || 3}次</span>
                  </div>
                }
              >

                <div className="package-grid">
                  {/* 基础套餐 - 浅绿色 */}
                  <Atropos
                    className="package-atropos"
                    activeOffset={30}
                    shadowScale={1.03}
                    rotateXMax={5}
                    rotateYMax={5}
                  >
                    <div className="package-card-3d package-card-basic">
                      <div
                        className="package-background-glow"
                        data-atropos-offset="-2"
                      ></div>
                      <div className="package-icon-bg" data-atropos-offset="0">
                        <StarOutlined />
                      </div>
                      <div className="package-content" data-atropos-offset="5">
                        <div className="package-price">3 USD</div>
                        <div className="package-credits">10次</div>
                        <div className="package-description" data-atropos-offset="2">
                          0.3 USD/次
                        </div>
                      </div>
                      <Button
                        className="package-button"
                        data-atropos-offset="3"
                        type="primary"
                        block
                        loading={purchaseLoading}
                        onClick={() => handlePurchase('small')}
                      >
                        购买
                      </Button>
                      <div className="shine-effect" data-atropos-offset="3"></div>
                    </div>
                  </Atropos>

                  {/* 标准套餐 - 中绿色 */}
                  <Atropos
                    className="package-atropos"
                    activeOffset={30}
                    shadowScale={1.03}
                    rotateXMax={5}
                    rotateYMax={5}
                  >
                    <div className="package-card-3d package-card-standard">
                      <div
                        className="package-background-glow"
                        data-atropos-offset="-2"
                      ></div>
                      <div className="package-icon-bg" data-atropos-offset="0">
                        <CrownOutlined />
                      </div>
                      <div className="package-content" data-atropos-offset="5">
                        <div className="package-price">5 USD</div>
                        <div className="package-credits">20次</div>
                        <div className="package-description" data-atropos-offset="2">
                          0.25 USD/次
                        </div>
                      </div>
                      <Button
                        className="package-button"
                        data-atropos-offset="3"
                        type="primary"
                        block
                        loading={purchaseLoading}
                        onClick={() => handlePurchase('medium')}
                      >
                        购买
                      </Button>
                      <div className="shine-effect" data-atropos-offset="3"></div>
                    </div>
                  </Atropos>

                  {/* 高级套餐 - 深绿色 */}
                  <Atropos
                    className="package-atropos"
                    activeOffset={30}
                    shadowScale={1.03}
                    rotateXMax={5}
                    rotateYMax={5}
                  >
                    <div className="package-card-3d package-card-premium">
                      <div
                        className="package-background-glow"
                        data-atropos-offset="-2"
                      ></div>
                      <div className="package-icon-bg" data-atropos-offset="0">
                        <RocketOutlined />
                      </div>
                      <div className="package-content" data-atropos-offset="5">
                        <div className="package-price">9 USD</div>
                        <div className="package-credits">50次</div>
                        <div className="package-description" data-atropos-offset="2">
                          0.18 USD/次
                        </div>
                      </div>
                      <Button
                        className="package-button"
                        data-atropos-offset="3"
                        type="primary"
                        block
                        loading={purchaseLoading}
                        onClick={() => handlePurchase('large')}
                      >
                        购买
                      </Button>
                      <div className="shine-effect" data-atropos-offset="3"></div>
                    </div>
                  </Atropos>
                </div>
              </Card>
            </div>

            {/* 右列 */}
            <div className="profile-right">
              {/* 积分明细 */}
              <Card className="transactions-card" title="积分明细">
                <Table
                  columns={transactionColumns}
                  dataSource={transactions}
                  loading={transactionLoading}
                  pagination={{
                    current: pagination.current,
                    total: pagination.total,
                    pageSize: pagination.pageSize,
                    showSizeChanger: false,
                    onChange: (page) => fetchTransactions(page),
                  }}
                  size="small"
                />
              </Card>
            </div>
          </div>
        </div>
      </Content>

      {/* 底部 */}
      <Footer />

      {/* 邀请说明Modal */}
      <Modal
        title="推荐计划"
        open={inviteModalVisible}
        onCancel={() => setInviteModalVisible(false)}
        footer={null}
        width={600}
        className="invite-modal"
        transitionName=""
        maskTransitionName=""
        centered
      >
        <div className="affiliate-program">
          <div className="mb-3">
            <Text type="secondary" className="d-block mb-2">
              与朋友分享此链接，当他们订阅时您将获得奖励
            </Text>
            <div className="input-group">
              <Input
                value={`https://www.jinseqiyu.com/auth?mode=register&ref=${user?.invite_code || '系统错误，请联系管理员'}`}
                readOnly
                size="small"
              />
              <Button
                type="primary"
                size="small"
                icon={<CopyOutlined />}
                onClick={copyInviteLink}
              >
                复制链接
              </Button>
            </div>
          </div>

          <div className="text-center mb-4">
            <div style={{ display: 'flex', gap: '16px' }}>
              <div className="border rounded p-3" style={{ flex: 1, border: '1px solid #d9d9d9', borderRadius: '6px', padding: '16px', textAlign: 'center' }}>
                <div className="h4 mb-1" style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff', margin: '0 0 8px 0' }}>
                  {user?.agent_stats?.total_invitees || 0}
                </div>
                <Text type="secondary">总推荐数</Text>
              </div>
              <div className="border rounded p-3" style={{ flex: 1, border: '1px solid #d9d9d9', borderRadius: '6px', padding: '16px', textAlign: 'center' }}>
                <div className="h4 mb-1" style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a', margin: '0 0 8px 0' }}>
                  {user?.agent_stats?.active_invitees || 0}
                </div>
                <Text type="secondary">有效推荐</Text>
              </div>
            </div>
          </div>

          <div className="mb-3">
            <div style={{ marginBottom: '16px' }}>
              <div style={{ marginBottom: '8px' }}>1. 与朋友分享您的专属推荐链接</div>
              <div style={{ marginBottom: '8px' }}>2. 您的朋友使用您的链接注册</div>
              <div style={{ marginBottom: '8px' }}>3. 当他们进行首次消费或后续消费时，你们都将获得奖励！</div>
              <div style={{ marginBottom: '16px', color: '#1890ff' }}>
                <span style={{ marginRight: '8px' }}>ℹ️</span>
                注意：账户间的推荐关系是永久有效的
              </div>
            </div>

            <Title level={5} style={{ marginBottom: '16px' }}>奖励系统</Title>

            <div style={{ display: 'flex', gap: '16px', marginBottom: '16px' }}>
              <div style={{ flex: 1 }}>
                <Card size="small" style={{ borderColor: '#52c41a' }}>
                  <div style={{ padding: '16px' }}>
                    <Title level={5} style={{ color: '#52c41a', marginBottom: '8px' }}>一级返利</Title>
                    <Text style={{ fontSize: '12px', margin: 0 }}>
                      您的直接邀请人消费时，您获得消费金额的 1/3 返利
                    </Text>
                  </div>
                </Card>
              </div>
              <div style={{ flex: 1 }}>
                <Card size="small" style={{ borderColor: '#faad14' }}>
                  <div style={{ padding: '16px' }}>
                    <Title level={5} style={{ color: '#faad14', marginBottom: '8px' }}>二级返利</Title>
                    <Text style={{ fontSize: '12px', margin: 0 }}>
                      您的间接邀请人消费时，您获得消费金额的 1/5 返利
                    </Text>
                  </div>
                </Card>
              </div>
            </div>

            <div style={{ fontSize: '12px', color: '#8c8c8c', marginTop: '16px' }}>
              <span style={{ marginRight: '8px' }}>💡</span>
              被推荐人可以在任何时候进行多次消费，每次符合条件的消费都会获得相应奖励
            </div>
          </div>
        </div>
      </Modal>
    </Layout>
  );
};

export default Profile;