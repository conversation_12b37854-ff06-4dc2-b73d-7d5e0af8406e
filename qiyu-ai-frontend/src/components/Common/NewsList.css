/* 新闻列表样式 */
.news-list {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  backdrop-filter: blur(10px);
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 新闻头部 */
.news-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.news-title {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.news-badge .ant-badge-count {
  background: #4CAF50;
  color: white;
  border: none;
  font-size: 10px;
  min-width: 16px;
  height: 16px;
  line-height: 16px;
  padding: 0 4px;
}

/* 新闻内容 */
.news-content {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.news-item {
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.news-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

/* 新闻元信息 */
.news-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.news-time {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

.news-meta .ant-badge {
  color: #4CAF50;
  font-size: 12px;
}

.news-meta .ant-badge-status-dot {
  background: #4CAF50;
}

.news-meta .ant-badge-status-text {
  color: #4CAF50;
  font-size: 12px;
}

/* 新闻标题 */
.news-item-title {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

/* 新闻摘要 */
.news-excerpt {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  line-height: 1.5;
  margin: 0 0 8px 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 新闻操作 */
.news-actions {
  display: flex;
  justify-content: flex-end;
}

.read-more {
  color: #4CAF50;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.3s ease;
}

.read-more:hover {
  color: #66bb6a;
}

/* 滚动条样式 */
.news-content::-webkit-scrollbar {
  width: 4px;
}

.news-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.news-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.news-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .news-list {
    padding: 12px;
    border-radius: 8px;
  }
  
  .news-header {
    margin-bottom: 12px;
    padding-bottom: 8px;
  }
  
  .news-title {
    font-size: 14px;
  }
  
  .news-item {
    padding: 10px;
    gap: 12px;
  }
  
  .news-item-title {
    font-size: 13px;
  }
  
  .news-excerpt {
    font-size: 11px;
  }
  
  .news-time,
  .read-more {
    font-size: 11px;
  }
}
