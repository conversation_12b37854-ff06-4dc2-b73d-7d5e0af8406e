import React from 'react';
import { Progress } from 'antd';
import './StatsCard.css';

const StatsCard = ({ 
  title, 
  icon, 
  type = 'prediction', // 'prediction', 'market', 'analysis'
  data = {},
  className = ''
}) => {
  
  // 渲染预测卡片
  const renderPredictionCard = () => {
    const {
      bullish = 39.48,
      veryBullish = 32.30,
      bearish = 16.52,
      veryBearish = 6.81,
      other = 4.89
    } = data;

    const chartData = [
      { label: '看涨', value: bullish, color: '#26a69a' },
      { label: '非常看涨', value: veryBullish, color: '#4CAF50' },
      { label: '看跌', value: bearish, color: '#ff9800' },
      { label: '非常看跌', value: veryBearish, color: '#f44336' },
      { label: '其他', value: other, color: '#2196f3' }
    ];

    return (
      <div className="prediction-content">
        <div className="prediction-legend">
          {chartData.map((item, index) => (
            <div key={index} className="legend-item">
              <div
                className="legend-color"
                style={{ backgroundColor: item.color }}
              />
              <span className="legend-label">{item.label}</span>
              <span className="legend-value">{item.value}%</span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // 渲染市场情绪卡片
  const renderMarketCard = () => {
    const { 
      btcSentiment = 45,
      ethSentiment = 25,
      updateTime = '2025-07-03 23:30:00'
    } = data;

    return (
      <div className="market-content">
        <div className="market-header">
          <span className="update-time">实时更新时间: {updateTime}</span>
        </div>
        <div className="sentiment-items">
          <div className="sentiment-item">
            <div className="sentiment-info">
              <span className="coin-name">BTC</span>
              <span className="sentiment-value">{btcSentiment}%多头</span>
            </div>
            <Progress 
              percent={btcSentiment} 
              strokeColor="#26a69a"
              trailColor="rgba(255, 255, 255, 0.1)"
              showInfo={false}
              size="small"
            />
          </div>
          <div className="sentiment-item">
            <div className="sentiment-info">
              <span className="coin-name">ETH</span>
              <span className="sentiment-value">{ethSentiment}%多头</span>
            </div>
            <Progress 
              percent={ethSentiment} 
              strokeColor="#26a69a"
              trailColor="rgba(255, 255, 255, 0.1)"
              showInfo={false}
              size="small"
            />
          </div>
        </div>
      </div>
    );
  };

  // 渲染分析卡片
  const renderAnalysisCard = () => {
    const { 
      isLocked = true,
      message = '消耗1积分查看',
      actionText = '点此查看分析'
    } = data;

    return (
      <div className="analysis-content">
        {isLocked ? (
          <div className="locked-content">
            <div className="lock-icon">🔒</div>
            <div className="lock-message">
              <p>{message}</p>
              <span className="action-text">{actionText}</span>
            </div>
          </div>
        ) : (
          <div className="analysis-text">
            {/* 这里可以显示实际的分析内容 */}
            <p>AI分析内容将在这里显示...</p>
          </div>
        )}
      </div>
    );
  };

  const renderContent = () => {
    switch (type) {
      case 'prediction':
        return renderPredictionCard();
      case 'market':
        return renderMarketCard();
      case 'analysis':
        return renderAnalysisCard();
      default:
        return null;
    }
  };

  return (
    <div className={`stats-card stats-card--${type} ${className}`}>
      <div className="stats-card-header">
        {icon && <div className="card-icon">{icon}</div>}
        <h3 className="card-title">{title}</h3>
      </div>
      <div className="stats-card-content">
        {renderContent()}
      </div>
    </div>
  );
};

export default StatsCard;
