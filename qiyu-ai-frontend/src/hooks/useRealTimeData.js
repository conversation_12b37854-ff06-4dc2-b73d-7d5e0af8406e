/**
 * 实时数据Hook - 已禁用WebSocket实时连接
 * 改为使用定时刷新模式，更稳定可靠
 */
import { useCallback } from 'react';

export const useRealTimeData = (symbol, interval, chartInstance, enabled = false) => {
  console.log(`实时数据Hook已禁用: ${symbol} ${interval}, 使用定时刷新模式`);

  // 获取连接状态 - 返回禁用状态
  const getConnectionStatus = useCallback(() => {
    return 'disabled';
  }, []);

  // 手动重连 - 无操作
  const reconnect = useCallback(() => {
    console.log('WebSocket重连已禁用，使用定时刷新模式');
  }, []);

  return {
    connectionStatus: 'disabled',
    reconnect,
    stats: { 
      connected: false, 
      subscriptions: 0,
      totalMessages: 0,
      errors: 0
    }
  };
};

export default useRealTimeData;