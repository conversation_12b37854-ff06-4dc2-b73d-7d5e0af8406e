import React, { useState, useCallback } from 'react';
import { Card, Button, Select, InputNumber, Radio, Spin, Alert, Typography, Divider, Progress } from 'antd';
import { RobotOutlined, BarChartOutlined, ThunderboltOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import aiAnalysisService from '../../services/AIAnalysisService';
import AIAnalysisDebugPanel from './AIAnalysisDebugPanel';

const { Option } = Select;
const { Text, Paragraph } = Typography;

const EnhancedAIAnalyzer = ({ symbol = 'BTC', onAnalysisComplete }) => {
  const [loading, setLoading] = useState(false);
  const [analysisResult, setAnalysisResult] = useState('');
  const [progress, setProgress] = useState('');
  const [error, setError] = useState(null);
  const [dataStatus, setDataStatus] = useState({});
  const [progressPercent, setProgressPercent] = useState(0);
  const [debugInfo, setDebugInfo] = useState(null);
  const [requestData, setRequestData] = useState(null);
  
  // 分析参数
  const [analysisType, setAnalysisType] = useState('quick');
  const [positionAmount, setPositionAmount] = useState(300);
  const [positionPercentage, setPositionPercentage] = useState(2.7);
  const [isHolding, setIsHolding] = useState(true);

  // 分析类型选项
  const analysisOptions = [
    { value: 'quick', label: '快速分析', icon: <ThunderboltOutlined />, description: '基于24小时数据的快速分析' },
    { value: 'four_hour', label: '闪电分析', icon: <ThunderboltOutlined />, description: '未来4小时精准预测' },
    { value: 'one_day', label: '一日分析', icon: <BarChartOutlined />, description: '未来24小时详细分析' },
    { value: 'three_day', label: '三日分析', icon: <BarChartOutlined />, description: '未来3天趋势分析' },
    { value: 'week', label: '一周分析', icon: <BarChartOutlined />, description: '未来一周市场预测' },
    { value: 'long_term', label: '长期分析', icon: <BarChartOutlined />, description: '长期投资建议' }
  ];

  // 处理进度更新
  const handleProgress = useCallback((message, type = 'progress') => {
    if (type === 'streaming') {
      setAnalysisResult(message);
      setProgressPercent(90); // 流式接收阶段
    } else if (type === 'complete') {
      setAnalysisResult(message);
      setLoading(false);
      setProgress('分析完成');
      setProgressPercent(100);
      if (onAnalysisComplete) {
        onAnalysisComplete(message);
      }
    } else {
      setProgress(message);
      // 根据进度消息更新百分比
      if (message.includes('获取K线数据')) {
        setProgressPercent(20);
        setDataStatus(prev => ({ ...prev, kline: 'loading' }));
      } else if (message.includes('获取实时价格')) {
        setProgressPercent(35);
        setDataStatus(prev => ({ ...prev, kline: 'success', ticker: 'loading' }));
      } else if (message.includes('获取订单簿')) {
        setProgressPercent(50);
        setDataStatus(prev => ({ ...prev, ticker: 'success', orderbook: 'loading' }));
      } else if (message.includes('获取BTC')) {
        setProgressPercent(65);
        setDataStatus(prev => ({ ...prev, orderbook: 'success', btc: 'loading' }));
      } else if (message.includes('处理分析数据')) {
        setProgressPercent(75);
        setDataStatus(prev => ({ ...prev, btc: 'success', processing: 'loading' }));
      } else if (message.includes('开始AI分析')) {
        setProgressPercent(80);
        setDataStatus(prev => ({ ...prev, processing: 'success', ai: 'loading' }));
      }
    }
  }, [onAnalysisComplete]);

  // 开始AI分析
  const startAnalysis = async () => {
    try {
      setLoading(true);
      setError(null);
      setAnalysisResult('');
      setProgress('准备开始分析...');
      setProgressPercent(0);
      setDataStatus({});

      console.log(`🚀 开始AI分析: ${symbol} - ${analysisType}`);

      const result = await aiAnalysisService.analyzeWithAI(symbol, analysisType, {
        positionAmount,
        positionPercentage,
        isHolding,
        onProgress: handleProgress,
        enableFallback: true // 启用降级处理
      });

      console.log('✅ AI分析完成:', result?.length || 0, '字符');
      
    } catch (err) {
      console.error('❌ AI分析失败:', err);
      
      // 保存调试信息
      if (err.debugInfo) {
        setDebugInfo(err.debugInfo);
      }
      
      // 处理增强的错误信息
      if (err.type && err.title) {
        setError(err);
      } else {
        setError({
          type: 'unknown',
          title: '分析失败',
          message: err.message || '未知错误，请稍后重试',
          suggestion: '请检查网络连接或稍后重试',
          debugInfo: err.debugInfo
        });
      }
      
      setLoading(false);
      setProgress('');
      setProgressPercent(0);
      setDataStatus(prev => ({ ...prev, error: true }));
    }
  };

  // 获取当前分析类型的配置
  const currentAnalysisConfig = analysisOptions.find(opt => opt.value === analysisType);

  return (
    <Card 
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <RobotOutlined style={{ color: '#26A69A' }} />
          <span>AI智能分析</span>
        </div>
      }
      size="small"
      style={{ marginBottom: '16px' }}
    >
      {/* 分析参数配置 */}
      <div style={{ marginBottom: '16px' }}>
        <div style={{ marginBottom: '12px' }}>
          <Text strong>分析类型：</Text>
          <Select
            value={analysisType}
            onChange={setAnalysisType}
            style={{ width: '200px', marginLeft: '8px' }}
            disabled={loading}
          >
            {analysisOptions.map(option => (
              <Option key={option.value} value={option.value}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                  {option.icon}
                  <span>{option.label}</span>
                </div>
              </Option>
            ))}
          </Select>
        </div>

        {currentAnalysisConfig && (
          <div style={{ marginBottom: '12px' }}>
            <Text type="secondary">{currentAnalysisConfig.description}</Text>
          </div>
        )}

        <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap', marginBottom: '12px' }}>
          <div>
            <Text strong>持仓数量：</Text>
            <InputNumber
              value={positionAmount}
              onChange={setPositionAmount}
              min={0}
              style={{ width: '100px', marginLeft: '8px' }}
              disabled={loading}
            />
          </div>
          
          <div>
            <Text strong>仓位比例：</Text>
            <InputNumber
              value={positionPercentage}
              onChange={setPositionPercentage}
              min={0}
              max={100}
              step={0.1}
              formatter={value => `${value}%`}
              parser={value => value.replace('%', '')}
              style={{ width: '100px', marginLeft: '8px' }}
              disabled={loading}
            />
          </div>
        </div>

        <div style={{ marginBottom: '16px' }}>
          <Text strong>建议类型：</Text>
          <Radio.Group
            value={isHolding}
            onChange={e => setIsHolding(e.target.value)}
            style={{ marginLeft: '8px' }}
            disabled={loading}
          >
            <Radio value={true}>持仓建议</Radio>
            <Radio value={false}>开仓建议</Radio>
          </Radio.Group>
        </div>
      </div>

      <Divider />

      {/* 分析按钮 */}
      <div style={{ textAlign: 'center', marginBottom: '16px' }}>
        <Button
          type="primary"
          size="large"
          icon={<RobotOutlined />}
          onClick={startAnalysis}
          loading={loading}
          disabled={loading}
          style={{ 
            background: 'linear-gradient(135deg, #26A69A 0%, #00695C 100%)',
            border: 'none',
            borderRadius: '8px',
            height: '48px',
            fontSize: '16px',
            fontWeight: 'bold'
          }}
        >
          {loading ? '分析中...' : `开始${currentAnalysisConfig?.label || '分析'}`}
        </Button>
      </div>

      {/* 进度显示 */}
      {loading && (
        <div style={{ marginBottom: '16px' }}>
          <div style={{ textAlign: 'center', marginBottom: '12px' }}>
            <Spin size="small" style={{ marginRight: '8px' }} />
            <Text type="secondary">{progress}</Text>
          </div>
          
          {/* 进度条 */}
          <Progress 
            percent={progressPercent} 
            size="small" 
            status={error ? 'exception' : 'active'}
            showInfo={false}
            style={{ marginBottom: '12px' }}
          />
          
          {/* 数据获取状态 */}
          <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap', fontSize: '12px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              {dataStatus.kline === 'success' ? (
                <CheckCircleOutlined style={{ color: '#52c41a' }} />
              ) : dataStatus.kline === 'loading' ? (
                <Spin size="small" />
              ) : (
                <div style={{ width: '14px', height: '14px', borderRadius: '50%', backgroundColor: '#d9d9d9' }} />
              )}
              <Text type="secondary">K线数据</Text>
            </div>
            
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              {dataStatus.ticker === 'success' ? (
                <CheckCircleOutlined style={{ color: '#52c41a' }} />
              ) : dataStatus.ticker === 'loading' ? (
                <Spin size="small" />
              ) : (
                <div style={{ width: '14px', height: '14px', borderRadius: '50%', backgroundColor: '#d9d9d9' }} />
              )}
              <Text type="secondary">实时价格</Text>
            </div>
            
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              {dataStatus.orderbook === 'success' ? (
                <CheckCircleOutlined style={{ color: '#52c41a' }} />
              ) : dataStatus.orderbook === 'loading' ? (
                <Spin size="small" />
              ) : (
                <div style={{ width: '14px', height: '14px', borderRadius: '50%', backgroundColor: '#d9d9d9' }} />
              )}
              <Text type="secondary">订单簿</Text>
            </div>
            
            {symbol !== 'BTC' && (
              <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                {dataStatus.btc === 'success' ? (
                  <CheckCircleOutlined style={{ color: '#52c41a' }} />
                ) : dataStatus.btc === 'loading' ? (
                  <Spin size="small" />
                ) : (
                  <div style={{ width: '14px', height: '14px', borderRadius: '50%', backgroundColor: '#d9d9d9' }} />
                )}
                <Text type="secondary">BTC参考</Text>
              </div>
            )}
            
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              {dataStatus.ai === 'success' ? (
                <CheckCircleOutlined style={{ color: '#52c41a' }} />
              ) : dataStatus.ai === 'loading' ? (
                <Spin size="small" />
              ) : (
                <div style={{ width: '14px', height: '14px', borderRadius: '50%', backgroundColor: '#d9d9d9' }} />
              )}
              <Text type="secondary">AI分析</Text>
            </div>
          </div>
        </div>
      )}

      {/* 错误显示 */}
      {error && (
        <Alert
          message={error.title || "分析失败"}
          description={
            <div>
              <div style={{ marginBottom: '8px' }}>{error.message || error}</div>
              {error.suggestion && (
                <div style={{ fontSize: '12px', color: '#666' }}>
                  💡 建议: {error.suggestion}
                </div>
              )}
              {error.debugInfo && (
                <details style={{ marginTop: '8px', fontSize: '11px' }}>
                  <summary style={{ cursor: 'pointer', color: '#999' }}>调试信息</summary>
                  <pre style={{ 
                    background: '#f5f5f5', 
                    padding: '8px', 
                    borderRadius: '4px', 
                    marginTop: '4px',
                    fontSize: '10px',
                    overflow: 'auto',
                    maxHeight: '200px'
                  }}>
                    {JSON.stringify(error.debugInfo, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          }
          type="error"
          showIcon
          style={{ marginBottom: '16px' }}
          action={
            <Button 
              size="small" 
              onClick={() => {
                setError(null);
                setAnalysisResult('');
                setProgress('');
                setProgressPercent(0);
                setDataStatus({});
              }}
            >
              重试
            </Button>
          }
        />
      )}

      {/* 分析结果 */}
      {analysisResult && (
        <Card 
          title="分析结果" 
          size="small" 
          style={{ 
            background: 'rgba(38, 166, 154, 0.05)',
            border: '1px solid rgba(38, 166, 154, 0.2)'
          }}
        >
          <div style={{ 
            maxHeight: '400px', 
            overflowY: 'auto',
            padding: '8px',
            background: 'rgba(0, 0, 0, 0.02)',
            borderRadius: '4px',
            border: '1px solid rgba(0, 0, 0, 0.06)'
          }}>
            <Paragraph style={{ 
              whiteSpace: 'pre-wrap', 
              margin: 0,
              fontSize: '14px',
              lineHeight: '1.6'
            }}>
              {analysisResult}
            </Paragraph>
          </div>
          
          {loading && (
            <div style={{ 
              position: 'absolute', 
              bottom: '8px', 
              right: '8px',
              background: 'rgba(38, 166, 154, 0.1)',
              padding: '4px 8px',
              borderRadius: '4px',
              fontSize: '12px'
            }}>
              <Spin size="small" style={{ marginRight: '4px' }} />
              实时生成中...
            </div>
          )}
        </Card>
      )}

      {/* 分析参数摘要 */}
      {!loading && !analysisResult && (
        <Card size="small" style={{ background: 'rgba(0, 0, 0, 0.02)' }}>
          <Text type="secondary">
            当前配置：{symbol} | {currentAnalysisConfig?.label} | 
            {isHolding ? '持仓' : '开仓'}建议 | 
            数量：{positionAmount} | 比例：{positionPercentage}%
          </Text>
        </Card>
      )}

      {/* 调试面板 */}
      {(process.env.NODE_ENV === 'development' || debugInfo || error?.debugInfo) && (
        <AIAnalysisDebugPanel
          debugInfo={debugInfo || error?.debugInfo}
          dataSummary={debugInfo?.dataSummary}
          integrityCheck={debugInfo?.dataIntegrity}
          requestData={requestData}
          visible={false}
        />
      )}
    </Card>
  );
};

export default EnhancedAIAnalyzer;
