/**
 * K线数据服务 - 集成到React项目
 * 提供统一的数据获取接口，支持本地和COS数据源
 */

import MockDataGenerator from './MockDataGenerator';

class KlineDataService {
    constructor(config = {}) {
        // 数据源配置
        this.config = {
            // 本地数据路径 (开发环境) - 修正为正确的public路径
            localBaseUrl: config.localBaseUrl || '/processed_data/',
            // COS数据路径 (生产环境)  
            cosBaseUrl: config.cosBaseUrl || 'https://your-cos-domain.com/kline-data/processed/',
            // 当前使用的数据源
            dataSource: config.dataSource || 'local', // 'local' | 'cos' | 'mock'
            // 支持的交易对
            symbols: ['BTCUSDT', 'ETHUSDT', 'LTCUSDT', 'XRPUSDT', 'DOGEUSDT', 'SOLUSDT'],
            // 缓存配置
            cacheEnabled: config.cacheEnabled !== false,
            cacheExpiry: config.cacheExpiry || 10 * 60 * 1000, // 10分钟
            // 开发模式设置
            useMockData: config.useMockData !== false // 默认在数据加载失败时使用模拟数据
        };
        
        // 内存缓存
        this.cache = new Map();
        this.metadata = null;
    }

    /**
     * 获取数据索引元信息
     */
    async getMetadata() {
        if (this.metadata && this.config.cacheEnabled) {
            return this.metadata;
        }

        try {
            const url = this._buildUrl('metadata/data-index.json');
            const response = await fetch(url);
            
            if (!response.ok) {
                throw new Error(`Failed to fetch metadata: ${response.status}`);
            }

            this.metadata = await response.json();
            return this.metadata;
        } catch (error) {
            console.error('Error fetching metadata:', error);
            throw error;
        }
    }

    /**
     * 获取当前年份数据 (2025年)
     * @param {string} symbol - 交易对
     * @param {string} interval - 时间精度 ('1d' | '1h')
     */
    async getCurrentYearData(symbol, interval = '1d') {
        const cacheKey = `current-${symbol}-${interval}-2025`;
        
        // 检查缓存
        if (this._checkCache(cacheKey)) {
            return this.cache.get(cacheKey).data;
        }

        // 对于非日线数据，直接使用模拟数据
        if (interval !== '1d' && interval !== '1h') {
            console.log(`📋 ${interval} 时间周期使用模拟数据: ${symbol}`);
            const mockData = MockDataGenerator.generateCurrentYearData(symbol, interval);
            this._setCache(cacheKey, mockData);
            return mockData;
        }

        try {
            const url = this._buildUrl(`current/${symbol}-${interval}-2025.json`);
            const data = await this._fetchJson(url, 3000); // 减少超时时间到3秒

            // 缓存数据
            this._setCache(cacheKey, data);
            return data;
        } catch (error) {
            console.warn(`❌ ${symbol} 2025年数据不可用:`, error.message);

            // 立即尝试使用2024年数据作为当年数据
            console.log(`🔄 立即使用2024年数据作为${symbol}当年数据...`);
            try {
                const fallbackData = await this.getYearlyData(symbol, 2024);
                if (fallbackData && fallbackData.data && fallbackData.data.length > 0) {
                    // 使用2024年数据作为当年数据
                    const currentYearData = {
                        ...fallbackData,
                        year: 2025,
                        metadata: {
                            ...fallbackData.metadata,
                            note: 'Using 2024 data as current year fallback',
                            originalYear: 2024
                        }
                    };
                    this._setCache(cacheKey, currentYearData);
                    console.log(`✅ 成功使用2024年数据作为${symbol}当年数据`);
                    return currentYearData;
                }
            } catch (fallbackError) {
                console.error(`❌ 使用2024数据作为备选也失败:`, fallbackError.message);
            }

            // 如果启用模拟数据，最后才使用模拟数据
            if (this.config.useMockData) {
                console.log(`🎲 生成模拟数据: ${symbol} 当年数据`);
                const mockData = MockDataGenerator.generateCurrentYearData(symbol);
                this._setCache(cacheKey, mockData);
                return mockData;
            }

            // 抛出错误让上层处理
            throw new Error(`无法获取${symbol}当年数据`);
        }
    }

    /**
     * 获取指定年份数据
     * @param {string} symbol - 交易对
     * @param {number} year - 年份
     */
    async getYearlyData(symbol, year) {
        const cacheKey = `yearly-${symbol}-${year}`;
        
        // 检查缓存
        if (this._checkCache(cacheKey)) {
            return this.cache.get(cacheKey).data;
        }

        try {
            const url = this._buildUrl(`yearly/${symbol}-1d-${year}.json`);
            const data = await this._fetchJson(url, 5000); // 5秒超时

            // 历史数据缓存时间更长
            this._setCache(cacheKey, data, 60 * 60 * 1000); // 1小时
            console.log(`✅ ${symbol} ${year}年数据获取成功，数据量: ${data.data?.length || 0}`);
            return data;
        } catch (error) {
            console.warn(`❌ ${symbol} ${year}年数据不可用:`, error.message);

            // 降级到模拟数据
            if (this.config.useMockData) {
                console.log(`📋 使用模拟数据: ${symbol} ${year}年数据`);
                const mockData = MockDataGenerator.generateKlineData(symbol, year, '1d', 365);
                this._setCache(cacheKey, mockData, 60 * 60 * 1000);
                return mockData;
            }

            throw new Error(`无法获取${symbol} ${year}年数据`);
        }
    }

    /**
     * 获取8年概览数据
     * @param {string} symbol - 交易对
     */
    async getOverviewData(symbol) {
        const cacheKey = `overview-${symbol}`;
        
        // 检查缓存
        if (this._checkCache(cacheKey)) {
            return this.cache.get(cacheKey).data;
        }

        try {
            const url = this._buildUrl(`overview/${symbol}-overview.json`);
            const data = await this._fetchJson(url, 5000); // 5秒超时

            // 概览数据缓存时间更长
            this._setCache(cacheKey, data, 2 * 60 * 60 * 1000); // 2小时
            console.log(`✅ ${symbol} 概览数据获取成功，数据量: ${data.data?.length || 0}`);
            return data;
        } catch (error) {
            console.warn(`❌ ${symbol} 概览数据不可用:`, error.message);

            // 降级到模拟数据
            if (this.config.useMockData) {
                console.log(`📋 使用模拟数据: ${symbol} 概览数据`);
                const mockData = MockDataGenerator.generateOverviewData(symbol);
                this._setCache(cacheKey, mockData, 2 * 60 * 60 * 1000);
                return mockData;
            }

            throw new Error(`无法获取${symbol}概览数据`);
        }
    }

    /**
     * 获取支持的交易对列表
     */
    async getSupportedSymbols() {
        try {
            const metadata = await this.getMetadata();
            return Object.keys(metadata.yearly || {});
        } catch (error) {
            console.warn('Failed to get supported symbols, using default:', error);
            return this.config.symbols;
        }
    }

    /**
     * 获取指定交易对的可用年份
     * @param {string} symbol - 交易对
     */
    async getAvailableYears(symbol) {
        try {
            const metadata = await this.getMetadata();
            const yearlyData = metadata.yearly?.[symbol] || [];
            return yearlyData.map(item => item.year).sort((a, b) => b - a); // 降序
        } catch (error) {
            console.warn(`Failed to get available years for ${symbol}:`, error);
            return [];
        }
    }

    /**
     * 批量获取多年数据
     * @param {string} symbol - 交易对
     * @param {number[]} years - 年份数组
     */
    async getBatchYearlyData(symbol, years) {
        const promises = years.map(year => this.getYearlyData(symbol, year));
        const results = await Promise.allSettled(promises);
        
        const data = {};
        results.forEach((result, index) => {
            const year = years[index];
            if (result.status === 'fulfilled' && result.value) {
                data[year] = result.value;
            } else {
                console.warn(`Failed to load data for ${symbol}-${year}`);
            }
        });
        
        return data;
    }

    /**
     * 数据格式转换为KlineCharts格式
     * @param {Object} rawData - 原始数据
     */
    convertToKlineFormat(rawData) {
        const startTime = performance.now();
        
        if (!rawData || !rawData.data || !Array.isArray(rawData.data)) {
            console.warn('数据格式错误:', rawData);
            return [];
        }

        console.log(`开始数据转换，原始数据量: ${rawData.data.length}`);
        
        const converted = rawData.data.map(item => {
            // 确保所有数值都是有效的
            let timestamp;
            if (typeof item.timestamp === 'number') {
                timestamp = item.timestamp;
            } else if (typeof item.timestamp === 'string') {
                timestamp = new Date(item.timestamp).getTime();
            } else if (item.timestamp && typeof item.timestamp === 'object') {
                // 处理对象类型时间戳（如DateTimeFormat对象）
                const objName = item.timestamp.constructor?.name || 'Unknown';

                if (objName === 'DateTimeFormat') {
                    console.warn('DateTimeFormat对象不能直接转换为时间戳，返回当前时间');
                    timestamp = Date.now(); // ✅ 使用当前时间作为备用
                } else if (item.timestamp.valueOf && typeof item.timestamp.valueOf === 'function') {
                    timestamp = item.timestamp.valueOf();
                } else if (item.timestamp.getTime && typeof item.timestamp.getTime === 'function') {
                    timestamp = item.timestamp.getTime();
                } else if (item.timestamp.timestamp) {
                    timestamp = item.timestamp.timestamp;
                } else {
                    console.warn('无法解析对象类型时间戳:', objName, item.timestamp);
                    return null;
                }
            } else {
                console.warn('无效的时间戳类型:', typeof item.timestamp, item.timestamp);
                return null;
            }
            
            const open = parseFloat(item.open);
            const high = parseFloat(item.high);
            const low = parseFloat(item.low);
            const close = parseFloat(item.close);
            const volume = parseFloat(item.volume) || 0;

            // 验证数据有效性
            if (isNaN(open) || isNaN(high) || isNaN(low) || isNaN(close) || 
                isNaN(timestamp) || timestamp <= 0) {
                console.warn('无效的K线数据项:', {
                    timestamp: item.timestamp,
                    parsedTimestamp: timestamp,
                    open: item.open,
                    high: item.high,
                    low: item.low,
                    close: item.close,
                    volume: item.volume
                });
                return null;
            }

            // 验证价格数据的合理性
            if (open <= 0 || high <= 0 || low <= 0 || close <= 0 || 
                high < Math.max(open, close) || low > Math.min(open, close)) {
                console.warn('价格数据不合理:', {
                    open, high, low, close, timestamp
                });
                return null;
            }

            return {
                timestamp,
                open,
                high,
                low,
                close,
                volume
            };
        }).filter(item => item !== null); // 过滤掉无效数据

        const endTime = performance.now();

        // 验证转换后的数据格式
        const sampleData = converted.slice(0, 2);
        const hasInvalidTimestamp = converted.some(item =>
            typeof item.timestamp !== 'number' ||
            item.timestamp?.constructor?.name === 'DateTimeFormat'
        );

        console.log('数据转换完成:', {
            原始数量: rawData.data.length,
            转换后数量: converted.length,
            耗时: `${(endTime - startTime).toFixed(2)}ms`,
            样本数据: sampleData,
            时间戳类型检查: sampleData.map(item => ({
                timestamp: item.timestamp,
                type: typeof item.timestamp,
                constructor: item.timestamp?.constructor?.name
            })),
            有无效时间戳: hasInvalidTimestamp
        });

        if (hasInvalidTimestamp) {
            console.error('❌ 转换后的数据包含无效时间戳！');
        }

        return converted;
    }

    /**
     * 获取数据统计信息
     * @param {Object} rawData - 原始数据
     */
    getDataStats(rawData) {
        if (!rawData || !rawData.data || rawData.data.length === 0) {
            return null;
        }

        const data = rawData.data;
        const prices = data.map(item => item.close);
        const volumes = data.map(item => item.volume);

        return {
            count: data.length,
            period: {
                start: new Date(data[0].timestamp),
                end: new Date(data[data.length - 1].timestamp)
            },
            price: {
                min: Math.min(...prices),
                max: Math.max(...prices),
                first: data[0].close,
                last: data[data.length - 1].close,
                change: data[data.length - 1].close - data[0].close,
                changePercent: ((data[data.length - 1].close - data[0].close) / data[0].close * 100).toFixed(2)
            },
            volume: {
                total: volumes.reduce((sum, vol) => sum + vol, 0),
                average: volumes.reduce((sum, vol) => sum + vol, 0) / volumes.length,
                max: Math.max(...volumes)
            }
        };
    }

    /**
     * 清除缓存
     * @param {string} pattern - 缓存键模式 (可选)
     */
    clearCache(pattern = null) {
        if (pattern) {
            const keysToDelete = [];
            for (const key of this.cache.keys()) {
                if (key.includes(pattern)) {
                    keysToDelete.push(key);
                }
            }
            keysToDelete.forEach(key => this.cache.delete(key));
        } else {
            this.cache.clear();
            this.metadata = null;
        }
    }

    /**
     * 获取缓存统计信息
     */
    getCacheStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys()),
            memoryUsage: JSON.stringify(Array.from(this.cache.values())).length
        };
    }

    // 私有方法

    _buildUrl(path) {
        const baseUrl = this.config.dataSource === 'cos' 
            ? this.config.cosBaseUrl 
            : this.config.localBaseUrl;
        return `${baseUrl}${path}`;
    }

    async _fetchJson(url, timeout = 10000) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        try {
            console.log(`🌐 请求数据: ${url}`);
            const response = await fetch(url, {
                signal: controller.signal,
                headers: {
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache'
                }
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log(`✅ 数据获取成功: ${url}, 数据量: ${data.data?.length || 0}`);
            return data;
        } catch (error) {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                throw new Error(`请求超时: ${url}`);
            }
            console.error(`❌ 数据获取失败: ${url}`, error);
            throw error;
        }
    }

    _checkCache(key) {
        if (!this.config.cacheEnabled) {
            return false;
        }

        const cached = this.cache.get(key);
        if (!cached) {
            return false;
        }

        // 检查是否过期
        if (Date.now() - cached.timestamp > cached.expiry) {
            this.cache.delete(key);
            return false;
        }

        return true;
    }

    _setCache(key, data, customExpiry = null) {
        if (!this.config.cacheEnabled) {
            return;
        }

        this.cache.set(key, {
            data,
            timestamp: Date.now(),
            expiry: customExpiry || this.config.cacheExpiry
        });
    }
}

// 创建全局实例
const klineDataService = new KlineDataService({
    dataSource: 'local',
    localBaseUrl: '/processed_data/' // 绝对路径，相对于public目录
});

export default klineDataService;