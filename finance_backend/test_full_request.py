#!/usr/bin/env python3
"""
生成完整的测试请求数据
"""
import json
import time

def generate_test_data():
    """生成足够的测试K线数据"""
    base_timestamp = 1735689600000  # 2025-01-01 00:00:00 UTC
    base_price = 95000
    
    short_term_klines = []
    daily_klines = []
    
    # 生成100条短期K线数据（满足技术指标计算需求）
    for i in range(100):
        timestamp = base_timestamp + i * 60000  # 每分钟一条
        open_price = base_price + (i % 20 - 10) * 100  # 价格波动
        high_price = open_price + 200
        low_price = open_price - 200
        close_price = open_price + (i % 10 - 5) * 50
        volume = 1000 + i * 10
        
        short_term_klines.append([
            timestamp, open_price, high_price, low_price, close_price, volume
        ])
    
    # 生成30条日K线数据
    for i in range(30):
        timestamp = base_timestamp + i * 86400000  # 每天一条
        open_price = base_price + (i % 15 - 7) * 500
        high_price = open_price + 1000
        low_price = open_price - 1000
        close_price = open_price + (i % 7 - 3) * 200
        volume = 50000 + i * 1000
        
        daily_klines.append([
            timestamp, open_price, high_price, low_price, close_price, volume
        ])
    
    return {
        "messages": [
            {"role": "user", "content": "请分析BTC的技术指标，给出市场情绪判断和多空预测"}
        ],
        "cryptoData": {
            "ticker": {
                "symbol": "BTCUSDT",
                "lastPrice": "95000",
                "priceChangePercent": "2.5",
                "volume": "50000",
                "timestamp": str(int(time.time() * 1000))
            },
            "shortTermKlines": short_term_klines,
            "dailyKlines": daily_klines
        }
    }

if __name__ == "__main__":
    test_data = generate_test_data()
    
    print("生成的测试数据:")
    print(f"短期K线数据: {len(test_data['cryptoData']['shortTermKlines'])} 条")
    print(f"日K线数据: {len(test_data['cryptoData']['dailyKlines'])} 条")
    
    # 保存到文件
    with open('test_request.json', 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    print("测试数据已保存到 test_request.json")
    print("\n使用方法:")
    print("curl -X POST http://localhost:8000/api/ai/chat/ \\")
    print("  -H \"Content-Type: application/json\" \\")
    print("  -d @test_request.json \\")
    print("  --connect-timeout 30 --max-time 120")