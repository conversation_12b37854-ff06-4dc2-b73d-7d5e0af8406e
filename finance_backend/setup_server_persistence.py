#!/usr/bin/env python3
"""
服务器端数据持久化设置脚本
运行此脚本设置数据库表和初始配置
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

def setup_server_persistence():
    """设置服务器端数据持久化"""
    
    print("🚀 开始设置服务器端数据持久化...")
    
    # 设置Django环境
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'finance_project.settings')
    django.setup()
    
    print("1. 🗃️ 创建数据库迁移文件...")
    try:
        execute_from_command_line(['manage.py', 'makemigrations', 'crypto_api'])
        print("   ✅ 迁移文件创建成功")
    except Exception as e:
        print(f"   ❌ 迁移文件创建失败: {e}")
        return False
    
    print("2. 🚀 执行数据库迁移...")
    try:
        execute_from_command_line(['manage.py', 'migrate'])
        print("   ✅ 数据库迁移完成")
    except Exception as e:
        print(f"   ❌ 数据库迁移失败: {e}")
        return False
    
    print("3. 📊 验证数据表创建...")
    try:
        from crypto_api.models import RealTimeKlineData, DataSyncStatus
        
        # 测试表是否可用
        kline_count = RealTimeKlineData.objects.count()
        sync_count = DataSyncStatus.objects.count()
        
        print(f"   ✅ RealTimeKlineData 表: {kline_count} 条记录")
        print(f"   ✅ DataSyncStatus 表: {sync_count} 条记录")
        
    except Exception as e:
        print(f"   ❌ 数据表验证失败: {e}")
        return False
    
    print("\n🎉 服务器端数据持久化设置完成!")
    print("\n📋 后续步骤:")
    print("1. 启动Django服务器: python manage.py runserver 0.0.0.0:8000")
    print("2. 在前端控制台运行: debugCrypto.getServerStatus()")
    print("3. 开始实时数据收集: debugCrypto.startCollection('BTCUSDT')")
    print("4. 查看5分钟同步效果: debugCrypto.forceUploadToServer()")
    
    print("\n💡 新特性说明:")
    print("- 🚀 服务器端数据持久化 (数据库存储)")
    print("- ⏰ 5分钟自动上传和渲染更新周期")
    print("- 🔄 断开重连时从服务器恢复数据")
    print("- 📊 实时数据统计和监控")
    print("- 🛡️ 数据去重和完整性保证")
    
    return True

if __name__ == '__main__':
    if setup_server_persistence():
        sys.exit(0)
    else:
        sys.exit(1)