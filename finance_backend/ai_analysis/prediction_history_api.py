#!/usr/bin/env python3
"""
历史预测查询API - 为前端提供预测历史数据
"""
import json
import logging
from datetime import datetime, timedelta
from django.http import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.core.paginator import Paginator
from django.db.models import Q, Count, Avg, Max, Min
from django.utils import timezone
from django.views.decorators.http import require_http_methods

from .models import PredictionRecord, AutoPredictionTask

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name='dispatch')
class PredictionHistoryAPIView(View):
    """历史预测查询API"""
    
    def options(self, request, *args, **kwargs):
        """处理CORS预检请求"""
        response = JsonResponse({})
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"
        response["Access-Control-Max-Age"] = "86400"
        return response
    
    def get(self, request, *args, **kwargs):
        """获取历史预测记录"""
        try:
            # 解析查询参数
            symbol = request.GET.get('symbol', '').upper()
            page = int(request.GET.get('page', 1))
            page_size = min(int(request.GET.get('page_size', 20)), 100)  # 限制最大100条
            
            # 时间范围筛选
            hours = request.GET.get('hours')  # 最近N小时
            days = request.GET.get('days')    # 最近N天
            start_date = request.GET.get('start_date')  # 开始日期 YYYY-MM-DD
            end_date = request.GET.get('end_date')      # 结束日期 YYYY-MM-DD
            
            # 状态筛选
            status = request.GET.get('status')  # completed, failed, processing
            
            # 构建查询
            queryset = PredictionRecord.objects.all()
            
            # 币种筛选
            if symbol:
                queryset = queryset.filter(symbol=symbol)
            
            # 时间范围筛选
            if hours:
                start_time = timezone.now() - timedelta(hours=int(hours))
                queryset = queryset.filter(created_at__gte=start_time)
            elif days:
                start_time = timezone.now() - timedelta(days=int(days))
                queryset = queryset.filter(created_at__gte=start_time)
            elif start_date or end_date:
                if start_date:
                    start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
                    queryset = queryset.filter(prediction_datetime__gte=start_datetime)
                if end_date:
                    end_datetime = datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
                    queryset = queryset.filter(prediction_datetime__lte=end_datetime)
            
            # 状态筛选
            if status:
                queryset = queryset.filter(prediction_status=status)
            
            # 按时间倒序排列
            queryset = queryset.order_by('-created_at')
            
            # 分页
            paginator = Paginator(queryset, page_size)
            page_obj = paginator.get_page(page)
            
            # 构建响应数据
            predictions = []
            for record in page_obj:
                predictions.append(record.to_api_dict())
            
            # 统计信息
            total_count = queryset.count()
            completed_count = queryset.filter(prediction_status='completed').count()
            failed_count = queryset.filter(prediction_status='failed').count()
            
            return JsonResponse({
                'success': True,
                'data': {
                    'predictions': predictions,
                    'pagination': {
                        'page': page,
                        'page_size': page_size,
                        'total_pages': paginator.num_pages,
                        'total_count': total_count,
                        'has_next': page_obj.has_next(),
                        'has_previous': page_obj.has_previous(),
                    },
                    'statistics': {
                        'total_count': total_count,
                        'completed_count': completed_count,
                        'failed_count': failed_count,
                        'success_rate': round((completed_count / total_count * 100), 1) if total_count > 0 else 0
                    }
                },
                'timestamp': timezone.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"获取历史预测失败: {e}", exc_info=True)
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class PredictionStatsAPIView(View):
    """预测统计分析API"""
    
    def options(self, request, *args, **kwargs):
        """处理CORS预检请求"""
        response = JsonResponse({})
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"
        response["Access-Control-Max-Age"] = "86400"
        return response
    
    def get(self, request, *args, **kwargs):
        """获取预测统计数据"""
        try:
            symbol = request.GET.get('symbol', '').upper()
            days = int(request.GET.get('days', 7))  # 默认最近7天
            
            # 时间范围
            end_time = timezone.now()
            start_time = end_time - timedelta(days=days)
            
            # 基础查询
            base_query = PredictionRecord.objects.filter(
                created_at__gte=start_time,
                created_at__lte=end_time
            )
            
            if symbol:
                base_query = base_query.filter(symbol=symbol)
            
            # 总体统计
            total_predictions = base_query.count()
            completed_predictions = base_query.filter(prediction_status='completed').count()
            failed_predictions = base_query.filter(prediction_status='failed').count()
            
            # 按币种统计
            symbol_stats = base_query.values('symbol').annotate(
                count=Count('id'),
                completed=Count('id', filter=Q(prediction_status='completed')),
                failed=Count('id', filter=Q(prediction_status='failed')),
                avg_processing_time=Avg('processing_time_seconds'),
                avg_sentiment_score=Avg('prediction_distribution__看涨', default=0)
            ).order_by('-count')
            
            # 按时间统计 (按小时分组)
            time_stats = []
            for i in range(days * 24):  # 每小时一个数据点
                hour_start = start_time + timedelta(hours=i)
                hour_end = hour_start + timedelta(hours=1)
                
                hour_count = base_query.filter(
                    created_at__gte=hour_start,
                    created_at__lt=hour_end
                ).count()
                
                if hour_count > 0:  # 只包含有数据的小时
                    time_stats.append({
                        'timestamp': int(hour_start.timestamp() * 1000),
                        'datetime': hour_start.isoformat(),
                        'count': hour_count
                    })
            
            # 情绪趋势分析
            sentiment_trend = []
            completed_records = base_query.filter(prediction_status='completed').order_by('created_at')
            
            for record in completed_records[-20:]:  # 最近20个预测的情绪趋势
                sentiment_trend.append({
                    'timestamp': int(record.created_at.timestamp() * 1000),
                    'symbol': record.symbol,
                    'sentiment_score': record.sentiment_score,
                    'is_bullish': record.is_bullish
                })
            
            # 热门币种（最近预测最多的）
            popular_symbols = list(base_query.values('symbol').annotate(
                count=Count('id')
            ).order_by('-count')[:10])
            
            return JsonResponse({
                'success': True,
                'data': {
                    'overview': {
                        'total_predictions': total_predictions,
                        'completed_predictions': completed_predictions,
                        'failed_predictions': failed_predictions,
                        'success_rate': round((completed_predictions / total_predictions * 100), 1) if total_predictions > 0 else 0,
                        'time_range_days': days
                    },
                    'symbol_stats': list(symbol_stats),
                    'time_stats': time_stats,
                    'sentiment_trend': sentiment_trend,
                    'popular_symbols': popular_symbols
                },
                'timestamp': timezone.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"获取预测统计失败: {e}", exc_info=True)
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class AutoTaskStatusAPIView(View):
    """自动任务状态API"""
    
    def options(self, request, *args, **kwargs):
        """处理CORS预检请求"""
        response = JsonResponse({})
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"
        response["Access-Control-Max-Age"] = "86400"
        return response
    
    def get(self, request, *args, **kwargs):
        """获取自动任务状态"""
        try:
            from .auto_prediction_manager import AutoPredictionManager
            manager = AutoPredictionManager()
            
            status = manager.get_task_status()
            
            return JsonResponse({
                'success': True,
                'data': status,
                'timestamp': timezone.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"获取任务状态失败: {e}", exc_info=True)
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=500)
    
    def post(self, request, *args, **kwargs):
        """手动触发预测任务"""
        try:
            data = json.loads(request.body) if request.body else {}
            force = data.get('force', False)
            
            from .auto_prediction_manager import AutoPredictionManager
            manager = AutoPredictionManager()
            
            result = manager.run_auto_predictions(force_all=force)
            
            return JsonResponse({
                'success': True,
                'data': result,
                'timestamp': timezone.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"手动触发任务失败: {e}", exc_info=True)
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=500)

from django.db.models import Count, Avg, Q

@csrf_exempt
@require_http_methods(["GET"])
def get_prediction_history(request):
    """获取AI预测历史记录"""
    try:
        symbol = request.GET.get('symbol', None)
        limit = int(request.GET.get('limit', 5))
        
        query = {}
        if symbol:
            query['symbol'] = symbol
        
        predictions = PredictionRecord.objects.filter(**query).order_by('-created_at')[:limit]
        
        results = []
        for prediction in predictions:
            result = {
                'id': prediction.id,
                'symbol': prediction.symbol,
                'prediction_datetime': prediction.prediction_datetime.isoformat(),
                'analysis_summary': prediction.analysis_summary,
                'prediction_distribution': prediction.prediction_distribution,
                'market_sentiment': prediction.market_sentiment,
                'confidence_level': prediction.confidence_level,
                'data_points_count': prediction.data_points_count,
                'historical_years_count': prediction.historical_years_count
            }
            results.append(result)
        
        return JsonResponse(results, safe=False)
    
    except Exception as e:
        logger.error(f"获取预测历史失败: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@csrf_exempt
@require_http_methods(["GET"])
def get_latest_prediction(request):
    """获取指定币种的最新预测"""
    try:
        symbol = request.GET.get('symbol')
        
        if not symbol:
            return JsonResponse({'success': False, 'error': '缺少必要参数: symbol'}, status=400)
        
        try:
            prediction = PredictionRecord.objects.filter(symbol=symbol).order_by('-created_at')[0]
            
            result = {
                'id': prediction.id,
                'symbol': prediction.symbol,
                'prediction_datetime': prediction.prediction_datetime.isoformat(),
                'analysis_summary': prediction.analysis_summary,
                'prediction_distribution': prediction.prediction_distribution,
                'market_sentiment': prediction.market_sentiment,
                'confidence_level': prediction.confidence_level,
                'data_points_count': prediction.data_points_count,
                'historical_years_count': prediction.historical_years_count
            }
            
            return JsonResponse(result)
            
        except IndexError:
            return JsonResponse({'success': False, 'error': f'未找到{symbol}的预测记录'}, status=404)
    
    except Exception as e:
        logger.error(f"获取最新预测失败: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)}, status=500)