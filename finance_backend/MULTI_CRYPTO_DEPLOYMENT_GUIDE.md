# 多货币多周期数据收集器部署指南

## 系统概述

多货币多周期实时数据收集器支持同时收集多个交易对的多个时间周期数据，包括：

### 支持的交易对
- **主流货币**: BTCUSDT, ETHUSDT, SOLUSDT
- **热门货币**: DOGEUSDT, ADAUSDT, DOTUSDT
- **传统货币**: LINKUSDT, LTCUSDT, XRPUSDT

### 支持的时间周期
- **1m**: 1分钟 (基础数据，WebSocket实时收集)
- **15m**: 15分钟 (基于1分钟数据聚合)
- **1h**: 1小时 (基于1分钟数据聚合)
- **4h**: 4小时 (基于1分钟数据聚合)
- **1d**: 1天 (基于1分钟数据聚合)

## 本地部署

### 1. 环境准备
```bash

# 进入后端目录
cd finance_backend
```

### 2. 启动单货币收集器（传统方式）
```bash
# 启动BTCUSDT 1分钟数据收集
python manage.py collect_realtime_data --symbol=BTCUSDT --debug

# 后台运行
python manage.py collect_realtime_data --symbol=BTCUSDT --daemon
```

### 3. 启动多货币收集器（推荐）
```bash
# 使用启动脚本
./start_multi_collector.sh

# 或者手动启动
python manage.py collect_multi_data \
    --symbols="BTCUSDT,ETHUSDT,SOLUSDT" \
    --timeframes="1m,15m,1h,4h,1d" \
    --interval=300 \
    --debug
```

### 4. 查看运行状态
```bash
# 查看多货币收集器日志
tail -f logs/multi_collector.log

# 查看单货币收集器日志
tail -f logs/collector.log

# 检查进程
ps aux | grep collect
```


### 3. 网络测试
```bash
# 测试服务器网络连接
python3 test_server_network.py

# 预期输出
🌐 测试Binance API连接...
✅ API镜像 1: 连接成功 (0.25s)
🗂️ 测试COS访问...
✅ COS访问正常 (0.18s)
📊 测试K线数据获取...
✅ K线数据获取成功 (0.31s)
🎉 所有测试通过！服务器网络环境良好
```

### 4. 启动服务
```bash
# 加载环境变量并启动
source server_env_template.sh
./start_multi_collector.sh

# 或使用systemd服务
sudo cp realtime-collector.service /etc/systemd/system/
sudo systemctl enable realtime-collector
sudo systemctl start realtime-collector
```

## 数据存储结构

### COS存储路径格式
```
crypto-kline-data-v2/20250726/
├── BTCUSDT/
│   ├── 1m/daily/BTCUSDT_1m_07-29_2025_compressed.json
│   ├── 15m/daily/BTCUSDT_15m_07-29_2025_compressed.json
│   ├── 1h/daily/BTCUSDT_1h_07-29_2025_compressed.json
│   ├── 4h/daily/BTCUSDT_4h_07-29_2025_compressed.json
│   └── 1d/daily/BTCUSDT_1d_07-29_2025_compressed.json
├── ETHUSDT/
│   ├── 1m/daily/ETHUSDT_1m_07-29_2025_compressed.json
│   └── ...
└── SOLUSDT/
    ├── 1m/daily/SOLUSDT_1m_07-29_2025_compressed.json
    └── ...
```

### JSON数据格式
```json
{
  "metadata": {
    "symbol": "BTCUSDT",
    "timeframe": "1h", 
    "date": "2025-07-29",
    "total_records": 24,
    "upload_time": "2025-07-29T19:15:00",
    "data_source": "realtime_websocket"
  },
  "data": [
    [1722240000000, 67890.50, 68100.20, 67800.30, 68050.80, 123.45],
    [1722243600000, 68050.80, 68200.10, 67950.40, 68150.60, 145.67]
  ]
}
```

## 前端集成

### 1. 多货币数据管理器
```javascript
import multiCryptoDataManager from './services/MultiCryptoDataManager.js'

// 加载单个货币数据
const btcData = await multiCryptoDataManager.loadCryptoData('BTCUSDT', '1h', '2025')

// 批量加载多个货币
const { successData, failedSymbols } = await multiCryptoDataManager.loadMultipleSymbols(
  ['BTCUSDT', 'ETHUSDT', 'SOLUSDT'], 
  '1m', 
  '2025'
)

// 加载所有时间周期
const allTimeframes = await multiCryptoDataManager.loadAllTimeframes('BTCUSDT', '2025')
```

### 2. 全局调试接口
```javascript
// 浏览器控制台中使用
window.multiCryptoManager.load('ETHUSDT', '1h', '2025')
window.multiCryptoManager.loadMultiple(['BTCUSDT', 'ETHUSDT'], '1m', '2025')
window.multiCryptoManager.status()
window.multiCryptoManager.clearCache()
```

## 监控和维护

### 1. 日志监控
```bash
# 实时查看日志
tail -f logs/multi_collector.log

# 搜索错误
grep "ERROR\|❌" logs/multi_collector.log

# 统计上传成功率
grep "✅.*上传成功" logs/multi_collector.log | wc -l
```

### 2. 数据质量检查
```bash
# 检查今日数据文件
ls -la /path/to/cos/data/*/1m/daily/*$(date +%m-%d)*

# 验证JSON格式
python -m json.tool data_file.json

# 统计数据条数
jq '.metadata.total_records' data_file.json
```

### 3. 性能监控
```bash
# 查看内存使用
ps aux | grep collect | awk '{print $4, $11}'

# 查看网络连接
netstat -an | grep :9443

# 查看磁盘使用
df -h
```

## 故障排除

### 1. WebSocket连接失败
```bash
# 错误: HTTP 451
# 原因: 网络限制
# 解决: 部署到有外网访问的服务器

# 错误: Connection timeout
# 原因: 网络不稳定
# 解决: 使用多节点重试机制（已内置）
```

### 2. COS上传失败
```bash
# 检查COS配置
echo $AWS_ACCESS_KEY_ID
echo $AWS_SECRET_ACCESS_KEY
echo $AWS_STORAGE_BUCKET_NAME

# 测试COS连接
python -c "from crypto_api.services.cos_service import COSRealtimeService; cos = COSRealtimeService(); print('COS连接正常')"
```

### 3. 数据缺口问题
```bash
# 手动触发缺口修复
python manage.py shell
>>> from crypto_api.services.gap_recovery import GapRecoveryService
>>> gap_service = GapRecoveryService(debug=True)
>>> gap_service.fill_gap('BTCUSDT', start_time, end_time)
```

## 扩展配置

### 1. 添加新的交易对
```python
# 在 start_multi_collector.sh 中修改
SYMBOLS="BTCUSDT,ETHUSDT,SOLUSDT,NEWCOIN"
```

### 2. 调整上传频率
```python
# 在启动脚本中修改
UPLOAD_INTERVAL=600  # 改为10分钟
```

### 3. 启用/禁用调试模式
```bash
# 启用调试
DEBUG_MODE="--debug"

# 禁用调试（生产环境推荐）
DEBUG_MODE=""
```

## 系统要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **磁盘**: 50GB以上（用于日志和临时数据）
- **网络**: 稳定的外网连接

### 软件要求
- **Python**: 3.8+
- **Django**: 4.2.7
- **Conda**: 用于环境管理
- **WebSocket**: 支持异步连接

## 性能优化建议

1. **批量上传**: 使用5分钟间隔批量上传，减少COS请求频率
2. **数据压缩**: JSON数据自动压缩，减少存储空间
3. **缓存策略**: 合理使用内存缓存，避免重复计算
4. **错误重试**: 内置多节点重试机制，提高稳定性
5. **资源监控**: 定期检查内存和磁盘使用情况