import React, { useState } from 'react';
import { Form, Input, Button, message } from 'antd';
import { UserOutlined, LockOutlined, SafetyOutlined } from '@ant-design/icons';
import { userService } from '../../lib/supabase';
import './Auth.css';

const Register = ({ onSwitchToLogin, onRegisterSuccess, inviteCode }) => {
  const [loading, setLoading] = useState(false);
  const [smsLoading, setSmsLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [otpSent, setOtpSent] = useState(false);
  const [form] = Form.useForm();

  // 发送短信验证码
  const handleSendSMS = async () => {
    try {
      const phone = form.getFieldValue('phone');
      if (!phone) {
        message.error('请先输入手机号码');
        return;
      }
      if (!/^1[3-9]\d{9}$/.test(phone)) {
        message.error('请输入正确的手机号码');
        return;
      }

      setSmsLoading(true);

      // 使用 SMS 发送短信验证码
      const { data, error } = await userService.sendSignUpOTP(phone);

      if (error) {
        throw error;
      }

      // 检查是否是开发模式，显示验证码
      if (data && data.message && data.message.includes('开发模式')) {
        const codeMatch = data.message.match(/(\d{6})/);
        if (codeMatch) {
          message.success(`验证码已发送：${codeMatch[1]}（开发模式）`);
        } else {
          message.success('验证码已发送，请查收短信');
        }
      } else {
        message.success('验证码已发送，请查收短信');
      }
      setOtpSent(true);

      // 开始倒计时
      setCountdown(60);
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

    } catch (error) {
      console.error('发送验证码失败:', error);
      
      // 处理不同类型的错误
      let errorMessage = '发送验证码失败，请稍后重试';
      
      if (typeof error === 'string') {
        errorMessage = error;
        if (error.includes('rate limit') || error.includes('频繁')) {
          message.error('发送过于频繁，请稍后再试');
          return;
        }
      } else if (error && error.message) {
        errorMessage = error.message;
        if (error.message.includes('rate limit') || error.message.includes('频繁')) {
          message.error('发送过于频繁，请稍后再试');
          return;
        }
      }
      
      message.error(errorMessage);
    } finally {
      setSmsLoading(false);
    }
  };

  const handleRegister = async (values) => {
    setLoading(true);
    try {
      const { phone, smsCode, password } = values;

      // 生成默认用户名
      const username = `用户${Date.now()}`;

      if (otpSent) {
        // 使用短信验证码注册 - 调用新的SMS服务
        const { smsAuthService } = await import('../../lib/smsAuth');
        const result = await smsAuthService.registerWithSMS(phone, smsCode, password, username, inviteCode);

        if (result.error) {
          throw result.error;
        }

        if (result.data && result.data.user) {
          message.success('注册成功！正在自动登录...');
          // 如果有邀请码，显示额外提示
          if (inviteCode) {
            message.success(`🎉 通过邀请码 ${inviteCode} 注册成功，您和邀请人都将获得充值奖励！`);
          }
          // 注册成功后自动登录
          onRegisterSuccess && onRegisterSuccess(result.data.user);
        }
      } else {
        // 提示用户需要先获取验证码
        message.error('请先获取短信验证码');
        return;
      }
    } catch (error) {
      console.error('注册失败:', error);

      // 处理不同类型的错误
      let errorMessage = '注册失败，请稍后重试';
      
      if (typeof error === 'string') {
        errorMessage = error;
      } else if (error && error.message) {
        errorMessage = error.message;
      }
      
      // 根据错误信息显示不同的提示
      if (errorMessage.includes('已注册') || errorMessage.includes('User already registered')) {
        message.error('该手机号已注册，请直接登录');
      } else if (errorMessage.includes('验证码错误') || errorMessage.includes('Invalid token') || errorMessage.includes('已过期')) {
        message.error('验证码错误或已过期，请重新获取');
      } else if (errorMessage.includes('密码') || errorMessage.includes('Password')) {
        message.error('密码格式不正确，请重新设置');
      } else {
        message.error(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="auth-container">
      {/* 背景装饰元素 */}
      <div className="auth-decorations">
        <img src="/element1.png" alt="" className="decoration decoration-1" />
        <img src="/element2.png" alt="" className="decoration decoration-2" />
        <img src="/element3.png" alt="" className="decoration decoration-3" />
        <img src="/element4.png" alt="" className="decoration decoration-4" />
        <img src="/element5.png" alt="" className="decoration decoration-5" />
        <img src="/F.svg" alt="" className="decoration decoration-f" />
      </div>

      {/* 注册表单 */}
      <div className="auth-form-container">
        <div className="auth-form">
          {/* Logo 和标题 */}
          <div className="auth-header">
            <div className="auth-logo">
              <img src="/logo_textless.png" alt="旗鱼AI" className="auth-logo-image" />
              <span className="auth-logo-text">旗鱼AI</span>
            </div>
            <p className="auth-subtitle">智能加密货币行情分析平台</p>
            
            {/* 邀请码提示 */}
            {inviteCode && (
              <div style={{ 
                background: 'rgba(24, 144, 255, 0.1)', 
                border: '1px solid rgba(24, 144, 255, 0.3)',
                borderRadius: '6px',
                padding: '8px 12px',
                marginTop: '12px',
                textAlign: 'center'
              }}>
                <span style={{ 
                  color: '#1890ff', 
                  fontSize: '14px',
                  fontWeight: '500'
                }}>
                  🎁 通过邀请码 <strong>{inviteCode}</strong> 注册，您和好友都将获得充值奖励
                </span>
              </div>
            )}
          </div>

          {/* 注册表单 */}
          <Form
            form={form}
            name="register"
            onFinish={handleRegister}
            autoComplete="off"
            layout="vertical"
            className="auth-form-content"
          >
            <Form.Item
              label="手机号码"
              name="phone"
              rules={[
                { required: true, message: '请输入手机号码' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="请输入手机号码"
                size="large"
                className="auth-input"
              />
            </Form.Item>

            <Form.Item
              label="短信验证码"
              name="smsCode"
              rules={[
                { required: true, message: '请输入验证码' },
                { len: 6, message: '验证码为6位数字' }
              ]}
            >
              <div className="sms-input-group">
                <Input
                  prefix={<SafetyOutlined />}
                  placeholder="请输入验证码"
                  size="large"
                  className="auth-input sms-input"
                  maxLength={6}
                />
                <Button
                  type="default"
                  size="large"
                  loading={smsLoading}
                  disabled={countdown > 0}
                  onClick={handleSendSMS}
                  className="sms-btn"
                >
                  {countdown > 0 ? `${countdown}s` : '获取验证码'}
                </Button>
              </div>
            </Form.Item>

            <Form.Item
              label="设置密码"
              name="password"
              rules={[
                { required: true, message: '请设置密码' },
                { min: 6, message: '密码至少6位' }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="请设置密码"
                size="large"
                className="auth-input"
              />
            </Form.Item>

            <Form.Item
              label="确认密码"
              name="confirmPassword"
              dependencies={['password']}
              rules={[
                { required: true, message: '请确认密码' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'));
                  },
                }),
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="请再次输入密码"
                size="large"
                className="auth-input"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                size="large"
                className="auth-submit-btn"
                block
              >
                注册
              </Button>
            </Form.Item>
          </Form>

          {/* 切换到登录 */}
          <div className="auth-switch">
            <span>已有账号？</span>
            <button 
              type="button" 
              className="auth-switch-link"
              onClick={onSwitchToLogin}
            >
              立即登录
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Register;
