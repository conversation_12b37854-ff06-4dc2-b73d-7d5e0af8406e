-- 创建SMS认证系统的数据库表
-- 请在Django项目中运行: python manage.py dbshell 然后执行此SQL

-- Web用户表
CREATE TABLE IF NOT EXISTS web_user (
    id CHAR(36) PRIMARY KEY,
    phone VARCHAR(20) UNIQUE NOT NULL,
    password VARCHAR(128),
    nickname VARCHAR(100) NOT NULL DEFAULT '',
    avatar VARCHAR(500),
    email VARCHAR(254),
    credits INTEGER NOT NULL DEFAULT 3,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    phone_verified BOOLEAN NOT NULL DEFAULT 0,
    last_login DATETIME,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);

-- 短信记录表
CREATE TABLE IF NOT EXISTS sms_record (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    phone VARCHAR(20) NOT NULL,
    code VARCHAR(10) NOT NULL,
    purpose VARCHAR(50) NOT NULL,
    is_used BOOLEAN NOT NULL DEFAULT 0,
    sent_at DATETIME NOT NULL,
    used_at DATETIME,
    expires_at DATETIME NOT NULL
);

-- 积分交易表
CREATE TABLE IF NOT EXISTS credit_transaction (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id CHAR(36) NOT NULL,
    type VARCHAR(20) NOT NULL,
    amount INTEGER NOT NULL,
    balance_before INTEGER NOT NULL,
    balance_after INTEGER NOT NULL,
    description VARCHAR(200),
    created_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES web_user (id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_web_user_phone ON web_user(phone);
CREATE INDEX IF NOT EXISTS idx_sms_record_phone ON sms_record(phone);
CREATE INDEX IF NOT EXISTS idx_sms_record_code ON sms_record(code);
CREATE INDEX IF NOT EXISTS idx_credit_transaction_user ON credit_transaction(user_id);
CREATE INDEX IF NOT EXISTS idx_credit_transaction_created ON credit_transaction(created_at);