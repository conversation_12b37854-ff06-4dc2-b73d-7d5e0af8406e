from django.urls import path
from .views import CryptoDataView, AIAnalysisView
from .binance_proxy import HistoricalDataView, repair_data_gap
from .realtime_views import (
    RealTimeDataUploadView, 
    RealTimeDataQueryView,
    DataSyncStatusView,
    MergedDataView
)

urlpatterns = [
    # 获取加密货币数据
    path('data/<str:symbol>/', CryptoDataView.as_view(), name='crypto_data'),
    # AI分析
    path('analysis/', AIAnalysisView.as_view(), name='ai_analysis'),
    # 🎯 Binance代理接口
    path('binance/historical/', HistoricalDataView.as_view(), name='binance_historical'),
    path('binance/repair-gap/', repair_data_gap, name='repair_data_gap'),
    
    # 🚀 新增：服务器端实时数据持久化接口
    path('realtime/upload/', RealTimeDataUploadView.as_view(), name='realtime_upload'),
    path('realtime/query/', RealTimeDataQueryView.as_view(), name='realtime_query'),
    path('realtime/status/', DataSyncStatusView.as_view(), name='sync_status'),
    path('realtime/merged/', MergedDataView.as_view(), name='merged_data'),
] 