"""
数据缺口恢复服务
负责检测和填补数据缺口，确保数据连续性
"""

import json
import logging
import requests
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
from .cos_service import COSRealtimeService

logger = logging.getLogger(__name__)

class GapRecoveryService:
    """数据缺口恢复服务"""
    
    def __init__(self, debug=False):
        self.debug = debug
        self.cos_service = COSRealtimeService(debug=debug)
        
        # Binance API配置 - 使用多个镜像提高连接成功率
        self.binance_endpoints = [
            'https://api.binance.com/api/v3/klines',
            'https://api1.binance.com/api/v3/klines', 
            'https://api2.binance.com/api/v3/klines',
            'https://api3.binance.com/api/v3/klines'
        ]
        
        # 请求限制配置
        self.max_klines_per_request = 1000  # Binance限制
        self.request_delay = 0.1  # 请求间隔（秒）
        
        logger.info("🔧 数据缺口恢复服务初始化完成")
    
    def fill_gap(self, symbol: str, start_time: datetime, end_time: datetime) -> bool:
        """
        填补数据缺口
        
        Args:
            symbol: 交易对符号
            start_time: 缺口开始时间
            end_time: 缺口结束时间
            
        Returns:
            bool: 修复成功返回True
        """
        try:
            logger.info(f"🔧 开始修复数据缺口: {symbol}")
            logger.info(f"⏰ 时间范围: {start_time} ~ {end_time}")
            
            # 计算缺口时长
            gap_duration = end_time - start_time
            gap_minutes = int(gap_duration.total_seconds() / 60)
            
            if gap_minutes <= 0:
                logger.info("✅ 无需修复，数据是连续的")
                return True
            
            logger.info(f"📊 缺口时长: {gap_minutes} 分钟")
            
            # 分批获取历史数据
            gap_data = self.fetch_gap_data(symbol, start_time, end_time)
            
            if not gap_data:
                logger.error("❌ 未获取到缺口数据")
                return False
            
            logger.info(f"📥 缺口数据获取成功: {len(gap_data)} 条K线记录")
            
            # 上传缺口数据到COS
            logger.info("📤 开始上传缺口数据到COS...")
            success = self.cos_service.upload_realtime_data(symbol, gap_data)
            
            if success:
                logger.info(f"✅ 缺口修复完成: {len(gap_data)}条数据已上传到COS")
                return True
            else:
                logger.error("❌ 缺口数据COS上传失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 数据缺口修复异常: {e}")
            return False
    
    def fetch_gap_data(self, symbol: str, start_time: datetime, end_time: datetime) -> List[Dict]:
        """获取缺口数据"""
        try:
            # 转换为毫秒时间戳
            start_ms = int(start_time.timestamp() * 1000)
            end_ms = int(end_time.timestamp() * 1000)
            
            all_klines = []
            current_start = start_ms
            
            while current_start < end_ms:
                # 计算当前批次的结束时间
                # 每批最多获取1000条数据，1分钟间隔 = 16.67小时
                batch_end = min(
                    current_start + (self.max_klines_per_request * 60 * 1000),
                    end_ms
                )
                
                logger.debug(f"📥 获取批次数据: {datetime.fromtimestamp(current_start/1000)} ~ {datetime.fromtimestamp(batch_end/1000)}")
                
                # 调用Binance API
                batch_data = self.fetch_binance_klines(
                    symbol=symbol,
                    interval='1m',
                    start_time=current_start,
                    end_time=batch_end
                )
                
                if batch_data:
                    all_klines.extend(batch_data)
                    logger.debug(f"✅ 批次数据: {len(batch_data)} 条")
                else:
                    logger.warning(f"⚠️ 批次数据为空")
                
                # 更新下一批次开始时间
                current_start = batch_end + 60000  # +1分钟避免重复
                
                # 请求间隔
                if current_start < end_ms:
                    import time
                    time.sleep(self.request_delay)
            
            # 转换数据格式
            formatted_data = self.format_kline_data(all_klines)
            
            logger.info(f"📊 缺口数据获取完成: {len(formatted_data)} 条记录")
            return formatted_data
            
        except Exception as e:
            logger.error(f"❌ 缺口数据获取失败: {e}")
            return []
    
    def fetch_binance_klines(self, symbol: str, interval: str, start_time: int, end_time: int) -> List[List]:
        """调用Binance API获取K线数据 - 多镜像重试机制"""
        params = {
            'symbol': symbol,
            'interval': interval,
            'startTime': start_time,
            'endTime': end_time,
            'limit': self.max_klines_per_request
        }
        
        logger.info(f"🌐 开始尝试Binance API请求...")
        logger.info(f"📊 请求参数: symbol={symbol}, interval={interval}")
        logger.info(f"⏰ 时间范围: {datetime.fromtimestamp(start_time/1000)} ~ {datetime.fromtimestamp(end_time/1000)}")
        
        # 🔄 尝试多个API镜像
        for i, endpoint in enumerate(self.binance_endpoints):
            try:
                logger.info(f"🔗 尝试API镜像 {i+1}/{len(self.binance_endpoints)}: {endpoint}")
                
                response = requests.get(
                    endpoint, 
                    params=params, 
                    timeout=15,  # 增加超时时间
                    headers={
                        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
                    }
                )
                response.raise_for_status()
                
                data = response.json()
                logger.debug(f"✅ API镜像 {i+1} 连接成功，获取到 {len(data)} 条数据")
                return data
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"⚠️ API镜像 {i+1} 连接失败: {e}")
                if i == len(self.binance_endpoints) - 1:  # 最后一个镜像也失败
                    logger.error(f"❌ 所有Binance API镜像均连接失败")
                continue
                
            except json.JSONDecodeError as e:
                logger.error(f"❌ JSON解析失败: {e}")
                continue
        
        return []
    
    def format_kline_data(self, raw_klines: List[List]) -> List[Dict]:
        """格式化K线数据"""
        formatted_data = []
        
        for kline in raw_klines:
            try:
                # Binance K线数据格式:
                # [
                #   1499040000000,      // 开盘时间
                #   "0.01634790",       // 开盘价
                #   "0.80000000",       // 最高价
                #   "0.01575800",       // 最低价
                #   "0.01577100",       // 收盘价
                #   "148976.11427815",  // 成交量
                #   1499644799999,      // 收盘时间
                #   "2434.19055334",    // 成交额
                #   308,                // 成交笔数
                #   "1756.87402397",    // 主动买入成交量
                #   "28.46694368",      // 主动买入成交额
                #   "17928899.62484339" // 忽略
                # ]
                
                formatted_data.append({
                    'timestamp': int(kline[0]),  # 开盘时间
                    'open': float(kline[1]),     # 开盘价
                    'high': float(kline[2]),     # 最高价
                    'low': float(kline[3]),      # 最低价
                    'close': float(kline[4]),    # 收盘价
                    'volume': float(kline[5]),   # 成交量
                    'turnover': float(kline[7])  # 成交额
                })
                
            except (IndexError, ValueError) as e:
                logger.error(f"❌ K线数据格式异常: {e}")
                continue
        
        return formatted_data
    
    def detect_gaps(self, symbol: str, days_back: int = 7) -> List[Tuple[datetime, datetime]]:
        """检测最近几天的数据缺口"""
        try:
            gaps = []
            
            # 检查最近几天的数据
            for day_offset in range(days_back):
                check_date = datetime.now() - timedelta(days=day_offset)
                date_str = check_date.strftime('%Y-%m-%d')
                
                # 生成该日期的COS路径
                cos_key = self.cos_service.generate_cos_path(symbol, date_str)
                
                # 检查文件是否存在
                if not self.cos_service.check_file_exists(cos_key):
                    # 计算该天的时间范围
                    day_start = check_date.replace(hour=0, minute=0, second=0, microsecond=0)
                    day_end = day_start + timedelta(days=1) - timedelta(microseconds=1)
                    
                    gaps.append((day_start, day_end))
                    logger.info(f"🔍 发现缺口: {date_str}")
            
            return gaps
            
        except Exception as e:
            logger.error(f"❌ 缺口检测失败: {e}")
            return []
    
    def get_recovery_status(self, symbol: str) -> Dict:
        """获取恢复状态"""
        try:
            # 检测缺口
            gaps = self.detect_gaps(symbol)
            
            # 获取最后上传时间
            last_upload = self.cos_service.get_last_upload_time(symbol)
            
            return {
                'symbol': symbol,
                'last_upload_time': last_upload.isoformat() if last_upload else None,
                'detected_gaps': len(gaps),
                'gaps_detail': [
                    {
                        'start': gap[0].isoformat(),
                        'end': gap[1].isoformat(),
                        'duration_hours': (gap[1] - gap[0]).total_seconds() / 3600
                    }
                    for gap in gaps
                ],
                'check_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ 获取恢复状态失败: {e}")
            return {
                'symbol': symbol,
                'error': str(e),
                'check_time': datetime.now().isoformat()
            }