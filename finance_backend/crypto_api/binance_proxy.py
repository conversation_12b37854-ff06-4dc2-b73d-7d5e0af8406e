"""
Binance API代理服务
解决前端CORS问题，提供历史数据抓取功能
"""

import requests
import time
import json
from datetime import datetime, timedelta
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View
import logging

logger = logging.getLogger(__name__)

class BinanceProxy:
    def __init__(self):
        self.base_url = "https://api.binance.com/api/v3"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        })
        
    def get_klines(self, symbol, interval, start_time=None, end_time=None, limit=1000):
        """
        获取K线数据
        """
        url = f"{self.base_url}/klines"
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }
        
        if start_time:
            params['startTime'] = int(start_time)
        if end_time:
            params['endTime'] = int(end_time)
            
        try:
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Binance API请求失败: {e}")
            raise

    def get_historical_data_batch(self, symbol, interval, start_time, end_time):
        """
        批量获取历史数据，自动处理分页
        """
        all_data = []
        current_start = start_time
        batch_size = 1000
        
        # 计算每批次的时间跨度
        interval_ms = self._get_interval_ms(interval)
        batch_duration = batch_size * interval_ms
        
        while current_start < end_time:
            current_end = min(current_start + batch_duration, end_time)
            
            try:
                batch_data = self.get_klines(
                    symbol=symbol,
                    interval=interval,
                    start_time=current_start,
                    end_time=current_end,
                    limit=batch_size
                )
                
                if not batch_data:
                    break
                    
                all_data.extend(batch_data)
                logger.info(f"获取数据批次: {datetime.fromtimestamp(current_start/1000)} ~ {datetime.fromtimestamp(current_end/1000)}, {len(batch_data)}条")
                
                # 更新起始时间为最后一条数据的时间+1
                if batch_data:
                    last_time = int(batch_data[-1][0])
                    current_start = last_time + interval_ms
                else:
                    current_start = current_end + 1
                
                # 避免频率限制
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"批次数据获取失败: {e}")
                # 跳过失败的批次，继续下一个
                current_start = current_end + 1
                time.sleep(1)  # 失败后等待更长时间
                
        return all_data
    
    def _get_interval_ms(self, interval):
        """获取时间间隔的毫秒数"""
        interval_map = {
            '1m': 60 * 1000,
            '15m': 15 * 60 * 1000,
            '1h': 60 * 60 * 1000,
            '4h': 4 * 60 * 60 * 1000,
            '1d': 24 * 60 * 60 * 1000
        }
        return interval_map.get(interval, 60 * 1000)

# 全局实例
binance_proxy = BinanceProxy()

@method_decorator(csrf_exempt, name='dispatch')
class HistoricalDataView(View):
    """
    历史数据代理接口
    """
    
    def post(self, request):
        try:
            data = json.loads(request.body)
            symbol = data.get('symbol', 'BTCUSDT')
            interval = data.get('interval', '1m')
            start_time = data.get('start_time')  # 毫秒时间戳
            end_time = data.get('end_time')      # 毫秒时间戳
            
            if not start_time or not end_time:
                return JsonResponse({
                    'success': False,
                    'error': '缺少时间范围参数'
                }, status=400)
            
            logger.info(f"请求历史数据: {symbol} {interval} {datetime.fromtimestamp(start_time/1000)} ~ {datetime.fromtimestamp(end_time/1000)}")
            
            # 获取历史数据
            klines_data = binance_proxy.get_historical_data_batch(
                symbol=symbol,
                interval=interval,
                start_time=start_time,
                end_time=end_time
            )
            
            return JsonResponse({
                'success': True,
                'data': klines_data,
                'count': len(klines_data),
                'symbol': symbol,
                'interval': interval,
                'time_range': {
                    'start': datetime.fromtimestamp(start_time/1000).isoformat(),
                    'end': datetime.fromtimestamp(end_time/1000).isoformat()
                }
            })
            
        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': '无效的JSON格式'
            }, status=400)
        except Exception as e:
            logger.error(f"历史数据获取失败: {e}")
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def repair_data_gap(request):
    """
    修复数据断层的专用接口
    """
    try:
        data = json.loads(request.body)
        symbol = data.get('symbol', 'BTCUSDT')
        
        # 7-28 8:00 到现在的时间范围
        july_28_8am = datetime(2025, 7, 28, 8, 0, 0)
        start_time = int(july_28_8am.timestamp() * 1000)
        end_time = int(datetime.now().timestamp() * 1000)
        
        logger.info(f"开始修复数据断层: {symbol} {july_28_8am} ~ {datetime.now()}")
        
        # 支持的时间周期
        intervals = ['1m', '15m', '1h', '4h', '1d']
        results = {}
        
        for interval in intervals:
            try:
                klines_data = binance_proxy.get_historical_data_batch(
                    symbol=symbol,
                    interval=interval,
                    start_time=start_time,
                    end_time=end_time
                )
                
                results[interval] = {
                    'success': True,
                    'count': len(klines_data),
                    'data': klines_data
                }
                
                logger.info(f"{interval} 数据断层修复完成: {len(klines_data)} 条记录")
                
            except Exception as e:
                logger.error(f"{interval} 数据断层修复失败: {e}")
                results[interval] = {
                    'success': False,
                    'error': str(e)
                }
        
        return JsonResponse({
            'success': True,
            'symbol': symbol,
            'time_range': {
                'start': july_28_8am.isoformat(),
                'end': datetime.now().isoformat()
            },
            'results': results
        })
        
    except Exception as e:
        logger.error(f"数据断层修复失败: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)