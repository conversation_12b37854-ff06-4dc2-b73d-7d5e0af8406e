"""
自动AI分析结果查询API
"""

import json
import logging
from datetime import datetime, timedelta
from django.http import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.core.cache import cache
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import AllowAny

logger = logging.getLogger(__name__)

@method_decorator(csrf_exempt, name='dispatch')
class AutoAnalysisAPIView(View):
    """自动AI分析结果查询API (Django原生View)"""
    
    def options(self, request, *args, **kwargs):
        """处理OPTIONS请求（CORS预检请求）"""
        response = JsonResponse({})
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"
        response["Access-Control-Max-Age"] = "86400"
        return response

    def get(self, request, *args, **kwargs):
        """获取自动AI分析结果"""
        try:
            # 获取请求参数
            symbol = request.GET.get('symbol', 'BTC')
            analysis_type = request.GET.get('analysis_type', 'quick')
            
            # 构建缓存键
            cache_key = f"auto_ai_analysis_{symbol}_{analysis_type}"
            
            # 从缓存获取结果
            cached_data = cache.get(cache_key)
            
            if cached_data:
                # 计算数据年龄
                cache_timestamp = datetime.fromisoformat(cached_data['timestamp'])
                age_minutes = (datetime.now() - cache_timestamp).total_seconds() / 60
                
                response_data = {
                    'code': 200,
                    'message': '获取成功',
                    'data': {
                        'symbol': cached_data['symbol'],
                        'analysis_type': cached_data['analysis_type'],
                        'result': cached_data['result'],
                        'timestamp': cached_data['timestamp'],
                        'age_minutes': round(age_minutes, 1),
                        'is_fresh': age_minutes < 15,  # 15分钟内算新鲜
                        'position_amount': cached_data.get('position_amount'),
                        'position_percentage': cached_data.get('position_percentage'),
                        'market_data_summary': cached_data.get('market_data_summary', {})
                    }
                }
                
                logger.info(f"返回缓存的AI分析结果: {symbol} {analysis_type} (年龄: {age_minutes:.1f}分钟)")
            else:
                response_data = {
                    'code': 404,
                    'message': '暂无分析结果',
                    'data': {
                        'symbol': symbol,
                        'analysis_type': analysis_type,
                        'available_cache_keys': self.get_available_cache_keys()
                    }
                }
                
                logger.info(f"未找到AI分析结果: {symbol} {analysis_type}")

            response = JsonResponse(response_data)
            response["Access-Control-Allow-Origin"] = "*"
            return response

        except Exception as e:
            logger.error(f"获取自动AI分析结果失败: {e}", exc_info=True)
            error_response = JsonResponse({
                'code': 500,
                'message': f'获取失败: {str(e)}',
                'data': None
            }, status=500)
            error_response["Access-Control-Allow-Origin"] = "*"
            return error_response

    def post(self, request, *args, **kwargs):
        """触发新的AI分析（可选功能）"""
        try:
            data = json.loads(request.body)
            symbol = data.get('symbol', 'BTC')
            analysis_type = data.get('analysis_type', 'quick')
            force = data.get('force', False)  # 是否强制重新分析
            
            # 检查是否需要重新分析
            cache_key = f"auto_ai_analysis_{symbol}_{analysis_type}"
            cached_data = cache.get(cache_key)
            
            if cached_data and not force:
                cache_timestamp = datetime.fromisoformat(cached_data['timestamp'])
                age_minutes = (datetime.now() - cache_timestamp).total_seconds() / 60
                
                if age_minutes < 15:  # 15分钟内不重复分析
                    response_data = {
                        'code': 200,
                        'message': '使用现有分析结果',
                        'data': {
                            'symbol': cached_data['symbol'],
                            'analysis_type': cached_data['analysis_type'],
                            'result': cached_data['result'],
                            'timestamp': cached_data['timestamp'],
                            'age_minutes': round(age_minutes, 1),
                            'is_fresh': True
                        }
                    }
                    
                    response = JsonResponse(response_data)
                    response["Access-Control-Allow-Origin"] = "*"
                    return response

            # 异步触发新的分析（这里可以使用Celery或其他异步任务队列）
            # 现在先返回一个提示消息
            response_data = {
                'code': 202,
                'message': '分析请求已提交，请稍后查询结果',
                'data': {
                    'symbol': symbol,
                    'analysis_type': analysis_type,
                    'estimated_completion': (datetime.now() + timedelta(minutes=5)).isoformat(),
                    'check_url': f'/api/ai/auto-analysis/?symbol={symbol}&analysis_type={analysis_type}'
                }
            }

            response = JsonResponse(response_data, status=202)
            response["Access-Control-Allow-Origin"] = "*"
            return response

        except Exception as e:
            logger.error(f"触发AI分析失败: {e}", exc_info=True)
            error_response = JsonResponse({
                'code': 500,
                'message': f'触发失败: {str(e)}',
                'data': None
            }, status=500)
            error_response["Access-Control-Allow-Origin"] = "*"
            return error_response

    def get_available_cache_keys(self):
        """获取可用的缓存键列表（用于调试）"""
        try:
            # Django缓存没有直接的keys()方法，这里返回一些常见的键
            common_symbols = ['BTC', 'ETH', 'BNB', 'SOL']
            common_types = ['quick', 'four_hour', 'one_day']
            
            available_keys = []
            for symbol in common_symbols:
                for analysis_type in common_types:
                    cache_key = f"auto_ai_analysis_{symbol}_{analysis_type}"
                    if cache.get(cache_key):
                        available_keys.append(cache_key)
            
            return available_keys
        except:
            return []


class AutoAnalysisAPIViewDRF(APIView):
    """自动AI分析结果查询API (DRF版本)"""
    permission_classes = [AllowAny]

    def get(self, request):
        """获取自动AI分析结果"""
        try:
            symbol = request.GET.get('symbol', 'BTC')
            analysis_type = request.GET.get('analysis_type', 'quick')
            
            cache_key = f"auto_ai_analysis_{symbol}_{analysis_type}"
            cached_data = cache.get(cache_key)
            
            if cached_data:
                cache_timestamp = datetime.fromisoformat(cached_data['timestamp'])
                age_minutes = (datetime.now() - cache_timestamp).total_seconds() / 60
                
                return Response({
                    'code': 200,
                    'message': '获取成功',
                    'data': {
                        'symbol': cached_data['symbol'],
                        'analysis_type': cached_data['analysis_type'],
                        'result': cached_data['result'],
                        'timestamp': cached_data['timestamp'],
                        'age_minutes': round(age_minutes, 1),
                        'is_fresh': age_minutes < 15,
                        'position_amount': cached_data.get('position_amount'),
                        'position_percentage': cached_data.get('position_percentage'),
                        'market_data_summary': cached_data.get('market_data_summary', {})
                    }
                })
            else:
                return Response({
                    'code': 404,
                    'message': '暂无分析结果',
                    'data': {
                        'symbol': symbol,
                        'analysis_type': analysis_type
                    }
                }, status=404)

        except Exception as e:
            logger.error(f"DRF获取自动AI分析结果失败: {e}")
            return Response({
                'code': 500,
                'message': f'获取失败: {str(e)}',
                'data': None
            }, status=500)

    def post(self, request):
        """触发新的AI分析"""
        try:
            symbol = request.data.get('symbol', 'BTC')
            analysis_type = request.data.get('analysis_type', 'quick')
            force = request.data.get('force', False)
            
            cache_key = f"auto_ai_analysis_{symbol}_{analysis_type}"
            cached_data = cache.get(cache_key)
            
            if cached_data and not force:
                cache_timestamp = datetime.fromisoformat(cached_data['timestamp'])
                age_minutes = (datetime.now() - cache_timestamp).total_seconds() / 60
                
                if age_minutes < 15:
                    return Response({
                        'code': 200,
                        'message': '使用现有分析结果',
                        'data': {
                            'symbol': cached_data['symbol'],
                            'analysis_type': cached_data['analysis_type'],
                            'result': cached_data['result'],
                            'timestamp': cached_data['timestamp'],
                            'age_minutes': round(age_minutes, 1),
                            'is_fresh': True
                        }
                    })

            return Response({
                'code': 202,
                'message': '分析请求已提交，请稍后查询结果',
                'data': {
                    'symbol': symbol,
                    'analysis_type': analysis_type,
                    'estimated_completion': (datetime.now() + timedelta(minutes=5)).isoformat()
                }
            }, status=202)

        except Exception as e:
            logger.error(f"DRF触发AI分析失败: {e}")
            return Response({
                'code': 500,
                'message': f'触发失败: {str(e)}',
                'data': None
            }, status=500)