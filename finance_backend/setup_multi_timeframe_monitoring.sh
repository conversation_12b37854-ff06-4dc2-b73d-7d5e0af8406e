#!/bin/bash

# 多时间周期数据监控设置脚本
# 配置定时任务监控和自动修复多时间周期数据

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "🔧 配置多时间周期数据监控系统..."

# 确保脚本有执行权限
chmod +x "$SCRIPT_DIR/repair_all_timeframes.sh"
chmod +x "$SCRIPT_DIR/check_multi_timeframe_integrity.py"

echo "✅ 脚本权限设置完成"

# 创建多时间周期监控脚本
cat > "$SCRIPT_DIR/multi_timeframe_monitor.sh" << 'EOF'
#!/bin/bash

# 多时间周期数据自动监控和修复脚本
# 检查各时间周期数据完整性，自动修复严重缺失

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

LOG_FILE="logs/multi_timeframe_monitor.log"
REPORT_DIR="logs/multi_timeframe_reports"

# 确保目录存在
mkdir -p "$REPORT_DIR"

# 写日志函数
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log_message "🔍 开始多时间周期数据监控..."

# 检查最近7天的数据完整性
START_DATE=$(date -d '7 days ago' '+%Y-%m-%d')
END_DATE=$(date '+%Y-%m-%d')
REPORT_FILE="$REPORT_DIR/monitor_$(date '+%Y%m%d_%H%M%S').txt"

log_message "📅 检查日期范围: $START_DATE ~ $END_DATE"

# 执行多时间周期完整性检查
python3 check_multi_timeframe_integrity.py \
    --start-date="$START_DATE" \
    --end-date="$END_DATE" \
    --output="$REPORT_FILE" >> "$LOG_FILE" 2>&1

EXIT_CODE=$?

if [ $EXIT_CODE -eq 0 ]; then
    log_message "✅ 多时间周期完整性检查完成"
else
    log_message "⚠️ 多时间周期完整性检查发现问题"
fi

# 分析检查结果
if [ -f "$REPORT_FILE" ]; then
    # 提取各时间周期覆盖率
    COVERAGE_1M=$(grep -E "1m.*:" "$REPORT_FILE" | grep -o '[0-9.]*%' | head -1 | sed 's/%//')
    COVERAGE_15M=$(grep -E "15m.*:" "$REPORT_FILE" | grep -o '[0-9.]*%' | head -1 | sed 's/%//')
    COVERAGE_1H=$(grep -E "1h.*:" "$REPORT_FILE" | grep -o '[0-9.]*%' | head -1 | sed 's/%//')
    COVERAGE_4H=$(grep -E "4h.*:" "$REPORT_FILE" | grep -o '[0-9.]*%' | head -1 | sed 's/%//')
    COVERAGE_1D=$(grep -E "1d.*:" "$REPORT_FILE" | grep -o '[0-9.]*%' | head -1 | sed 's/%//')
    
    log_message "📊 时间周期覆盖率:"
    log_message "  1m: ${COVERAGE_1M:-未知}%"
    log_message "  15m: ${COVERAGE_15M:-未知}%"
    log_message "  1h: ${COVERAGE_1H:-未知}%"
    log_message "  4h: ${COVERAGE_4H:-未知}%"
    log_message "  1d: ${COVERAGE_1D:-未知}%"
    
    # 检查是否需要自动修复
    NEED_REPAIR=false
    
    # 如果任何非1分钟时间周期覆盖率低于50%，触发自动修复
    for coverage in "$COVERAGE_15M" "$COVERAGE_1H" "$COVERAGE_4H" "$COVERAGE_1D"; do
        if [ -n "$coverage" ] && (( $(echo "$coverage < 50" | bc -l) )); then
            NEED_REPAIR=true
            break
        fi
    done
    
    if [ "$NEED_REPAIR" = true ]; then
        log_message "🚨 检测到严重的多时间周期数据缺失，启动自动修复"
        
        # 只修复最近3天的数据（避免过度负载）
        REPAIR_START=$(date -d '3 days ago' '+%Y-%m-%d')
        REPAIR_END=$(date '+%Y-%m-%d')
        
        log_message "🔧 开始自动修复: $REPAIR_START ~ $REPAIR_END"
        
        # 逐个时间周期修复
        for timeframe in "15m" "1h" "4h" "1d"; do
            for symbol in "BTCUSDT" "ETHUSDT" "SOLUSDT" "DOGEUSDT" "XRPUSDT"; do
                log_message "  修复 $symbol $timeframe"
                
                if timeout 600 python3 manage.py repair_multi_timeframe_data \
                    --symbol="$symbol" \
                    --timeframe="$timeframe" \
                    --start="$REPAIR_START" \
                    --end="$REPAIR_END" >> "$LOG_FILE" 2>&1; then
                    log_message "  ✅ $symbol $timeframe 修复成功"
                else
                    log_message "  ❌ $symbol $timeframe 修复失败"
                fi
                
                # 避免过载
                sleep 2
            done
        done
        
        log_message "🎯 自动修复完成"
    else
        log_message "✅ 多时间周期数据质量良好，无需自动修复"
    fi
else
    log_message "❌ 完整性检查报告生成失败"
fi

# 清理旧报告（保留最近20个）
find "$REPORT_DIR" -name "monitor_*.txt" -type f | sort -r | tail -n +21 | xargs rm -f 2>/dev/null

log_message "🏁 多时间周期监控完成"
log_message "📄 报告文件: $REPORT_FILE"
EOF

chmod +x "$SCRIPT_DIR/multi_timeframe_monitor.sh"

echo "✅ 多时间周期监控脚本创建完成"

# 备份当前crontab
crontab -l > /tmp/crontab_backup_multi_$(date +%Y%m%d_%H%M%S) 2>/dev/null || echo "# 新建crontab" > /tmp/crontab_backup_multi_$(date +%Y%m%d_%H%M%S)

echo "📋 当前crontab备份完成"

# 创建新的crontab条目
MULTI_CRON_ENTRIES=$(cat << 'EOFCRON'

# 多时间周期数据监控任务
# 每6小时执行多时间周期检查和自动修复
0 */6 * * * cd /home/<USER>/qiyuai-web/finance_backend && ./multi_timeframe_monitor.sh

# 每周日凌晨4点执行完整的多时间周期修复（从7月16日开始）
0 4 * * 0 cd /home/<USER>/qiyuai-web/finance_backend && echo "y" | ./repair_all_timeframes.sh

EOFCRON
)

# 检查是否已存在相关任务
if crontab -l 2>/dev/null | grep -q "multi_timeframe"; then
    echo "⚠️ 发现已存在的多时间周期监控任务"
    echo "📋 当前相关任务:"
    crontab -l 2>/dev/null | grep -E "multi_timeframe"
    echo ""
    read -p "是否要替换现有任务? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # 删除现有的多时间周期任务
        crontab -l 2>/dev/null | grep -v -E "multi_timeframe" | crontab -
        echo "🗑️ 已删除现有任务"
    else
        echo "❌ 取消配置，保持现有任务不变"
        exit 0
    fi
fi

# 添加新任务到crontab
(crontab -l 2>/dev/null; echo "$MULTI_CRON_ENTRIES") | crontab -

echo "✅ 多时间周期监控定时任务配置完成!"
echo ""
echo "📅 配置的定时任务:"
echo "  • 每6小时监控: 自动检查多时间周期数据完整性并修复"
echo "  • 每周完整修复: 周日凌晨4点执行全面的多时间周期数据修复"
echo ""
echo "📋 当前完整crontab:"
crontab -l

echo ""
echo "🔍 手动测试命令:"
echo "多时间周期检查: python3 check_multi_timeframe_integrity.py --start-date=2025-07-16"
echo "单时间周期修复: python3 manage.py repair_multi_timeframe_data --symbol=BTCUSDT --timeframe=15m --start=2025-07-29 --end=2025-07-30"
echo "批量修复: ./repair_all_timeframes.sh"
echo "监控脚本: ./multi_timeframe_monitor.sh"
echo ""
echo "📄 日志文件位置:"
echo "  • 监控日志: logs/multi_timeframe_monitor.log"
echo "  • 修复日志: logs/multi_timeframe_repair.log"
echo "  • 检查报告: logs/multi_timeframe_reports/"
echo ""
echo "💡 建议："
echo "  • 首次运行可手动执行: ./repair_all_timeframes.sh"
echo "  • 监控脚本每6小时自动运行，自动修复严重缺失"
echo "  • 每周日会进行完整的历史数据修复"