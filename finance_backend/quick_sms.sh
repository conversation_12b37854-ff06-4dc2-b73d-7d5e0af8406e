#!/bin/bash
# 快速生成SMS验证码的脚本

PHONE=${1:-"17724245126"}

echo "🔑 为手机号 $PHONE 生成验证码..."

curl -s -X POST http://127.0.0.1:8000/api/web/sms/send-code/ \
  -H "Content-Type: application/json" \
  -d "{\"phone\": \"$PHONE\", \"purpose\": \"register\"}" | \
  python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    if 'code' in data:
        print(f'✅ 验证码: {data[\"code\"]}')
        print(f'📝 消息: {data[\"message\"]}')
    else:
        print('❌ 生成失败:', data)
except:
    print('❌ 解析响应失败')
"

echo ""
echo "🔍 同时查看数据库记录:"
python3 get_sms_code.py $PHONE