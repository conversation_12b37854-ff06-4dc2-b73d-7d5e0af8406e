import React from 'react';
import { Card } from 'antd';

const AIAnalyzer = ({ indicatorData, symbol, timeframe }) => {
  // 当指标数据变化时记录日志
  React.useEffect(() => {
    if (indicatorData) {
      console.log('🧠 AI分析器接收到指标数据:', indicatorData);
    }
  }, [indicatorData]);

  if (!indicatorData) {
    return (
      <Card title="AI分析结果" size="small">
        <p style={{ color: '#999' }}>等待指标数据计算完成...</p>
      </Card>
    );
  }

  // 简单显示指标数据信息
  const years = Object.keys(indicatorData);

  return (
    <Card title="AI分析结果" size="small">
      <div style={{ color: '#fff' }}>
        <div style={{ marginBottom: '12px' }}>
          <strong>分析参数: </strong>
          <span>{symbol} | {timeframe} | {years.length}年数据</span>
        </div>

        <div style={{ marginBottom: '12px' }}>
          <strong>数据概览: </strong>
          {years.map(year => (
            <div key={year} style={{ marginLeft: '16px', marginTop: '4px' }}>
              <span>{year}年: {indicatorData[year]?.stats?.dataCount || 0} 条数据</span>
            </div>
          ))}
        </div>

        <div>
          <strong>AI建议: </strong>
          <span style={{ color: '#999' }}>基于技术指标的分析功能开发中...</span>
        </div>
      </div>
    </Card>
  );
};

export default AIAnalyzer;
