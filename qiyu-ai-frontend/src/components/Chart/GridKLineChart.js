import React, { useState, useEffect, useRef, useCallback } from 'react'
import { Card, Space, Button, Radio, Skeleton } from 'antd'
import { ReloadOutlined, FullscreenOutlined, ClockCircleOutlined } from '@ant-design/icons'
import EnhancedKLineChart from './EnhancedKLineChart'
import cryptoDataService from '../../services/StreamDataService'
import cryptoMetadataService from '../../services/CryptoMetadataService'
import { calculateSmartYears } from '../../config/yearConfig.js'
import './GridKLineChart.css'

const GridKLineChart = ({
  symbol = 'BTCUSDT',
  years = null, // 改为null，表示动态加载
  timeframe = null, // 外部传入的时间周期
  enableRealTime = false,
  onBinanceConnect
}) => {
  const [currentTimeframe, setCurrentTimeframe] = useState(timeframe || '15m') // 使用外部timeframe或默认15分钟
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [realTimeConnected, setRealTimeConnected] = useState(false)
  const [loadedCharts, setLoadedCharts] = useState(new Set()) // 跟踪已加载的图表
  const [visibleCharts, setVisibleCharts] = useState(new Set()) // 跟踪可见的图表
  const [availableYears, setAvailableYears] = useState([]) // 动态获取的年份数据
  const containerRef = useRef(null)
  const observerRef = useRef(null)

  // 监听外部timeframe变化
  useEffect(() => {
    if (timeframe && timeframe !== currentTimeframe) {
      console.log(`🔄 [GridKLineChart] 外部时间周期变化: ${currentTimeframe} -> ${timeframe}`);
      setCurrentTimeframe(timeframe);
    }
  }, [timeframe, currentTimeframe]);

  // 使用useCallback优化onDataUpdate回调，避免重复渲染
  const handleDataUpdate = useCallback((year) => {
    return (data) => {
      // 数据更新日志已在StreamDataService中处理，这里不再重复记录
      setLoadedCharts(prev => new Set(prev.add(year)))
    }
  }, []) // 空依赖数组，确保函数引用稳定

  // 动态获取可用年份数据
  const loadAvailableYears = useCallback(async () => {
    // 如果已经手动传入years，则优先使用
    if (years && Array.isArray(years) && years.length > 0) {
      console.log('📝 使用手动传入的年份数据:', years)
      setAvailableYears(years)
      return
    }

    try {
      // 🎯 使用CryptoMetadataService实际验证数据可用性
      console.log(`📝 检查 ${symbol} ${currentTimeframe} 的实际可用年份...`)
      const verifiedYears = await cryptoMetadataService.getAvailableYears(symbol, currentTimeframe)
      console.log(`✅ 验证后的可用年份:`, verifiedYears)
      setAvailableYears(verifiedYears)
    } catch (error) {
      console.warn('⚠️ 年份验证失败，使用智能计算作为回退:', error.message)
      // 只在验证失败时才使用智能年份计算作为回退
      const smartYears = calculateSmartYears()
      setAvailableYears(smartYears)
    }
  }, [symbol, currentTimeframe, years])

  // 排序年份：最新年份在前
  const sortedYears = [...availableYears].sort((a, b) => parseInt(b) - parseInt(a))

  // 时间周期切换
  const handleTimeframeChange = (e) => {
    const newTimeframe = e.target.value
    console.log(`\n🕐 [时间周期] 用户切换时间周期: ${currentTimeframe} -> ${newTimeframe}`)
    console.log(`🎯 [时间周期] 当前货币: ${symbol}`)
    setCurrentTimeframe(newTimeframe)
    
    // 清除缓存和已加载状态，强制重新加载
    console.log(`🗑️ [时间周期] 清除缓存，准备重新加载数据`)
    cryptoDataService.clearCache()
    cryptoMetadataService.clearCache()
    setLoadedCharts(new Set())
    console.log(`🔄 [时间周期] 时间周期切换完成，等待图表重新渲染\n`)
    // 时间周期变化时重新获取年份数据
    // loadAvailableYears 会在 useEffect 中自动触发
  }

  // 刷新数据
  const refreshData = () => {
    cryptoDataService.clearCache()
    cryptoMetadataService.clearCache()
    setLoadedCharts(new Set())
    loadAvailableYears() // 重新加载年份数据
  }

  // 加载年份数据 - 在symbol或timeframe变化时触发
  useEffect(() => {
    loadAvailableYears()
  }, [loadAvailableYears])

  // 全屏切换
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  // Intersection Observer 用于懒加载 - 使用useCallback避免重新创建
  const setupIntersectionObserver = useCallback(() => {
    if (!containerRef.current) return

    // 清理现有的observer
    if (observerRef.current) {
      observerRef.current.disconnect()
    }

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const year = entry.target.dataset.year
          if (entry.isIntersecting) {
            // 移除重复的视野日志
            setVisibleCharts(prev => new Set(prev.add(year)))
          } else {
            // 移除重复的视野日志
            setVisibleCharts(prev => {
              const newSet = new Set(prev)
              newSet.delete(year)
              return newSet
            })
          }
        })
      },
      {
        root: containerRef.current,
        rootMargin: '100px', // 提前100px开始加载
        threshold: 0.1
      }
    )

    // 观察所有图表容器
    const chartElements = containerRef.current.querySelectorAll('.chart-grid-item')
    chartElements.forEach(el => observerRef.current?.observe(el))
  }, []) // 空依赖数组，避免重复创建

  // 设置Intersection Observer - 只在组件挂载时运行一次
  useEffect(() => {
    const timer = setTimeout(() => {
      setupIntersectionObserver()
    }, 100) // 延迟100ms确保DOM已渲染

    return () => {
      clearTimeout(timer)
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [setupIntersectionObserver])

  // 初始化可见图表（显示所有图表）- 只在sortedYears变化时运行
  useEffect(() => {
    if (sortedYears.length > 0) {
      setVisibleCharts(new Set(sortedYears)) // 显示所有图表
    }
  }, [sortedYears.join(',')]) // 使用join避免数组引用问题

  // 实时连接 - 只在enableRealTime变化时运行
  useEffect(() => {
    if (enableRealTime) {
      const timer = setTimeout(() => {
        console.log('🔌 连接实时数据...')
        setRealTimeConnected(true)
        if (onBinanceConnect) {
          onBinanceConnect()
        }
      }, 2000)
      
      return () => clearTimeout(timer)
    }
  }, [enableRealTime, onBinanceConnect])

  return (
    <div className={`grid-kline-chart ${isFullscreen ? 'fullscreen' : ''}`}>
      {/* 2x 网格容器 */}
      <div 
        className="charts-grid-container"
        ref={containerRef}
      >
        <div className="charts-grid">
          {sortedYears.map((year, index) => {
            const isVisible = visibleCharts.has(year)
            const shouldLoad = true // 由于只有两个图表，直接全部加载
            
            return (
              <div 
                key={year}
                className="chart-grid-item"
                data-year={year}
              >
                <Card
                  className="chart-card"
                  styles={{ body: { padding: 0, height: '100%' } }}
                >
                  {shouldLoad ? (
                    <EnhancedKLineChart
                      key={year} // 只使用year作key，避免时间周期变化时重新挂载
                      symbol={symbol}
                      year={year}
                      timeframe={currentTimeframe}
                      width="100%"
                      height="100%"
                      showMACD={year === new Date().getFullYear().toString()}
                      enableRealTime={enableRealTime && year === new Date().getFullYear().toString()}
                      onDataUpdate={handleDataUpdate(year)}
                    />
                  ) : (
                    <div className="chart-placeholder">
                      <Skeleton active paragraph={{ rows: 8 }} />
                      <div className="lazy-load-hint">
                        滚动到此处自动加载 {year}年 数据
                      </div>
                    </div>
                  )}
                </Card>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

export default GridKLineChart