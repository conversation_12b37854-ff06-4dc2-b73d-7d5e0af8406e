import { calculateSmartYears, YEAR_CONFIG } from '../config/yearConfig.js'

// 加密货币数据元信息服务 - 获取可用年份、货币和时间周期
class CryptoMetadataService {
  constructor() {
    this.cache = new Map()
    
    // 多路径配置：不同年份使用不同的COS路径
    this.cosPaths = {
      // 历史数据路径（20250724）- 2017-2024年
      'legacy': 'https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/finance-1324685443/finance-1324685443/finance-1324685443/crypto-kline-data-v2/20250724',
      // 2025年数据路径（20250726）
      '2025': 'https://finance-1324685443.cos.ap-guangzhou.myqcloud.com/crypto-kline-data-v2/20250726'
    }
    
    // 从上传脚本获取的已知信息
    this.knownSymbols = ['BTCUSDT', 'ETHUSDT', 'LTCUSDT', 'DOGEUSDT', 'SOLUSDT', 'XRPUSDT', 'TRUMPUSDT'] // 所有货币

    // 🎯 智能年份计算配置
    this.yearConfig = {
      dataStartYear: 2017, // 数据开始年份
      maxHistoryYears: 99,   // 最多显示多少年历史数据
      currentYearCutoff: 2, // 当前年份的月份阈值（2月后认为当年数据稳定）
    }

    // 不同年份支持的时间框架
    this.timeframesByYear = {
      'current': ['1m', '15m', '1h', '4h', '1d', '1mo'], // 当前年份的时间框架
      'legacy': ['1m', '15m', '1h', '4h', '1d', '1mo'] // 历史年份有完整数据（包括1mo）
    }
  }

  /**
   * 获取指定货币和时间周期的可用年份
   * @param {string} symbol - 货币对符号，如 'BTCUSDT'
   * @param {string} timeframe - 时间周期，如 '1h'
   * @returns {Promise<string[]>} 可用年份数组
   */
  /**
   * 🎯 智能计算可用年份列表
   * 使用外部配置文件的年份计算逻辑
   */
  calculateAvailableYears() {
    return calculateSmartYears()
  }

  // 根据年份获取对应的COS路径
  getCosPathForYear(year) {
    const currentYear = new Date().getFullYear()
    if (parseInt(year) === currentYear) {
      return this.cosPaths['2025'] // 当前年份路径
    } else {
      return this.cosPaths['legacy'] // 历史年份路径
    }
  }

  // 根据年份获取支持的时间框架
  getTimeframesForYear(year) {
    const currentYear = new Date().getFullYear()
    if (parseInt(year) === currentYear) {
      return this.timeframesByYear['current']
    } else {
      return this.timeframesByYear['legacy']
    }
  }

  async getAvailableYears(symbol = 'BTCUSDT', timeframe = '1h') {
    const cacheKey = `years_${symbol}_${timeframe}`

    // 检查缓存
    if (this.cache.has(cacheKey)) {
      console.log(`📋 从缓存获取年份数据: ${symbol} ${timeframe}`)
      return this.cache.get(cacheKey)
    }

    console.log(`🔍 检查可用年份: ${symbol} ${timeframe}`)

    // 🎯 使用智能年份计算
    const candidateYears = this.calculateAvailableYears()

    // 并行检查所有候选年份的数据可用性
    const yearChecks = candidateYears.map(async (year) => {
      try {
        // 检查该年份是否支持这个时间框架
        const supportedTimeframes = this.getTimeframesForYear(year)
        if (!supportedTimeframes.includes(timeframe)) {
          console.log(`⏭️ ${year}年不支持${timeframe}时间框架`)
          return null
        }
        
        // 根据年份选择正确的COS路径
        const cosBasePath = this.getCosPathForYear(year)
        
        // 🎯 特殊处理2025年1分钟数据
        if (year === '2025' && timeframe === '1m') {
          // 检查2025年1分钟数据的第一个月度文件是否存在
          const testUrl = `${cosBasePath}/${symbol}/1m/${symbol}_1m_01_2025_compressed.json`
          
          try {
            const response = await fetch(testUrl, { method: 'HEAD' })
            if (response.ok) {
              console.log(`✅ 找到2025年1分钟数据: ${year}年`)
              return year
            } else {
              console.log(`❌ 未找到2025年1分钟数据: ${year}年 (${response.status})`)
              return null
            }
          } catch (error) {
            console.log(`❌ 检查2025年1分钟数据失败: ${year}年 - ${error.message}`)
            return null
          }
        }
        
        // 常规年份数据检查
        const url = `${cosBasePath}/${symbol}/${timeframe}/${symbol}_${timeframe}_${year}_compressed.json`
        console.log(`🔍 检查URL: ${url}`)
        
        // 使用HEAD请求检查文件是否存在，避免下载整个文件
        const response = await fetch(url, { method: 'HEAD' })
        
        if (response.ok) {
          console.log(`✅ 找到数据: ${year}年`)
          return year
        } else {
          console.log(`❌ 未找到数据: ${year}年 (${response.status})`)
          return null
        }
      } catch (error) {
        console.log(`❌ 检查失败: ${year}年 - ${error.message}`)
        return null
      }
    })

    // 等待所有检查完成
    const results = await Promise.all(yearChecks)
    
    // 过滤掉null值并按年份倒序排序（最新年份在前）
    const validYears = results
      .filter(year => year !== null)
      .sort((a, b) => parseInt(b) - parseInt(a))

    console.log(`📊 ${symbol} ${timeframe} 可用年份:`, validYears)
    
    // 缓存结果（缓存1小时）
    this.cache.set(cacheKey, validYears)
    setTimeout(() => {
      this.cache.delete(cacheKey)
    }, 60 * 60 * 1000)

    return validYears
  }

  /**
   * 获取所有支持的货币对
   * @returns {Promise<string[]>} 货币对数组
   */
  async getAvailableSymbols() {
    // 目前返回已知的货币对，后续可以扩展为动态检查
    return [...this.knownSymbols]
  }

  /**
   * 获取所有支持的时间周期（基于可用年份的并集）
   * @returns {Promise<string[]>} 时间周期数组
   */
  async getAvailableTimeframes() {
    const allTimeframes = new Set()
    
    // 合并所有年份支持的时间框架
    Object.values(this.timeframesByYear).forEach(timeframes => {
      timeframes.forEach(tf => allTimeframes.add(tf))
    })
    
    return Array.from(allTimeframes).sort((a, b) => {
      // 按时间长度排序：1m, 15m, 1h, 4h, 1d, 1mo
      const order = ['1m', '15m', '1h', '4h', '1d', '1mo']
      return order.indexOf(a) - order.indexOf(b)
    })
  }

  /**
   * 获取指定货币的完整元数据信息
   * @param {string} symbol - 货币对符号
   * @returns {Promise<Object>} 元数据对象
   */
  async getSymbolMetadata(symbol = 'BTCUSDT') {
    console.log(`📋 获取货币元数据: ${symbol}`)
    
    const metadata = {
      symbol,
      timeframes: {},
      totalYears: 0,
      availableTimeframes: []
    }

    // 获取所有时间周期的年份数据
    const allTimeframes = await this.getAvailableTimeframes()
    for (const timeframe of allTimeframes) {
      try {
        const years = await this.getAvailableYears(symbol, timeframe)
        if (years.length > 0) {
          metadata.timeframes[timeframe] = {
            years,
            totalYears: years.length,
            dateRange: {
              earliest: Math.min(...years.map(y => parseInt(y))),
              latest: Math.max(...years.map(y => parseInt(y)))
            }
          }
          metadata.availableTimeframes.push(timeframe)
          metadata.totalYears = Math.max(metadata.totalYears, years.length)
        }
      } catch (error) {
        console.warn(`⚠️ 获取${timeframe}数据失败:`, error.message)
      }
    }

    console.log(`📊 ${symbol} 元数据:`, metadata)
    return metadata
  }

  /**
   * 批量获取多个货币的年份数据
   * @param {string[]} symbols - 货币对数组
   * @param {string} timeframe - 时间周期
   * @returns {Promise<Object>} 包含所有货币年份数据的对象
   */
  async getBatchAvailableYears(symbols = null, timeframe = '1h') {
    if (!symbols) {
      symbols = await this.getAvailableSymbols()
    }

    console.log(`🔍 批量获取年份数据: ${symbols.join(', ')} - ${timeframe}`)

    const results = {}
    
    // 并行获取所有货币的年份数据
    const symbolChecks = symbols.map(async (symbol) => {
      try {
        const years = await this.getAvailableYears(symbol, timeframe)
        return { symbol, years }
      } catch (error) {
        console.warn(`⚠️ 获取${symbol}年份数据失败:`, error.message)
        return { symbol, years: [] }
      }
    })

    const symbolResults = await Promise.all(symbolChecks)
    
    // 整理结果
    symbolResults.forEach(({ symbol, years }) => {
      results[symbol] = years
    })

    return results
  }

  /**
   * 清除缓存
   */
  clearCache() {
    console.log('🗑️ 清除元数据缓存')
    this.cache.clear()
  }

  /**
   * 检查数据文件是否存在
   * @param {string} symbol - 货币对
   * @param {string} timeframe - 时间周期  
   * @param {string} year - 年份
   * @returns {Promise<boolean>} 是否存在
   */
  async checkDataExists(symbol, timeframe, year) {
    try {
      const cosBasePath = this.getCosPathForYear(year)
      
      // 🎯 特殊处理2025年1分钟数据
      if (year === '2025' && timeframe === '1m') {
        // 检查第一个月度文件是否存在
        const testUrl = `${cosBasePath}/${symbol}/1m/${symbol}_1m_01_2025_compressed.json`
        const response = await fetch(testUrl, { method: 'HEAD' })
        return response.ok
      }
      
      // 常规数据检查
      const url = `${cosBasePath}/${symbol}/${timeframe}/${symbol}_${timeframe}_${year}_compressed.json`
      const response = await fetch(url, { method: 'HEAD' })
      return response.ok
    } catch (error) {
      return false
    }
  }

  /**
   * 获取数据文件的URL
   * @param {string} symbol - 货币对
   * @param {string} timeframe - 时间周期
   * @param {string} year - 年份
   * @returns {string} 数据文件URL
   */
  getDataURL(symbol, timeframe, year) {
    const cosBasePath = this.getCosPathForYear(year)
    return `${cosBasePath}/${symbol}/${timeframe}/${symbol}_${timeframe}_${year}_compressed.json`
  }
}

// 创建单例
const cryptoMetadataService = new CryptoMetadataService()

export default cryptoMetadataService