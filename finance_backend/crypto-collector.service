[Unit]
Description=Cryptocurrency Data Collector
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/qiyuai-web/finance_backend
Environment=PYTHONPATH=/home/<USER>/qiyuai-web/finance_backend
ExecStart=/usr/bin/python3 manage.py collect_multi_data --daemon
ExecReload=/bin/kill -HUP $MAINPID
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

# 自动重启配置
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# 资源限制
MemoryMax=500M
CPUQuota=50%

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=crypto-collector

[Install]
WantedBy=multi-user.target