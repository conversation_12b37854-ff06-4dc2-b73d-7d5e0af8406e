from django.contrib import admin
from .models import WechatUser, CreditRecord

@admin.register(WechatUser)
class WechatUserAdmin(admin.ModelAdmin):
    list_display = ('id', 'openid', 'nickname', 'phone', 'credits', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('openid', 'nickname', 'phone')
    readonly_fields = ('openid', 'unionid', 'created_at', 'updated_at')
    ordering = ('-created_at',)

@admin.register(CreditRecord)
class CreditRecordAdmin(admin.ModelAdmin):
    list_display = ('id', 'user_info', 'type', 'amount', 'description', 'created_at')
    list_filter = ('type', 'created_at')
    search_fields = ('user__openid', 'user__nickname', 'description')
    ordering = ('-created_at',)
    
    def user_info(self, obj):
        return f"{obj.user.nickname or obj.user.openid} ({obj.user.phone or '未绑定手机'})"
    user_info.short_description = '用户'
