#!/usr/bin/env python3
"""
测试技术指标计算功能
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'finance_project.settings')
django.setup()

from historical_data.technical_indicators_service import TechnicalIndicatorService
import pandas as pd
from datetime import datetime

def test_technical_indicators():
    """测试技术指标计算"""
    print("🔧 开始测试技术指标计算功能...")
    
    # 创建测试数据
    test_data = []
    base_price = 95000
    base_timestamp = 1735689600000  # 2025-01-01 00:00:00 UTC
    
    for i in range(100):  # 生成100条测试数据
        timestamp = base_timestamp + i * 60000  # 每分钟一条
        open_price = base_price + (i % 10 - 5) * 100
        high_price = open_price + 200
        low_price = open_price - 200  
        close_price = open_price + (i % 5 - 2) * 50
        volume = 1000 + i * 10
        
        test_data.append({
            'open_time': timestamp,
            'open': float(open_price),
            'high': float(high_price),
            'low': float(low_price),
            'close': float(close_price),
            'volume': float(volume)
        })
    
    print(f"✅ 生成了 {len(test_data)} 条测试K线数据")
    
    # 测试技术指标服务
    try:
        indicator_service = TechnicalIndicatorService()
        print(f"✅ TechnicalIndicatorService 创建成功")
        print(f"   pandas-ta 可用: {indicator_service.pandas_ta_available}")
        
        # 准备DataFrame
        df = indicator_service.prepare_dataframe(test_data)
        print(f"✅ DataFrame 准备完成，数据量: {len(df)}")
        
        if not df.empty:
            # 计算技术指标
            indicators = indicator_service.calculate_all_indicators(df)
            print(f"✅ 技术指标计算完成")
            
            # 显示关键指标
            key_indicators = ['macd_line', 'macd_signal', 'macd_histogram', 'rsi', 'stoch_k', 'stoch_d', 'stoch_j']
            print("\n📊 关键技术指标:")
            for key in key_indicators:
                if key in indicators:
                    print(f"   {key}: {indicators[key]:.4f}")
            
            print(f"\n   当前价格: {indicators.get('current_price', 'N/A')}")
            print(f"   RSI状态: {'超买' if indicators.get('rsi', 50) > 70 else '超卖' if indicators.get('rsi', 50) < 30 else '正常'}")
            print(f"   MACD趋势: {'金叉' if indicators.get('macd_line', 0) > indicators.get('macd_signal', 0) else '死叉'}")
            
            return True
        else:
            print("❌ DataFrame为空")
            return False
            
    except Exception as e:
        print(f"❌ 技术指标计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_web_ai_prompt():
    """测试Web版AI提示词模板"""
    print("\n🤖 测试Web版AI提示词模板...")
    
    try:
        from ai_analysis.prompt_templates import AIPromptTemplates
        
        # 模拟技术指标数据
        technical_indicators = {
            'MACD': {
                'DIF': 0.1234,
                'DEA': 0.0987,
                'MACD': 0.0247,
                'trend': '金叉'
            },
            'KDJ': {
                'K': 65.23,
                'D': 62.45,
                'J': 70.79,
                'state': '正常'
            },
            'RSI': {
                'value': 58.45,
                'state': '正常'
            }
        }
        
        crypto_context = f"""## 当前市场数据分析

技术指标分析: {technical_indicators}
实时价格数据: {{"symbol": "BTCUSDT", "price": "95000", "change_percent": "2.5"}}
数据时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

        prompt = AIPromptTemplates.build_web_analysis_prompt(
            symbol="BTCUSDT",
            crypto_context=crypto_context,
            target_date="2025-01-30"
        )
        
        print("✅ Web版AI提示词模板生成成功")
        print(f"   提示词长度: {len(prompt)} 字符")
        print(f"   包含关键词: {'MACD' in prompt}, {'KDJ' in prompt}, {'RSI' in prompt}")
        
        return True
        
    except Exception as e:
        print(f"❌ 提示词模板测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 QiyuAI Web版技术指标系统测试")
    print("=" * 50)
    
    success_count = 0
    total_tests = 2
    
    # 测试技术指标计算
    if test_technical_indicators():
        success_count += 1
    
    # 测试AI提示词模板
    if test_web_ai_prompt():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"📈 测试完成: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！Web版AI分析系统的技术指标功能正常工作")
    else:
        print("⚠️  部分测试失败，请检查系统配置")
        sys.exit(1)