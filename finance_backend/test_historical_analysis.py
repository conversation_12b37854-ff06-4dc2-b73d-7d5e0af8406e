#!/usr/bin/env python3
"""
测试历史同期数据分析API endpoints
"""

import os
import sys
import django
import requests
import json
from datetime import datetime
import random
import string

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'finance_project.settings')
django.setup()

from web_api.models import WebUser
from django.test import Client

def create_test_user_and_login():
    """使用现有用户登录并获取JWT令牌"""
    try:
        # 使用现有的测试用户
        test_phone = '13454662592'
        test_password = '123456'
        
        print(f"使用测试用户: {test_phone}")
        
        # 使用SMS登录API获取JWT令牌
        login_data = {
            'phone_number': test_phone,
            'password': test_password
        }
        
        response = requests.post(
            'http://localhost:8000/api/web/sms/password-login/',
            json=login_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            if 'access' in result:
                print(f"获取JWT令牌成功")
                return result['access']
            else:
                print(f"登录响应中没有access令牌: {result}")
                return None
        else:
            print(f"登录失败: {response.status_code} - {response.text}")
            return None
        
    except Exception as e:
        print(f"创建测试用户失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_api_endpoint(endpoint, token, params=None):
    """测试API端点"""
    base_url = "http://localhost:8000/api/historical/"
    url = base_url + endpoint
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        print(f"\n=== 测试 {endpoint} ===")
        print(f"URL: {url}")
        
        if params:
            print(f"参数: {params}")
            response = requests.get(url, headers=headers, params=params)
        else:
            response = requests.get(url, headers=headers)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("响应预览:")
            # 打印响应的关键信息，而不是完整的JSON
            if 'capabilities' in result:
                print(f"  功能数量: {len(result['capabilities'])}")
                for cap_name in result['capabilities'].keys():
                    print(f"  - {cap_name}")
            elif 'historical_data' in result:
                print(f"  目标日期: {result.get('target_date')}")
                print(f"  交易对: {result.get('symbol')}")
                print(f"  历史年份数量: {result.get('total_years', 0)}")
            elif 'indicators' in result:
                print(f"  目标日期: {result.get('target_date')}")
                print(f"  计算的指标: {list(result['indicators'].keys())}")
            else:
                print(f"  响应键: {list(result.keys())}")
        else:
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"请求失败: {e}")

def main():
    """主测试函数"""
    print("=== 历史同期数据分析API测试 ===")
    
    # 创建测试用户和令牌
    token = create_test_user_and_login()
    if not token:
        print("无法获取认证令牌，退出测试")
        return
    
    # 测试分析功能说明API（无需认证）
    print("\n1. 测试分析功能说明")
    try:
        response = requests.get("http://localhost:8000/api/historical/analysis-capabilities/")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"支持的功能: {list(result['capabilities'].keys())}")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 测试历史同期数据API
    print("\n2. 测试历史同期数据获取")
    test_api_endpoint('same-period/', token, {
        'target_date': '2024-01-15',
        'symbol': 'BTCUSDT',
        'days_range': 3
    })
    
    # 测试时间段技术指标API
    print("\n3. 测试时间段技术指标计算")
    test_api_endpoint('period-indicators/', token, {
        'target_date': '2024-01-15',
        'symbol': 'BTCUSDT',
        'days_range': 3,
        'indicators': 'rsi,macd'
    })
    
    # 测试历史同期对比分析API
    print("\n4. 测试历史同期对比分析")
    test_api_endpoint('comparative-analysis/', token, {
        'target_date': '2024-01-15',
        'symbol': 'BTCUSDT',
        'days_range': 3
    })
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()