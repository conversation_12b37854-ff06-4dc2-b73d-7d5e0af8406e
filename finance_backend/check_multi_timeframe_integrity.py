#!/usr/bin/env python3
"""
多时间周期数据完整性检查脚本
检查1m, 15m, 1h, 4h, 1d各时间周期的数据完整性
"""

import os
import sys
import django
import json
from datetime import datetime, timedelta
from typing import Dict, List, Set
from collections import defaultdict

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'finance_project.settings')
django.setup()

from crypto_api.services.cos_service import COSRealtimeService

class MultiTimeframeIntegrityChecker:
    """多时间周期数据完整性检查器"""
    
    def __init__(self, debug=False):
        self.debug = debug
        self.cos_service = COSRealtimeService(debug=debug)
        
        # 支持的交易对和时间周期
        self.symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'DOGEUSDT', 'XRPUSDT']
        self.timeframes = ['1m', '15m', '1h', '4h', '1d']
        
        # 每个时间周期一天应有的数据条数
        self.expected_records_per_day = {
            '1m': 1440,    # 24 * 60
            '15m': 96,     # 24 * 4
            '1h': 24,      # 24
            '4h': 6,       # 24 / 4
            '1d': 1        # 1
        }
        
        print(f"🔍 多时间周期数据完整性检查器初始化完成")
        print(f"📊 支持时间周期: {', '.join(self.timeframes)}")
    
    def check_timeframe_coverage(self, start_date: str, end_date: str = None) -> Dict:
        """检查多时间周期数据覆盖情况"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d')
        
        print(f"📅 检查时间周期覆盖: {start_date} ~ {end_date}")
        
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        overall_report = {
            'check_period': {'start': start_date, 'end': end_date},
            'timeframes': {},
            'symbols': {},
            'summary': {
                'total_days': 0,
                'timeframe_coverage': {},
                'symbol_coverage': {},
                'missing_data': []
            }
        }
        
        # 按时间周期检查
        for timeframe in self.timeframes:
            print(f"\n📊 检查时间周期: {timeframe}")
            timeframe_data = self.check_single_timeframe(timeframe, start_dt, end_dt)
            overall_report['timeframes'][timeframe] = timeframe_data
        
        # 按交易对汇总
        for symbol in self.symbols:
            symbol_summary = self.summarize_symbol_data(symbol, overall_report['timeframes'], start_dt, end_dt)
            overall_report['symbols'][symbol] = symbol_summary
        
        # 计算总体统计
        self.calculate_coverage_statistics(overall_report, start_dt, end_dt)
        
        return overall_report
    
    def check_single_timeframe(self, timeframe: str, start_dt: datetime, end_dt: datetime) -> Dict:
        """检查单个时间周期的数据"""
        timeframe_data = {
            'timeframe': timeframe,
            'expected_per_day': self.expected_records_per_day[timeframe],
            'symbols': {},
            'total_coverage': 0.0,
            'missing_periods': []
        }
        
        for symbol in self.symbols:
            print(f"  🔍 检查 {symbol} {timeframe}")
            symbol_data = self.check_symbol_timeframe(symbol, timeframe, start_dt, end_dt)
            timeframe_data['symbols'][symbol] = symbol_data
        
        return timeframe_data
    
    def check_symbol_timeframe(self, symbol: str, timeframe: str, start_dt: datetime, end_dt: datetime) -> Dict:
        """检查特定交易对和时间周期的数据"""
        symbol_data = {
            'symbol': symbol,
            'timeframe': timeframe,
            'daily_checks': {},
            'total_expected': 0,
            'total_actual': 0,
            'coverage_percent': 0.0,
            'missing_dates': [],
            'data_quality': 'unknown'
        }
        
        current_date = start_dt
        while current_date <= end_dt:
            date_str = current_date.strftime('%Y-%m-%d')
            
            # 检查该日期的数据
            daily_result = self.check_daily_timeframe_data(symbol, timeframe, date_str)
            symbol_data['daily_checks'][date_str] = daily_result
            
            # 累计统计
            expected = self.expected_records_per_day[timeframe]
            actual = daily_result['actual_records']
            
            symbol_data['total_expected'] += expected
            symbol_data['total_actual'] += actual
            
            # 记录缺失日期
            if actual == 0:
                symbol_data['missing_dates'].append(date_str)
            elif actual < expected * 0.8:  # 少于80%认为数据不完整
                symbol_data['missing_dates'].append(f"{date_str} (部分缺失: {actual}/{expected})")
            
            current_date += timedelta(days=1)
        
        # 计算覆盖率
        if symbol_data['total_expected'] > 0:
            symbol_data['coverage_percent'] = (symbol_data['total_actual'] / symbol_data['total_expected']) * 100
        
        # 评估数据质量
        if symbol_data['coverage_percent'] >= 95:
            symbol_data['data_quality'] = 'excellent'
        elif symbol_data['coverage_percent'] >= 80:
            symbol_data['data_quality'] = 'good'
        elif symbol_data['coverage_percent'] >= 50:
            symbol_data['data_quality'] = 'fair'
        else:
            symbol_data['data_quality'] = 'poor'
        
        if self.debug and symbol_data['missing_dates']:
            print(f"    ⚠️ {symbol} {timeframe} 缺失: {len(symbol_data['missing_dates'])} 个时段")
        
        return symbol_data
    
    def check_daily_timeframe_data(self, symbol: str, timeframe: str, date_str: str) -> Dict:
        """检查单日特定时间周期数据"""
        result = {
            'date': date_str,
            'file_exists': False,
            'actual_records': 0,
            'expected_records': self.expected_records_per_day[timeframe],
            'coverage_percent': 0.0,
            'file_path': '',
            'error': None
        }
        
        try:
            # 生成COS文件路径
            cos_key = self.cos_service.generate_cos_path(symbol, date_str, timeframe)
            result['file_path'] = cos_key
            
            # 检查文件是否存在
            if not self.cos_service.check_file_exists(cos_key):
                return result
            
            result['file_exists'] = True
            
            # 下载并解析文件
            file_obj = self.cos_service.cos_client.get_object(
                Bucket=self.cos_service.bucket_name,
                Key=cos_key
            )
            file_content = file_obj['Body'].read().decode('utf-8')
            cleaned_content = self.cos_service.clean_chunked_data(file_content)
            data = json.loads(cleaned_content)
            
            klines = data.get('data', [])
            result['actual_records'] = len(klines)
            
            if result['expected_records'] > 0:
                result['coverage_percent'] = (result['actual_records'] / result['expected_records']) * 100
                
        except Exception as e:
            result['error'] = str(e)
            if self.debug:
                print(f"    ❌ 检查异常 {symbol} {timeframe} {date_str}: {e}")
        
        return result
    
    def summarize_symbol_data(self, symbol: str, timeframes_data: Dict, start_dt: datetime, end_dt: datetime) -> Dict:
        """汇总单个交易对的数据情况"""
        symbol_summary = {
            'symbol': symbol,
            'timeframes': {},
            'overall_quality': 'unknown',
            'recommended_actions': []
        }
        
        quality_scores = []
        
        for timeframe, tf_data in timeframes_data.items():
            if symbol in tf_data['symbols']:
                symbol_data = tf_data['symbols'][symbol]
                symbol_summary['timeframes'][timeframe] = {
                    'coverage': symbol_data['coverage_percent'],
                    'quality': symbol_data['data_quality'],
                    'missing_dates': len(symbol_data['missing_dates'])
                }
                
                # 收集质量评分
                quality_map = {'excellent': 100, 'good': 80, 'fair': 60, 'poor': 20, 'unknown': 0}
                quality_scores.append(quality_map.get(symbol_data['data_quality'], 0))
        
        # 计算总体质量
        if quality_scores:
            avg_quality = sum(quality_scores) / len(quality_scores)
            if avg_quality >= 90:
                symbol_summary['overall_quality'] = 'excellent'
            elif avg_quality >= 70:
                symbol_summary['overall_quality'] = 'good'
            elif avg_quality >= 50:
                symbol_summary['overall_quality'] = 'fair'
            else:
                symbol_summary['overall_quality'] = 'poor'
        
        # 生成建议操作
        symbol_summary['recommended_actions'] = self.generate_symbol_recommendations(symbol, symbol_summary['timeframes'])
        
        return symbol_summary
    
    def generate_symbol_recommendations(self, symbol: str, timeframes: Dict) -> List[str]:
        """为特定交易对生成修复建议"""
        recommendations = []
        
        for timeframe, tf_data in timeframes.items():
            if tf_data['coverage'] < 80:
                if tf_data['missing_dates'] > 0:
                    recommendations.append(f"修复 {symbol} {timeframe} 数据 (覆盖率: {tf_data['coverage']:.1f}%)")
        
        if not recommendations:
            recommendations.append(f"{symbol} 数据质量良好，无需修复")
        
        return recommendations
    
    def calculate_coverage_statistics(self, report: Dict, start_dt: datetime, end_dt: datetime):
        """计算总体覆盖统计"""
        summary = report['summary']
        summary['total_days'] = (end_dt - start_dt).days + 1
        
        # 按时间周期统计
        for timeframe, tf_data in report['timeframes'].items():
            total_expected = 0
            total_actual = 0
            
            for symbol, symbol_data in tf_data['symbols'].items():
                total_expected += symbol_data['total_expected']
                total_actual += symbol_data['total_actual']
            
            coverage = (total_actual / total_expected * 100) if total_expected > 0 else 0
            summary['timeframe_coverage'][timeframe] = {
                'coverage_percent': coverage,
                'expected': total_expected,
                'actual': total_actual
            }
        
        # 按交易对统计
        for symbol, symbol_data in report['symbols'].items():
            summary['symbol_coverage'][symbol] = {
                'overall_quality': symbol_data['overall_quality'],
                'timeframes_count': len(symbol_data['timeframes']),
                'avg_coverage': sum(tf['coverage'] for tf in symbol_data['timeframes'].values()) / len(symbol_data['timeframes']) if symbol_data['timeframes'] else 0
            }
    
    def generate_multi_timeframe_report(self, coverage_data: Dict, output_file: str = None) -> str:
        """生成多时间周期完整性报告"""
        report_lines = []
        
        # 报告头部
        report_lines.append("=" * 80)
        report_lines.append("🔍 多时间周期数据完整性报告")
        report_lines.append("=" * 80)
        report_lines.append(f"📅 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"📊 检查期间: {coverage_data['check_period']['start']} ~ {coverage_data['check_period']['end']}")
        report_lines.append(f"⏱️  总天数: {coverage_data['summary']['total_days']} 天")
        report_lines.append("")
        
        # 按时间周期统计
        report_lines.append("📈 时间周期覆盖统计")
        report_lines.append("-" * 50)
        for timeframe, tf_stats in coverage_data['summary']['timeframe_coverage'].items():
            coverage = tf_stats['coverage_percent']
            status = "🎉" if coverage >= 95 else "✅" if coverage >= 80 else "⚠️" if coverage >= 50 else "❌"
            report_lines.append(f"{timeframe:>4}: {status} {coverage:6.2f}% ({tf_stats['actual']:,}/{tf_stats['expected']:,})")
        report_lines.append("")
        
        # 按交易对统计
        report_lines.append("📊 交易对数据质量")
        report_lines.append("-" * 50)
        for symbol, symbol_stats in coverage_data['summary']['symbol_coverage'].items():
            quality = symbol_stats['overall_quality']
            avg_coverage = symbol_stats['avg_coverage']
            status = {"excellent": "🎉", "good": "✅", "fair": "⚠️", "poor": "❌"}.get(quality, "❓")
            report_lines.append(f"{symbol:>8}: {status} {quality:>9} (平均覆盖: {avg_coverage:5.1f}%)")
        report_lines.append("")
        
        # 详细建议
        report_lines.append("🔧 修复建议")
        report_lines.append("-" * 50)
        repair_commands = self.generate_multi_timeframe_repair_commands(coverage_data)
        
        if repair_commands:
            for cmd in repair_commands[:10]:  # 显示前10个修复命令
                report_lines.append(cmd)
        else:
            report_lines.append("🎉 所有数据质量良好，无需修复")
        
        report_lines.append("")
        
        # 数据质量总结
        report_lines.append("📋 数据质量总结")
        report_lines.append("-" * 50)
        
        excellent_count = sum(1 for s in coverage_data['summary']['symbol_coverage'].values() if s['overall_quality'] == 'excellent')
        good_count = sum(1 for s in coverage_data['summary']['symbol_coverage'].values() if s['overall_quality'] == 'good')
        fair_count = sum(1 for s in coverage_data['summary']['symbol_coverage'].values() if s['overall_quality'] == 'fair')
        poor_count = sum(1 for s in coverage_data['summary']['symbol_coverage'].values() if s['overall_quality'] == 'poor')
        
        report_lines.append(f"🎉 优秀: {excellent_count} 个交易对")
        report_lines.append(f"✅ 良好: {good_count} 个交易对")
        report_lines.append(f"⚠️ 一般: {fair_count} 个交易对")
        report_lines.append(f"❌ 较差: {poor_count} 个交易对")
        
        report_content = "\n".join(report_lines)
        
        # 保存到文件
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            print(f"📄 报告已保存: {output_file}")
        
        return report_content
    
    def generate_multi_timeframe_repair_commands(self, coverage_data: Dict) -> List[str]:
        """生成多时间周期修复命令"""
        commands = []
        
        for symbol in self.symbols:
            symbol_data = coverage_data['symbols'].get(symbol, {})
            
            for timeframe in self.timeframes:
                tf_data = symbol_data.get('timeframes', {}).get(timeframe, {})
                
                if tf_data.get('coverage', 100) < 80:  # 覆盖率低于80%需要修复
                    # 生成该时间周期的修复命令
                    start_date = coverage_data['check_period']['start']
                    end_date = coverage_data['check_period']['end']
                    
                    cmd = f"python3 manage.py repair_multi_timeframe_data --symbol={symbol} --timeframe={timeframe} --start='{start_date}' --end='{end_date}'"
                    commands.append(cmd)
        
        return commands

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='多时间周期数据完整性检查工具')
    parser.add_argument('--start-date', type=str, required=True, help='开始日期 YYYY-MM-DD')
    parser.add_argument('--end-date', type=str, help='结束日期 YYYY-MM-DD (默认今天)')
    parser.add_argument('--output', type=str, help='输出报告文件路径')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    
    args = parser.parse_args()
    
    # 创建检查器
    checker = MultiTimeframeIntegrityChecker(debug=args.debug)
    
    # 执行检查
    print("🚀 开始多时间周期数据完整性检查...")
    coverage_data = checker.check_timeframe_coverage(args.start_date, args.end_date)
    
    # 生成报告
    output_file = args.output or f"logs/integrity_reports/multi_timeframe_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    report_content = checker.generate_multi_timeframe_report(coverage_data, output_file)
    
    # 显示摘要
    timeframe_stats = coverage_data['summary']['timeframe_coverage']
    print(f"\n📊 检查完成!")
    print("时间周期覆盖率:")
    for tf, stats in timeframe_stats.items():
        print(f"  {tf}: {stats['coverage_percent']:.2f}%")
    print(f"报告文件: {output_file}")

if __name__ == '__main__':
    main()