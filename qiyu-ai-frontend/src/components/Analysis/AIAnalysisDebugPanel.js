import React, { useState } from 'react';
import { Card, Collapse, Tag, Button, Typography, Space, Divider } from 'antd';
import { BugOutlined, EyeOutlined, DownloadOutlined } from '@ant-design/icons';

const { Panel } = Collapse;
const { Text, Paragraph } = Typography;

const AIAnalysisDebugPanel = ({ 
  debugInfo, 
  dataSummary, 
  integrityCheck, 
  requestData,
  visible = false 
}) => {
  const [isVisible, setIsVisible] = useState(visible);

  if (!isVisible) {
    return (
      <Button 
        type="dashed" 
        size="small" 
        icon={<BugOutlined />}
        onClick={() => setIsVisible(true)}
        style={{ marginTop: '8px' }}
      >
        显示调试信息
      </Button>
    );
  }

  const downloadDebugInfo = () => {
    const debugData = {
      timestamp: new Date().toISOString(),
      debugInfo,
      dataSummary,
      integrityCheck,
      requestData: requestData ? {
        ...requestData,
        cryptoData: '数据已省略' // 避免文件过大
      } : null
    };

    const blob = new Blob([JSON.stringify(debugData, null, 2)], { 
      type: 'application/json' 
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ai-analysis-debug-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Card 
      title={
        <Space>
          <BugOutlined />
          <span>调试面板</span>
          <Tag color="orange">开发模式</Tag>
        </Space>
      }
      size="small"
      extra={
        <Space>
          <Button 
            type="text" 
            size="small" 
            icon={<DownloadOutlined />}
            onClick={downloadDebugInfo}
          >
            导出
          </Button>
          <Button 
            type="text" 
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => setIsVisible(false)}
          >
            隐藏
          </Button>
        </Space>
      }
      style={{ 
        marginTop: '16px',
        border: '1px dashed #faad14',
        background: 'rgba(255, 193, 7, 0.02)'
      }}
    >
      <Collapse size="small" ghost>
        {/* 数据完整性检查 */}
        {integrityCheck && (
          <Panel 
            header={
              <Space>
                <span>数据完整性</span>
                <Tag color={integrityCheck.score >= 80 ? 'green' : integrityCheck.score >= 60 ? 'orange' : 'red'}>
                  {integrityCheck.score}分
                </Tag>
              </Space>
            } 
            key="integrity"
          >
            <div style={{ fontSize: '12px' }}>
              {integrityCheck.issues.length > 0 && (
                <div style={{ marginBottom: '8px' }}>
                  <Text type="danger" strong>问题:</Text>
                  <ul style={{ margin: '4px 0', paddingLeft: '16px' }}>
                    {integrityCheck.issues.map((issue, index) => (
                      <li key={index}>{issue}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              {integrityCheck.warnings.length > 0 && (
                <div style={{ marginBottom: '8px' }}>
                  <Text type="warning" strong>警告:</Text>
                  <ul style={{ margin: '4px 0', paddingLeft: '16px' }}>
                    {integrityCheck.warnings.map((warning, index) => (
                      <li key={index}>{warning}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              {integrityCheck.issues.length === 0 && integrityCheck.warnings.length === 0 && (
                <Text type="success">✅ 数据完整性良好</Text>
              )}
            </div>
          </Panel>
        )}

        {/* 数据摘要 */}
        {dataSummary && (
          <Panel header="数据摘要" key="summary">
            <div style={{ fontSize: '12px' }}>
              <div style={{ marginBottom: '8px' }}>
                <Text strong>数据类型: </Text>
                <Space wrap>
                  {dataSummary.dataTypes.map(type => (
                    <Tag key={type} size="small">{type}</Tag>
                  ))}
                </Space>
              </div>
              
              {Object.entries(dataSummary.details).map(([key, value]) => (
                <div key={key} style={{ marginBottom: '8px' }}>
                  <Text strong>{key}: </Text>
                  <pre style={{ 
                    display: 'inline', 
                    background: '#f5f5f5', 
                    padding: '2px 4px',
                    borderRadius: '2px',
                    fontSize: '11px'
                  }}>
                    {JSON.stringify(value, null, 2)}
                  </pre>
                </div>
              ))}
            </div>
          </Panel>
        )}

        {/* 请求数据 */}
        {requestData && (
          <Panel header="请求数据" key="request">
            <div style={{ fontSize: '11px' }}>
              <div style={{ marginBottom: '8px' }}>
                <Text strong>消息数量: </Text>
                <Tag>{requestData.messages?.length || 0}</Tag>
              </div>
              
              <div style={{ marginBottom: '8px' }}>
                <Text strong>分析周期: </Text>
                <Tag color="blue">{requestData.period}</Tag>
              </div>
              
              <div style={{ marginBottom: '8px' }}>
                <Text strong>目标日期: </Text>
                <Tag color="green">{requestData.target_date}</Tag>
              </div>
              
              <div style={{ marginBottom: '8px' }}>
                <Text strong>最大令牌: </Text>
                <Tag color="purple">{requestData.max_tokens}</Tag>
              </div>
              
              <div style={{ marginBottom: '8px' }}>
                <Text strong>流式模式: </Text>
                <Tag color={requestData.stream ? 'green' : 'red'}>
                  {requestData.stream ? '启用' : '禁用'}
                </Tag>
              </div>
            </div>
          </Panel>
        )}

        {/* 系统信息 */}
        {debugInfo && (
          <Panel header="系统信息" key="system">
            <div style={{ fontSize: '11px' }}>
              <div style={{ marginBottom: '4px' }}>
                <Text strong>时间戳: </Text>
                <Text code>{debugInfo.timestamp}</Text>
              </div>
              
              <div style={{ marginBottom: '4px' }}>
                <Text strong>用户代理: </Text>
                <Paragraph 
                  copyable 
                  style={{ 
                    fontSize: '10px', 
                    margin: 0,
                    wordBreak: 'break-all'
                  }}
                >
                  {debugInfo.userAgent}
                </Paragraph>
              </div>
              
              <div style={{ marginBottom: '4px' }}>
                <Text strong>页面URL: </Text>
                <Text code style={{ fontSize: '10px' }}>{debugInfo.url}</Text>
              </div>
              
              {debugInfo.error && (
                <div style={{ marginTop: '8px' }}>
                  <Text strong type="danger">错误信息: </Text>
                  <pre style={{ 
                    background: '#fff2f0', 
                    border: '1px solid #ffccc7',
                    padding: '8px', 
                    borderRadius: '4px',
                    fontSize: '10px',
                    overflow: 'auto',
                    maxHeight: '150px'
                  }}>
                    {JSON.stringify(debugInfo.error, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </Panel>
        )}
      </Collapse>
      
      <Divider style={{ margin: '12px 0' }} />
      
      <div style={{ textAlign: 'center', fontSize: '11px', color: '#999' }}>
        💡 此面板仅在开发环境显示，用于调试AI分析功能
      </div>
    </Card>
  );
};

export default AIAnalysisDebugPanel;