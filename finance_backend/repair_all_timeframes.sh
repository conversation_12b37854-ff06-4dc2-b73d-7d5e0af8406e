#!/bin/bash

# 多时间周期数据批量修复脚本
# 从现有的1分钟数据生成15m, 1h, 4h, 1d数据

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

LOG_FILE="logs/multi_timeframe_repair.log"

# 写日志函数
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# 配置参数
SYMBOLS=("BTCUSDT" "ETHUSDT" "SOLUSDT" "DOGEUSDT" "XRPUSDT")
TIMEFRAMES=("15m" "1h" "4h" "1d")
START_DATE="2025-07-16"  # 从7月16日开始修复
END_DATE=$(date '+%Y-%m-%d')  # 到今天

log_message "🚀 开始多时间周期数据批量修复"
log_message "📅 修复日期范围: $START_DATE ~ $END_DATE"
log_message "📊 交易对: ${SYMBOLS[*]}"
log_message "⏰ 时间周期: ${TIMEFRAMES[*]}"

# 统计变量
TOTAL_TASKS=0
SUCCESS_TASKS=0
FAILED_TASKS=0

# 计算总任务数
for symbol in "${SYMBOLS[@]}"; do
    for timeframe in "${TIMEFRAMES[@]}"; do
        ((TOTAL_TASKS++))
    done
done

log_message "📈 总计需要处理: $TOTAL_TASKS 个任务"

# 询问是否继续
echo "🔍 即将处理 $TOTAL_TASKS 个时间周期修复任务"
echo "📅 日期范围: $START_DATE ~ $END_DATE"
echo "⏱️ 预计耗时: 约 $((TOTAL_TASKS * 2)) 分钟"
echo ""
read -p "是否继续执行批量修复? (y/N): " -n 1 -r
echo

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log_message "❌ 用户取消批量修复操作"
    exit 0
fi

log_message "🎯 开始执行批量修复任务..."

# 按交易对和时间周期循环处理
for symbol in "${SYMBOLS[@]}"; do
    log_message "📊 开始处理交易对: $symbol"
    
    for timeframe in "${TIMEFRAMES[@]}"; do
        log_message "  ⏰ 处理时间周期: $symbol $timeframe"
        
        # 记录开始时间
        start_time=$(date +%s)
        
        # 执行修复命令（30分钟超时）
        if timeout 1800 python3 manage.py repair_multi_timeframe_data \
            --symbol="$symbol" \
            --timeframe="$timeframe" \
            --start="$START_DATE" \
            --end="$END_DATE" >> "$LOG_FILE" 2>&1; then
            
            # 计算耗时
            end_time=$(date +%s)
            duration=$((end_time - start_time))
            
            log_message "  ✅ $symbol $timeframe 修复成功 (耗时: ${duration}秒)"
            ((SUCCESS_TASKS++))
        else
            # 计算耗时
            end_time=$(date +%s)
            duration=$((end_time - start_time))
            
            log_message "  ❌ $symbol $timeframe 修复失败 (耗时: ${duration}秒)"
            ((FAILED_TASKS++))
        fi
        
        # 显示进度
        COMPLETED_TASKS=$((SUCCESS_TASKS + FAILED_TASKS))
        PROGRESS=$((COMPLETED_TASKS * 100 / TOTAL_TASKS))
        log_message "📈 进度: $COMPLETED_TASKS/$TOTAL_TASKS ($PROGRESS%)"
        
        # 短暂休息，避免过载COS服务
        sleep 3
    done
    
    log_message "📊 $symbol 处理完成"
    echo ""
done

# 输出最终统计
log_message "🏁 批量修复完成!"
log_message "📊 最终统计:"
log_message "  ✅ 成功: $SUCCESS_TASKS 个任务"
log_message "  ❌ 失败: $FAILED_TASKS 个任务"
log_message "  📈 成功率: $((SUCCESS_TASKS * 100 / TOTAL_TASKS))%"

if [ $FAILED_TASKS -eq 0 ]; then
    log_message "🎉 所有时间周期数据修复成功！"
    log_message "💡 建议运行多时间周期完整性检查验证结果:"
    log_message "   python3 check_multi_timeframe_integrity.py --start-date=$START_DATE"
    exit 0
else
    log_message "⚠️ 部分任务失败，请检查日志详情"
    
    # 生成失败任务的重试脚本
    RETRY_SCRIPT="logs/retry_failed_timeframes.sh"
    log_message "📝 生成重试脚本: $RETRY_SCRIPT"
    
    cat > "$RETRY_SCRIPT" << EOF
#!/bin/bash
# 重试失败的时间周期修复任务
# 自动生成于 $(date)

cd "$(dirname "\${BASH_SOURCE[0]}")/.."

EOF
    
    # 这里可以添加失败任务的记录和重试逻辑
    chmod +x "$RETRY_SCRIPT"
    
    exit 1
fi