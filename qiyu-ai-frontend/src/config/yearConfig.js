/**
 * 🎯 智能年份计算配置
 * 根据数据更新周期和业务需求配置年份计算策略
 */

export const YEAR_CONFIG = {
  // 数据范围配置
  DATA_START_YEAR: 2017,        // 最早的数据年份
  MAX_HISTORY_YEARS: 8,         // 最多显示多少年的历史数据
  
  // 当前年份数据稳定性判断
  CURRENT_YEAR_CUTOFF_MONTH: 1, // 几月后认为当年数据稳定（1月就包含当年数据）
  
  // 数据更新周期配置
  DATA_UPDATE_SCHEDULE: {
    month: 1,                   // 每年1月更新数据
    description: '每年1月自动将上一年数据存入COS'
  },
  
  // COS路径策略配置
  COS_PATH_STRATEGY: {
    // 当前年份使用独立路径（便于实时更新）
    currentYearPath: '2025',
    // 历史年份使用统一路径（数据稳定）
    legacyPath: 'legacy'
  }
}

/**
 * 🎯 智能计算可用年份列表
 * @param {Object} customConfig - 自定义配置（可选）
 * @returns {string[]} 年份字符串数组，按时间倒序排列
 */
export const calculateSmartYears = (customConfig = {}) => {
  const config = { ...YEAR_CONFIG, ...customConfig }
  
  const now = new Date()
  const currentYear = now.getFullYear()
  const currentMonth = now.getMonth() + 1 // 0-based to 1-based
  
  // 判断当前年份数据是否稳定
  const isCurrentYearStable = currentMonth >= config.CURRENT_YEAR_CUTOFF_MONTH
  
  // 计算年份范围
  const years = []
  const startYear = Math.max(
    config.DATA_START_YEAR, 
    currentYear - config.MAX_HISTORY_YEARS
  )
  
  // 添加当前年份（如果数据稳定）
  if (isCurrentYearStable) {
    years.push(currentYear.toString())
  }
  
  // 添加历史年份
  for (let year = currentYear - 1; year >= startYear; year--) {
    years.push(year.toString())
  }
  
  return years
}

/**
 * 🔮 预测未来年份变化
 * 用于测试和演示年份计算逻辑
 */
export const simulateYearCalculation = () => {
  const scenarios = [
    { year: 2025, month: 1, description: '2025年1月 - 当年数据不稳定' },
    { year: 2025, month: 3, description: '2025年3月 - 当年数据稳定' },
    { year: 2026, month: 1, description: '2026年1月 - 新年数据不稳定' },
    { year: 2026, month: 3, description: '2026年3月 - 新年数据稳定' },
    { year: 2030, month: 6, description: '2030年6月 - 未来场景' }
  ]
  
  console.log('🔮 年份计算模拟:')
  scenarios.forEach(scenario => {
    // 模拟特定时间
    const mockDate = new Date(scenario.year, scenario.month - 1, 15)
    const originalDate = Date
    global.Date = class extends Date {
      constructor(...args) {
        if (args.length === 0) {
          return mockDate
        }
        return new originalDate(...args)
      }
      static now() {
        return mockDate.getTime()
      }
    }
    
    const years = calculateSmartYears()
    console.log(`  ${scenario.description}: [${years.join(', ')}]`)
    
    // 恢复原始Date
    global.Date = originalDate
  })
}

/**
 * 📊 获取年份计算统计信息
 */
export const getYearCalculationStats = () => {
  const years = calculateSmartYears()
  const now = new Date()
  const currentYear = now.getFullYear()
  const currentMonth = now.getMonth() + 1
  
  return {
    totalYears: years.length,
    yearRange: `${years[years.length - 1]} - ${years[0]}`,
    includesCurrentYear: years.includes(currentYear.toString()),
    currentTime: `${currentYear}年${currentMonth}月`,
    nextUpdateTime: `${currentYear + 1}年1月`,
    config: YEAR_CONFIG
  }
}

export default {
  YEAR_CONFIG,
  calculateSmartYears,
  simulateYearCalculation,
  getYearCalculationStats
}
