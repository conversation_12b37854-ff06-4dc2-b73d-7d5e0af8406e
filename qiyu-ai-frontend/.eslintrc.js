module.exports = {
  extends: ['react-app'],
  env: {
    browser: true,
    es6: true,
    node: true,
    jest: true, // 全局启用 jest 环境
  },
  overrides: [
    {
      files: ['**/__tests__/**/*', '**/*.{spec,test}.*'],
      env: {
        jest: true,
      },
      plugins: ['jest'],
      rules: {
        // Jest 基础规则
        'jest/no-disabled-tests': 'warn',
        'jest/no-focused-tests': 'error',
        'jest/no-identical-title': 'error',
        'jest/prefer-to-have-length': 'warn',
        'jest/valid-expect': 'error',
      },
    },
  ],
};